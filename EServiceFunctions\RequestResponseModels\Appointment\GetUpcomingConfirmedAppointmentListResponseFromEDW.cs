﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.RequestResponseModels.Appointment;

public class GetUpcomingConfirmedAppointmentListResponseFromEdw
{
    [Key]
    [Column("APPOINTMENT_ID")]
    public string? AppointmentId { get; set; }
    [Column("QID")]
    public long? QId { get; set; }
    [Column("QID_EXPIRY_DT")]
    public DateTime? QIdExpiryDt { get; set; }
    [Column("FIRST_NAME_EN")]
    public string? FNameEn { get; set; }
    [Column("MIDDLE_NAME_EN")]
    public string? MNameEn { get; set; }
    [Column("LAST_NAME_EN")]
    public string? LNameEn { get; set; }
    [Column("FIRST_NAME_AR")]
    public string? FNameAr { get; set; }
    [Column("MIDDLE_NAME_AR")]
    public string? MNameAr { get; set; }
    [Column("LAST_NAME_AR")]
    public string? LNameAr { get; set; }
    [Column("NATIONALITY_CODE")]
    public string? NationalityCode { get; set; }
    [Column("NATIONALITY_EN")]
    public string? NationalityEn { get; set; }
    [Column("NATIONALITY_AR")]
    public string? NationalityAr { get; set; }
    [Column("DOB")]
    public DateTime? Dob { get; set; }
    [Column("HC_NUMBER")]
    public string? HCNumber { get; set; }
    [Column("HC_EXPIRY_DATE")]
    public DateTime? HCExpiryDate { get; set; }
    [Column("GENDER_CODE")]
    public string? GenderCode { get; set; }
    [Column("GENDER_EN")]
    public string? GenderEn { get; set; }
    [Column("GENDER_AR")]
    public string? GenderAr { get; set; }
    [Column("PHONE_MOBILE")]
    public string? PhoneMobile { get; set; }
    [Column("ASSIGNED_HC_CODE")]
    public string? AggignedHCntCode { get; set; }
    [Column("ASSIGNED_HC_EN")]
    public string? AggignedHCntNameEn { get; set; }
    [Column("ASSIGNED_HC_ARB")]
    public string? AggignedHCntNameAr { get; set; }
    [Column("APPOINTMENT_HC_CODE")]
    public string? AppointmentHCntCode { get; set; }
    [Column("APPOINTMENT_HC_EN")]
    public string? AppointmentHCntNameEn { get; set; }
    [Column("APPOINTMENT_HC_ARB")]
    public string? AppointmentHCntNameAr { get; set; }
    [Column("APPOINTMENT_CLINIC_CODE")]
    public string? ClinicCode { get; set; }
    [Column("APPOINTMENT_CLINIC_EN")]
    public string? ClinicNameEn { get; set; }
    [Column("APPOINTMENT_CLINIC_ARB")]
    public string? ClinicNameAr { get; set; }
    [Column("GIS_ADDRESS_STREET")]
    public string? GisAddressStreet { get; set; }
    [Column("GIS_ADDRESS_BUILDING")]
    public string? GisAddressBuilding { get; set; }
    [Column("GIS_ADDRESS_ZONE")]
    public string? GisAddressZone { get; set; }
    [Column("GIS_ADDRESS_UNIT")]
    public string? GisAddressUnit { get; set; }
    [Column("PHYSICIAN_CODE")]
    public string? PhysicianId { get; set; }
    [Column("PHYSICIAN_FULL_NAME_EN")]
    public string? PhysicianFullNameEn { get; set; }
    [Column("PHYSICIAN_FULL_NAME_AR")]
    public string? PhysicianFullNameAr { get; set; }
    [Column("BOOKED_DATETIME")]
    public DateTime? BookedDateTime { get; set; }
    [Column("APPOINTMENT_DATETIME")]
    public DateTime? AppointmentDateTime { get; set; }
    [Column("APPOINTMENT_TYPE")]
    public string? AppointmentType { get; set; }
    [Column("APPOINTMENT_STATUS")]
    public string? AppointmentStatus { get; set; }
    [Column("APPOINTMENT_LOCATION")]
    public string? AppointmentLocation { get; set; }
    [NotMapped]
    public string? AppointmentFacility { get; set; }
}