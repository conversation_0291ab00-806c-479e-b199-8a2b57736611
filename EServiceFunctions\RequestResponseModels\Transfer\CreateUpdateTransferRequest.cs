﻿namespace EServiceFunctions.RequestResponseModels.Transfer;

public class CreateUpdateTransferRequest
{
    public long QId { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? Nationality { get; set; }
    public DateTime? Dob { get; set; }
    public bool? Consent { get; set; }
    public string? HCNumber { get; set; }
    public int? BNo { get; set; }
    public int? ZNo { get; set; }
    public int? SNo { get; set; }
    public int? UNo { get; set; }
    public string? CurrentHC { get; set; }
    public string? CatchmentHC { get; set; }
    public string? PrefHC { get; set; }
    public Guid? AttachId1 { get; set; }
    public Guid? AttachId2 { get; set; }
    public Guid? AttachId3 { get; set; }
    public Guid? AttachId4 { get; set; }
    public Guid? AttachId5 { get; set; }
    public Guid? AttachId6 { get; set; }
    public string? TransferReason { get; set; }
    public string? SubmittedBy { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }
    public DateTime? GisAddressUpdatedAt { get; set; }
    public bool? IsGisAddressManualyEntered { get; set; }
}