# GetFamilyPhysicianList

## Overview
Retrieves a filtered list of family physicians based on health center and various optional criteria.

## Endpoint
- **Route**: `familyphysicians`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **hcCode** (query, required): Health Center Code
- **gender** (query, optional): Filter by gender
  - 0: Female
  - 1: Male
  - 2: Unknown
- **language** (query, optional): Filter by language code (e.g., AR, EN)
- **speciality** (query, optional): Filter by specialty code
  - 1: NCD
  - 2: Postnatal/Antenatal
  - 3: Pediatrics
  etc.
- **skip** (query, optional): Number of records to skip
- **take** (query, optional): Number of records to take

## Response
- **200 OK**: Returns list of physicians
  ```json
  [
    {
      "PhyCode": "string",
      "PhyNameEn": "string",
      "PhyNameAr": "string",
      "Title": "string",
      "ClinicalTitleEn": "string",
      "ClinicalTitleAr": "string",
      "Language": "string",
      "Qualification": "string",
      "TotalExp": "integer",
      "SpltyCode": "string",
      "Gender": "string",
      "Image": "string",
      "OtherLanguage": "string",
      "OtherSpecialty": "string"
    }
  ]
  ```
- **204 No Content**: No physicians found
- **400 Bad Request**: Missing health center code

## Business Logic
1. Validates required health center code
2. Queries active physicians (STATUS = true)
3. Applies filters:
   - Health center code (required)
   - Gender (with code mapping)
   - Language
   - Specialty
4. Implements pagination
5. Orders results by physician code

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient filtering with Where clauses
- Pagination implementation
- Direct projection to response model

## Data Security
- Only returns active physicians
- Photo included only with consent
- Proper null handling for all fields

## Gender Code Mapping
- Input "2" → "0" (Female)
- Input "0" → "2" (Unknown)
- Other → "1" (Male)

## Multilingual Support
- Bilingual physician names
- Bilingual clinical titles
- Multiple language support through language codes
