﻿global using System;
global using System.Collections.Generic;
global using System.Data;
global using System.Diagnostics.CodeAnalysis;
global using System.IO;
global using System.Linq;
global using System.Net;
global using System.Runtime.Serialization;
global using System.Text;
global using System.Threading.Tasks;
global using System.Xml;
global using System.Xml.Linq;

global using Azure.Identity;
global using Azure.Security.KeyVault.Secrets;

global using EServiceFunctions.Helpers;
global using EServiceFunctions.RequestResponseModels;

global using Microsoft.Azure.Functions.Worker;
global using Microsoft.Azure.Functions.Worker.Http;
global using Microsoft.Azure.WebJobs;
global using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
global using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Extensions;
global using Microsoft.Data.SqlClient;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Logging;
global using Microsoft.OpenApi.Models;

global using Newtonsoft.Json;

global using Throw;

global using static System.Convert;
global using static System.Environment;
global using static System.Net.HttpStatusCode;
global using static System.StringComparison;
global using static System.Text.RegularExpressions.Regex;
global using static System.TimeSpan;

global using static MockDataLibrary.GlobalStatus;

global using static EServiceFunctions.Helpers.DateTimeHelper;
global using static EServiceFunctions.Helpers.RequestNumberHelper;
global using static EServiceFunctions.Helpers.ValidationHelper;
global using static EServiceFunctions.Helpers.UtilityHelper;

global using static MockDataLibrary.GlobalConstants;

global using static Newtonsoft.Json.JsonConvert;

global using HttpTrigger = Microsoft.Azure.Functions.Worker.HttpTriggerAttribute;