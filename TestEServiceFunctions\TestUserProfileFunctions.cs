﻿using EServiceFunctions.RequestResponseModels;

namespace TestEServiceFunctions;

[Collection("UserProfileFunctions")]
public class TestUserProfileFunctions(IRestLibrary restLibrary, ITestOutputHelper testOutputHelper)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetUserAndDependentListByQId()
    {
        // Arrange
        var request = GetRestRequest("userprofiles/user-dependents/{qId}");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddOrUpdateHeader(PhccEnvironment, "STG");

        // Act
        var response = await _client.GetAsync<GetUserProfileResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();
        response.Dependents.ThrowIfNull();
        var dependent = response.Dependents.ToList();

        // Assert
        True(dependent.Count > 0);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetUserAndDependentListByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("userprofiles/user-dependents/{qId}");
        request.AddUrlSegment("qId", "22222222211");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222211");
        request.AddOrUpdateHeader(PhccEnvironment, "STG");
        request.Method = Method.Get;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(NoContent == response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestValidateQId()
    {
        // Arrange
        var request = GetRestRequest("validate-qid/{qId}");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddOrUpdateHeader(PhccEnvironment, "STG");

        // Act
        var response = await _client.GetAsync<ValidateQIDFlags>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.isCitizen == false);
        True(response.isMinor == false);
        True(response.isHCardPresent == true);
        True(response.isHCardValid == true);
        True(response.isHCardinGrace == false);
        True(response.isQIDValid == true);
        True(response.isQIDinGrace == false);
        True(response.isFPhysicianAssigned == true);
        True(response.isHCenterAssigned == true);
        True(response.isGISAddressNotRecent == false);
        True(response.isOGCC == false);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestValidateWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("validate-qid/{qId}");
        request.AddUrlSegment("qId", "22222222211");
        request.AddHeader(PhccEnvironment, "STG");

        // Act
        var response = await _client.GetAsync<ValidateQIDFlags>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response is null);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task<long> TestCreateUserProfile()
    {
        // Arrange
        var request = GetRestRequest("userprofiles");
        var qId = RandomNumber(11);
        request.AddOrUpdateHeader(JwtClaimsQId, qId);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateUserProfile");

        var requestBody = new Faker<CreateUpdateUserProfileRequest>()
            .RuleFor(x => x.QId, qId)
            .RuleFor(x => x.PrefSMSLang, f => f.PickRandom("0", "1"))
            .RuleFor(x => x.PrefComMode, f => f.PickRandom("0", "1", "0,1"))
            .RuleFor(x => x.SecondaryPhoneMobile, f => f.Phone.PhoneNumber())
            .RuleFor(x => x.Consent, true)
            .Generate();

        request.AddJsonBody(requestBody);

        // Act
        var response = await _client.PostAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.StatusCode == Created);
        return response.StatusCode == Created ? requestBody.QId : 0;
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCreateUserProfileWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("userprofiles");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateUserProfile");
        request.Method = Method.Post;
        var requestBody = new CreateUpdateUserProfileRequest();
        request.AddJsonBody(requestBody);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        var responseObject = DeserializeObject<ErrorResponse>(response.Content!);

        // Assert
        Equal(BadRequest, response.StatusCode);
        Equal("Invalid request body. Please try again.", responseObject?.Message);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateUserProfileByQId_Should_Return_OK()
    {
        // Arrange
        const long qId = 22222222272;
        var request = GetRestRequest("UserProfiles/{qId}");
        request.AddUrlSegment("qId", qId);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateUserProfileByQID-123");
        request.AddOrUpdateHeader(JwtClaimsQId, qId);

        var requestBody = new Faker<CreateUpdateUserProfileRequest>()
            .RuleFor(x => x.QId, GetMoqUser().UserQId)
            .RuleFor(x => x.PrefSMSLang, f => f.PickRandom("0", "1"))
            .RuleFor(x => x.PrefComMode, f => f.PickRandom("0,1", "0,1", "0,1"))
            .RuleFor(x => x.SecondaryPhoneMobile, f => f.Phone.PhoneNumber())
            .RuleFor(x => x.Consent, true)
            .Generate();

        request.AddJsonBody(requestBody);

        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(OK == response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestUpdateUserProfileByQIdWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("UserProfiles/{qId}");
        request.AddUrlSegment("qId", 2323232323211);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateUserProfileByQID");
        request.Method = Method.Put;

        var requestBody = new Faker<CreateUpdateUserProfileRequest>()
            .RuleFor(x => x.QId, GetMoqUser().UserQId)
            .RuleFor(x => x.PrefSMSLang, f => f.PickRandom("0", "1"))
            .RuleFor(x => x.PrefComMode, f => f.PickRandom("0", "1", "0,1"))
            .RuleFor(x => x.SecondaryPhoneMobile, f => f.Phone.PhoneNumber())
            .RuleFor(x => x.Consent, true)
            .Generate();

        request.AddJsonBody(requestBody);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        False(response is null);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteUserProfileByqId()
    {
        // Arrange
        long qId;
        do
        {
            qId = await TestCreateUserProfile();
        } while (qId == 0);

        var request = GetRestRequest("UserProfiles/{qId}");
        request.AddUrlSegment("qId", qId);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-DeleteUserProfileByQID");
        request.AddOrUpdateHeader(JwtClaimsQId, qId);

        // Act
        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.StatusCode == OK);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestDeleteUserProfileByQIdWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("UserProfiles/{qId}");
        request.AddUrlSegment("qId", 2323232323211);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-DeleteUserProfileByQID");
        request.Method = Method.Delete;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        var responseObject = DeserializeObject<ErrorResponse>(response.Content!);

        // Assert
        Equal(BadRequest, response.StatusCode);
        Equal("Invalid QID 2323232323211", responseObject?.Message);
    }
}