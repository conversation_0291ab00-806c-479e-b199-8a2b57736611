# GetClinicShiftListByHealthCenterCode

## Overview
Retrieves clinic shift information for a specific health center with various filtering options.

## Endpoint
- **Route**: `healthcenters/{hcCode}/clinic-shift`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **hcCode** (path, required): Health center code
- **clinicCode** (query, optional): Filter by clinic code
- **workDay** (query, optional): Filter by work day
  - 1: Sunday
  - 2: Monday
  - 3: Tuesday
  - 4: Wednesday
  - 5: Thursday
  - 6: Friday
  - 7: Saturday
- **shift** (query, optional): Filter by shift time
  - 0: AM
  - 1: PM
  - 2: BOTH

## Response
- **200 OK**: Returns list of clinic shifts
  ```json
  [
    {
      "ClinicCode": "string",
      "ClinicNameEn": "string",
      "ClinicNameAr": "string",
      "WorkDays": "string",
      "ShiftTime": "string"
    }
  ]
  ```
- **204 No Content**: No clinic shifts found
- **400 Bad Request**: Invalid workDay or shift value

## Business Logic
1. Validates input parameters:
   - Work day must be 1-7
   - Shift must be 0-2
2. Joins ClinicShift and Clinic tables
3. Applies filters:
   - Health center code (required)
   - Clinic code
   - Work days
   - Shift time
4. Returns distinct results

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient join operation
- Distinct results to avoid duplicates

## Input Validation
- Work day format validation
- Shift time validation
- Proper error messages

## Multilingual Support
- Bilingual clinic names (English/Arabic)
