# Download Attachments Tests

## Overview

These tests verify the functionality of downloading file attachments from the system. The test suite covers various download scenarios, including single file downloads, batch downloads, and streaming of large files.

## Test Cases

### TestDownloadAttachment_ValidId_ReturnsFile

Tests the successful download of a valid attachment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDownloadAttachment_ValidId_ReturnsFile()
{
    // Arrange
    var attachmentId = await UploadTestFile();
    var request = GetRestRequest($"attachments/{attachmentId}/download");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
    NotNull(response.RawBytes);
    Equal("application/octet-stream", response.ContentType);
    NotEmpty(response.Headers.First(h => h.Name == "Content-Disposition").Value);
}
```

### TestDownloadMultipleAttachments_ValidIds_ReturnsZip

Tests downloading multiple attachments as a zip file.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDownloadMultipleAttachments_ValidIds_ReturnsZip()
{
    // Arrange
    var attachmentIds = new[]
    {
        await UploadTestFile("test1.pdf", "application/pdf"),
        await UploadTestFile("test2.jpg", "image/jpeg")
    };

    var request = GetRestRequest("attachments/batch-download");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    request.AddJsonBody(new { AttachmentIds = attachmentIds });

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
    Equal("application/zip", response.ContentType);
    NotNull(response.RawBytes);
    True(response.RawBytes.Length > 0);
}
```

## Request/Response Models

### DownloadRequest
```csharp
public class DownloadRequest
{
    public string AttachmentId { get; set; }
    public bool Inline { get; set; }
}
```

### BatchDownloadRequest
```csharp
public class BatchDownloadRequest
{
    public List<string> AttachmentIds { get; set; }
    public string ZipFileName { get; set; }
}
```

### DownloadResponse
```csharp
public class DownloadResponse
{
    public Stream FileStream { get; set; }
    public string FileName { get; set; }
    public string ContentType { get; set; }
    public long ContentLength { get; set; }
}
```

## Implementation Details

### 1. Single File Download
```csharp
private async Task<DownloadResponse> DownloadFile(string attachmentId)
{
    var attachment = await GetAttachmentOrThrow(attachmentId);
    await ValidateDownloadPermission(attachment);

    var filePath = GetStoragePath(attachment);
    var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);

    return new DownloadResponse
    {
        FileStream = stream,
        FileName = attachment.FileName,
        ContentType = attachment.ContentType,
        ContentLength = new FileInfo(filePath).Length
    };
}
```

### 2. Batch Download
```csharp
private async Task<Stream> CreateDownloadZip(List<string> attachmentIds)
{
    var memoryStream = new MemoryStream();
    using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
    {
        foreach (var id in attachmentIds)
        {
            var attachment = await GetAttachmentOrThrow(id);
            var entry = archive.CreateEntry(attachment.FileName);
            
            using (var entryStream = entry.Open())
            using (var fileStream = File.OpenRead(GetStoragePath(attachment)))
            {
                await fileStream.CopyToAsync(entryStream);
            }
        }
    }

    memoryStream.Position = 0;
    return memoryStream;
}
```

### 3. Large File Streaming
```csharp
private async Task StreamLargeFile(
    string attachmentId,
    Stream outputStream,
    IProgress<int> progress)
{
    var attachment = await GetAttachmentOrThrow(attachmentId);
    var buffer = new byte[81920]; // 80KB chunks
    var totalBytesRead = 0;

    using (var fileStream = File.OpenRead(GetStoragePath(attachment)))
    {
        int bytesRead;
        while ((bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
        {
            await outputStream.WriteAsync(buffer, 0, bytesRead);
            totalBytesRead += bytesRead;
            progress.Report((int)((double)totalBytesRead / fileStream.Length * 100));
        }
    }
}
```

## Performance Optimization

### 1. Range Request Support
```csharp
private async Task<Stream> HandleRangeRequest(
    string attachmentId,
    RangeHeaderValue range)
{
    var attachment = await GetAttachmentOrThrow(attachmentId);
    var fileStream = File.OpenRead(GetStoragePath(attachment));

    if (range == null)
        return fileStream;

    var start = range.Ranges.First().From ?? 0;
    var end = range.Ranges.First().To ?? fileStream.Length - 1;

    fileStream.Seek(start, SeekOrigin.Begin);
    return new LimitedStream(fileStream, end - start + 1);
}
```

### 2. Caching Strategy
```csharp
private async Task<Stream> GetCachedFile(string attachmentId)
{
    var cacheKey = $"file_{attachmentId}";
    
    if (_cache.TryGetValue(cacheKey, out byte[] cachedData))
        return new MemoryStream(cachedData);

    var fileData = await ReadFileData(attachmentId);
    var cacheOptions = new MemoryCacheEntryOptions
    {
        SlidingExpiration = TimeSpan.FromMinutes(30),
        Size = fileData.Length
    };

    _cache.Set(cacheKey, fileData, cacheOptions);
    return new MemoryStream(fileData);
}
```

## Error Handling

### 1. Download Validation
```csharp
private async Task ValidateDownload(string attachmentId)
{
    var attachment = await GetAttachmentOrThrow(attachmentId);

    if (!File.Exists(GetStoragePath(attachment)))
        throw new FileNotFoundException($"File not found for attachment {attachmentId}");

    if (!await HasDownloadPermission(attachment))
        throw new UnauthorizedException("Not authorized to download this file");

    if (IsFileLocked(GetStoragePath(attachment)))
        throw new IOException("File is currently in use");
}
```

### 2. Batch Download Validation
```csharp
private async Task ValidateBatchDownload(List<string> attachmentIds)
{
    if (attachmentIds.Count > MaxBatchSize)
        throw new ValidationException($"Cannot download more than {MaxBatchSize} files at once");

    var totalSize = await GetTotalSize(attachmentIds);
    if (totalSize > MaxBatchDownloadSize)
        throw new ValidationException($"Total download size exceeds {MaxBatchDownloadSize} bytes");
}
```

## Security Considerations

### 1. Access Control
```csharp
private async Task<bool> HasDownloadPermission(Attachment attachment)
{
    var user = GetCurrentUser();
    
    // Check direct access
    if (attachment.CreatedBy == user.Id)
        return true;

    // Check shared access
    var shared = await _sharingRepository.GetSharedAccess(
        attachment.Id,
        user.Id
    );
    
    return shared != null && shared.HasDownloadPermission;
}
```

### 2. Rate Limiting
```csharp
private async Task EnforceRateLimit(string userId)
{
    var key = $"download_rate_{userId}";
    var count = await _cache.GetOrCreateAsync(key, entry =>
    {
        entry.AbsoluteExpiration = DateTimeOffset.Now.AddHours(1);
        return Task.FromResult(0);
    });

    if (count >= MaxDownloadsPerHour)
        throw new RateLimitException("Download rate limit exceeded");

    await _cache.IncrementAsync(key);
}
```

## Notes

1. **File Access**
   - Implement proper cleanup
   - Handle concurrent access
   - Monitor download speeds

2. **Performance**
   - Support range requests
   - Implement caching
   - Use appropriate chunk sizes

3. **Security**
   - Validate permissions
   - Implement rate limiting
   - Track download history

## Recommendations

1. **Download Strategy**
   - Support resumable downloads
   - Add progress tracking
   - Implement bandwidth throttling

2. **Security Measures**
   - Add download logging
   - Implement IP-based limits
   - Track unusual patterns

3. **Performance**
   - Optimize chunk size
   - Use CDN for large files
   - Implement compression
