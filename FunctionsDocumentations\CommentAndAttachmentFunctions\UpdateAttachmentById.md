# UpdateAttachmentById

## Overview
Updates an existing attachment using JSON request body.

## Endpoint
- **Route**: `attachments/{id}`
- **Method**: PUT
- **Authorization**: Function level

## Parameters
- `id` (path, required): Attachment ID in GUID format

## Headers
- `X-RequestOrigin`: Required header indicating request origin

## Request Body
```json
{
  "AttachmentName": "string",
  "AttachmentType": "string",
  "AttachmentData": "base64string",
  "SubmitterQId": "long"
}
```

## Responses
- **200 OK**: Returns updated attachment
- **400 Bad Request**: 
  - Invalid ID format
  - Invalid request body
  - Invalid file type
  - File size exceeds limit
- **401 Unauthorized**: QID mismatch
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates attachment ID format
2. Validates request origin header
3. Checks QID authorization
4. Validates file extension and size
5. Updates attachment record
6. Returns updated attachment

## Validation Rules
- Maximum file size: 5MB
- File extension must be valid
- Request origin must be provided
- QID authorization required
- Attachment must exist

## Dependencies
- CommentAndAttachmentContext
- Authorization middleware
- File validation utilities
