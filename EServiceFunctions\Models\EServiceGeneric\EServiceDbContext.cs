﻿namespace EServiceFunctions.Models.EServiceGeneric;

public class EServiceDbContext(DbContextOptions<EServiceDbContext> options) : DbContext(options)
{
    public virtual DbSet<MyTaskStatusStats>? MyTaskStatusStat { get; set; }
    public virtual DbSet<StaticDropDownList>? StaticDDLs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<MyTaskStatusStats>(entity =>
        {
            entity.HasNoKey();
        });
    }
}