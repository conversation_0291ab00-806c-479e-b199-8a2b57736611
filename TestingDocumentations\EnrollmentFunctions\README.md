# Enrollment Functions Test Documentation

## Overview

This test suite verifies the functionality of enrollment-related operations in the EServices REST API. The tests are implemented in `TestEnrollmentFunctions.cs`.

## Test Categories

### 1. Enrollment Retrieval
- [Get Enrollment List](./GetEnrollmentList.md) - Get enrollments by QID
- [Get Enrollment Statistics](./GetEnrollmentStats.md) - Get enrollment statistics by QID
- [Get Enrollment Item](./GetEnrollmentItem.md) - Get enrollment by request number

### 2. Enrollment Validation
- [Check In-Process Enrollment](./CheckInProcessEnrollment.md) - Validate in-process enrollments

### 3. Enrollment Management
- [Create Enrollment](./CreateEnrollment.md) - Create new enrollments
- [Update Enrollment](./UpdateEnrollment.md) - Update existing enrollments
- [Delete Enrollment](./DeleteEnrollment.md) - Delete enrollments

## Common Components

### Headers
```csharp
JwtClaimsQId: Authentication QID
IsApp: Application identifier
RequestOrigin: Origin identifier for tracking
```

### Test Data Generation
```csharp
public static CreateUpdateEnrollmentRequest GenerateEnrollment()
{
    var enrollment = new Faker<CreateUpdateEnrollmentRequest>()
        .RuleFor(x => x.QId, RandomNumber(11))
        .RuleFor(x => x.HCNumber, f => f.Random.AlphaNumeric(10))
        .RuleFor(x => x.FNameEn, f => f.Name.FirstName())
        // ... other rules
        .Generate();

    return enrollment;
}
```

## Test Environment

### Prerequisites
- Valid test user credentials
- Test data generation utilities
- REST client configuration

### Configuration
- Base URL: EServices REST API endpoint
- Authentication: JWT-based
- Test Data: Faker-generated

## Best Practices

1. **Test Data**
   - Use Faker for realistic data
   - Include both English and Arabic
   - Generate unique IDs

2. **Authentication**
   - Use valid JWT QID claims
   - Maintain test credentials
   - Handle token expiry

3. **Validation**
   - Check response status
   - Validate data integrity
   - Handle error cases

## Error Handling

1. **HTTP Status Codes**
   - 200: Success
   - 400: Bad Request
   - 404: Not Found

2. **Response Validation**
   - Null checks
   - Data type validation
   - Business rule validation

## Notes

1. **Request Numbers**
   - 11 characters length
   - Unique per enrollment
   - Required for updates/deletes

2. **QID Validation**
   - 11 digits
   - Format validation
   - Existence check

3. **Performance**
   - Response time monitoring
   - Resource cleanup
   - Async operations
