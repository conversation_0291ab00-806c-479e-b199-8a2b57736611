﻿namespace EServiceFunctions.RequestResponseModels.Appointment;

public class GetAppointmentItemResponse
{
    public long? QId { get; set; }
    public string? ReqNumber { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? Gender { get; set; }
    public string? SecondaryPhoneMobile { get; set; }
    public bool? CancellationCall { get; set; }
    public bool? Consent { get; set; }
    public string? Nationality { get; set; }
    public string? Dob { get; set; }
    public string? HcNumber { get; set; }
    public string? Clinic { get; set; }
    public string? ClinicTime { get; set; }
    public string? PrefDate { get; set; }
    public string? AmPm { get; set; }
    public string? AmPmDescriptionEn { get; set; }
    public string? AmPmDescriptionAr { get; set; }
    public string? SelectedHc { get; set; }
    public string? RequestType { get; set; }
    public string? RequestTypeDescriptionEn { get; set; }
    public string? RequestTypeDescriptionAr { get; set; }
    public string? PrefContactTime { get; set; }
    public string? PrefContactTimeDescriptionEn { get; set; }
    public string? PrefContactTimeDescriptionAr { get; set; }
    public string? SubmittedBy { get; set; }
    public string? SubmittedAt { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }
    public string? CreatedAt { get; set; }
    public string? UpdatedAt { get; set; }
    public string? Status { get; set; }
    public string? StatusDescriptionEn { get; set; }
    public string? StatusDescriptionAr { get; set; }
    public string? SN { get; set; }
    public long? AppointmentId { get; set; }
}