# Booking Validation Tests

## Overview

These tests verify the validation rules and constraints for appointment booking. The test suite ensures proper validation of booking requests, handling of invalid inputs, and enforcement of business rules.

## Test Cases

### TestBookingValidation_ValidRequest_PassesValidation

Tests that a valid booking request passes all validation rules.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestBookingValidation_ValidRequest_PassesValidation()
{
    // Arrange
    var request = GetRestRequest("appointments/validate");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var bookingRequest = new BookingRequest
    {
        QId = GetMoqUser().UserQId.ToString(),
        PreferredDate = DateTime.Now.AddDays(1),
        ServiceType = "General",
        Location = "Main Clinic"
    };
    request.AddJsonBody(bookingRequest);

    // Act
    var response = await _client.ExecuteAsync<ValidationResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    NotNull(response.Data);
    True(response.Data.IsValid);
    Empty(response.Data.ValidationErrors);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid booking request
  - Complete required fields
- **Expected Output**: 
  - Validation success
  - No validation errors
- **Validation Points**:
  - Response success
  - Validation status
  - Error list empty

### TestBookingValidation_InvalidRequest_ReturnsErrors

Tests the validation response for an invalid booking request.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestBookingValidation_InvalidRequest_ReturnsErrors()
{
    // Arrange
    var request = GetRestRequest("appointments/validate");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var bookingRequest = new BookingRequest
    {
        QId = GetMoqUser().UserQId.ToString(),
        PreferredDate = DateTime.Now.AddDays(-1), // Past date
        ServiceType = "Invalid" // Invalid service
    };
    request.AddJsonBody(bookingRequest);

    // Act
    var response = await _client.ExecuteAsync<ValidationResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    NotNull(response.Data);
    False(response.Data.IsValid);
    NotEmpty(response.Data.ValidationErrors);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Invalid booking data
- **Expected Output**: Validation errors
- **Validation Points**:
  - Response success
  - Invalid status
  - Error messages present

## Request/Response Models

### BookingRequest
```csharp
public class BookingRequest
{
    public string QId { get; set; }
    public DateTime PreferredDate { get; set; }
    public string ServiceType { get; set; }
    public string Location { get; set; }
    public string? Notes { get; set; }
}
```

### ValidationResponse
```csharp
public class ValidationResponse
{
    public bool IsValid { get; set; }
    public List<ValidationError> ValidationErrors { get; set; }
    public Dictionary<string, string> ValidationMessages { get; set; }
}
```

## Validation Rules

1. **Required Fields**
   - QId
   - PreferredDate
   - ServiceType
   - Location

2. **Date Validation**
   - Must be future date
   - Within booking window
   - Valid business day

3. **Service Validation**
   - Valid service type
   - Available at location
   - User eligible for service

## Validation Implementation

### 1. Field Validation
```csharp
private List<ValidationError> ValidateFields(BookingRequest request)
{
    var errors = new List<ValidationError>();

    if (string.IsNullOrEmpty(request.QId))
        errors.Add(new ValidationError("QId", "QId is required"));

    if (request.PreferredDate == default)
        errors.Add(new ValidationError("PreferredDate", "Preferred date is required"));

    if (string.IsNullOrEmpty(request.ServiceType))
        errors.Add(new ValidationError("ServiceType", "Service type is required"));

    if (string.IsNullOrEmpty(request.Location))
        errors.Add(new ValidationError("Location", "Location is required"));

    return errors;
}
```

### 2. Business Rule Validation
```csharp
private async Task<List<ValidationError>> ValidateBusinessRules(BookingRequest request)
{
    var errors = new List<ValidationError>();

    // Date validation
    if (request.PreferredDate <= DateTime.Now)
        errors.Add(new ValidationError("PreferredDate", "Date must be in the future"));

    if (!await IsValidServiceType(request.ServiceType))
        errors.Add(new ValidationError("ServiceType", "Invalid service type"));

    if (!await IsLocationAvailable(request.Location, request.PreferredDate))
        errors.Add(new ValidationError("Location", "Location not available"));

    return errors;
}
```

### 3. User Eligibility
```csharp
private async Task<List<ValidationError>> ValidateUserEligibility(string qId, string serviceType)
{
    var errors = new List<ValidationError>();
    var user = await GetUserProfile(qId);

    if (!user.IsActive)
        errors.Add(new ValidationError("QId", "User account is not active"));

    if (!await IsEligibleForService(user, serviceType))
        errors.Add(new ValidationError("ServiceType", "User not eligible for this service"));

    return errors;
}
```

## Error Handling

### 1. Validation Error Collection
```csharp
public class ValidationError
{
    public string Field { get; set; }
    public string Message { get; set; }
    public string Code { get; set; }
    public ValidationSeverity Severity { get; set; }
}
```

### 2. Error Response Format
```csharp
private ValidationResponse CreateErrorResponse(List<ValidationError> errors)
{
    return new ValidationResponse
    {
        IsValid = false,
        ValidationErrors = errors,
        ValidationMessages = errors.ToDictionary(
            e => e.Field,
            e => e.Message
        )
    };
}
```

## Test Helper Methods

### 1. Test Data Generation
```csharp
private BookingRequest CreateValidBookingRequest()
{
    return new BookingRequest
    {
        QId = GetMoqUser().UserQId.ToString(),
        PreferredDate = DateTime.Now.AddDays(1),
        ServiceType = "General",
        Location = "Main Clinic"
    };
}
```

### 2. Validation Helpers
```csharp
private async Task<bool> IsValidServiceType(string serviceType)
{
    var validTypes = await _cache.GetOrAddAsync(
        "valid_service_types",
        async () => await _serviceRepository.GetActiveServiceTypes()
    );
    return validTypes.Contains(serviceType);
}
```

## Performance Optimization

1. **Caching Validation Data**
```csharp
private async Task<HashSet<string>> GetCachedServiceTypes()
{
    return await _cache.GetOrAddAsync(
        "service_types",
        async () => await _serviceRepository.GetAllServiceTypes(),
        TimeSpan.FromHours(1)
    );
}
```

2. **Parallel Validation**
```csharp
private async Task<List<ValidationError>> ValidateAllRules(BookingRequest request)
{
    var validationTasks = new[]
    {
        ValidateFields(request),
        ValidateBusinessRules(request),
        ValidateUserEligibility(request.QId, request.ServiceType)
    };

    var results = await Task.WhenAll(validationTasks);
    return results.SelectMany(x => x).ToList();
}
```

## Notes

- Comprehensive validation rules
- Clear error messages
- Optimized performance
- Cached validation data
- Parallel validation execution

## Recommendations

1. **Validation Strategy**
   - Layer validations (field, business, user)
   - Cache validation rules
   - Use async validation

2. **Error Handling**
   - Clear error messages
   - Validation codes
   - Severity levels

3. **Performance**
   - Cache validation data
   - Parallel validation
   - Optimize queries
