﻿namespace EServiceFunctions.Models.EServiceGeneric;

public class EServiceContext(DbContextOptions<EServiceContext> options) : DbContext(options)
{
    public virtual DbSet<Shift>? Shift { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Shift>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Shift__A25C5AA613765CB3");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);
        });
    }
}