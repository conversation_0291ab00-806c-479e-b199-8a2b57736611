# Delete Comments Tests

## Overview

These tests verify the functionality of deleting comments from the system. The test suite covers various deletion scenarios including permission validation and attachment cleanup.

## Test Cases

### TestDeleteComment_ValidRequest_ReturnsOk

Tests the successful deletion of a comment by its author.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteComment_ValidRequest_ReturnsOk()
{
    // Arrange
    var commentId = await CreateTestComment();
    var request = GetRestRequest($"comments/{commentId}");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: Valid comment ID
- **Expected Output**: 200 OK response
- **Validation Points**:
  - Response success
  - Comment removal
  - Attachment cleanup

### TestDeleteComment_WithAttachments_ReturnsOk

Tests deletion of a comment with attached files.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteComment_WithAttachments_ReturnsOk()
{
    // Arrange
    var commentId = await CreateTestCommentWithAttachments();
    var request = GetRestRequest($"comments/{commentId}");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
    
    // Verify attachments cleanup
    var attachments = await GetAttachments(commentId);
    Empty(attachments);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: Comment ID with attachments
- **Expected Output**: 
  - Success response
  - Cleaned up attachments
- **Validation Points**:
  - Comment deletion
  - Attachment removal
  - Storage cleanup

## Deletion Rules

1. **Permission Rules**
   - Author only
   - Admin override
   - Time window

2. **Attachment Rules**
   - Remove files
   - Clean storage
   - Update references

3. **Validation Rules**
   - Comment exists
   - Valid permissions
   - Status check

## Implementation Details

### 1. Delete Processing
```csharp
private async Task DeleteComment(string commentId)
{
    var comment = await GetCommentOrThrow(commentId);
    await ValidateDeletePermission(comment);

    // Remove attachments first
    await CleanupAttachments(comment);

    // Remove comment
    await _commentRepository.DeleteAsync(comment);

    // Clear cache
    await InvalidateCache(comment.EntityId);
}
```

### 2. Attachment Cleanup
```csharp
private async Task CleanupAttachments(Comment comment)
{
    foreach (var attachment in comment.Attachments)
    {
        // Remove from storage
        await _storageService.DeleteFileAsync(attachment.StoragePath);

        // Remove from database
        await _attachmentRepository.DeleteAsync(attachment);
    }
}
```

### 3. Permission Validation
```csharp
private async Task ValidateDeletePermission(Comment comment)
{
    var currentUser = GetCurrentUser();
    
    if (comment.CreatedBy != currentUser && !IsAdmin(currentUser))
        throw new UnauthorizedException("Insufficient permissions to delete this comment");

    if (!IsAdmin(currentUser))
    {
        var deleteWindow = DateTime.UtcNow - comment.CreatedAt;
        if (deleteWindow > TimeSpan.FromHours(24))
            throw new ValidationException("Comment can only be deleted within 24 hours");
    }
}
```

## Error Handling

### 1. Not Found Handling
```csharp
private async Task<Comment> GetCommentOrThrow(string commentId)
{
    var comment = await _commentRepository.GetByIdAsync(commentId);
    if (comment == null)
        throw new NotFoundException($"Comment {commentId} not found");
    return comment;
}
```

### 2. Permission Errors
```csharp
private void ValidateUserPermissions(string userId, Comment comment)
{
    if (comment.CreatedBy != userId && !IsAdmin(userId))
        throw new UnauthorizedException("User not authorized to delete this comment");
}
```

### 3. Cleanup Errors
```csharp
private async Task SafeCleanupAttachments(Comment comment)
{
    var errors = new List<string>();

    foreach (var attachment in comment.Attachments)
    {
        try
        {
            await _storageService.DeleteFileAsync(attachment.StoragePath);
        }
        catch (Exception ex)
        {
            errors.Add($"Failed to delete attachment {attachment.Id}: {ex.Message}");
            // Log error
        }
    }

    if (errors.Any())
        throw new CleanupException("Some attachments could not be deleted", errors);
}
```

## Test Helper Methods

### 1. Comment Creation
```csharp
private async Task<string> CreateTestCommentWithAttachments()
{
    var comment = new Comment
    {
        Content = "Test content",
        Type = "Test",
        CreatedBy = GetMoqUser().UserQId.ToString(),
        CreatedAt = DateTime.UtcNow
    };

    // Add test attachments
    comment.Attachments = new List<Attachment>
    {
        await CreateTestAttachment(),
        await CreateTestAttachment()
    };

    await _commentRepository.AddAsync(comment);
    return comment.Id;
}
```

### 2. Attachment Verification
```csharp
private async Task<bool> VerifyAttachmentCleanup(string commentId)
{
    var storageFiles = await _storageService.ListFilesAsync($"comments/{commentId}");
    var dbAttachments = await _attachmentRepository.GetByCommentIdAsync(commentId);

    return !storageFiles.Any() && !dbAttachments.Any();
}
```

## Notes

- Implements safe deletion
- Handles attachment cleanup
- Validates permissions
- Maintains data integrity
- Proper error handling

## Recommendations

1. **Deletion Strategy**
   - Consider soft delete
   - Implement recovery
   - Track deletion history

2. **Cleanup Process**
   - Batch processing
   - Async cleanup
   - Retry mechanism

3. **Performance**
   - Optimize queries
   - Handle large attachments
   - Manage cache
