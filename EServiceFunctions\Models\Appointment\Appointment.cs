﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.Appointment;

public class Appointment
{
    public int Id { get; set; }
    public long? QId { get; set; }
    public string? ReqNumber { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? Gender { get; set; }
    public string? SecondaryPhoneMobile { get; set; }
    public bool? CancellationCall { get; set; }
    public bool? Consent { get; set; }
    public string? Nationality { get; set; }
    public DateTime? Dob { get; set; }
    public string? HCNumber { get; set; }
    public string? Clinic { get; set; }
    public string? ClinicTime { get; set; }
    public DateTime? PrefDate { get; set; }
    public string? AmPm { get; set; }
    public string? SelectedHC { get; set; }
    public string? RequestType { get; set; }
    public string? PrefContactTime { get; set; }
    public string? SubmittedBy { get; set; }
    public DateTime? SubmittedAt { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Status { get; set; }
    public string? StatusInternal { get; set; }
    public string? SN { get; set; }
    public long? AppointmentId { get; set; }
    public string? CreateSource { get; set; }
    public string? UpdateSource { get; set; }
    public Status? StatusNavigation { get; set; }
    public AppointmentRequestType? AppointmentRequestTypeNavigation { get; set; }
    public Shift? AmPmShiftNavigation { get; set; }
    public Shift? PrefContactTimeShiftNavigation { get; set; }
}