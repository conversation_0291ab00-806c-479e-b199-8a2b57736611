﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.HealthCenter;

[Table("Mrd_Clinic_Shifts")]
public class ClinicShift
{   
    [Key]
    public int ID { get; set; }
    [Column("FAC_CODE")]
    public string? HCntCode { get; set; }
    [Column("CLINIC_CODE")]
    public string? ClinicCode { get; set; }
    [Column("AVAILABLE_DAYS")]
    public string? WorkDays { get; set; }
    [Column("SHIFT_TIME")]
    public int? ShiftTime { get; set; }
    public DateTime? CREATED_DATE { get; set; }
}