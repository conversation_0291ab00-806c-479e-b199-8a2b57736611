# Get MHDS Item Tests

## Test Cases

### 1. TestGetMhdsItemByReqNumber

Tests retrieval of MHDS item by request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetMhdsItemByReqNumber()
{
    // Arrange
    var request = GetRestRequest("mhds/{reqNumber}");
    request.AddUrlSegment("reqNumber", "MZ6NXCTNS4W");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

    // Act
    var response = await _client.GetAsync<GetMHDSItemResponse>(request);

    // Assert
    True(response?.ReqNumber == "MZ6NXCTNS4W");
    True(response.ReqNumber is not null);
}
```

### 2. TestGetMhdsItemByReqNumberWithInvalidReqNumber

Tests retrieval with invalid request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetMhdsItemByReqNumberWithInvalidReqNumber()
{
    // Arrange
    var reqNumber = "InvalidReqNumber";
    var request = GetRestRequest("mhds/{reqNumber}");
    request.AddUrlSegment("reqNumber", reqNumber);

    // Act
    var response = await _client.GetAsync<GetMHDSItemResponse>(request);

    // Assert
    True(response is null);
}
```

## Request Details

### Endpoint
```
GET mhds/{reqNumber}
```

### Headers
- `JwtClaimsQId`: "22222222270"

### URL Parameters
- `reqNumber`: MHDS request number

## Response Model

### GetMHDSItemResponse
```csharp
public class GetMHDSItemResponse
{
    public string ReqNumber { get; set; }
    // Other MHDS request details
}
```

## Test Data

### Success Case
- Request Number: "MZ6NXCTNS4W"
- Expected: Valid MHDS item

### Error Case
- Request Number: "InvalidReqNumber"
- Expected: Null response

## Validation Rules

### Success Case Validation
1. Response not null
2. Request number matches
3. Request number not null

### Error Case Validation
1. Response is null for invalid request

## Error Cases

1. **Invalid Request Number**
   - Non-existent request number
   - Malformed request number
   - Invalid format

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - Insufficient permissions

3. **Not Found Cases**
   - Deleted requests
   - Expired requests
   - Invalid status

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID validation
   - Permission checks

2. **Request Number Format**
   - Case sensitive
   - Specific format required
   - Length validation

3. **Response Handling**
   - Null for not found
   - Complete data for found
   - Error status codes
