﻿namespace TestEServiceFunctions;

[Collection("ReleaseOfInformationFunctions")]
public class TestReleaseOfInformationFunctions(IRestLibrary restLibrary, ITestOutputHelper testOutputHelper)
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetReleaseListByQId()
    {
        var request = GetRestRequest("releases/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222214);
        request.AddUrlSegment("qId", 22222222214);

        var response = await _client.GetAsync<List<GetReleaseListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        True(response.Count > 0);
        Contains(response, x => x.ReqNumber == "ZOVR92346");
        Contains(response, x => x.FNameEn == "TEST1");
        Contains(response, x => x.MNameEn == "6338466");
        Contains(response, x => x.LNameEn == "TEST16338466");
        Contains(response, x => x.FNameAr == EmptyString);
        Contains(response, x => x.Status == "Saved");
        Contains(response, x => x.StatusDescriptionEn == "Saved");
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetReleaseStatsByQId()
    {
        var request = GetRestRequest("releases/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222214);
        request.AddUrlSegment("qId", 22222222214);

        var response = await _client.GetAsync<GetReleaseStatsResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        True(response.Count > 0);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessReleaseByQId()
    {
        // Arrange
        var request = GetRestRequest("releases/{qId}/inprocess-validation");
        request.AddUrlSegment("qId", 22222222214);

        // Act
        var response = await _client.GetAsync<CheckInProcessReleaseResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.IsInprocessExist);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetReleaseItemByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("releases/{reqNumber}");
        request.AddUrlSegment("reqNumber", "ZOVR92346");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222214);

        // Act
        var response = await _client.GetAsync<GetReleaseItemResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal("ZOVR92346", response.ReqNumber);
        Equal("TEST1", response.FNameEn);
        Equal("6338466", response.MNameEn);
        Equal("TEST16338466", response.LNameEn);
        Equal(EmptyString, response.FNameAr);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task<string> TestCreateRelease()
    {
        // Arrange
        var request = CreateRelease();
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222214);

        // Act
        var response = await _client.PostAsync<CreateReleaseResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response.ReqNumber);
        NotEmpty(response.ReqNumber);
        return response.ReqNumber;
    }

    private static RestRequest CreateRelease()
    {
        var countries = MockNationality().Select(x => x.NatCode).ToList();
        var request = GetRestRequest("releases");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateRelease");

        var requestBody = new Faker<CreateUpdateReleaseRequest>()
            .RuleFor(x => x.QId, RandomNumber(11))
            .RuleFor(x => x.FNameEn, FirstNameEnglish)
            .RuleFor(x => x.MNameEn, MiddleNameEnglish)
            .RuleFor(x => x.LNameEn, LastNameEnglish)
            .RuleFor(x => x.FNameAr, FirstNameArabic)
            .RuleFor(x => x.MNameAr, MiddleNameArabic)
            .RuleFor(x => x.LNameAr, LastNameArabic)
            .RuleFor(x => x.HCNumber, "HC" + RandomNumber(8))
            .RuleFor(x => x.Nationality, f => f.PickRandom(countries))
            .RuleFor(x => x.Dob, f => f.Date.Past(50))
            .RuleFor(x => x.CurrentAssignedHC, "HC" + RandomNumber(8))
            .RuleFor(x => x.AttendanceRptStartDate, f => f.Date.Past(50))
            .RuleFor(x => x.AttendanceRptEndDate, f => f.Date.Past(50))
            .RuleFor(x => x.ReleaseItems,
                f => f.PickRandom("RI001", "RI002", "RI002", "RI004", "RI002", "RI003", "RI1", "RI2", "RI3", "RI001",
                    "RI002", "RI001", "RI002", "RI001", "RI003", "RI008", "RI009", "RI002", "RI005", "RI007"))
            .RuleFor(x => x.OtherReleaseItems, f => f.Lorem.Sentence())
            .RuleFor(x => x.EncounterStartDate, f => f.Date.Past(50))
            .RuleFor(x => x.EncounterEndDate, f => f.Date.Past(50))
            .RuleFor(x => x.PickupDelegated, f => f.Random.Bool())
            .RuleFor(x => x.AuthorizedQId, RandomNumber(11))
            .RuleFor(x => x.AuthorizedPersonName, f => f.Name.FullName())
            .RuleFor(x => x.AuthorizedPersonRelation,
                f => f.PickRandom("Father", "Mother", "Brother", "Sister", "Friend", "Other"))
            .RuleFor(x => x.DelegationOthers, f => f.Lorem.Sentence())
            .RuleFor(x => x.AttachId1, NewGuid())
            .RuleFor(x => x.AttachId2, NewGuid())
            .RuleFor(x => x.AttachId3, NewGuid())
            .RuleFor(x => x.AttachId4, NewGuid())
            .RuleFor(x => x.AttachId5, NewGuid())
            .RuleFor(x => x.AttachId6, NewGuid())
            .RuleFor(x => x.SubmittedBy, f => f.PickRandom("Self", "AuthorizedPerson"))
            .RuleFor(x => x.SubmittedAt, f => f.Date.Past(50))
            .RuleFor(x => x.SubmitterQId, 22222222214)
            .RuleFor(x => x.SubmitterEmail, "<EMAIL>")
            .RuleFor(x => x.SubmitterMobile, "43528687")
            .Generate();

        request.AddJsonBody(requestBody);
        return request;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateReleaseByReqNumber_Should_Return_OK()
    {
        // Arrange
        var request = UpdateRelease();
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222214);

        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal(OK, response.StatusCode);
    }

    private static RestRequest UpdateRelease()
    {
        var nationalityCodeList = MockNationality().Select(o => o.NatCode).ToList();

        var request = GetRestRequest("releases/{reqNumber}");
        request.AddUrlSegment("reqNumber", "IEQPFPHDNDV");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateRelease");

        var requestBody = new Faker<CreateUpdateReleaseRequest>()
            .RuleFor(x => x.QId, RandomNumber(11))
            .RuleFor(x => x.FNameEn, FirstNameEnglish)
            .RuleFor(x => x.MNameEn, MiddleNameEnglish)
            .RuleFor(x => x.LNameEn, LastNameEnglish)
            .RuleFor(x => x.FNameAr, FirstNameArabic)
            .RuleFor(x => x.MNameAr, MiddleNameArabic)
            .RuleFor(x => x.LNameAr, LastNameArabic)
            .RuleFor(x => x.HCNumber, "HC" + RandomNumber(8))
            .RuleFor(x => x.Nationality, f => f.PickRandom(nationalityCodeList))
            .RuleFor(x => x.Dob, f => f.Date.Past(50))
            .RuleFor(x => x.CurrentAssignedHC, "HC" + RandomNumber(8))
            .RuleFor(x => x.AttendanceRptStartDate, f => f.Date.Past(50))
            .RuleFor(x => x.AttendanceRptEndDate, f => f.Date.Past(50))
            .RuleFor(x => x.ReleaseItems,
                f => f.PickRandom("RI001", "RI002", "RI002", "RI004", "RI002", "RI003", "RI1", "RI2", "RI3", "RI001",
                    "RI002", "RI001", "RI002", "RI001", "RI003", "RI008", "RI009", "RI002", "RI005", "RI007"))
            .RuleFor(x => x.OtherReleaseItems, f => f.Lorem.Sentence())
            .RuleFor(x => x.EncounterStartDate, f => f.Date.Past(50))
            .RuleFor(x => x.EncounterEndDate, f => f.Date.Past(50))
            .RuleFor(x => x.PickupDelegated, f => f.Random.Bool())
            .RuleFor(x => x.AuthorizedQId, RandomNumber(11))
            .RuleFor(x => x.AuthorizedPersonName, f => f.Name.FullName())
            .RuleFor(x => x.AuthorizedPersonRelation,
                f => f.PickRandom("Father", "Mother", "Brother", "Sister", "Friend", "Other"))
            .RuleFor(x => x.DelegationOthers, f => f.Lorem.Sentence())
            .RuleFor(x => x.AttachId1, NewGuid())
            .RuleFor(x => x.AttachId2, NewGuid())
            .RuleFor(x => x.AttachId3, NewGuid())
            .RuleFor(x => x.AttachId4, NewGuid())
            .RuleFor(x => x.AttachId5, NewGuid())
            .RuleFor(x => x.AttachId6, NewGuid())
            .RuleFor(x => x.SubmittedBy, f => f.PickRandom("Self", "AuthorizedPerson"))
            .RuleFor(x => x.SubmittedAt, f => f.Date.Past(50))
            .RuleFor(x => x.SubmitterQId, 22222222214)
            .RuleFor(x => x.SubmitterEmail, "<EMAIL>")
            .RuleFor(x => x.SubmitterMobile, "43528687")
            .Generate();

        request.AddJsonBody(requestBody);
        return request;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteReleaseByReqNumber()
    {
        // Arrange
        var requestNumber = await TestCreateRelease();
        requestNumber.ThrowIfNull();
        await Task.Delay(500);
        var request = GetRestRequest("releases/{reqNumber}");
        request.AddUrlSegment("reqNumber", requestNumber);
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222214);

        // Act
        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal(OK, response.StatusCode);
    }
}