﻿global using System.Diagnostics.CodeAnalysis;
global using Bogus;
global using RestSharp;
global using Xunit.Abstractions;
global using static System.Convert;
global using static System.Guid;
global using static System.IO.Directory;
global using static System.StringComparison;
global using static System.TimeSpan;
global using static Xunit.Assert;
global using static EServiceFunctions.Helpers.DateTimeHelper;
global using static TestEServiceFunctions.Shared.GlobalBuilder;
global using static MockDataLibrary.GenerateMoqData;
global using static Newtonsoft.Json.JsonConvert;
global using static System.Net.HttpStatusCode;
global using static TestEServiceFunctions.RequestSourceOrigin;
global using static System.DateTime;
global using TestEServiceFunctions.Shared;
global using System.Net;
global using EServiceFunctions.RequestResponseModels.GeneralWorks;
global using Throw;
global using Xunit;
global using Microsoft.Extensions.Configuration;
global using EServiceFunctions.RequestResponseModels.Appointment;
global using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Extensions;
global using EServiceFunctions.RequestResponseModels.Assignment;
global using FluentAssertions;
global using EServiceFunctions.RequestResponseModels.CommentAndAttachment;
global using EServiceFunctions.RequestResponseModels.EDWDropDownList;
global using EServiceFunctions.RequestResponseModels.HealthCenter;
global using EServiceFunctions.RequestResponseModels.Enrollment;
global using EServiceFunctions.RequestResponseModels.FamilyPhysician;
global using EServiceFunctions.RequestResponseModels.MHDS;
global using EServiceFunctions.RequestResponseModels.Registration;
global using EServiceFunctions.RequestResponseModels.Miscellaneous;
global using EServiceFunctions.RequestResponseModels.ReleaseItem;
global using EServiceFunctions.RequestResponseModels.ReleaseOfInformation;
global using EServiceFunctions.RequestResponseModels.UserVerification;
global using EServiceFunctions.RequestResponseModels.UserProfile;
global using EServiceFunctions.RequestResponseModels.Transfer;
global using Microsoft.IdentityModel.Tokens;
global using System.Globalization;
global using System.Net.Security;
global using System.Runtime.Serialization;
global using System.Security.Cryptography.X509Certificates;
global using EServiceFunctions.Functions;
global using EServiceFunctions.Models.Shared;

global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.DependencyInjection;

global using SixLabors.ImageSharp;

global using static MockDataLibrary.GlobalConstants;
global using static MockDataLibrary.GlobalStatus;
global using static MockDataLibrary.MockModels.MockPersonRecord;

global using static RestSharp.ParameterType;

using Microsoft.Azure.Functions.Worker.Http;
using static Newtonsoft.Json.Formatting;

namespace TestEServiceFunctions;

[ExcludeFromCodeCoverage]
public static class GlobalLogger
{
    public static void LogToConsole(this ITestOutputHelper testOutputHelper, object? sender)
    {
        try
        {
            switch (sender)
            {
                case HttpResponseData response:
                    testOutputHelper.LogTestOutput(response, "Response :");
                    break;
                case RestRequest restRequest:
                    testOutputHelper.LogTestOutput(restRequest, "Request :");
                    break;
                default:
                    testOutputHelper.LogTestOutput(sender, "Response :");
                    break;
            }
        }
        catch (Exception ex)
        {
            testOutputHelper.WriteLine(ex.Message);
        }
    }

    private static void LogTestOutput(this ITestOutputHelper testOutputHelper, object? sender, string message)
    {
        testOutputHelper.WriteLine(message);
        testOutputHelper.WriteLine(SerializeObject(sender, Indented));
    }
}

public static class AppEnvironment
{
    public const string Development = "appconfigs.dev.json";
}

internal static class RequestSourceOrigin
{
    public static readonly string UnitTestCreate = $"UnitTestCreate {UtcNow + FromHours(3):dd/MM/yyyy HH:mm:ss}";
    public static readonly string UnitTestUpdate = $"UnitTestUpdate {UtcNow + FromHours(3):dd/MM/yyyy HH:mm:ss}";
}