﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.UserProfile;

public class UserProfile
{
    [Key]
    public long QId { get; set; }
    public string? PrefSMSLang { get; set; }
    public string? PrefComMode { get; set; }
    public string? SecondaryPhoneMobile { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public bool? IsActive { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public bool? Consent { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreateSource { get; set; }
    public string? UpdateSource { get; set; }
}