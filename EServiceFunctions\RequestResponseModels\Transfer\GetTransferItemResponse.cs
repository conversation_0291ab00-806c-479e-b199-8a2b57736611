﻿namespace EServiceFunctions.RequestResponseModels.Transfer;

public class GetTransferItemResponse
{
    public long? QId { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? ReqNumber { get; set; }
    public string? Nationality { get; set; }
    public string? Dob { get; set; }
    public bool? Consent { get; set; }
    public string? HCNumber { get; set; }
    public int? BNo { get; set; }
    public int? ZNo { get; set; }
    public int? SNo { get; set; }
    public int? UNo { get; set; }
    public string? CurrentHC { get; set; }
    public string? CatchmentHC { get; set; }
    public string? PrefHC { get; set; }
    public Guid? AttachId1 { get; set; }
    public Guid? AttachId2 { get; set; }
    public Guid? AttachId3 { get; set; }
    public Guid? AttachId4 { get; set; }
    public Guid? AttachId5 { get; set; }
    public Guid? AttachId6 { get; set; }
    public string? TransferReason { get; set; }
    public string? TransferReasonDescriptionEn { get; set; }
    public string? TransferReasonDescriptionAr { get; set; }
    public string? SubmittedBy { get; set; }
    public string? SubmittedAt { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }
    public string? CreatedAt { get; set; }
    public string? UpdatedAt { get; set; }
    public string? Status { get; set; }
    public string? StatusDescriptionEn { get; set; }
    public string? StatusDescriptionAr { get; set; }
    public string? SN { get; set; }
    public string? GisAddressUpdatedAt { get; set; }
    public bool? IsGisAddressManualyEntered { get; set; }
}