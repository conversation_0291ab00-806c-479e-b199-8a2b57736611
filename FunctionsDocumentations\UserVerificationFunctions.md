# User Verification Functions API Documentation

This document provides detailed information about the User Verification Functions API endpoints, which handle user age verification and vital status checks based on QID (Qatar ID).

## API Endpoints

### Get User Verification By QID
- **Endpoint**: `GET /userVerification/isValid/{qId}`
- **Description**: Retrieves user's age verification status and vital information based on their QID. This endpoint determines whether the user is an adult and calculates their age based on their date of birth from MOI (Ministry of Interior) records.
- **Authorization**: 
  - QID-based authorization required
  - User must be authenticated and authorized to access their own information
- **Parameters**:
  - `qId` (path, required): User QID (long)
    - Must be a valid Qatar ID number
    - Must match the authenticated user's QID
- **Response Format**:
  ```json
  {
    "isAdult": "boolean",
    "age": "number"
  }
  ```
- **Response Fields**:
  - `isAdult`: Boolean indicating if the user is 18 years or older
  - `age`: Calculated age of the user based on their date of birth
- **Response Codes**:
  - 200 (OK): Successfully retrieved user verification details
  - 204 (No Content): User not found in the system
  - 401 (Unauthorized): Authentication failed or unauthorized access attempt
  - 500 (Internal Server Error): Server-side error during processing

## Technical Implementation Details

### Data Source
- EDW (Enterprise Data Warehouse) Database Context
  - PersonMoiDetail table
    - Contains user demographic information
    - Stores date of birth and other MOI data

### Security Measures
1. Authorization:
   - QID-based authorization check
   - Validates user's right to access the requested information
   - Prevents unauthorized access to other users' data

2. Input Validation:
   - QID format validation
   - Date of birth null checks
   - Data integrity verification

### Performance Optimizations
1. Database Query Efficiency:
   - Uses AsNoTracking() for read-only operations
   - Efficient LINQ queries
   - Asynchronous database operations

2. Response Handling:
   - Lightweight response model
   - Minimal data transfer
   - Quick age calculation

### Error Handling
1. Comprehensive error management:
   - Null reference checks
   - Date parsing validation
   - Database query exception handling

2. Standardized error responses:
   - Consistent error message format
   - Appropriate HTTP status codes
   - Detailed logging for troubleshooting

## Response Models

### UserDetailsResponse
- **Purpose**: Provides user verification details
- **Fields**:
  - `isAdult`: Boolean
    - `true`: User is 18 years or older
    - `false`: User is under 18 years
  - `age`: Number
    - Calculated age based on date of birth
    - Derived from MOI records

## Dependencies
- Entity Framework Core
  - Database context management
  - Asynchronous query operations
- Azure Functions
  - HTTP trigger functionality
  - OpenAPI integration
- Custom Extensions
  - Age calculation helpers
  - Authorization extensions
  - Error handling utilities

## Usage Considerations

### Age Calculation
- Based on current date and stored date of birth
- Handles different date formats
- Considers timezone differences

### Data Privacy
- Sensitive information handling
- Minimal data exposure
- Secure data transmission

### Error Scenarios
1. User Not Found:
   - Returns 204 No Content
   - No sensitive information disclosed

2. Authorization Failure:
   - Returns 401 Unauthorized
   - Logs unauthorized access attempts

3. Invalid Data:
   - Returns 500 Internal Server Error
   - Logs detailed error information
   - Maintains data integrity

## Best Practices
1. Always verify authorization before accessing data
2. Use asynchronous operations for better scalability
3. Implement comprehensive error logging
4. Validate input parameters
5. Handle null values appropriately
6. Maintain data privacy and security

## Logging
- Request access logging
- Error condition logging
- Authorization failure logging
- Performance metrics tracking

## Future Enhancements
1. Additional verification parameters
2. Caching implementation
3. Rate limiting
4. Enhanced error details
5. Performance monitoring
6. Extended validation rules