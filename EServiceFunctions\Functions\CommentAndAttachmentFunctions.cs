using EServiceFunctions.Models.CommentAndAttachment;
using EServiceFunctions.RequestResponseModels.CommentAndAttachment;

using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;

using static System.IO.Path;

using static EServiceFunctions.Helpers.UtilityHelper;
using static EServiceFunctions.MapperModels.CommentAndAttachmentMapper;

using static Microsoft.Net.Http.Headers.HeaderUtilities;

using ErrorResponse = EServiceFunctions.RequestResponseModels.ErrorResponse;

namespace EServiceFunctions.Functions;

public static class CommentAndAttachmentConstants
{
    public const string UnauthorizedAccessMessage = "Unauthorized ({QId}) Access!!!";
    public const string InvalidRequestBodyMessage = "Invalid request body. Please try again.";
    public const string InvalidFileMessage = "Invalid File. Please try again.";
    public const string InvalidFileSizeMessage = "Invalid File Size. Please upload file of size upto 5MB.";
    public const string RequiredRequestOriginMessage = "Required 'X-RequestOrigin' header value";
    public const string InvalidIdMessage = "Invalid ID {0}";
    public const string InvalidReqNumberMessage = "Invalid Req Number {0}";
    public const string ErrorMessageFormat = "Error: {ErrorMessage}";
}

public class CommentAndAttachmentFunctions(
    IDbContextFactory<CommentAndAttachmentContext> dbContextFactory,
    ILogger<CommentAndAttachmentFunctions> logger)
{
    private readonly IDbContextFactory<CommentAndAttachmentContext>? _dbContextFactory = dbContextFactory;

    #region GetAttachmentItemById

    /// <summary>
    /// Get attachment item for the given id
    /// </summary>
    /// <param name="req"></param>
    /// <param name="id">Attachment ID</param>
    /// <returns></returns>
    [Function("GetAttachmentItemById")]
    [OpenApiOperation(operationId: "GetAttachmentItemById", tags: ["Attachment"],
        Summary = "Get Attachment Item By Id", Description = "Get attachment item for the given id")]
    [OpenApiParameter("id", Description = "Attachment ID", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetAttachmentItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetAttachmentItemById(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "attachments/{id}")]
        HttpRequestData req, string id)
    {
        try
        {
            var sendAttachmentData = req.IsBooleanHeader("X-SendData");
            var submitter = req.GetClaims();
            
            logger.LogInformation("Getting Attachment Item By ID {Id}", id);

            if (string.IsNullOrWhiteSpace(id) || !Guid.TryParse(id, out Guid guid))
            {
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
            }

            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();

            var response = await dbContext.Attachment!.AsNoTracking().SingleOrDefaultAsync(item => item.Id == guid);

            if (response == null)
            {
                logger.LogInformation("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(CommentAndAttachmentConstants.UnauthorizedAccessMessage, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (!sendAttachmentData)
            {
                response.AttachmentData = null;
            }

            var getResponse = MapAttachmentToGetAttachmentItemResponse(response);

            logger.LogInformation("Returning Attachment Item By ID {Id}", id);

            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CreateAttachment

    /// <summary>
    /// Create new attachment request
    /// </summary>
    /// <param name="req">New Attachment Create Model</param>
    /// <returns></returns>
    [ExcludeFromCodeCoverage]
    [Function("CreateAttachment")]
    [OpenApiOperation(operationId: "CreateAttachment", tags: ["Attachment"], Summary = "Create Attachment",
        Description = "Create new attachment request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateAttachmentRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(CreateAttachmentResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateAttachment(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "attachments")]
        HttpRequestData req)
    {
        logger.LogInformation("Creating An Attachment");
        var requestOriginSource = req.TryGetHeader(RequestOrigin);

        try
        {
            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.RequiredRequestOriginMessage);
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

            var request = DeserializeObject<CreateUpdateAttachmentRequest>(requestBody);
            
            if (!req.IsAuthorized(request.SubmitterQId))
            {
                logger.LogWarning(CommentAndAttachmentConstants.UnauthorizedAccessMessage, request.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidRequestBodyMessage);
            }

            if (!IsValidFileExtension(request.AttachmentData!))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileMessage);
            }

            if (!IsValidFileSize(request.AttachmentData!))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileSizeMessage);
            }

            Attachment create = MapCreateUpdateAttachmentRequestToAttachment(request);

            create.Id = Guid.NewGuid();
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;

            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();

            try
            {
                await dbContext.Attachment!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, CommentAndAttachmentConstants.ErrorMessageFormat, e.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError);
            }

            logger.LogInformation("Created A New Attachment With ID {Id}", create.Id);
            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, CommentAndAttachmentConstants.ErrorMessageFormat, ex.Message);
            return await req.WriteErrorResponseAsync(InternalServerError);
        }
    }

    #endregion

    #region CreateAttachmentV2

    /// <summary>
    /// Create new attachment request
    /// </summary>
    /// <param name="req">New Attachment Create Model</param>
    /// <returns></returns>
    [Function("CreateAttachmentV2")]
    [OpenApiOperation(operationId: "CreateAttachmentV2", tags: ["Attachment"], Summary = "Create Attachment V2",
        Description = "Upload a new attachment through multipart/form-data")]
    [OpenApiRequestBody("multipart/form-data", typeof(object))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(Attachment))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateAttachmentV2(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "attachments-v2")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Creating An Attachment");
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.RequiredRequestOriginMessage);
            }
            
            var contentType = req.TryGetHeader("Content-Type");
            var submitter = req.GetClaims();
            var boundary = RemoveQuotes(MediaTypeHeaderValue.Parse(contentType).Boundary).Value;

            if (req.Body.CanSeek)  req.Body.Position = 0;
            var reader = new MultipartReader(boundary, req.Body);

            if (await reader.ReadNextSectionAsync() is not { } section)
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidRequestBodyMessage);
            }

            if (!ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidRequestBodyMessage);
            }

            if (!contentDisposition.FileName.HasValue)
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidRequestBodyMessage);
            }

            using var fileContent = new MemoryStream();
            await section.Body.CopyToAsync(fileContent);
            fileContent.Position = 0;

            if (!IsValidFileExtensionByFileName(contentDisposition.FileName.Value))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileMessage);
            }

            if (!IsValidFileSize(fileContent.Length))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileSizeMessage);
            }

            Attachment create = new()
            {
                Id = Guid.NewGuid(),
                AttachmentName = contentDisposition.FileName.Value,
                AttachmentType = GetExtension(contentDisposition.FileName.Value).TrimStart('.'),
                AttachmentData = fileContent.ToBase64(),
                CreatedAt = GetCurrentTime(),
                CreateSource = requestOriginSource,
                SubmitterQId = submitter.QId
            };

            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();
            try
            {
                await dbContext.Attachment!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, CommentAndAttachmentConstants.ErrorMessageFormat, e.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError);
            }

            logger.LogInformation("Created A New Attachment With ID {Id}", create.Id);
            
            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UpdateAttachmentById

    /// <summary>
    /// Update attachment for the given id
    /// </summary>
    /// <param name="req"></param>
    /// <param name="id">Attachment ID </param>
    /// <returns></returns>
    [ExcludeFromCodeCoverage]
    [Function("UpdateAttachmentById")]
    [OpenApiOperation(operationId: "UpdateAttachmentById", tags: ["Attachment"],
        Summary = "Update Attachment By Id", Description = "Update attachment item for the given id")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateAttachmentRequest))]
    [OpenApiParameter("id", Description = "Attachment ID", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateAttachmentById(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "attachments/{id}")]
        HttpRequestData req, string id)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.RequiredRequestOriginMessage);
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateAttachmentRequest>(requestBody);
            
            if (!req.IsAuthorized(request.SubmitterQId))
            {
                logger.LogWarning(CommentAndAttachmentConstants.UnauthorizedAccessMessage, request.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidRequestBodyMessage);
            }

            if (!IsValidFileExtension(request.AttachmentData!))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileMessage);
            }

            if (!IsValidFileSize(request.AttachmentData!))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileSizeMessage);
            }

            logger.LogInformation("Updating An Attachment");
            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();

            if (!Guid.TryParse(id, out Guid guid))
            {
                logger.LogWarning(string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
            }

            Attachment? response = await dbContext.Attachment!.SingleOrDefaultAsync(item =>
                item.Id == guid && item.SubmitterQId == request.SubmitterQId);

            if (response == null)
            {
                logger.LogWarning(string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
            }

            var mapperResponse = MapCreateUpdateAttachmentRequestToAttachment(request);

            mapperResponse.Id = response.Id;
            mapperResponse.CreatedAt = response.CreatedAt;
            mapperResponse.UpdatedAt = GetCurrentTime();
            mapperResponse.CreateSource = response.CreateSource;
            mapperResponse.UpdateSource = requestOriginSource;

            dbContext.Entry(response).CurrentValues.SetValues(mapperResponse);

            try
            {
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, CommentAndAttachmentConstants.ErrorMessageFormat, ex.InnerException?.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Updated Attachment With ID {Id}", response.Id);

            mapperResponse.AttachmentData = null;
            
            return await req.WriteOkResponseAsync(mapperResponse);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UpdateAttachmentByIdV2

    /// <summary>
    /// Update attachment for the given id
    /// </summary>
    /// <param name="req"></param>
    /// <param name="id">Attachment ID </param>
    /// <returns></returns>
    [Function("UpdateAttachmentByIdV2")]
    [OpenApiOperation(operationId: "UpdateAttachmentByIdV2", tags: ["Attachment"],
        Summary = "Update Attachment By Id V2",
        Description = "Update attachment item for the given id & multipart/form-data")]
    [OpenApiRequestBody("multipart/form-data", typeof(object))]
    [OpenApiParameter("id", Description = "Attachment ID", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateAttachmentByIdV2(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "attachments-v2/{id}")]
        HttpRequestData req, string id)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.RequiredRequestOriginMessage);
            }

            var submitter = req.GetClaims();
            var contentType = req.TryGetHeader("Content-Type");
            var boundary = RemoveQuotes(MediaTypeHeaderValue.Parse(contentType).Boundary).Value;
            
            if (req.Body.CanSeek)  req.Body.Position = 0;
            var reader = new MultipartReader(boundary, req.Body);

            if (await reader.ReadNextSectionAsync() is not { } section)
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidRequestBodyMessage);
            }

            if (!ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition))
            {
                return await req.WriteErrorResponseAsync(BadRequest,
                    "Invalid request body. Please ensure it is well-formed and try again.");
            }

            if (!contentDisposition.FileName.HasValue)
            {
                return await req.WriteErrorResponseAsync(BadRequest,
                    "Invalid request body. Please provide a valid filename and try again.");
            }

            using var fileContent = new MemoryStream();
            await section.Body.CopyToAsync(fileContent);
            fileContent.Position = 0;

            if (fileContent.Length == 0)
            {
                return await req.WriteErrorResponseAsync(BadRequest,
                    "Invalid File Object. Please try again.");
            }

            if (!IsValidFileExtensionByFileName(contentDisposition.FileName.Value))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileMessage);
            }

            if (!IsValidFileSize(fileContent.Length))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidFileSizeMessage);
            }

            logger.LogInformation("Updating An Attachment");
            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();

            Attachment? response = await dbContext.Attachment!.SingleOrDefaultAsync(item => item.Id == Guid.Parse(id));

            if (response is null)
            {
                logger.LogWarning(string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
            }

            Attachment update = new()
            {
                Id = response.Id,
                AttachmentName = contentDisposition.FileName.Value,
                AttachmentType = GetExtension(contentDisposition.FileName.Value).TrimStart('.'),
                AttachmentData = fileContent.ToBase64(),
                CreatedAt = response.CreatedAt,
                UpdatedAt = GetCurrentTime(),
                CreateSource = response.CreateSource,
                UpdateSource = requestOriginSource,
                SubmitterQId = submitter.QId
            };

            dbContext.Entry(response).CurrentValues.SetValues(update);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("Updated Attachment With ID {Id}", response.Id);
            return await req.WriteOkResponseAsync(update);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region DeleteAttachmentById

    /// <summary>
    /// Delete attachment for the given id
    /// </summary>
    /// <param name="req"></param>
    /// <param name="id">Attachment ID </param>
    /// <returns></returns>
    [Function("DeleteAttachmentById")]
    [OpenApiOperation(operationId: "DeleteAttachmentById", tags: ["Attachment"],
        Summary = "Delete Attachment By Id", Description = "Delete attachment item for the given id")]
    [OpenApiParameter("id", Description = "Attachment ID", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteAttachmentById(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "attachments/{id}")]
        HttpRequestData req, string id)
    {
        try
        {
            var submitter = req.GetClaims();
            logger.LogInformation("Deleting An Attachment");

            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();

            var response = await dbContext.Attachment!.SingleOrDefaultAsync(item =>
                item.Id == Guid.Parse(id) && item.SubmitterQId == submitter.QId);

            if (response == null || IsEmptyObject(response))
            {
                logger.LogWarning(string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(CommentAndAttachmentConstants.InvalidIdMessage, id));
            }

            try
            {
                dbContext.Attachment!.Remove(response);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, CommentAndAttachmentConstants.ErrorMessageFormat, e.InnerException!.Message);
                return await req.WriteErrorResponseAsync(InternalServerError);
            }

            logger.LogInformation("Deleted Attachment By ID {Id}", response.Id);
            return await req.WriteOkResponseAsync("Successfully Deleted");
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetCommentByReqNumber

    /// <summary>
    /// Get comments  for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Comment Request Number</param>
    /// <returns></returns>
    [Function("GetCommentByReqNumber")]
    [OpenApiOperation(operationId: "GetCommentByReqNumber", tags: ["Comment"],
        Summary = "Get Comments By ReqNumber", Description = "Get comments for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Comment Request Number", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetCommentResponse>))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetCommentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "comments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();
            if (string.IsNullOrWhiteSpace(reqNumber))
            {
                return await req.WriteErrorResponseAsync(BadRequest, string.Format(CommentAndAttachmentConstants.InvalidReqNumberMessage, reqNumber));
            }

            logger.LogInformation("Getting Comment By Request Number {ReqNumber}", reqNumber);
            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();

            var response = await dbContext.Comment!.AsQueryable().AsNoTracking()
                .Where(item => item.ReqNumber! == reqNumber
                               && (item.DisplayToSubmitter ?? false)
                               && (submitter.QId == null || item.SubmitterQId == submitter.QId))
                .Select(item =>
                    new GetCommentResponse
                    {
                        Action = item.Action,
                        CommentText = item.CommentText,
                        CreatedAt = item.CreatedAt.ToUtcString()
                    }).ToListAsync();


            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for a given ReqNumber {ReqNumber}", reqNumber);
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning comments ({Count}) By ReqNumber {ReqNumber}", response.Count, reqNumber);
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, CommentAndAttachmentConstants.ErrorMessageFormat, ex.Message);
            return await req.WriteErrorResponseAsync(InternalServerError);
        }
    }

    #region Create New Comment Request

    /// <summary>
    /// Create new comment request
    /// </summary>
    /// <param name="req">New Comment Create Model</param>
    /// <returns></returns>
    [Function("CreateComment")]
    [OpenApiOperation(operationId: "CreateComment", tags: ["Comment"], Summary = "Create Comment",
        Description = "Create new comment request")]
    [OpenApiRequestBody("application/json", typeof(CreateCommentRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(object))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateComment(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "comments")]
        HttpRequestData req)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.RequiredRequestOriginMessage);
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            logger.LogInformation("Creating a comment with the request body {RequestBody}", requestBody);
            var request = DeserializeObject<CreateCommentRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, CommentAndAttachmentConstants.InvalidRequestBodyMessage);
            }

            if (string.IsNullOrWhiteSpace(request.ReqNumber))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request number. Please try again.");
            }

            Comment create = MapCreateCommentRequestToComment(request);
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;
            
            await using var dbContext = await _dbContextFactory!.CreateDbContextAsync();

            try
            {
                await dbContext.Comment!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, CommentAndAttachmentConstants.ErrorMessageFormat, ex.InnerException?.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Created A New Comment With Id {Id}", create.Id);
            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, CommentAndAttachmentConstants.ErrorMessageFormat, ex.Message);
            return await req.WriteErrorResponseAsync(InternalServerError);
        }
    }

    #endregion

    #endregion
}