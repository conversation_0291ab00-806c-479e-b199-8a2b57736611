using EServiceFunctions.Models.Enrollment;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.Enrollment;

using static EServiceFunctions.MapperModels.EnrollmentMapper;

using static Microsoft.OpenApi.Models.ParameterLocation;

namespace EServiceFunctions.Functions;

public class EnrollmentFunctions(
    IDbContextFactory<EnrollmentContext> enrollmentDbContextFactory,
    IDbContextFactory<EDWDbContext> edwDbContextFactory,
    ILogger<EnrollmentFunctions> logger)
{
    private const string InvalidRequestBodyMsg = "Invalid request body. Please try again.";
    private const string EnrollmentFunctionErrorMsg = "An exception occurred in EnrollmentFunctions";
    private const string UnauthorizedMsg = "Unauthorized ({QId}) Access!!!";

    #region GetEnrollmentListByQID

    /// <summary>
    /// Get the enrollment list for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetEnrollmentListByQID")]
    [OpenApiOperation(operationId: "GetEnrollmentListByQID", tags: ["Enrollment"],
        Summary = "Get Enrollment List By Submitter's QID",
        Description = " Get the enrollment list for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked} or Archived {Approved, Cancelled, CancelledByEServ, Rejected}.",
        In = Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetEnrollmentListResponse>),
        Description =
            "Type of requests to return viz. Submitted, Cancelled, CancelledByEServ, Rework, Rejected, Approved, Reworked.")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetEnrollmentListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "enrollments/submitter-qid/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Enrollment List By Submitter's QId {QId}", qId);
            
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedMsg, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string enrollmentStatus = req.GetCleanedQueryString("status");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await enrollmentDbContextFactory.CreateDbContextAsync();

            var query = dbContext.Enrollment!.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (enrollment, status) => new { enrollment, status }).AsQueryable().AsNoTracking();

            query = query.Where(item => item.enrollment!.SubmitterQId == qId);

            if (enrollmentStatus.IsNotNullOrWhiteSpace())
            {
                query = query.Where(item =>
                    item.status.Category! == enrollmentStatus || item.enrollment!.Status == enrollmentStatus);
            }

            var response = await query.Select(item => new GetEnrollmentListResponse
            {
                QId = item.enrollment!.QId,
                ReqNumber = item.enrollment.ReqNumber,
                FNameEn = item.enrollment.FNameEn,
                MNameEn = item.enrollment.MNameEn,
                LNameEn = item.enrollment.LNameEn,
                FNameAr = item.enrollment.FNameAr,
                MNameAr = item.enrollment.MNameAr,
                LNameAr = item.enrollment.LNameAr,
                Status = item.enrollment.StatusNavigation!.Code,
                StatusDescriptionEn = item.enrollment.StatusNavigation.DescriptionEn,
                StatusDescriptionAr = item.enrollment.StatusNavigation.DescriptionAr,
                SubmittedAt = item.enrollment.SubmittedAt.ToUtcString()
            }).OrderBy(p => p.Status).Skip(skip).Take(take).ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for a given QID {QId} and Status {Status}", qId, enrollmentStatus);
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Enrollment List ({Count}) By Submitter's QID {QId}", response.Count, qId);
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while getting Enrollment List By Submitter's QId {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetEnrollmentStatsByQID

    /// <summary>
    /// Get the enrollment stats for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetEnrollmentStatsByQID")]
    [OpenApiOperation(operationId: "GetEnrollmentStatsByQID", tags: ["Enrollment"],
        Summary = "Get Enrollment Stats By Submitter's QID",
        Description = "Get the enrollment stats for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked} or Archived {Approved, Cancelled, CancelledByEServ,  Rejected}.",
        In = Query, Required = false, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetEnrollmentStatsResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetEnrollmentStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "enrollments/submitter-qid/{qId}/stats")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Enrollment Stats By Submitter's QID {QId}", qId);
            
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedMsg, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string enrollmentStatus = req.GetCleanedQueryString("status");
            await using var dbContext = await enrollmentDbContextFactory.CreateDbContextAsync();

            var query = dbContext.Enrollment
                .Join(dbContext.Status,
                    e => e.Status,
                    s => s.Code,
                    (enrollment, status) => new { enrollment, status })
                .AsNoTracking()
                .Where(item => item.enrollment.SubmitterQId == qId);

            if (enrollmentStatus.IsNotNullOrWhiteSpace())
            {
                query = query.Where(item => item.status.Category != null &&
                                            (item.status.Category == enrollmentStatus ||
                                             item.enrollment.Status == enrollmentStatus));
            }

            var response = await query.CountAsync();
            logger.LogInformation("Returning Enrollments count {Count} By Submitter's QID {QId} & Status {Status}", response, qId, enrollmentStatus);
            return await req.WriteOkResponseAsync(new GetEnrollmentStatsResponse { Count = response });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while getting Enrollment Stats By Submitter's QId {QId}", qId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CheckInProcessErollment

    /// <summary>
    /// CheckInProcessEnrollment 
    /// </summary>
    /// <param name="req"></param>
    /// <param name="applicantQId"></param>
    /// <param name="submitterQId"></param>
    /// <returns></returns>
    [Function("CheckInProcessEnrollment")]
    [OpenApiOperation(operationId: "CheckInProcessEnrollment", tags: ["Enrollment"],
        Summary = "Check InProcess Enrollment(s) By Requester's QID",
        Description = "Check inprocess enrollment(s) for the given requester's QId")]
    [OpenApiParameter("applicantQId", Description = "Applicant's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("submitterQId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessEnrollmentResponse),
        Description = "Returns True Or False")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckInProcessEnrollment(
        [HttpTrigger(AuthorizationLevel.Function, "get",
            Route = "enrollments/inprocess-validation/{applicantQId}/{submitterQId}")]
        HttpRequestData req, long applicantQId, long submitterQId)
    {
        try
        {
            logger.LogInformation("Checking InProcess Enrollment(s) By Applicant's QID {ApplicantQId} & Submitter's QId {SubmitterQId}", applicantQId, submitterQId);
            
            if(!req.IsAuthorized(submitterQId))
            {
                logger.LogWarning(UnauthorizedMsg, submitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            await using var dbContext = await enrollmentDbContextFactory.CreateDbContextAsync();

            var query = dbContext.Enrollment?.Join(dbContext.Status!,
                e => e!.Status,
                s => s.Code,
                (enrollment, status) => new
                {
                    enrollment,
                    status
                }).AsQueryable().AsNoTracking();


            var isInProcessRequestExist = await query!.AnyAsync(item => item.enrollment!.QId == applicantQId
                                                                        && item.enrollment.SubmitterQId == submitterQId
                                                                        && item.status.Category! == InProcess);

            logger.LogInformation("Returning InProcess Enrollment(s) Validation Result {IsInProcess} By Applicant's QID {ApplicantQId} & Submitter's QId {SubmitterQId}", isInProcessRequestExist, applicantQId, submitterQId);

            return await req.WriteOkResponseAsync(new CheckInProcessEnrollmentResponse
            {
                IsInprocessExist = isInProcessRequestExist
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while checking InProcess Enrollment(s) By Applicant's QID {ApplicantQId} & Submitter's QId {SubmitterQId}", applicantQId, submitterQId);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetEnrollmentItemByReqNumber

    /// <summary>
    /// Get enrollment item for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Enrollment Request Number</param>
    /// <returns></returns>
    [Function("GetEnrollmentItemByReqNumber")]
    [OpenApiOperation(operationId: "GetEnrollmentItemByReqNumber", tags: ["Enrollment"],
        Summary = "Get Enrollment Item By ReqNumber", Description = "Get enrollment item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Enrollment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetEnrollmentItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetEnrollmentItemByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "enrollments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();
            if (reqNumber.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            logger.LogInformation("Getting Enrollment Item By Request Number {ReqNumber}", reqNumber);

            await using var dbContext = await enrollmentDbContextFactory.CreateDbContextAsync();

            var response = await dbContext.Enrollment!.AsNoTracking().Include(s => s.StatusNavigation)
                .SingleOrDefaultAsync(item => item!.ReqNumber! == reqNumber);

            if (response == null)
            {
                logger.LogInformation("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedMsg, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var getResponse = MapEnrollmentToGetEnrollmentItemResponse(response);
            getResponse.Status = response.StatusNavigation!.Code;
            getResponse.StatusDescriptionEn = response.StatusNavigation.DescriptionEn;
            getResponse.StatusDescriptionAr = response.StatusNavigation.DescriptionAr;

            logger.LogInformation("Returning Enrollment Item By Request Number {ReqNumber}", getResponse.ReqNumber);

            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while getting Enrollment Item By Request Number {ReqNumber}", reqNumber);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CreateEnrollment

    /// <summary>
    /// Create new enrollment request
    /// </summary>
    /// <param name="req">New Enrollment Create Model</param>
    /// <returns></returns>
    [Function("CreateEnrollment")]
    [OpenApiOperation(operationId: "CreateEnrollment", tags: ["Enrollment"], Summary = "Create Enrollment",
        Description = "Create new enrollment request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateEnrollmentRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(Enrollment))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateEnrollment(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "enrollments")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Creating An Enrollment");
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (requestOriginSource.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateEnrollmentRequest>(requestBody);

            if (request.SubmitterQId is null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, InvalidRequestBodyMsg);
            }

            long qId = request.SubmitterQId.Value;
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedMsg, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, InvalidRequestBodyMsg);
            }

            Enrollment create = MapCreateUpdateEnrollmentRequestToEnrollment(request);

            create.ReqNumber = RandomPassword();
            create.SubmittedAt = GetCurrentTime();
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;
            create.Status = Saved;

            if (string.IsNullOrWhiteSpace(create.Nationality))
            {
                var env = req.TryGetHeader(PhccEnvironment);
                var dependentQId = ToInt64(create.QId);
                var qIdList = new List<long> { dependentQId };
                string builder = BuildQIdListAsXml(qIdList);

                var getUserDetailsResponse = await GetUserDetailsFromEdw(builder, env);
                if (getUserDetailsResponse is not null)
                {
                    create.FNameEn = getUserDetailsResponse.FNameEn;
                    create.MNameEn = getUserDetailsResponse.MNameEn;
                    create.LNameEn = getUserDetailsResponse.LNameEn;
                    create.FNameAr = getUserDetailsResponse.FNameAr;
                    create.MNameAr = getUserDetailsResponse.MNameAr;
                    create.LNameAr = getUserDetailsResponse.LNameAr;
                    create.Nationality = getUserDetailsResponse.NationalityCode;
                    create.HCNumber = getUserDetailsResponse.HcNumber;
                }
            }

            await using var dbContext = await enrollmentDbContextFactory.CreateDbContextAsync();

            // Check for existing in-process enrollment requests
            var existingInProcessQuery = dbContext.Enrollment!
                .Join(dbContext.Status!, e => e.Status, s => s.Code, (enrollment, status) => new { enrollment, status })
                .AsQueryable();

            var isInProcessRequestExist = await existingInProcessQuery!.AnyAsync(item =>
                item.enrollment!.QId == request.QId
                && item.enrollment.SubmitterQId == request.SubmitterQId
                && item.status.Category! == InProcess);

            if (isInProcessRequestExist)
            {
                logger.LogWarning("Duplicate enrollment request detected for QId {QId} and SubmitterQId {SubmitterQId}", request.QId, request.SubmitterQId);
                return await req.WriteErrorResponseAsync(BadRequest, "An in-process enrollment request already exists for this applicant by the same submitter.");
            }

            try
            {
                await dbContext.Enrollment!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while creating an enrollment");
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Created A New Enrollment With Request Number {ReqNumber}", create.ReqNumber);

            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while creating an enrollment");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region BuildQIDsXML

    private static string BuildQIdListAsXml(IReadOnlyCollection<long>? qIdList)
    {
        return qIdList is { Count: > 0 }
            ? Replace(new XElement("QIDList", qIdList.Select(x => new XElement("QID", x))).ToString(), @"\s+",
                EmptyString)
            : EmptyString;
    }

    #endregion

    #region GetUserDetailsFromEDW

    private async Task<GetUserDetailsResponse?> GetUserDetailsFromEdw(string builder, string env)
    {
        try
        {
            var inputXml = new SqlParameter("@InputXML", SqlDbType.Xml)
            {
                Value = builder
            };
            var envParam = new SqlParameter("@ENV", SqlDbType.VarChar)
            {
                Value = env
            };

            await using var edwDbContext = await edwDbContextFactory.CreateDbContextAsync();

            var response = await edwDbContext.UserProfileResponseFromEDW?
                .FromSqlInterpolated($"EXEC dbo.SP_GET_USER_AND_DEPENDENTS_DETAILS_V2 {inputXml}, {envParam}")
                .FirstOrDefaultAsync();

            if (response is null)
            {
                logger.LogInformation("No data found for the given QID/QIDs");
                return null;
            }

            logger.LogInformation("Getting data for the given QID");

            var item = new GetUserDetailsResponse
            {
                FNameEn = response.FNameEn,
                MNameEn = response.MNameEn,
                LNameEn = response.LNameEn,
                FNameAr = response.FNameAr,
                MNameAr = response.MNameAr,
                LNameAr = response.LNameAr,
                NationalityCode = response.NationalityCode,
                HcExpiryDate = response.HcExpiryDate.ToUtcString(),
                HcNumber = !string.IsNullOrWhiteSpace(response.HcExpiryDate.ToUtcString())
                    ? response.HcNumber
                    : string.Empty
            };

            return item;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while getting user details from EDW");
            return new GetUserDetailsResponse();
        }
    }

    #endregion

    #region UpdateEnrollmentByReqNumber

    /// <summary>
    /// Update enrollment for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Enrollment Request Number </param>
    /// <returns></returns>
    [Function("UpdateEnrollmentByReqNumber")]
    [OpenApiOperation(operationId: "UpdateEnrollmentByReqNumber", tags: ["Enrollment"],
        Summary = "Update Enrollment By ReqNumber",
        Description = "Update enrollment item for the given request number")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateEnrollmentRequest))]
    [OpenApiParameter("reqNumber", Description = "Enrollment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateEnrollmentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "enrollments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

            var request = DeserializeObject<CreateUpdateEnrollmentRequest>(requestBody);

            if (request == null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, InvalidRequestBodyMsg);
            }

            if (request.SubmitterQId is null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, InvalidRequestBodyMsg);
            }

            long qId = request.SubmitterQId.Value;
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedMsg, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            logger.LogInformation("Updating An Enrollment");

            await using var dbContext = await enrollmentDbContextFactory.CreateDbContextAsync();

            var response = await dbContext.Enrollment!.FirstOrDefaultAsync(item =>
                item!.ReqNumber == reqNumber);

            if (response == null || IsEmptyObject(response))
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            if (response.SubmitterQId != request.SubmitterQId)
            {
                logger.LogWarning(UnauthorizedMsg, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var mapperResponse = MapCreateUpdateEnrollmentRequestToEnrollment(request);

            mapperResponse.Id = response.Id;
            mapperResponse.ReqNumber = response.ReqNumber;
            mapperResponse.Status = response.Status;
            mapperResponse.StatusInternal = response.StatusInternal;
            mapperResponse.SN = response.SN;
            mapperResponse.SubmittedAt = GetCurrentTime();
            mapperResponse.CreatedAt = response.CreatedAt;
            mapperResponse.UpdatedAt = GetCurrentTime();
            mapperResponse.CreateSource = response.CreateSource;
            mapperResponse.UpdateSource = requestOriginSource;

            dbContext.Entry(response).CurrentValues.SetValues(mapperResponse);

            try
            {
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, EnrollmentFunctionErrorMsg + ": Error while updating an enrollment");
                return e.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Updated A New Enrollment With Request Number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync(mapperResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while updating an enrollment");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region DeleteEnrollmentByReqNumber

    /// <summary>
    /// Delete enrollment for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Enrollment Request Number </param>
    /// <returns></returns>
    [Function("DeleteEnrollmentByReqNumber")]
    [OpenApiOperation(operationId: "DeleteEnrollmentByReqNumber", tags: ["Enrollment"],
        Summary = "Delete Enrollment By ReqNumber",
        Description = "Delete enrollment item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Enrollment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteEnrollmentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "enrollments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();

            await using var dbContext = await enrollmentDbContextFactory.CreateDbContextAsync();

            var response = await dbContext.Enrollment!.FirstOrDefaultAsync(item => item!.ReqNumber == reqNumber);

            if (response == null)
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedMsg, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            logger.LogInformation("Deleting Enrollment By Request Number {ReqNumber}", reqNumber);

            try
            {
                dbContext.Enrollment!.Remove(response);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, EnrollmentFunctionErrorMsg + ": Error while deleting an enrollment");
                return e.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation("Deleted Enrollment By Request Number {ReqNumber}", reqNumber);

            return await req.WriteOkResponseAsync("Deleted Successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, EnrollmentFunctionErrorMsg + ": Error while deleting an enrollment");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}