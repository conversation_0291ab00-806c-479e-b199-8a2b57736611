﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.CommentAndAttachment;

public class Comment
{
    public int Id { get; set; }
    public string? ReqNumber { get; set; }
    public string? Action { get; set; }
    public string? CommentText { get; set; }
    public string? CommentType { get; set; }
    public string? CommenterName { get; set; }
    public long? SubmitterQId { get; set; }
    public bool? DisplayToSubmitter { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    public string? CreateSource { get; set; }
    public string? UpdateSource { get; set; }
}