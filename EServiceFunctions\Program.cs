using EServiceFunctions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.Azure.Functions.Worker.Extensions.OpenApi.Extensions;
using Microsoft.Extensions.Configuration;
using static System.Console;
using static EServiceFunctions.Helpers.StartupHelpers;

try
{
    IHost host = new HostBuilder()
        .ConfigureAppConfiguration((_, config) =>
        {
            config.AddEnvironmentVariables()
                  .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true);
        })
        .ConfigureFunctionsWorkerDefaults()
        .ConfigureServices(services =>
        {
            try
            {
                ConfigureApplicationServices(services);
                ConfigureEnvironmentVariables();
                ConfigureDatabaseContexts(services);
                ConfigureApplicationInsights(services);
                services.AddSingleton<IOpenApiConfigurationOptions>(_ => OpenApiOptionsFactory.CreateOptions());
            }
            catch (Exception ex)
            {
                WriteLine($"Error configuring services: {ex.Message}");
                throw;
            }
        })
        .ConfigureOpenApi()
        .Build();

    await host.RunAsync();
}
catch (Exception ex)
{
    WriteLine($"Critical error running host: {ex.Message}");
    throw;
}