# Check In-Process MHDS Tests

## Test Cases

### 1. TestCheckInProcessMhdsByQId

Tests validation of in-process MHDS requests.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckInProcessMhdsByQId()
{
    // Arrange
    var request = GetRestRequest($"mhds/{22222222270}/inprocess-validation");

    // Act
    var response = await _client.GetAsync<CheckInProcessMHDSResponse>(request);

    // Assert
    True(response is not null);
    True(response.IsInProcessExist);
}
```

### 2. TestCheckInProcessMhdsByQIdWithInvalidQId

Tests in-process validation with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestCheckInProcessMhdsByQIdWithInvalidQId()
{
    // Arrange
    const long qId = 343434224343;
    var request = GetRestRequest($"mhds/{qId}/inprocess-validation");

    // Act
    var response = await _client.GetAsync<CheckInProcessMHDSResponse>(request);

    // Assert
    False(response?.IsInProcessExist);
}
```

## Request Details

### Endpoint
```
GET mhds/{qId}/inprocess-validation
```

### URL Parameters
- `qId`: QID to check for in-process requests

## Response Model

### CheckInProcessMHDSResponse
```csharp
public class CheckInProcessMHDSResponse
{
    public bool IsInProcessExist { get; set; }
}
```

## Test Data

### Success Case
- QID: 22222222270
- Expected: In-process request exists

### Error Case
- QID: 343434224343
- Expected: No in-process request

## Validation Rules

### Success Case Validation
1. Response not null
2. In-process request exists

### Error Case Validation
1. Response may be null
2. No in-process request exists

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Out of range QID

2. **System State**
   - No requests found
   - All requests completed
   - System unavailable

3. **Data Access**
   - Database errors
   - Service errors
   - Timeout issues

## Notes

1. **Request State**
   - Checks only in-process status
   - Quick validation
   - Boolean response

2. **Use Cases**
   - Prevent duplicate requests
   - Validate request state
   - Process flow control

3. **Performance**
   - Simple status check
   - No detailed data
   - Efficient query
