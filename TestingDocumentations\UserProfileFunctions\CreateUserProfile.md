# Create User Profile Tests

## Test Cases

### 1. TestCreateUserProfile

Tests successful creation of user profile.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task<long> TestCreateUserProfile()
{
    // Arrange
    var request = GetRestRequest("userprofiles");
    var qId = RandomNumber(11);
    request.AddOrUpdateHeader(JwtClaimsQId, qId);
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateUserProfile");

    var requestBody = new Faker<CreateUpdateUserProfileRequest>()
        .RuleFor(x => x.QId, qId)
        .RuleFor(x => x.PrefSMSLang, f => f.PickRandom("0", "1"))
        .RuleFor(x => x.PrefComMode, f => f.PickRandom("0", "1", "0,1"))
        .RuleFor(x => x.SecondaryPhoneMobile, f => f.Phone.PhoneNumber())
        .RuleFor(x => x.Consent, true)
        .Generate();

    request.AddJsonBody(requestBody);

    // Act
    var response = await _client.PostAsync(request);
    response.ThrowIfNull();

    // Assert
    True(response.StatusCode == Created);
    return response.StatusCode == Created ? requestBody.QId : 0;
}
```

### 2. TestCreateUserProfileWithInvalidQId

Tests profile creation with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestCreateUserProfileWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("userprofiles");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateUserProfile");
    request.Method = Method.Post;
    var requestBody = new CreateUpdateUserProfileRequest();
    request.AddJsonBody(requestBody);

    // Act
    var response = await _client.ExecuteAsync(request);
    var responseObject = DeserializeObject<ErrorResponse>(response.Content!);

    // Assert
    Equal(BadRequest, response.StatusCode);
    Equal("Invalid request body. Please try again.", responseObject?.Message);
}
```

## Request Details

### Endpoint
```
POST userprofiles
```

### Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: "UnitTest-CreateUserProfile"

## Request Model

### CreateUpdateUserProfileRequest
```csharp
public class CreateUpdateUserProfileRequest
{
    public long QId { get; set; }
    public string PrefSMSLang { get; set; }
    public string PrefComMode { get; set; }
    public string SecondaryPhoneMobile { get; set; }
    public bool Consent { get; set; }
}
```

## Test Data

### Success Case
- Random 11-digit QID
- SMS Language: "0" or "1"
- Communication Mode: "0", "1", or "0,1"
- Valid phone number
- Consent: true

### Error Case
- Empty request body
- Expected: BadRequest with error message

## Validation Rules

### Success Case Validation
1. Response not null
2. Status code is Created
3. Returns valid QID

### Error Case Validation
1. Status code is BadRequest
2. Error message matches expected

## Error Cases

1. **Invalid Request Body**
   - Missing required fields
   - Invalid field formats
   - Data validation failures

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID
   - Unauthorized creation

3. **System Errors**
   - Database errors
   - Service unavailable
   - Creation conflicts

## Notes

1. **Test Data Generation**
   - Uses Faker library
   - Random QID generation
   - Valid field formats

2. **Request Format**
   - JSON body
   - Required headers
   - Valid QID format

3. **Performance**
   - Data generation overhead
   - Response validation
   - Error handling
