# DeleteAttachmentById

## Overview
Deletes an existing attachment by its ID.

## Endpoint
- **Route**: `attachments/{id}`
- **Method**: DELETE
- **Authorization**: Function level

## Parameters
- `id` (path, required): Attachment ID in GUID format

## Responses
- **200 OK**: Successfully deleted
- **400 Bad Request**: Invalid ID format
- **401 Unauthorized**: QID mismatch
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates attachment ID format
2. Checks QID authorization
3. Verifies attachment exists
4. Deletes attachment record
5. Returns success message

## Security Considerations
- Only the original submitter can delete their attachments
- QID validation required
- Function-level authorization

## Dependencies
- CommentAndAttachmentContext
- Authorization middleware
