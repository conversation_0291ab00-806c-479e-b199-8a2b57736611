﻿namespace TestEServiceFunctions;

[Collection("HealthCenterFunctions")]
public class TestHealthCenterFunctions(IRestLibrary restLibrary, ITestOutputHelper testOutputHelper)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetHealthCenterList()
    {
        // Arrange
        var request = GetRestRequest("healthcenters");
        
        // Act
        var response = await _client.GetAsync<List<GetHealthCenterListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();
        response = response.OrderBy(x => x.HCntCode).ToList();

        // Assert
        NotNull(response);
        True(response.Count > 0);
        True(response[0].HCntCode == "ABN");
        True(response[0].HCntNameEn == "Abu Nakhla Health Center");
        True(response[0].HCntPurpose == "Regular");
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetClinicList()
    {
        // Arrange
        var request = GetRestRequest("clinics");
        
        // Act
        var response = await _client.GetAsync<List<GetClinicListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();
        response = response.OrderBy(x => x.ClinicCode).ToList();

        // Assert
        NotNull(response);
        True(response.Count > 0);
        True(response[0].ClinicCode == "10");
        True(response[0].ClinicNameEn == "Antenatal");
        True(response[1].ClinicCode == "11");
        True(response[1].ClinicNameEn == "CDC");
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetClinicShiftListByHealthCenterCode()
    {
        // Arrange
        var request = GetRestRequest("healthcenters/{hcCode}/clinic-shift");
        request.AddUrlSegment("hcCode", "KHR");
        request.AddQueryParameter("clinicCode", "10");
        request.AddQueryParameter("workDay", "2");
        
        // Act
        var response = await _client.GetAsync<List<GetClinicShiftListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.Count == 1);
        True(response[0].ClinicCode == "10");
        True(response[0].ClinicNameEn == "Antenatal");
        True(response[0].WorkDays == "1,2,3,4,5");
        True(response[0].ShiftTime == "2");
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetClinicDayShiftListByHealthCenterAndClinicCode()
    {
        // Arrange
        var request = GetRestRequest("clinic-day-shift/{hcCode}/{clinicCode}");
        request.AddUrlSegment("hcCode", "KBN");
        request.AddUrlSegment("clinicCode", "13");
        
        // Act
        var response = await _client.GetAsync<List<GetShiftDataListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count > 0);
        True(response[0].ShiftCode == "0");
        True(response[0].ShiftDescriptionEn == "AM");
    }
}