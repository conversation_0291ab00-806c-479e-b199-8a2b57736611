namespace TestEServiceFunctions;

[Collection("AssignmentFunctions")]
public class TestAssignmentFunctions(ITestOutputHelper testOutputHelper, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetAssignmentListByQId()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", "10");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        var content = DeserializeObject<List<GetAssignmentListResponse>>(response.Content!);
        NotNull(content);
        True(content.Count > 0);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAssignmentListByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}");
        request.AddUrlSegment("qId", 22222222233);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", "10");
        request.Method = Method.Get;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(Unauthorized, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetAssignmentStatsByQId()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        var content = DeserializeObject<GetAssignmentStatsResponse>(response.Content!);
        NotNull(content);
        content.Count.Should().BeGreaterThan(0);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAssignmentStatsByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}/stats");
        request.AddUrlSegment("qId", 22222222233);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(Unauthorized, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessAssignmentByQId()
    {
        // Arrange
        var request = GetRestRequest("assignments/{qId}/inprocess-validation");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        var content = DeserializeObject<CheckInProcessAssignmentResponse>(response.Content!);
        NotNull(content);
        True(content.IsInprocessExist);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProcessAssignmentByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("assignments/{qId}/inprocess-validation");
        request.AddUrlSegment("qId", 22222222233);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        var content = DeserializeObject<CheckInProcessAssignmentResponse>(response.Content!);
        NotNull(content);
        False(content.IsInprocessExist);
        Equal(OK, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetAssignmentItemByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("assignments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "8AN0KE0I2TZ");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
        testOutputHelper.LogToConsole(request);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.Should().NotBeNull();

        // Assert
        var content = DeserializeObject<GetAssignmentItemResponse>(response.Content!);
        NotNull(content);
        Equal("8AN0KE0I2TZ", content.ReqNumber);
        Equal(6829880266, content.QId);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAssignmentItemByInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("assignments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "8AN0KE0I2TZ1");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(NoContent, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAssignmentListByQId_EmptyPagination()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        var content = DeserializeObject<List<GetAssignmentListResponse>>(response.Content!);
        NotNull(content);
        True(content.Count >= 0);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAssignmentListByQId_InvalidStatus()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", "InvalidStatus");
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", "10");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(NoContent, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetAssignmentStatsByQId_InProcessStatus()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        var content = DeserializeObject<GetAssignmentStatsResponse>(response.Content!);
        NotNull(content);
        True(content.Count >= 0);
        Equal(OK, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetAssignmentStatsByQId_ArchivedStatus()
    {
        // Arrange
        var request = GetRestRequest("assignments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", "Archived");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        var content = DeserializeObject<GetAssignmentStatsResponse>(response.Content!);
        NotNull(content);
        True(content.Count >= 0);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCreateAssignment_MissingRequestOrigin()
    {
        // Arrange
        var request = GetRestRequest("assignments");
        request.AddOrUpdateHeader(JwtClaimsQId, 28614415144);
        request.Method = Method.Post;
        var requestBody = new CreateUpdateAssignmentRequest
        {
            QId = 28614415144,
            SubmitterQId = 28614415144,
            FNameEn = FirstNameEnglish(),
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "1234567890"
        };

        request.AddJsonBody(requestBody);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(Created, response.StatusCode);
        
        // Get the created assignment request number 
        var responseContent = DeserializeObject<CreateAssignmentResponse>(response.Content!);
        NotNull(responseContent);
        NotNull(responseContent.ReqNumber);
        
        // Delete the created assignment
        var deleteRequest = GetRestRequest("assignments/{reqNumber}");
        deleteRequest.AddUrlSegment("reqNumber", responseContent.ReqNumber);
        deleteRequest.AddOrUpdateHeader(JwtClaimsQId, 28614415144);
        deleteRequest.Method = Method.Delete;
        var deleteResponse = await _client.ExecuteAsync(deleteRequest);
        testOutputHelper.LogToConsole(deleteResponse);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCreateAssignment_EmptyRequestBody()
    {
        // Arrange
        var request = GetRestRequest("assignments");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateAssignment");
        request.Method = Method.Post;
        request.AddJsonBody(new CreateUpdateAssignmentRequest());

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(BadRequest, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCreateAssignment_InvalidEmailFormat()
    {
        // Arrange
        var request = GetRestRequest("assignments");
        request.AddOrUpdateHeader(JwtClaimsQId, 28614415149);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateAssignment");
        request.Method = Method.Post;
        request.AddJsonBody(new CreateUpdateAssignmentRequest
        {
            QId = 28614415149,
            SubmitterQId = 28614415149,
            FNameEn = FirstNameEnglish(),
            MNameEn = MiddleNameEnglish(),
            LNameEn = LastNameEnglish(),
            FNameAr = FirstNameArabic(),
            MNameAr = MiddleNameArabic(),
            LNameAr = LastNameArabic(),
            HCNumber = "**********",
            Nationality = "356",
            Dob = Now.AddYears(-30),
            CurrentAssignedHC = "**********",
            CurrentPhysician = "Dr. Smith",
            SelectedPhysician = "Dr. Jones",
            ChangeReason = "Test Reason",
            SubmittedBy = "Test User",
            SubmitterEmail = "invalid-email",
            SubmitterMobile = "1234567890"
        });

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        //extract the ReqNumber from the response
        var content = DeserializeObject<CreateAssignmentResponse>(response.Content!);

        // Assert
        NotNull(response);
        Equal(Created, response.StatusCode);
        
        // Delete the created assignment
        var deleteRequest = GetRestRequest("assignments/{reqNumber}");
        deleteRequest.AddUrlSegment("reqNumber", content.ReqNumber);
        deleteRequest.AddOrUpdateHeader(JwtClaimsQId, 28614415149);
        deleteRequest.Method = Method.Delete;
        
        var deleteResponse = await _client.ExecuteAsync(deleteRequest);
        testOutputHelper.LogToConsole(deleteResponse);
    }

    private static RestRequest CreateAssignment(long submitterQId = 0)
    {
        var request = GetRestRequest("assignments");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateAssignment");
        var qId = submitterQId == 0 ? RandomNumber(11) : submitterQId;

        var requestBody = new Faker<CreateUpdateAssignmentRequest>()
            .RuleFor(x => x.QId, qId)
            .RuleFor(x => x.SubmitterQId, qId)
            .RuleFor(x => x.FNameEn, FirstNameEnglish())
            .RuleFor(x => x.MNameEn, MiddleNameEnglish())
            .RuleFor(x => x.LNameEn, LastNameEnglish())
            .RuleFor(x => x.FNameAr, FirstNameArabic())
            .RuleFor(x => x.MNameAr, MiddleNameArabic())
            .RuleFor(x => x.LNameAr, LastNameArabic())
            .RuleFor(x => x.HCNumber, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.Nationality, f => f.PickRandom("356", "634", "826", "818"))
            .RuleFor(x => x.Dob, f => f.Date.Past())
            .RuleFor(x => x.CurrentAssignedHC, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.CurrentPhysician, f => f.Name.JobTitle())
            .RuleFor(x => x.SelectedPhysician, f => f.Name.JobTitle())
            .RuleFor(x => x.AttachId1, NewGuid())
            .RuleFor(x => x.AttachId2, NewGuid())
            .RuleFor(x => x.AttachId3, NewGuid())
            .RuleFor(x => x.AttachId4, NewGuid())
            .RuleFor(x => x.AttachId5, NewGuid())
            .RuleFor(x => x.AttachId6, NewGuid())
            .RuleFor(x => x.ChangeReason, f => f.Lorem.Sentence())
            .RuleFor(x => x.SubmittedBy, f => f.Name.FullName())
            .RuleFor(x => x.SubmitterEmail, f => f.Internet.Email())
            .RuleFor(x => x.SubmitterMobile, f => f.Phone.PhoneNumber())
            .Generate();
        request.Method = Method.Post;
        request.AddJsonBody(requestBody);
        return request;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCreateAssignment()
    {
        // Arrange
        var request = GetRestRequest("assignments");
        request.AddOrUpdateHeader(JwtClaimsQId, 28614415164);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateAssignment");
        request.Method = Method.Post;
        request.AddJsonBody(new CreateUpdateAssignmentRequest
        {
            QId = 28614415164,
            SubmitterQId = 28614415164,
            FNameEn = FirstNameEnglish(),
            MNameEn = MiddleNameEnglish(),
            LNameEn = LastNameEnglish(),
            FNameAr = FirstNameArabic(),
            MNameAr = MiddleNameArabic(),
            LNameAr = LastNameArabic(),
            HCNumber = "**********",
            Nationality = "356",
            Dob = Now.AddYears(-30),
            CurrentAssignedHC = "**********",
            CurrentPhysician = "Dr. Smith",
            SelectedPhysician = "Dr. Jones",
            ChangeReason = "Test Reason",
            SubmittedBy = "Test User",
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "1234567890"
        });

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        var content = DeserializeObject<CreateAssignmentResponse>(response.Content!);
        NotNull(content);
        NotNull(content.ReqNumber);
        Equal(Created, response.StatusCode);
        
        // Cleanup
        var deleteRequest = GetRestRequest("assignments/{reqNumber}");
        deleteRequest.AddUrlSegment("reqNumber", content.ReqNumber);
        deleteRequest.AddOrUpdateHeader(JwtClaimsQId, 28614415164);
        deleteRequest.Method = Method.Delete;
        var deleteResponse = await _client.ExecuteAsync(deleteRequest);
        testOutputHelper.LogToConsole(deleteResponse);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateAssignmentByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("assignments/{reqNumber}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("reqNumber", "8AN0KE0I2TZ");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateAssignmentByReqNumber");

        var requestBody = new Faker<CreateUpdateAssignmentRequest>()
            .RuleFor(x => x.QId, 6829880266)
            .RuleFor(x => x.FNameEn, FirstNameEnglish())
            .RuleFor(x => x.MNameEn, MiddleNameEnglish())
            .RuleFor(x => x.LNameEn, LastNameEnglish())
            .RuleFor(x => x.FNameAr, FirstNameArabic())
            .RuleFor(x => x.MNameAr, MiddleNameArabic())
            .RuleFor(x => x.LNameAr, LastNameArabic())
            .RuleFor(x => x.HCNumber, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.ChangeReason, f => f.Lorem.Sentence())
            .RuleFor(x => x.AttachId1, NewGuid())
            .RuleFor(x => x.AttachId2, NewGuid())
            .RuleFor(x => x.AttachId3, NewGuid())
            .RuleFor(x => x.AttachId4, NewGuid())
            .RuleFor(x => x.AttachId5, NewGuid())
            .RuleFor(x => x.AttachId6, NewGuid())
            .RuleFor(x => x.Nationality, f => f.PickRandom("356", "634", "826", "818"))
            .RuleFor(x => x.Dob, f => f.Date.Past())
            .RuleFor(x => x.CurrentAssignedHC, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.CurrentPhysician, f => f.Name.LastName())
            .RuleFor(x => x.SelectedPhysician, f => f.Name.LastName())
            .RuleFor(x => x.SubmitterQId, GetMoqUser().UserQId)
            .RuleFor(x => x.SubmittedBy, f => f.Name.FullName())
            .RuleFor(x => x.SubmitterEmail, f => f.Internet.Email())
            .RuleFor(x => x.SubmitterMobile, f => f.Phone.PhoneNumber())
            .Generate();

        request.AddJsonBody(requestBody);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestUpdateAssignmentByInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("assignments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "8AN0KE0IQEA");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateAssignmentByReqNumber");
        request.Method = Method.Put;

        var requestBody = new Faker<CreateUpdateAssignmentRequest>()
            .RuleFor(x => x.QId, 6829880266)
            .RuleFor(x => x.FNameEn, FirstNameEnglish())
            .RuleFor(x => x.MNameEn, MiddleNameEnglish())
            .RuleFor(x => x.LNameEn, LastNameEnglish())
            .RuleFor(x => x.FNameAr, FirstNameArabic())
            .RuleFor(x => x.MNameAr, MiddleNameArabic())
            .RuleFor(x => x.LNameAr, LastNameArabic())
            .RuleFor(x => x.HCNumber, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.ChangeReason, f => f.Lorem.Sentence())
            .RuleFor(x => x.AttachId1, NewGuid())
            .RuleFor(x => x.AttachId2, NewGuid())
            .RuleFor(x => x.AttachId3, NewGuid())
            .RuleFor(x => x.AttachId4, NewGuid())
            .RuleFor(x => x.AttachId5, NewGuid())
            .RuleFor(x => x.AttachId6, NewGuid())
            .RuleFor(x => x.Nationality, f => f.PickRandom("356", "634", "826", "818"))
            .RuleFor(x => x.Dob, f => f.Date.Past())
            .RuleFor(x => x.CurrentAssignedHC, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.CurrentPhysician, f => f.Name.LastName())
            .RuleFor(x => x.SelectedPhysician, f => f.Name.LastName())
            .RuleFor(x => x.SubmitterQId, LongQId)
            .RuleFor(x => x.SubmittedBy, f => f.Name.FullName())
            .RuleFor(x => x.SubmitterEmail, f => f.Internet.Email())
            .RuleFor(x => x.SubmitterMobile, f => f.Phone.PhoneNumber())
            .Generate();

        request.AddJsonBody(requestBody);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(BadRequest, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteAssignmentByReqNumber()
    {
        // Arrange
        var getRequestNumber = CreateAssignment(28614415140);
        getRequestNumber.AddOrUpdateHeader(JwtClaimsQId, 28614415140);
        var createdAssignment = await _client.ExecuteAsync(getRequestNumber);
        var content = DeserializeObject<CreateAssignmentResponse>(createdAssignment.Content!);

        // Act
        var request = GetRestRequest("assignments/{reqNumber}");
        request.AddUrlSegment("reqNumber", content.ReqNumber);
        request.AddOrUpdateHeader(JwtClaimsQId, 28614415140);
        request.Method = Method.Delete;
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        True(response.IsSuccessful);
        Equal(OK, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestDeleteAssignmentByInvalidReqNumber()
    {
        // Arrange
        var reqNumber = new Faker().Random.AlphaNumeric(10);
        var request = GetRestRequest("assignments/{reqNumber}");
        request.AddUrlSegment("reqNumber", reqNumber);
        request.Method = Method.Delete;
        
        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(BadRequest, response.StatusCode);
    }
}