# Get Registration List By QID

## Overview
Retrieves a paginated list of registration requests for a specific submitter based on their Qatar ID, with optional status filtering.

## Endpoint
- **Route**: `registrations/submitter-qid/{qId}`
- **Method**: GET
- **Authorization**: Function level with QID validation

## Parameters
- **qId** (path, required): Submitter's Qatar ID number
- **status** (query, optional): Filter by status category
  * InProcess: Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested
  * Archived: Approved, Cancelled, CancelledByEServ
- **skip** (query, optional): Number of records to skip for pagination
- **take** (query, optional): Number of records to return per page

## Response
- **200 OK**: Returns list of registrations
  ```json
  [
    {
      "QId": "long",
      "ReqNumber": "string",
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "Status": "string",
      "StatusDescriptionEn": "string",
      "StatusDescriptionAr": "string",
      "SubmittedAt": "string"
    }
  ]
  ```
- **204 No Content**: No registrations found
- **400 Bad Request**: Invalid parameters
- **401 Unauthorized**: Invalid QID authorization

## Business Logic
1. Validates user authorization against QID
2. Applies optional status filtering
3. Implements pagination with skip/take
4. Joins with Status table for descriptions
5. Orders results by status

## Query Optimization
- Uses AsNoTracking for read-only operations
- Efficient joins with Status table
- Pagination to limit result size
- Optimized data projection

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Unauthorized access logging

## Database Operations
- Joins Registration and Status tables
- Read-only transaction
- Efficient data projection
- Pagination support

## Multilingual Support
- English name fields (FNameEn, MNameEn, LNameEn)
- Arabic name fields (FNameAr, MNameAr, LNameAr)
- Bilingual status descriptions
