﻿namespace EServiceFunctions.RequestResponseModels.Appointment;

public class GetAppointmentListResponse
{
    public long? QId { get; set; }
    public string? ReqNumber { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? HcNumber { get; set; }
    public string? Clinic { get; set; }
    public string? PrefDate { get; set; }
    public string? AmPm { get; set; }
    public string? AmPmDescriptionEn { get; set; }
    public string? AmPmDescriptionAr { get; set; }
    public string? RequestType { get; set; }
    public string? RequestTypeDescriptionEn { get; set; }
    public string? RequestTypeDescriptionAr { get; set; }
    public string? Status { get; set; }
    public string? StatusDescriptionEn { get; set; }
    public string? StatusDescriptionAr { get; set; }
    public string? SubmittedAt { get; set; }
}