# GetHealthCenterList

## Overview
Retrieves a filtered list of health centers with support for various filtering criteria.

## Endpoint
- **Route**: `healthcenters`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **hcCode** (query, optional): Filter by health center code
- **hcPurpose** (query, optional): Filter by purpose
  - Regular: Standard health centers
  - Covid19: COVID-19 dedicated facilities
- **clinicCode** (query, optional): Filter by associated clinic code
- **skip** (query, optional): Number of records to skip
- **take** (query, optional): Number of records to take

## Response
- **200 OK**: Returns list of health centers
  ```json
  [
    {
      "HCntCode": "string",
      "HCntNameEn": "string",
      "HCntNameAr": "string",
      "HCntPurpose": "string"  // "Regular" or "Covid19"
    }
  ]
  ```
- **204 No Content**: No health centers found
- **400 Bad Request**: Invalid hcPurpose value

## Business Logic
1. Applies filters:
   - Health center code
   - Purpose (Regular/Covid19)
   - Associated clinic code
2. Additional filtering criteria:
   - Organization must be PHCC
   - Facility status must be Active
   - Facility type must be Health Center
   - Must have a region specified
3. Implements pagination
4. Returns distinct results

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient filtering with Where clauses
- Pagination implementation
- Distinct results to avoid duplicates

## Data Security
- Only returns active facilities
- Proper null handling
- Region validation

## Multilingual Support
- Bilingual health center names (English/Arabic)
