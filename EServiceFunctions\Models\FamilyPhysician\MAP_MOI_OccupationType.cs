﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.FamilyPhysician;

public class MAP_MOI_OccupationType
{
    [Key]
    [StringLength(50)]
    [Unicode(false)]
    public string OCCUPATION_CODE { get; set; } = null!;
    [StringLength(255)]
    [Unicode(false)]
    public string? DESCRIPTION_ENGLISH { get; set; }
    [StringLength(255)]
    public string? DESCRIPTION_ARABIC_MALE { get; set; }
    [StringLength(255)]
    public string? DESCRIPTION_ARABIC_FEMALE { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LAST_UPDATED_DATE { get; set; }
}