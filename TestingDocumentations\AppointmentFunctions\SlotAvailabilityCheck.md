# Slot Availability Check Tests

## Overview

These tests verify the functionality of checking slot availability before booking appointments. The test suite ensures accurate availability status and proper handling of concurrent booking scenarios.

## Test Cases

### TestCheckSlotAvailability_ValidRequest_ReturnsOk

Tests the successful verification of slot availability.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckSlotAvailability_ValidRequest_ReturnsOk()
{
    // Arrange
    var request = GetRestRequest("appointments/check-availability");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var checkRequest = new SlotAvailabilityRequest
    {
        Date = DateTime.Now.AddDays(1),
        ServiceType = "General",
        Location = "Main Clinic"
    };
    request.AddJsonBody(checkRequest);

    // Act
    var response = await _client.ExecuteAsync<SlotAvailabilityResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    NotNull(response.Data);
    True(response.Data.IsAvailable);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Future date
  - Service type
  - Location
- **Expected Output**: 
  - Availability status
  - Slot details
- **Validation Points**:
  - Response success
  - Availability flag
  - Capacity information

### TestCheckSlotAvailability_FullyBooked_ReturnsUnavailable

Tests the API's response when checking a fully booked slot.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestCheckSlotAvailability_FullyBooked_ReturnsUnavailable()
{
    // Arrange
    var bookedSlot = await CreateFullyBookedSlot();
    var request = GetRestRequest("appointments/check-availability");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var checkRequest = new SlotAvailabilityRequest
    {
        Date = bookedSlot.StartTime,
        ServiceType = bookedSlot.ServiceType,
        Location = bookedSlot.Location
    };
    request.AddJsonBody(checkRequest);

    // Act
    var response = await _client.ExecuteAsync<SlotAvailabilityResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    NotNull(response.Data);
    False(response.Data.IsAvailable);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Fully booked slot details
- **Expected Output**: Unavailable status
- **Validation Points**:
  - Response success
  - Unavailability flag
  - Alternative suggestions

## Request/Response Models

### SlotAvailabilityRequest
```csharp
public class SlotAvailabilityRequest
{
    public DateTime Date { get; set; }
    public string ServiceType { get; set; }
    public string Location { get; set; }
    public int? Duration { get; set; }
}
```

### SlotAvailabilityResponse
```csharp
public class SlotAvailabilityResponse
{
    public bool IsAvailable { get; set; }
    public int RemainingCapacity { get; set; }
    public List<DateTime> AlternativeSlots { get; set; }
    public string Message { get; set; }
}
```

## Availability Rules

1. **Capacity Check**
   - Check total capacity
   - Consider concurrent bookings
   - Account for buffer time

2. **Service Availability**
   - Check service schedule
   - Verify provider availability
   - Consider location hours

3. **Time Constraints**
   - Check business hours
   - Consider holidays
   - Account for maintenance

## Concurrent Booking Handling

### 1. Lock Management
```csharp
private async Task<bool> AcquireSlotLock(string slotId)
{
    using var lockScope = await _lockProvider.AcquireLockAsync(
        $"slot_{slotId}",
        TimeSpan.FromSeconds(30)
    );
    return lockScope != null;
}
```

### 2. Capacity Tracking
```csharp
private async Task<bool> CheckAndDecrementCapacity(string slotId)
{
    using var transaction = await _database.BeginTransactionAsync();
    try
    {
        var slot = await _database.Slots.FindAsync(slotId);
        if (slot.RemainingCapacity > 0)
        {
            slot.RemainingCapacity--;
            await _database.SaveChangesAsync();
            await transaction.CommitAsync();
            return true;
        }
        return false;
    }
    catch
    {
        await transaction.RollbackAsync();
        throw;
    }
}
```

## Error Scenarios

1. **Invalid Requests**
   - Past dates
   - Invalid service types
   - Unknown locations

2. **System Conditions**
   - Full capacity
   - Service unavailable
   - Location closed

3. **Concurrent Issues**
   - Race conditions
   - Deadlocks
   - Stale data

## Best Practices

### 1. Availability Check
```csharp
private async Task<AvailabilityResult> CheckAvailability(SlotAvailabilityRequest request)
{
    // Validate request
    if (!IsValidRequest(request))
        return AvailabilityResult.Invalid();

    // Check business hours
    if (!IsWithinBusinessHours(request.Date))
        return AvailabilityResult.OutsideHours();

    // Check capacity
    var capacity = await GetCurrentCapacity(request);
    if (capacity <= 0)
        return AvailabilityResult.Full();

    return AvailabilityResult.Available(capacity);
}
```

### 2. Alternative Suggestions
```csharp
private async Task<List<DateTime>> GetAlternativeSlots(SlotAvailabilityRequest request)
{
    var alternatives = new List<DateTime>();
    var searchRange = TimeSpan.FromDays(7);
    var searchEnd = request.Date.Add(searchRange);

    while (alternatives.Count < 5 && request.Date <= searchEnd)
    {
        request.Date = request.Date.AddDays(1);
        if (await IsSlotAvailable(request))
            alternatives.Add(request.Date);
    }

    return alternatives;
}
```

## Performance Optimization

1. **Caching Strategy**
```csharp
private async Task<int> GetCachedCapacity(string slotId)
{
    var cacheKey = $"slot_capacity_{slotId}";
    var capacity = await _cache.GetAsync<int>(cacheKey);
    if (!capacity.HasValue)
    {
        capacity = await CalculateCapacity(slotId);
        await _cache.SetAsync(cacheKey, capacity.Value, TimeSpan.FromMinutes(5));
    }
    return capacity.Value;
}
```

2. **Batch Processing**
```csharp
private async Task<Dictionary<string, bool>> CheckMultipleSlots(List<string> slotIds)
{
    var results = new Dictionary<string, bool>();
    var batches = slotIds.Chunk(10);
    
    foreach (var batch in batches)
    {
        var tasks = batch.Select(id => CheckSingleSlot(id));
        var batchResults = await Task.WhenAll(tasks);
        
        for (int i = 0; i < batch.Length; i++)
            results[batch[i]] = batchResults[i];
    }
    
    return results;
}
```

## Notes

- Implements optimistic concurrency
- Handles race conditions
- Provides alternative suggestions
- Maintains data consistency
- Optimizes performance

## Recommendations

1. **Concurrency Control**
   - Use distributed locks
   - Implement retry logic
   - Monitor lock timeouts

2. **Data Consistency**
   - Regular capacity sync
   - Audit booking records
   - Clean up stale locks

3. **User Experience**
   - Show real-time availability
   - Provide waiting list
   - Send notifications
