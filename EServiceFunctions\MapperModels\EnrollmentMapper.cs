﻿using EServiceFunctions.Models.Enrollment;
using EServiceFunctions.RequestResponseModels.Enrollment;

namespace EServiceFunctions.MapperModels;

public static class EnrollmentMapper
{
    public static GetEnrollmentItemResponse MapEnrollmentToGetEnrollmentItemResponse(Enrollment enrollment)
    {
        var response = new GetEnrollmentItemResponse
        {
            QId = enrollment.QId,
            ReqNumber = enrollment.ReqNumber,
            HCNumber = enrollment.HCNumber,
            FNameEn = enrollment.FNameEn,
            MNameEn = enrollment.MNameEn,
            LNameEn = enrollment.LNameEn,
            FNameAr = enrollment.FNameAr,
            MNameAr = enrollment.MNameAr,
            LNameAr = enrollment.LNameAr,
            Nationality = enrollment.Nationality,
            AttachId1 = enrollment.AttachId1,
            AttachId2 = enrollment.AttachId2,
            AttachId3 = enrollment.AttachId3,
            AttachId4 = enrollment.AttachId4,
            AttachId5 = enrollment.AttachId5,
            AttachId6 = enrollment.AttachId6,
            SubmittedBy = enrollment.SubmittedBy,
            SubmittedAt = enrollment.SubmittedAt.ToUtcString(),
            SubmitterQId = enrollment.SubmitterQId,
            SubmitterEmail = enrollment.SubmitterEmail,
            SubmitterMobile = enrollment.SubmitterMobile,
            SubmitterNationality = enrollment.SubmitterNationality,
            SubmitterHCNumber = enrollment.SubmitterHCNumber,
            SubmitterHCntCode = enrollment.SubmitterHCntCode,
            Consent = enrollment.Consent,
            CreatedAt = enrollment.CreatedAt.ToUtcString(),
            UpdatedAt = enrollment.UpdatedAt.ToUtcString(),
            Status = enrollment.StatusNavigation?.Code,
            StatusDescriptionEn = enrollment.StatusNavigation?.DescriptionEn,
            StatusDescriptionAr = enrollment.StatusNavigation?.DescriptionAr,
            SN = enrollment.SN
        };
        return response;
    }

    public static Enrollment MapCreateUpdateEnrollmentRequestToEnrollment(CreateUpdateEnrollmentRequest createUpdateEnrollmentRequest)
    {
        var response = new Enrollment
        {
            QId = createUpdateEnrollmentRequest.QId,
            HCNumber = createUpdateEnrollmentRequest.HCNumber,
            FNameEn = createUpdateEnrollmentRequest.FNameEn,
            MNameEn = createUpdateEnrollmentRequest.MNameEn,
            LNameEn = createUpdateEnrollmentRequest.LNameEn,
            FNameAr = createUpdateEnrollmentRequest.FNameAr,
            MNameAr = createUpdateEnrollmentRequest.MNameAr,
            LNameAr = createUpdateEnrollmentRequest.LNameAr,
            Nationality = createUpdateEnrollmentRequest.Nationality,
            AttachId1 = createUpdateEnrollmentRequest.AttachId1,
            AttachId2 = createUpdateEnrollmentRequest.AttachId2,
            AttachId3 = createUpdateEnrollmentRequest.AttachId3,
            AttachId4 = createUpdateEnrollmentRequest.AttachId4,
            AttachId5 = createUpdateEnrollmentRequest.AttachId5,
            AttachId6 = createUpdateEnrollmentRequest.AttachId6,
            SubmittedBy = createUpdateEnrollmentRequest.SubmittedBy,                
            SubmitterQId = createUpdateEnrollmentRequest.SubmitterQId,
            SubmitterEmail = createUpdateEnrollmentRequest.SubmitterEmail,
            SubmitterMobile = createUpdateEnrollmentRequest.SubmitterMobile,
            SubmitterNationality = createUpdateEnrollmentRequest.SubmitterNationality,
            SubmitterHCNumber = createUpdateEnrollmentRequest.SubmitterHCNumber,
            SubmitterHCntCode = createUpdateEnrollmentRequest.SubmitterHCntCode,
            Consent = createUpdateEnrollmentRequest.Consent
        };
        return response;
    }
}