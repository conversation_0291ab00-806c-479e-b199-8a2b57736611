# Get MHDS Statistics Tests

## Test Cases

### 1. TestGetMhdsStatsByQId

Tests retrieval of MHDS statistics by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetMhdsStatsByQId()
{
    // Arrange
    var request = GetRestRequest($"mhds/submitter-qid/{22222222270}/stats");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

    // Act
    var response = await _client.GetAsync<GetMHDSStatsResponse>(request);

    // Assert
    True(response is not null);
    True(response.Count > 0);
}
```

### 2. TestGetMhdsStatsByQIdWithInvalidQId

Tests statistics retrieval with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetMhdsStatsByQIdWithInvalidQId()
{
    // Arrange
    var qId = 343434224343;
    var request = GetRestRequest($"mhds/submitter-qid/{qId}/stats");
    request.AddOrUpdateHeader(JwtClaimsQId, qId);

    // Act
    var response = await _client.GetAsync<GetMHDSStatsResponse>(request);

    // Assert
    True(response?.Count == 0);
}
```

## Request Details

### Endpoint
```
GET mhds/submitter-qid/{qId}/stats
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `qId`: Submitter QID

## Response Model

### GetMHDSStatsResponse
```csharp
public class GetMHDSStatsResponse
{
    public int Count { get; set; }
}
```

## Test Data

### Success Case
- QID: 22222222270
- Expected: Count > 0

### Error Case
- QID: 343434224343
- Expected: Count = 0

## Validation Rules

### Success Case Validation
1. Response not null
2. Count greater than 0

### Error Case Validation
1. Response may be null
2. Count equals 0 if response exists

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Out of range QID

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - QID mismatch

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims
   - Authorization validation

2. **Statistics**
   - Count of MHDS requests
   - All statuses included
   - Performance optimized

3. **Performance**
   - Quick count operation
   - No detailed data
   - Efficient query
