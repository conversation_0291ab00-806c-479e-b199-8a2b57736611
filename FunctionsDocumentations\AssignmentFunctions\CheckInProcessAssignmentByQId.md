# CheckInProcessAssignmentByQId

## Overview
Checks if there are any in-process assignments for a given requester's QId.

## Endpoint
- **Route**: `assignments/{qId}/inprocess-validation`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `qId` (path, required): Requester's QID

## Responses
- **200 OK**: Returns `CheckInProcessAssignmentResponse`
  ```json
  {
    "IsInprocessExist": boolean
  }
  ```
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication failed

## Business Logic
1. Joins Assignment with Status table
2. Checks for any assignments where:
   - QId matches the requester's QId
   - Status category is "InProcess"
3. Returns boolean indicating existence of in-process assignments

## Dependencies
- AssignmentContext
- Status entity
