﻿using System.Diagnostics.CodeAnalysis;

namespace MockDataLibrary;

[ExcludeFromCodeCoverage]
public static class GlobalConstants
{
    public const string UnitTest = "UnitTestCases";
    public const string Category = "Category";
    public const string Take = "100";
    public const string Skip = "0";
    public const string HappyPath = "HappyPath";
    public const string UnHappyPath = "UnHappyPath";
    public const string EmptyString = "";
    public const long OneMb = 1048576;
    public const string EligibleClinicCodes = "X-ClinicCodes";
    public const string PhccEnvironment = "PHCC-Environment";
    public const string RequestOrigin = "X-RequestOrigin";
    public const string JwtClaimsQId = "X-JWT-Claims-QID";
    public const string IsAuthValReq = "IsAuthValReq";
    public const string ContentType = "Content-Type";
    public const string ApplicationJson = "application/json";
    public const string Post = "POST";
    public const string Put = "PUT";
    public const string Delete = "DELETE";
    public const string Phcc = "PHCC";
    public const string Active = "Active";
    public const string HealthCenter = "Health Center";
    public const string Covid19 = "Covid19";
    public const string Regular = "Regular";
}