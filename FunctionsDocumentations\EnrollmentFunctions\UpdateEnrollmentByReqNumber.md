# UpdateEnrollmentByReqNumber

## Overview
Updates an existing enrollment for a given request number.

## Endpoint
- **Route**: `enrollments/{reqNumber}`
- **Method**: PUT
- **Authorization**: Function level with submitter QId validation

## Headers
- **X-RequestOrigin** (required): Source of the request

## Parameters
- **reqNumber** (path, required): Enrollment Request Number

## Request Body
```json
{
  "SubmitterQId": "long",
  "QId": "long",
  "FNameEn": "string",
  "MNameEn": "string",
  "LNameEn": "string",
  "FNameAr": "string",
  "MNameAr": "string",
  "LNameAr": "string",
  "Nationality": "string",
  "HCNumber": "string"
  // Additional enrollment fields...
}
```

## Response
- **200 OK**: Returns updated enrollment
- **400 Bad Request**: Invalid request number, body, or missing headers
- **401 Unauthorized**: Invalid submitter QId access
- **500 Internal Server Error**: Database operation failure

## Business Logic
1. Validates request headers and body
2. Validates submitter QId authorization
3. Retrieves existing enrollment
4. Validates ownership (submitter QId match)
5. Updates enrollment fields while preserving:
   - Request number
   - Status
   - Creation details
6. Updates timestamps and source
7. Saves changes

## Data Preservation
- Maintains original:
  - Request number
  - Status and internal status
  - SN
  - Creation timestamp and source
  
## Error Handling
- Validates request completeness
- Ensures enrollment exists
- Proper DB exception handling
