# EservicesRESTapis Test Documentation

This documentation provides a comprehensive overview of the test cases in the EservicesRESTapis project. Each section covers a specific functional area and details the test cases that verify its behavior.

## Test Categories

1. [Assignment Functions](./AssignmentFunctions/README.md)
2. [Appointment Functions](./AppointmentFunctions/README.md)
3. [Comment and Attachments Functions](./CommentAndAttachmentsFunctions/README.md)
4. [Covid Vaccine Functions](./CovidVaccineFunctions/README.md)
5. [EDW Drop Down List Functions](./EDWDropDownListFunctions/README.md)
6. [Enrollment Functions](./EnrollmentFunctions/README.md)
7. [Family Physician Functions](./FamilyPhysicianFunctions/README.md)
8. [Health Center Functions](./HealthCenterFunctions/README.md)
9. [MHDS Functions](./MHDSFunctions/README.md)
10. [Registration Functions](./RegistrationFunctions/README.md)
11. [Release Item Functions](./ReleaseItemFunctions/README.md)
12. [Release of Information Functions](./ReleaseOfInformationFunctions/README.md)
13. [Token Generator Functions](./TokenGeneratorFunctions/README.md)
14. [Transfer Functions](./TransferFunctions/README.md)
15. [User Profile Functions](./UserProfileFunctions/README.md)
16. [User Verification Functions](./UserVerificationFunctions/README.md)

## Test Structure

Each test class follows a consistent structure:

1. **Setup**: Test initialization and configuration
2. **Test Categories**: 
   - Happy Path Tests: Verify expected behavior with valid inputs
   - Unhappy Path Tests: Verify error handling with invalid inputs
3. **Assertions**: Validation of responses and error conditions
4. **Cleanup**: Resource cleanup after test execution

## Common Test Patterns

1. **HTTP Status Code Verification**: Tests verify appropriate status codes for different scenarios
2. **Input Validation**: Tests cover various input combinations and edge cases
3. **Error Handling**: Tests verify proper error responses for invalid inputs
4. **Authentication**: Tests verify proper handling of authentication scenarios
5. **Data Integrity**: Tests verify data consistency and proper state changes

## Test Environment

The tests are configured to run against a cloud-hosted API environment. Configuration settings are managed through `appconfigs.dev.json`.

## Running Tests

To run the tests:

```bash
dotnet test
```

For specific test categories:

```bash
dotnet test --filter "FullyQualifiedName~TestCategoryName"
```
