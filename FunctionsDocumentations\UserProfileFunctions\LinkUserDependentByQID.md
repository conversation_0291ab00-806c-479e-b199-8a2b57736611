# Link User Dependent By QID

## Overview
Creates a relationship between a user and a dependent in the EServices system. This endpoint validates the relationship through MOI data and establishes the linkage in the system.

## Endpoint
```http
POST /userprofiles/link-dependents/{submitterQId}/{dependentQId}
```

## Authorization
- Function-level authorization
- QID-based access control
- Claims validation

## Headers
- `X-RequestOrigin` (required): Source of the request
- `X-PhccEnvironment` (optional): Environment identifier for EDW data retrieval

## Parameters

### Path Parameters
- `submitterQId` (long, required): User QID
- `dependentQId` (long, required): Dependent QID to link

## Response

### Success Response (201 Created)
Returns the created dependent relationship:
```json
{
  "qId": "long",
  "parentQId": "long",
  "createdAt": "string",
  "createSource": "string"
}
```

### Error Responses
- 400 Bad Request: Invalid QIDs, already linked, or missing required header
- 401 Unauthorized: Invalid or missing authorization
- 204 No Content: No eligible dependent found
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Validates through MOI data
- Creates relationship record
- Records creation metadata
- Manages linkage status
- Implements proper error handling
- Maintains data consistency

## Business Logic
- Validates QID authorization
- Verifies MOI relationship
- Checks existing linkage
- Manages relationship data
- Validates eligibility
- Maintains audit trail

## Data Validation
### MOI Validation
- Dependent existence
- Relationship validity
- Linkage status
- Nationality rules
- Age restrictions

### System Validation
- QID authorization
- Request origin
- Duplicate prevention
- Data consistency
- Input format

## Security Considerations
- Validates user authorization
- Implements function-level security
- Ensures data access control
- Validates request origin
- Protects sensitive data
- Proper error handling

## Performance Optimization
- Efficient database operations
- Proper connection handling
- Transaction management
- Resource cleanup
- Early validation
- Error prevention
