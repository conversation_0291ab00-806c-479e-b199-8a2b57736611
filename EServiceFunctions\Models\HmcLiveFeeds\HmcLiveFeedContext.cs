﻿namespace EServiceFunctions.Models.HmcLiveFeeds;

public class HmcLiveFeedContext(DbContextOptions<HmcLiveFeedContext> options) : DbContext(options)
{
    public DbSet<AppointmentBooking> AppointmentBookings { get; set; }
    public DbSet<Patient> Patients { get; set; }
    public DbSet<Visit> Visits { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AppointmentBooking>().ToTable("AppointmentBookings");
        modelBuilder.Entity<Patient>().ToTable("Patients");
        modelBuilder.Entity<Visit>().ToTable("Visits");
        
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Patient>(entity =>
        {
            entity.HasKey(e => e.QId);
        });

        modelBuilder.Entity<AppointmentBooking>(entity =>
        {
            entity.<PERSON><PERSON>ey(e => e.AppointmentId);

            entity.HasOne(d => d.Patient)
                .WithMany(p => p.AppointmentBookings)
                .HasForeignKey(d => d.QId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AppointmentBookings_Patients");
        });

        modelBuilder.Entity<Visit>(entity =>
        {
            entity.HasKey(e => e.VisitNumber);

            entity.HasOne(d => d.Patient)
                .WithMany(p => p.Visits)
                .HasForeignKey(d => d.QId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Visits_Patients");
        });
    }
}