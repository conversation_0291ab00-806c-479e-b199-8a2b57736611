﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.Assignment;

public class Assignment
{
    public int Id { get; set; }
    public long? QId { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? ReqNumber { get; set; }
    public string? HCNumber { get; set; }
    public string? Nationality { get; set; }
    public DateTime? Dob { get; set; }
    public string? CurrentAssignedHC { get; set; }
    public string? CurrentPhysician { get; set; }
    public string? SelectedPhysician { get; set; }
    public Guid? AttachId1 { get; set; }
    public Guid? AttachId2 { get; set; }
    public Guid? AttachId3 { get; set; }
    public Guid? AttachId4 { get; set; }
    public Guid? AttachId5 { get; set; }
    public Guid? AttachId6 { get; set; }
    public string? ChangeReason { get; set; }
    public string? SubmittedBy { get; set; }
    public DateTime? SubmittedAt { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? Status { get; set; }
    public string? StatusInternal { get; set; }
    public string? SN { get; set; }
    public string? CreateSource { get; set; }
    public string? UpdateSource { get; set; }
    public Status? StatusNavigation { get; set; }
}