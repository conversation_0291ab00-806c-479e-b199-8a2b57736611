# CreateMHDSRequest

## Overview
Creates a new Medicine Home Delivery Service (MHDS) request for a user with single or multiple medicine orders.

## Endpoint
- **Route**: `mhds`
- **Method**: POST
- **Authorization**: Function level

## Request Headers
- **X-RequestOrigin**: Required. Source of the request

## Request Body
```json
{
  "QId": "long",
  "SubmitterQId": "long",
  "FNameEn": "string",
  "FNameAr": "string",
  "MNameEn": "string",
  "MNameAr": "string",
  "LNameEn": "string",
  "LNameAr": "string",
  "HCNumber": "string", // REQUIRED - Health Card Number
  "CurrentAssignedHC": "string",
  "Dob": "datetime",
  "Nationality": "string",
  "UNo": "string",
  "BNo": "string",
  "SNo": "string",
  "ZNo": "string",
  "IsGisAddressManualyEntered": "boolean",
  "SubmitterMobile": "string",
  "SecondaryMobNo": "string",
  "SubmitterEmail": "string",
  "Consent": "boolean",
  "Action": "string",
  "MedInfo": [
    {
      "MedicineName": "string",
      "PrescriptionOrderId": "string",
      "PrescribedDate": "datetime",
      "PrescriptionRefillDueDate": "datetime",
      "LastDispenseDate": "datetime",
      "SupplyDuration": "string",
      "LastDispensedLocation": "string",
      "OrderByName": "string",
      "AttachId": "string"
    }
  ]
}
```

### Required Fields
- **HCNumber**: Health Card Number is mandatory for all MHDS requests. Must be a valid alphanumeric string between 5-20 characters.

## Response
- **201 Created**: Returns created MHDS request details
- **400 Bad Request**: Invalid request parameters or validation failures
  - Missing Health Card Number:
    ```json
    {
      "code": 400,
      "message": "Health Card No is missing for QID {QID} and requestname MHDS"
    }
    ```
  - Invalid Health Card Format:
    ```json
    {
      "code": 400,
      "message": "Health Card No format is invalid for QID {QID} and requestname MHDS"
    }
    ```
- **401 Unauthorized**: Invalid QID authorization
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates request origin header
2. Validates user authorization against submitter QID
3. **Validates Health Card Number** (NEW):
   - Checks if Health Card Number is present
   - Validates format (5-20 alphanumeric characters)
   - Returns 400 Bad Request if validation fails
   - Logs validation failures with timestamp, QID, request type, and source
4. Creates new MHDS request with:
   - Unique request number
   - Personal information
   - Address details
   - Contact information
   - Submission metadata
4. Creates medicine list entries for each medicine
5. Saves both request and medicine list to database

## Data Operations
1. Creates MHDSRequestDetails entry
2. Creates multiple MHDSRequestMedicineList entries
3. Performs both operations in single transaction

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Request origin validation
- Logging of all operations

## Data Validation
- Request body validation
- Required header validation
- QID authorization check

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Unauthorized access logging

## Multilingual Support
- Supports both English and Arabic names
- Proper handling of bilingual fields
