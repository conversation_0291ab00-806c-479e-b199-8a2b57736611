# Validate QID

## Overview
Validates a QID and returns various status flags regarding the user's profile, including citizenship status, document validity, healthcare card status, and other important indicators.

## Endpoint
```http
GET /validate-qid/{qId}
```

## Authorization
- Function-level authorization
- QID-based access control
- Claims validation

## Headers
- `X-PhccEnvironment` (optional): Environment identifier for EDW data retrieval

## Parameters

### Path Parameters
- `qId` (long, required): User QID to validate

## Response

### Success Response (200 OK)
Returns validation flags for the QID:
```json
{
  "isCitizen": "boolean",
  "isMinor": "boolean",
  "isQIDValid": "boolean",
  "isQIDinGrace": "boolean",
  "isHCardPresent": "boolean",
  "isHCardValid": "boolean",
  "isHCardinGrace": "boolean",
  "isGISAddressNotRecent": "boolean",
  "isFPhysicianAssigned": "boolean",
  "isHCenterAssigned": "boolean",
  "isOGCC": "boolean"
}
```

### No Content Response (204)
Returned when no profile is found for the given QID.

### Error Responses
- 401 Unauthorized: Invalid or missing authorization
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Retrieves user profile from EDW database
- Validates QID expiry dates
- Checks healthcare card status
- Verifies GIS address recency
- Validates physician assignment
- Checks nationality status

## Business Logic
- Determines citizenship based on nationality code
- Calculates minor status based on DOB
- Validates QID expiry status
- Checks healthcare card validity
- Verifies address update status
- Validates healthcare assignments

## Validation Rules
### QID Validation
- Valid: QID not expired
- Grace Period: Expired within last 365 days

### Healthcare Card Validation
- Present: Card number exists
- Valid: Card not expired
- Grace Period: Expired within last 365 days

### Address Validation
- Recent: Updated within configured timeframe
- Uses environment variable for threshold

### Nationality Validation
- Citizen: Code "634"
- OGCC: Codes ["414", "512", "682", "048", "784"]

## Security Considerations
- Validates user authorization
- Implements function-level security
- Ensures data access control
- Protects sensitive data
- Proper error handling
- Secure configuration

## Performance Optimization
- Efficient database queries
- Proper connection handling
- Optimized data retrieval
- Early validation checks
- Resource management
- Caching considerations
