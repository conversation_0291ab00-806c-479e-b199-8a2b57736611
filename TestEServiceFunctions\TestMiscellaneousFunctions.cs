﻿namespace TestEServiceFunctions;

[Collection("MiscellaneousFunctions")]
public class TestMiscellaneousFunctions(ITestOutputHelper testOutputHelper, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetRequestStatusStatsByQId()
    {
        // Arrange
        var request = GetRestRequest("requeststatus-stats/{qId}");
        request.AddUrlSegment("qId", "22222222267");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222267");
        
        // Act
        var response = await _client.GetAsync<List<MyTaskStatusStatsResponse>>(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        True(response.Count > 0);
        NotNull(response);
    }
}