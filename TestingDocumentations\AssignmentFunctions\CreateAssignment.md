# CreateAssignment Tests

## Overview
Tests that verify the functionality of creating new assignments through the API.

## Test Cases

### TestCreateAssignment_ValidRequest
Tests the successful creation of an assignment with valid request data.

#### Test Implementation
```csharp
[Fact] //happy path
public async Task TestCreateAssignment_ValidRequest()
{
    // Arrange
    var request = CreateAssignment();

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(Created, response.StatusCode);
    
    var content = DeserializeObject<CreateUpdateAssignmentResponse>(response.Content!);
    NotNull(content);
    NotEmpty(content.ReqNumber);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: Valid assignment request
- **Expected Output**: 201 Created with assignment details
- **Validation**:
  - Response is not null
  - Response is successful
  - Status code is 201 Created
  - Response contains valid request number

### TestCreateAssignment_MissingRequestOrigin
Tests assignment creation with missing request origin.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestCreateAssignment_MissingRequestOrigin()
{
    // Arrange
    var request = CreateAssignment();
    request.Parameters.RemoveParameter("RequestOrigin");

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(Created, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Assignment request without RequestOrigin
- **Expected Output**: 201 Created (server handles missing origin)
- **Validation**:
  - Response is not null
  - Status code is 201 Created

### TestCreateAssignment_InvalidEmailFormat
Tests assignment creation with invalid email format.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestCreateAssignment_InvalidEmailFormat()
{
    // Arrange
    var request = CreateAssignment();
    request.AddOrUpdateParameter("Email", "invalid-email");

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(Created, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Assignment request with invalid email
- **Expected Output**: 201 Created (server validates email)
- **Validation**:
  - Response is not null
  - Status code is 201 Created

### TestCreateAssignment_EmptyRequestBody
Tests assignment creation with empty request body.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestCreateAssignment_EmptyRequestBody()
{
    // Arrange
    var request = GetRestRequest("assignments", Method.Post);
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(BadRequest, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Empty request body
- **Expected Output**: 400 BadRequest
- **Validation**:
  - Response is not null
  - Status code is 400 BadRequest

## Common Patterns
1. **Request Creation**:
   - Uses `CreateAssignment` helper method
   - Modifies request parameters for specific test cases
   - Adds authentication headers

2. **Response Validation**:
   - Checks for null response
   - Verifies status code
   - Validates response content when applicable

3. **Error Handling**:
   - Uses `ThrowIfNull` for critical validations
   - Logs response for debugging

## Helper Methods
```csharp
private static RestRequest CreateAssignment(long submitterQId = 0)
{
    var request = GetRestRequest("assignments", Method.Post);
    request.AddOrUpdateHeader(JwtClaimsQId, submitterQId == 0 ? 22222222272 : submitterQId);
    request.AddOrUpdateParameter("RequestOrigin", "test");
    // Additional parameters...
    return request;
}
```

## Notes
- Tests cover both valid and invalid scenarios
- Proper error handling is verified
- Response content is validated when appropriate
- Logging is implemented for debugging purposes
