# Get Registration Statistics Tests

## Test Cases

### 1. TestGetRegistrationStatsByQId

Tests retrieval of registration statistics by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetRegistrationStatsByQId()
{
    // Arrange
    var request = GetRestRequest("registrations/submitter-qid/{qId}/stats");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);
    request.AddUrlSegment("qId", 22222222270);
    request.AddQueryParameter("status", InProcess);

    // Act
    var response = await _client.GetAsync<GetRegistrationStatsResponse>(request);

    // Assert
    response.ThrowIfNull();
    True(response.Count > 0);
}
```

### 2. TestGetRegistrationStatsByQIdWithInvalidQId

Tests statistics retrieval with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetRegistrationStatsByQIdWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("registrations/submitter-qid/{qId}/stats");
    request.AddOrUpdateHeader(JwtClaimsQId, 33322222230);
    request.AddUrlSegment("qId", 33322222230);
    request.AddQueryParameter("status", InProcess);

    // Act
    var response = await _client.GetAsync<GetRegistrationStatsResponse>(request);
    response.ThrowIfNull();

    // Assert
    Equal(0, response.Count);
}
```

## Request Details

### Endpoint
```
GET registrations/submitter-qid/{qId}/stats
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `qId`: Submitter QID

### Query Parameters
- `status`: Registration status (e.g., "InProcess")

## Response Model

### GetRegistrationStatsResponse
```csharp
public class GetRegistrationStatsResponse
{
    public int Count { get; set; }
}
```

## Test Data

### Success Case
- QID: 22222222270
- Status: InProcess
- Expected: Count > 0

### Error Case
- QID: 33322222230
- Status: InProcess
- Expected: Count = 0

## Validation Rules

### Success Case Validation
1. Response not null
2. Count greater than 0

### Error Case Validation
1. Response not null
2. Count equals 0

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Unauthorized access

2. **Invalid Status**
   - Unknown status
   - Invalid format
   - Missing status

3. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - QID mismatch

## Notes

1. **Status Parameter**
   - Filters by registration status
   - Case sensitive
   - Required parameter

2. **Response Format**
   - Simple count response
   - Zero for no matches
   - Null check required

3. **Performance**
   - Quick count operation
   - Status-based filtering
   - Efficient query execution
