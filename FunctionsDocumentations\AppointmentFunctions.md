# Appointment Functions

## Overview
The Appointment API provides comprehensive endpoints for managing healthcare appointments, including checking availability, booking, rescheduling, and cancellation. The service integrates with multiple data sources including AppointmentContext, EDWDbContext, and HmcLiveFeedContext to provide a unified appointment management system.

## Source Code Organization
```plaintext
EServiceFunctions/
├── Functions/
│   └── AppointmentFunctions.cs   # Main implementation
├── Models/
│   ├── Appointment/              # Appointment-related models
│   ├── HmcLiveFeeds/            # HMC live feed models
│   └── MHDS/EDW/                # EDW-related models
└── RequestResponseModels/
    └── Appointment/             # Request/Response DTOs

TestEServiceFunctions/
└── TestAppointmentFunctions.cs  # Test implementation
```

## Technical Implementation
- **Database Contexts**: 
  - `AppointmentContext`: Primary context for appointment management
  - `EDWDbContext`: Enterprise Data Warehouse context
  - `HmcLiveFeedContext`: Live feed context for real-time appointment data
- **Dependency Injection**: Uses IDbContextFactory pattern for efficient context management
- **Logging**: Implements ILogger for comprehensive error tracking and monitoring
- **Authentication**: Function-level authorization with JWT claims validation

## Endpoints

### 1. Check In-Progress PME Appointment (V1)
- **Endpoint**: `GET /appointments/exists/{qId}`
- **Description**: Checks if a PME Appointment request or a 'New' Cerner PME Appointment exists for the given QID
- **Authorization**: Function level
- **Parameters**:
  - `qId`: QID (path, required, long)
  - `EligibleClinicCodes`: List of eligible clinic codes (header, required)
- **Response**:
  - `200 OK`: Returns CheckInProcessConfirmedAppointmentResponse
    ```json
    {
      "IsInProcessConfirmedRequestExist": boolean
    }
    ```
  - `400 Bad Request`: Invalid request parameters
  - `401 Unauthorized`: Authentication failed
  - `404 Not Found`: No appointment found
  - `500 Internal Server Error`: Server error
- **Implementation Details**:
  - Validates clinic codes against eligible types
  - Checks both in-progress requests and confirmed future appointments
  - Considers appointment status (Confirmed/Reschedule)
  - Validates against eligible appointment types
  - Uses UTC date comparison for future appointments
- **Test Coverage**:
  - Happy Path: Valid QID returns correct appointment status
  - Unhappy Path: Empty QID list returns BadRequest
  - Edge Case: Invalid QID returns NoContent
  - Performance: Multiple QIDs batch processing

### 2. Get Appointments List By QID List (V1)
- **Endpoint**: `POST /appointments/bookings`
- **Description**: Batch retrieval of appointments for multiple QIDs
- **Authorization**: Function level, requires submitter QID in claims
- **Request Body**:
  ```json
  {
    "QIds": ["string"]
  }
  ```
- **Headers**:
  - `EligibleClinicCodes`: List of eligible clinic codes (required). _Header retrieval uses TryGetHeaders, supporting multiple headers and case-insensitive matching._
  - `JwtClaimsQId`: Submitter's QID from JWT token
- **Validation**:
  - If no clinic codes are provided (in any header variant), a `400 Bad Request` is returned.
  - Validation logic is updated to aggregate all `EligibleClinicCodes` headers regardless of case or multiplicity.
- **Async Database Access**:
  - All database queries are now performed asynchronously for improved performance and scalability.
- **Constants**:
  - Reusable constants are refactored for maintainability. Ensure any integration or test code references the updated constant names.
- **Response**:
  - `200 OK`: Returns list of AppointmentResponse objects
    ```json
    [{
      "QId": "string",
      "HcNumber": "string",
      "AppointmentId": "string",
      "AppointmentDateTime": "string",
      "AppointmentType": "string",
      "AppointmentStatus": "string",
      "AppointmentHCnt": "string"
    }]
    ```
  - `204 No Content`: No appointments found
  - `400 Bad Request`: Invalid request parameters
  - `401 Unauthorized`: Authentication failed
  - `403 Forbidden`: Access denied
  - `500 Internal Server Error`: Server error
- **Implementation Details**:
  - Implements pagination for large result sets
  - Validates QID format and length
  - Performs bulk data retrieval optimization
  - Includes error handling for database connectivity issues
- **Test Coverage**:
  - Empty QID list validation
  - Multiple QIDs batch processing
  - No appointments scenario
  - Invalid QID format handling

### 3. Get EServ Requested Appointment List
- **Endpoint**: `GET /appointments/submitter-qid/{qId}`
- **Description**: Retrieves eServ requested appointment list for a submitter's QID
- **Authorization**: Function level, requires QID authorization
- **Parameters**:
  - `qId`: Submitter's QID (path, required)
  - `status`: Filter by status (query, optional)
    - InProcess: Submitted, Rework, Reworked
    - Archived: Approved, Cancelled, CancelledByEServ, Rejected
  - `requestType`: Filter by request type (query, optional)
    - Values: New, Reschedule, Cancel
  - `clinicCode`: Filter by clinic code (query, optional)
  - `skip`: Number of records to skip (query, optional)
  - `take`: Number of records to take (query, optional)
- **Headers**:
  - `EligibleClinicCodes`: List of eligible clinic codes (required)
- **Response**:
  - `200 OK`: Returns list of GetAppointmentListResponse objects
  - `204 No Content`: No appointments found
  - `400 Bad Request`: Invalid parameters or missing clinic codes
  - `401 Unauthorized`: Authentication failed
- **Implementation Details**:
  - Implements server-side pagination
  - Performs status validation against allowed values
  - Includes clinic code authorization check
  - Optimizes database queries based on filter parameters
- **Test Coverage**:
  - Pagination functionality
  - Status filter validation
  - Clinic code authorization
  - Request type filtering

### 4. Get Appointment Stats By QID
- **Endpoint**: `GET /appointments/submitter-qid/{qId}/stats`
- **Description**: Get appointment statistics for a submitter's QID
- **Authorization**: Function level, requires QID authorization
- **Parameters**:
  - `qId`: Submitter's QID (path, required)
  - `status`: Filter by status (query, optional)
    - InProcess: Submitted, PendingOrder
    - Archived: Confirmed, CancelledFrom107
  - `requestType`: Filter by request type (query, required)
    - Values: New, Reschedule, Cancel
- **Headers**:
  - `EligibleClinicCodes`: List of eligible clinic codes (required)
- **Response**:
  - `200 OK`: Returns GetAppointmentStatsResponse
    ```json
    {
      "Count": number
    }
    ```
  - `400 Bad Request`: Invalid parameters or missing clinic codes
  - `401 Unauthorized`: Authentication failed
- **Implementation Details**:
  - Aggregates data across multiple contexts
  - Implements caching for performance optimization
  - Includes data validation and sanitization
- **Test Coverage**:
  - Status aggregation accuracy
  - Cache invalidation scenarios
  - Performance testing with large datasets

### 5. Check In-Process Confirmed Appointment
- **Endpoint**: `GET /appointments/inprocess-confirmed-check/{hcNumber}/{clinicCode}`
- **Description**: Check for in-process or confirmed appointments by health card number and clinic code
- **Authorization**: Function level
- **Parameters**:
  - `hcNumber`: Health Card Number (path, required)
  - `clinicCode`: Clinic Code (path, required)
- **Headers**:
  - `PhccEnvironment`: Environment information (required)
- **Response**:
  - `200 OK`: Returns CheckInProcessConfirmedAppointmentResponse
  - `401 Unauthorized`: Authentication failed
  - `400 Bad Request`: Invalid parameters
- **Implementation Details**:
  - Cross-references multiple data sources
  - Validates health card number format
  - Performs environment-specific checks
- **Test Coverage**:
  - Valid health card number scenarios
  - Invalid clinic code handling
  - Environment configuration testing

### 6. Get Appointment By Request Number
- **Endpoint**: `GET /appointments/{reqNumber}`
- **Description**: Get detailed appointment information by request number
- **Authorization**: Function level, requires submitter QID in claims
- **Parameters**:
  - `reqNumber`: Appointment Request Number (path, required)
- **Headers**:
  - `EligibleClinicCodes`: List of eligible clinic codes (required)
- **Response**:
  - `200 OK`: Returns GetAppointmentItemResponse
  - `204 No Content`: Appointment not found
  - `400 Bad Request`: Invalid request number or ineligible clinic
  - `401 Unauthorized`: Authentication failed
- **Implementation Details**:
  - Validates clinic eligibility
  - Includes detailed appointment information
  - Authorization check against submitter QID
  - Implements request number validation
- **Test Coverage**:
  - Valid request number scenarios
  - Authorization validation
  - Clinic eligibility checks

### 7. Check Appointment Change Request
- **Endpoint**: `GET /appointment-change/{appointmentId}`
- **Description**: Check if an appointment can be changed or cancelled
- **Authorization**: Function level
- **Parameters**:
  - `appointmentId`: Appointment ID (path, required)
- **Headers**:
  - `EligibleClinicCodes`: Clinic codes (optional)
- **Response**:
  - `200 OK`: Returns CheckAppointmentChangeEligibilityResponse
  - `400 Bad Request`: Invalid appointment ID
  - `204 No Content`: No change requests found
- **Implementation Details**:
  - Validates appointment status
  - Checks business rules for changes
  - Implements change request tracking
- **Test Coverage**:
  - Change eligibility rules
  - Cancellation scenarios
  - Status validation

## Function Documentation

### Appointment Queries
- [CheckInProgressPMEAppointmentV1](AppointmentFunctions/CheckInProgressPMEAppointmentV1.md)
- [GetAppointmentsListByQIdV1](AppointmentFunctions/GetAppointmentsListByQIdV1.md)
- [GetEServRequestedAppointmentListByQId](AppointmentFunctions/GetEServRequestedAppointmentListByQId.md)
- [GetAppointmentStatsByQId](AppointmentFunctions/GetAppointmentStatsByQId.md)
- [GetEServRequestedAppointmentByReqNumber](AppointmentFunctions/GetEServRequestedAppointmentByReqNumber.md)
- [CheckAppointmentChangeRequestExist](AppointmentFunctions/CheckAppointmentChangeRequestExist.md)

### Appointment Management
- [CreateAppointment](AppointmentFunctions/CreateAppointment.md)
- [UpdateAppointmentByReqNumber](AppointmentFunctions/UpdateAppointmentByReqNumber.md)
- [DeleteAppointmentByReqNumber](AppointmentFunctions/DeleteAppointmentByReqNumber.md)

### Confirmed Appointments
- [UpcomingConfirmedAppointmentsByHcNumberListV2](AppointmentFunctions/UpcomingConfirmedAppointmentsByHcNumberListV2.md)
- [GetUpcomingConfirmedAppointmentByAppointmentId](AppointmentFunctions/GetUpcomingConfirmedAppointmentByAppointmentId.md)

## Error Handling
- All endpoints implement comprehensive error handling
- Errors are logged with correlation IDs
- Client receives appropriate HTTP status codes with error details
- Database transaction rollback on failure
- Custom exception types for specific scenarios
- Detailed error logging with stack traces

## Performance Considerations
- Implements database query optimization
- Uses async/await patterns for better scalability
- Includes pagination for large result sets
- Implements caching where appropriate
- Query parameter validation
- Bulk operation support
- Connection pooling

## Security
- JWT-based authentication
- Role-based access control
- Input validation and sanitization
- Clinic code authorization checks
- Request origin validation
- Data encryption in transit
- Audit logging

## Monitoring and Logging
- Request/Response logging
- Performance metrics tracking
- Error rate monitoring
- Authentication attempts logging
- Database query performance tracking
- Cache hit/miss ratio monitoring

## Integration Features
- External system integration
- Environment-specific configuration
- Multiple clinic support
- Health card validation
- Real-time data synchronization
- Batch processing capabilities
- Failover handling