﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.GeneralWorks;

[ExcludeFromCodeCoverage]
public class CovidVaccination
{
    public Guid Id { get; set; }
    public string? IDType { get; set; }
    public string? IDNumber { get; set; }
    public string? FullNameEn { get; set; }
    public string? FullNameAr { get; set; }
    public string? MobileNumber { get; set; }
    public DateTime? DOB { get; set; }
    public string? Gender { get; set; }
    public string? Nationality { get; set; }
    public string? Email { get; set; }
    public string? Status { get; set; }
    public string? SN { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}