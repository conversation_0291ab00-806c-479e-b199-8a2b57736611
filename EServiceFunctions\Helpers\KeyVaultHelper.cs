﻿using static Azure.Core.RetryMode;

namespace EServiceFunctions.Helpers;

[ExcludeFromCodeCoverage]
public static class KeyVaultHelper
{
    private static readonly string KeyVaultUrl = GetEnvironmentVariable("KeyVaultUrl")!;
    private static readonly string ClientId = GetEnvironmentVariable("ClientId")!;
    private static readonly string ClientSecret = GetEnvironmentVariable("ClientSecret")!;
    private static readonly string TenantId = GetEnvironmentVariable("TenantId")!;

    private static SecretClient GetSecretClient()
    {
        //This block is necessary for local execution. Do not remove it.
        if (GetEnvironmentVariable("IsLocal")?.Equals("true", OrdinalIgnoreCase) ?? false)
        {
            return new SecretClient(new Uri(KeyVaultUrl), new ClientSecretCredential(TenantId, ClientId, ClientSecret));
        }

        SecretClientOptions options = new()
        {
            Retry =
            {
                Delay = FromSeconds(2),
                MaxDelay = FromSeconds(16),
                MaxRetries = 5,
                Mode = Exponential
            }
        };

        //Internally it is using SystemIdentity.
        var credential = new DefaultAzureCredential(new DefaultAzureCredentialOptions());
        return new SecretClient(new Uri(KeyVaultUrl), credential, options);
    }

    public static string GetSecret(string secretName)
    {
        return GetSecretClient().GetSecret(secretName).Value.Value;
    }
}