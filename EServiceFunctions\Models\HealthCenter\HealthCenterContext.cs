﻿namespace EServiceFunctions.Models.HealthCenter;

public class HealthCenterContext(DbContextOptions<HealthCenterContext> options) : DbContext(options)
{
    public virtual DbSet<Clinic>? Clinic { get; set; }
    public virtual DbSet<ClinicShift>? ClinicShift { get; set; }
    public virtual DbSet<HealthCenter>? HealthCenter { get; set; }
    public virtual DbSet<ClinicDaysShift>? ClinicDaysShift { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<Clinic>(entity =>
        {
            entity.Property(e => e.ClinicCode)
                .HasColumnName("CLINIC_CODE")
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.CLINIC_ID)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClinicNameAr).HasColumnName("CLINIC_NAME_ARB").HasMaxLength(150);

            entity.Property(e => e.ClinicNameEn)
                .HasColumnName("CLINIC_NAME_EN")
                .HasMaxLength(100)
                .IsUnicode(false);

            entity.Property(e => e.CREATED_DATE).HasColumnType("datetime");

            entity.Property(e => e.FAC_CODE)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();

            entity.Property(e => e.ID).ValueGeneratedOnAdd();

            entity.Property(e => e.LAST_UPDATED_DATE).HasColumnType("datetime");
        });

        modelBuilder.Entity<ClinicShift>(entity =>
        {
            entity.Property(e => e.WorkDays)
                .HasColumnName("AVAILABLE_DAYS")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ClinicCode)
                .HasColumnName("CLINIC_CODE")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.CREATED_DATE).HasColumnType("datetime");

            entity.Property(e => e.HCntCode)
                .HasColumnName("FAC_CODE")
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();

            entity.Property(e => e.ID).ValueGeneratedOnAdd();
        });

        modelBuilder.Entity<HealthCenter>(entity =>
        {
            entity.Property(e => e.AMBULATORY_PROVISION)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();

            entity.Property(e => e.ATTRIB_2)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ATTRIB_3)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.ATTRIB_4)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.CLOSING_YEAR)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();

            entity.Property(e => e.HCntCode).HasColumnName("FACILITY_CODE")
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();

            entity.Property(e => e.HCntNameAr).HasColumnName("FACILITY_NAME_ARB").HasMaxLength(500);

            entity.Property(e => e.HCntNameEn)
                .HasColumnName("FACILITY_NAME_EN")
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.Property(e => e.FACILITY_STATUS)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.FACILITY_TYPE)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.LAST_UPDATED_DATE).HasColumnType("datetime");

            entity.Property(e => e.OPENING_YEAR)
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();

            entity.Property(e => e.ORGANISATION)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.REGION_ARB).HasMaxLength(100);

            entity.Property(e => e.REGION_EN)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.RSC_HC_STATUS)
                .HasMaxLength(20)
                .IsUnicode(false);

            entity.Property(e => e.WORKING_DAYS)
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.Property(e => e.WORKING_HOURS)
                .HasMaxLength(20)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ClinicDaysShift>(entity =>
        {
            entity.Property(e => e.ClinicCode)
                .HasColumnName("CLINIC_CODE")
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.Property(e => e.DATE_FLD).HasColumnType("datetime");

            entity.Property(e => e.HCntCode)
                .HasColumnName("FAC_CODE")
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();

            entity.Property(e => e.ShiftCode)
                .HasColumnName("SHIFT_TIME")
                .HasMaxLength(10)
                .IsUnicode(false)
                .IsFixedLength();
        });
    }
}