# DeleteAppointmentByReqNumber

## Overview
Deletes (soft delete) an appointment request by its request number.

## Endpoint
- **Route**: `appointments/{reqNumber}`
- **Method**: DELETE
- **Authorization**: Function level

## Parameters
- `reqNumber` (path, required): Appointment request number to delete
- `EligibleClinicCodes` (header, required): List of eligible clinic codes

## Responses
- **200 OK**: Returns success response
  ```json
  {
    "Success": true,
    "Message": "string"
  }
  ```
- **400 Bad Request**: Invalid request number
- **401 Unauthorized**: Authentication failed
- **404 Not Found**: Appointment not found
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates request number
2. Checks authorization
3. Performs soft delete of appointment
4. Updates appointment status
5. Returns success confirmation

## Dependencies
- AppointmentContext
- Status entity
