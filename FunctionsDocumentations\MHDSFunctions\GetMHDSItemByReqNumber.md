# GetMHDSItemByReqNumber

## Overview
Retrieves detailed information about a specific Medicine Home Delivery Service (MHDS) request.

## Endpoint
- **Route**: `mhds/{reqNumber}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **reqNumber** (path, required): MHDS Request Number

## Response
- **200 OK**: Returns MHDS request details
  ```json
  {
    "ReqNumber": "string",
    "QId": "long",
    "SubmitterQId": "long",
    "Status": "string",
    "StatusDescriptionEn": "string",
    "StatusDescriptionAr": "string",
    "PersonalInfo": {
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "HCNumber": "string",
      "CurrentAssignedHC": "string",
      "Dob": "datetime",
      "Nationality": "string"
    },
    "AddressInfo": {
      "UNo": "string",
      "BNo": "string",
      "SNo": "string",
      "ZNo": "string",
      "IsGisAddressManualyEntered": "boolean"
    },
    "ContactInfo": {
      "SubmitterMobile": "string",
      "SecondaryMobNo": "string",
      "SubmitterEmail": "string"
    },
    "MedicineList": [
      {
        "MedicineName": "string",
        "PrescriptionOrderId": "string",
        "PrescribedDate": "datetime",
        "LastDispenseDate": "datetime",
        "SupplyDuration": "string",
        "LastDispensedLocation": "string",
        "OrderByName": "string",
        "PrescriptionRefillDueDate": "datetime",
        "AttachId": "string"
      }
    ]
  }
  ```
- **204 No Content**: Request not found
- **400 Bad Request**: Invalid request number
- **401 Unauthorized**: Invalid QID authorization

## Business Logic
1. Validates request number
2. Retrieves MHDS request with Status information
3. Validates submitter authorization
4. Retrieves associated medicine list
5. Maps data to response structure

## Data Operations
1. Joins MHDSRequestDetails with Status
2. Retrieves MHDSRequestMedicineList entries
3. Uses Include for efficient loading

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Logging of all operations

## Multilingual Support
- Bilingual names (English/Arabic)
- Bilingual status descriptions

## Error Handling
- Invalid request number validation
- Empty response validation
- Exception handling with default behavior
- Request URL logging
