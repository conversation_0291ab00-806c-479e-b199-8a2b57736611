# Delete User Profile By QID

## Overview
Deletes a user profile from the EServices system. This is an internal endpoint that should be used with caution as it permanently removes the profile data. **Note**: This endpoint is marked with `[OpenApiIgnore]` attribute and is excluded from the public API documentation.

## Endpoint
```http
DELETE /userprofiles/{qId}
```

## Authorization
- Function-level authorization
- QID-based access control
- Claims validation
- Internal use only
- Excluded from OpenAPI documentation (`[OpenApiIgnore]`)

## Parameters

### Path Parameters
- `qId` (long, required): User QID

## Response

### Success Response (200 OK)
Returns a success message:
```text
"Deleted UserProfile with the QId: {qId}"
```

### Error Responses
- 400 Bad Request: Invalid QID or QID not found
- 401 Unauthorized: Invalid or missing authorization
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Validates profile existence
- Performs authorization checks
- Executes profile deletion
- Handles database transactions
- Implements proper error handling
- Maintains data integrity

## Business Logic
- Validates QID authorization
- Verifies profile existence
- Manages data deletion
- Handles dependencies
- Validates permissions
- Maintains audit trail

## Security Considerations
- Validates user authorization
- Implements function-level security
- Ensures data access control
- Validates submitter claims
- Protects sensitive data
- Proper error handling

## Performance Optimization
- Efficient database operations
- Proper connection handling
- Transaction management
- Resource cleanup
- Early validation
- Error prevention

## Important Notes
- This endpoint is marked with `[OpenApiIgnore]`
- Internal use only
- Permanent data deletion
- No recovery mechanism
- Use with caution
- Proper authorization required
