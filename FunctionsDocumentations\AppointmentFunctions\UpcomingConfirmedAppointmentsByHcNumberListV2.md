# UpcomingConfirmedAppointmentsByHcNumberListV2

## Overview
Retrieves upcoming confirmed appointments for a list of health card numbers.

## Endpoint
- **Route**: `appointments/upcoming-confirmed`
- **Method**: POST
- **Authorization**: Function level

## Request Body
```json
{
  "HcNumbers": ["string"]
}
```

## Headers
- `EligibleClinicCodes`: List of eligible clinic codes

## Responses
- **200 OK**: Returns list of upcoming confirmed appointments
  ```json
  [{
    "AppointmentId": "string",
    "HcNumber": "string",
    "AppointmentDateTime": "datetime",
    "AppointmentType": "string",
    "ClinicType": "string",
    "HealthCenter": "string"
  }]
  ```
- **400 Bad Request**: Invalid request data
- **204 No Content**: No appointments found
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates health card numbers
2. Retrieves upcoming confirmed appointments
3. Filters by eligible clinic codes
4. Returns formatted appointment list

## Dependencies
- HmcLiveFeedContext
- EDWDbContext
