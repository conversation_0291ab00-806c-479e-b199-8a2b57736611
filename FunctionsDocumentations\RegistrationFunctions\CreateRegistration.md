# Create Registration

## Overview
Creates a new registration request with support for draft and final submissions.

## Endpoint
- **Route**: `registrations`
- **Method**: POST
- **Authorization**: Function level with submitter validation

## Headers
- **X-RequestOrigin** (required): Source of the request

## Request Body
```json
{
  "SubmitterQId": "long",
  "SubmitterEmail": "string",
  "SubmitterMobile": "string",
  "IsDraft": "boolean",
  "FNameEn": "string",
  "MNameEn": "string",
  "LNameEn": "string",
  "FNameAr": "string",
  "MNameAr": "string",
  "LNameAr": "string"
}
```

## Response
- **201 Created**: Returns created registration
  ```json
  {
    "ReqNumber": "string",
    "Status": "string",
    "SubmittedAt": "string",
    "CreatedAt": "string",
    "CreateSource": "string",
    "RegistrationDetails": "object"
  }
  ```
- **400 Bad Request**: Invalid request body or missing required fields
- **401 Unauthorized**: Invalid submitter authorization
- **500 Internal Server Error**: Database operation error

## Business Logic
1. Validates request origin header
2. Validates submitter authorization
3. Generates unique request number
4. Sets appropriate status (Saved/Ready)
5. Records timestamps and source

## Status Management
- **Draft Mode** (IsDraft = true):
  * Status set to "Saved"
  * Can be modified later
- **Final Submission** (IsDraft = false):
  * Status set to "Ready"
  * Proceeds to processing

## Security Considerations
- Function-level authorization
- Submitter validation
- Request origin validation
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Database error handling

## Database Operations
- New record creation
- Status assignment
- Timestamp management
- Source tracking

## Multilingual Support
- English name fields (FNameEn, MNameEn, LNameEn)
- Arabic name fields (FNameAr, MNameAr, LNameAr)
- Supports bilingual data entry
