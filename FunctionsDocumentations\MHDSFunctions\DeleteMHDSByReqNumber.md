# DeleteMHDSByReqNumber

## Overview
Deletes a Medicine Home Delivery Service (MHDS) request and its associated medicine list. This endpoint is for internal use only.

## Endpoint
- **Route**: `mhds/{reqNumber}`
- **Method**: DELETE
- **Authorization**: Function level
- **Note**: Internal use only (OpenApiIgnore)

## Parameters
- **reqNumber** (path, required): MHDS Request Number

## Response
- **204 No Content**: Successfully deleted
- **400 Bad Request**: Invalid request number or no request found
- **401 Unauthorized**: Invalid QID authorization
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates request number
2. Retrieves MHDS request and medicine list
3. Validates submitter authorization
4. Deletes in following order:
   - Medicine list entries
   - MHDS request details

## Data Operations
1. Retrieves MHDSRequestDetails entry
2. Retrieves associated MHDSRequestMedicineList entries
3. Performs cascading delete
4. <PERSON>les database update exceptions

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Internal use only restriction
- Logging of all operations

## Error Handling
- Invalid request number validation
- Empty response validation
- Database update exception handling
- Exception handling with default behavior
- Request URL logging

## Transaction Management
- Atomic deletion of related records
- Database update exception recovery
- Proper cleanup of resources
