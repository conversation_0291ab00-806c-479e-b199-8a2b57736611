# DeleteAssignment Tests

## Overview
Tests that verify the functionality of deleting assignments through the API.

## Test Cases

### TestDeleteAssignmentByReqNumber
Tests the successful deletion of an assignment using a valid request number.

#### Test Implementation
```csharp
[Fact] //happy path
public async Task TestDeleteAssignmentByReqNumber()
{
    // Arrange
    var createRequest = CreateAssignment();
    var createResponse = await _client.ExecuteAsync(createRequest);
    createResponse.ThrowIfNull();
    
    var content = DeserializeObject<CreateUpdateAssignmentResponse>(createResponse.Content!);
    NotNull(content);
    NotEmpty(content.ReqNumber);

    // Act
    var response = await DeleteAsync(content.ReqNumber);
    testOutputHelper.LogToConsole(response);

    // Assert
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: Valid request number
- **Expected Output**: 200 OK
- **Validation**:
  - Response is successful
  - Status code is 200 OK

### TestDeleteAssignmentByInvalidReqNumber
Tests error handling when deleting an assignment with an invalid request number.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestDeleteAssignmentByInvalidReqNumber()
{
    // Arrange
    const string invalidReqNumber = "INVALID";
    
    // Act
    var response = await DeleteAsync(invalidReqNumber);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(BadRequest, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Invalid request number
- **Expected Output**: 400 BadRequest
- **Validation**:
  - Response is not null
  - Status code is 400 BadRequest

## Common Patterns
1. **Request Setup**:
   - Creates test assignment first (for valid deletion)
   - Uses `DeleteAsync` helper method
   - Adds authentication headers

2. **Response Validation**:
   - Checks for null response
   - Verifies status code
   - Validates successful deletion

3. **Error Handling**:
   - Uses `ThrowIfNull` for critical validations
   - Logs response for debugging

## Helper Methods
```csharp
private async Task<RestResponse> DeleteAsync(string reqNumber)
{
    var request = GetRestRequest("assignments/{reqNumber}", Method.Delete);
    request.AddUrlSegment("reqNumber", reqNumber);
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
    
    var response = await _client.ExecuteAsync(request);
    return response;
}
```

## Notes
- Tests handle both successful and error scenarios
- Proper cleanup of test data
- Comprehensive validation of responses
- Logging implemented for debugging purposes
- Tests ensure proper resource cleanup
