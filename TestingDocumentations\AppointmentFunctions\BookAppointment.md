# Book Appointment Tests

## Overview

These tests verify the functionality of booking new appointments through the API. The test suite covers successful booking scenarios and various validation cases.

## Test Cases

### TestBookAppointment_ValidRequest_ReturnsCreated

Tests the successful booking of an appointment with valid request data.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestBookAppointment_ValidRequest_ReturnsCreated()
{
    // Arrange
    var request = GetRestRequest("appointments/book");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var bookingRequest = new BookingRequest
    {
        QId = GetMoqUser().UserQId.ToString(),
        PreferredDate = DateTime.Now.AddDays(1),
        ServiceType = "General",
        Location = "Main Clinic"
    };
    request.AddJsonBody(bookingRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(Created, response.StatusCode);
    var booking = JsonConvert.DeserializeObject<BookingResponse>(response.Content!);
    NotNull(booking);
    NotEmpty(booking.AppointmentId);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid QID
  - Future appointment date
  - Valid service type and location
- **Expected Output**: 
  - 201 Created response
  - Valid appointment ID
- **Validation Points**:
  - Response status code
  - Appointment ID generation
  - Booking confirmation

### TestBookAppointment_PastDate_ReturnsBadRequest

Tests validation of appointment booking with a past date.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestBookAppointment_PastDate_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest("appointments/book");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var bookingRequest = new BookingRequest
    {
        QId = GetMoqUser().UserQId.ToString(),
        PreferredDate = DateTime.Now.AddDays(-1),
        ServiceType = "General"
    };
    request.AddJsonBody(bookingRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(BadRequest, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Past appointment date
- **Expected Output**: 400 BadRequest response
- **Validation Points**:
  - Error status code
  - Validation message

## Request/Response Models

### BookingRequest
```csharp
public class BookingRequest
{
    public string QId { get; set; }
    public DateTime PreferredDate { get; set; }
    public string ServiceType { get; set; }
    public string Location { get; set; }
    public string Notes { get; set; }
}
```

### BookingResponse
```csharp
public class BookingResponse
{
    public string AppointmentId { get; set; }
    public string Status { get; set; }
    public DateTime ConfirmedDate { get; set; }
    public string Location { get; set; }
}
```

## Validation Rules

1. **Date Validation**
   - Must be a future date
   - Must be within booking window
   - Must be a valid working day

2. **Service Type Validation**
   - Must be a valid service type
   - Must be available at location
   - Must be appropriate for user

3. **Location Validation**
   - Must be a valid location
   - Must offer the requested service
   - Must be operational

## Error Scenarios

1. **Invalid Date**
   - Past dates
   - Non-working days
   - Outside booking window

2. **Invalid Service**
   - Unknown service type
   - Unavailable service
   - Restricted service

3. **Location Issues**
   - Invalid location
   - Service not available
   - Location closed

## Best Practices

### 1. Request Validation
```csharp
// Validate date
if (bookingRequest.PreferredDate <= DateTime.Now)
{
    return BadRequest("Appointment date must be in the future");
}

// Validate service type
if (!ValidServiceTypes.Contains(bookingRequest.ServiceType))
{
    return BadRequest("Invalid service type");
}
```

### 2. Response Handling
```csharp
// Success response
return new BookingResponse
{
    AppointmentId = GenerateAppointmentId(),
    Status = "Confirmed",
    ConfirmedDate = bookingRequest.PreferredDate
};

// Error response
return new ErrorResponse
{
    Message = "Validation failed",
    Details = validationErrors
};
```

### 3. Cleanup
```csharp
// After test
if (appointmentCreated)
{
    await CancelAppointment(appointmentId);
}
```

## Test Data Management

1. **Test User Setup**
```csharp
private User GetMoqUser() => new()
{
    UserQId = 22222222272,
    Permissions = ["BookAppointment"],
    PreferredLocation = "Main Clinic"
};
```

2. **Date Generation**
```csharp
private DateTime GetValidFutureDate()
{
    var date = DateTime.Now.AddDays(1);
    while (!IsWorkingDay(date))
    {
        date = date.AddDays(1);
    }
    return date;
}
```

## Notes

- Tests handle both success and failure scenarios
- Comprehensive validation implemented
- Proper cleanup of test data
- Detailed logging for debugging
- Error scenarios properly documented
