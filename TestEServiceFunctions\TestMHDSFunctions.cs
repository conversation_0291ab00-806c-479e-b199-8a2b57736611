﻿namespace TestEServiceFunctions;

[Collection("MHDSFunctions")]
public class TestMhdsFunctions(ITestOutputHelper testOutputHelper, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteMhdsByReqNumber()
    {
        // Arrange
        var getReqNumber = CreateMhdsRequest("28614415129");
        getReqNumber.AddOrUpdateHeader(JwtClaimsQId, 28614415129);
        var testResponse = await _client.PostAsync<CreateMHDSResponse>(getReqNumber);
        var reqNumber = testResponse?.ReqNumber;
        var request = GetRestRequest("mhds/{reqNumber}");
        request.AddUrlSegment("reqNumber", reqNumber!);
        request.AddOrUpdateHeader(JwtClaimsQId, 28614415129);

        // Act
        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response is not null);
        True(response.IsSuccessStatusCode);
        True(response.StatusCode == NoContent);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestDeleteMhdsByReqNumberWithInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("mhds/{reqNumber}");
        request.AddUrlSegment("reqNumber", "InvalidReqNumber");
        request.Method = Method.Delete;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response is not null);
        Equal(BadRequest, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetMhdsItemByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("mhds/{reqNumber}");
        request.AddUrlSegment("reqNumber", "MZ6NXCTNS4W");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

        // Act
        var response = await _client.GetAsync<GetMHDSItemResponse>(request);
        testOutputHelper.LogToConsole(response);
        response?.ReqNumber.Should().NotBeNull();

        // Assert
        True(response?.ReqNumber == "MZ6NXCTNS4W");
        True(response.ReqNumber is not null);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetMhdsItemByReqNumberWithInvalidReqNumber()
    {
        // Arrange
        var reqNumber = "InvalidReqNumber";
        var request = GetRestRequest("mhds/{reqNumber}");
        request.AddUrlSegment("reqNumber", reqNumber);

        // Act
        var response = await _client.GetAsync<GetMHDSItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response is null);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetMhdsListByQId()
    {
        // Arrange
        const long qId = 36298929958;
        var request = GetRestRequest("mhds/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, qId);
        request.AddUrlSegment("qId", qId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", "10");

        // Act
        var response = await _client.GetAsync<List<MHDSRequestList>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response is not null);
        True(response.Count > 0);
        True(response[0].ReqNumber is not null);
        response.ForEach(x => NotEmpty(x.ReqNumber));
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetMhdsListByQIdWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("mhds/submitter-qid/{qId}");
        request.AddUrlSegment("qId", 0);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", "10");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(Unauthorized, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetMhdsListByQIdWithInvalidStatus()
    {
        // Arrange
        var request = GetRestRequest("mhds/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, 36298929958);
        request.AddUrlSegment("qId", 36298929958);
        request.AddQueryParameter("status", "InvalidStatus");
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", "10");

        // Act
        var response = await _client.GetAsync<List<MHDSRequestList>>(request);
        testOutputHelper.LogToConsole(response);

        True(response is null);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetMhdsListByQIdWithInvalidSkipAndTakeQueryParameters()
    {
        // Arrange
        var request = GetRestRequest("mhds/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, 36298929958);
        request.AddUrlSegment("qId", 36298929958);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("skip", null);
        request.AddQueryParameter("take", null);

        // Act
        var response = await _client.GetAsync<List<MHDSRequestList>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response is not null);
        True(response.Count > 0);
        True(response[0].ReqNumber is not null);
        response.ForEach(x => NotEmpty(x.ReqNumber));
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateMhdsByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("mhds/{reqNumber}");
        request.AddUrlSegment("reqNumber", "GCGHP71DQGF");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateMHDSByReqNumber");

        var updateMhdsRequest = new Faker<UpdateMHDSRequest>()
            .RuleFor(x => x.QId, RandomNumber(11))
            .RuleFor(x => x.SubmitterQId, 71215041525)
            .RuleFor(x => x.IsGisAddressManualyEntered, true)
            .RuleFor(x => x.UNo, f => f.Random.Int(10000, 20000))
            .RuleFor(x => x.BNo, f => f.Random.Int(1000, 2000))
            .RuleFor(x => x.SNo, f => f.Random.Int(1, 9))
            .RuleFor(x => x.ZNo, f => f.Random.Int(10000, 99999))
            .Generate();

        request.AddJsonBody(updateMhdsRequest);
        request.AddOrUpdateHeader(JwtClaimsQId, 71215041525);

        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response is not null);
        True(response.IsSuccessStatusCode);
        True(response.StatusCode == OK);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestUpdateMhdsByReqNumberWithInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("mhds/{reqNumber}");
        request.AddUrlSegment("reqNumber", "InvalidReqNumber");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateMHDSByReqNumber");
        request.AddOrUpdateHeader(JwtClaimsQId, 71215041525);
        request.Method = Method.Put;
        
        var updateMhdsRequest = new Faker<UpdateMHDSRequest>()
            .RuleFor(x => x.QId, RandomNumber(11))
            .RuleFor(x => x.SubmitterQId, 71215041525)
            .RuleFor(x => x.IsGisAddressManualyEntered, true)
            .RuleFor(x => x.UNo, f => f.Random.Int(10000, 20000))
            .RuleFor(x => x.BNo, f => f.Random.Int(1000, 2000))
            .RuleFor(x => x.SNo, f => f.Random.Int(1, 9))
            .RuleFor(x => x.ZNo, f => f.Random.Int(10000, 99999))
            .Generate();

        request.AddJsonBody(updateMhdsRequest);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(BadRequest,  response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetMhdsStatsByQId()
    {
        // Arrange
        var request = GetRestRequest($"mhds/submitter-qid/{22222222270}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

        // Act
        var response = await _client.GetAsync<GetMHDSStatsResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.Should().NotBeNull();

        // Assert
        True(response is not null);
        True(response.Count > 0);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetMhdsStatsByQIdWithInvalidQId()
    {
        // Arrange
        var qId = 343434224343;
        var request = GetRestRequest($"mhds/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, qId);

        // Act
        var response = await _client.GetAsync<GetMHDSStatsResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response?.Count == 0);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessMhdsByQId()
    {
        // Arrange
        var request = GetRestRequest($"mhds/{22222222270}/inprocess-validation");

        // Act
        var response = await _client.GetAsync<CheckInProcessMHDSResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.Should().NotBeNull();

        // Assert
        True(response is not null);
        True(response.IsInProcessExist);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProcessMhdsByQIdWithInvalidQId()
    {
        // Arrange
        const long qId = 343434224343;
        var request = GetRestRequest($"mhds/{qId}/inprocess-validation");

        // Act
        var response = await _client.GetAsync<CheckInProcessMHDSResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        False(response?.IsInProcessExist);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestingGetMhdsListByHcNumber()
    {
        // Arrange
        var hcNumberList = new RequestMHDSMedicineList
        {
            HealthCardNumbers = ["**********", "**********", "**********"]
        };
        var request = GetRestRequest("mhds/medicinelist");
        request.AddJsonBody(hcNumberList);

        // Act
        var response = await _client.PostAsync<List<GetMedicineList>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response is not null);
        True(response.Count > 0);
        True(response[0].MedicineInformation?.Count > 0);
        response.ForEach(x => x.MedicineInformation?.ForEach(q => NotEmpty(q.MedicineName!)));
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestingGetMhdsListByHcNumberWithInvalidHcNumber()
    {
        // Arrange
        var hcNumberList = new RequestMHDSMedicineList
        {
            HealthCardNumbers = ["HC0228861", "HC0222274", "HC0228688"]
        };
        var request = GetRestRequest("mhds/medicinelist");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);
        request.AddJsonBody(hcNumberList);

        // Act
        var response = await _client.PostAsync<List<GetMedicineList>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCreateMhdsRequestAsync()
    {
        // Arrange
        var request = CreateMhdsRequest("28614415135");
        request.AddOrUpdateHeader(JwtClaimsQId, 28614415135);

        // Act
        var response = await _client.PostAsync<CreateMHDSResponse>(request) ?? null;
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.ReqNumber.Length > 0);
        
        // Delete the created MHDS request
        var deleteRequest = GetRestRequest("mhds/{reqNumber}");
        deleteRequest.AddUrlSegment("reqNumber", response.ReqNumber);
        deleteRequest.AddOrUpdateHeader(JwtClaimsQId, 28614415135);
        await _client.DeleteAsync(deleteRequest);
        testOutputHelper.LogToConsole("MHDS request deleted successfully");
    }

    private static RestRequest CreateMhdsRequest(string? submittersQId = null)
    {
        var qId = submittersQId is not null ? ToInt64(submittersQId) : RandomNumber(11);

        var mhdsRequest = new Faker<CreateMHDSRequest>()
            .RuleFor(x => x.QId, qId)
            .RuleFor(x => x.FNameEn, FirstNameEnglish)
            .RuleFor(x => x.FNameAr, FirstNameArabic)
            .RuleFor(x => x.MNameEn, MiddleNameEnglish)
            .RuleFor(x => x.MNameAr, MiddleNameArabic)
            .RuleFor(x => x.LNameEn, LastNameEnglish)
            .RuleFor(x => x.LNameAr, LastNameArabic)
            .RuleFor(x => x.HCNumber, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.CurrentAssignedHC, f => f.PickRandom("**********", "**********", "**********"))
            .RuleFor(x => x.Dob, f => f.Date.Past(50))
            .RuleFor(x => x.Nationality, f => f.Random.Int(1, 1).ToString())
            .RuleFor(x => x.UNo, f => f.Random.Int(10000, 20000))
            .RuleFor(x => x.BNo, f => f.Random.Int(1000, 2000))
            .RuleFor(x => x.SNo, f => f.Random.Int(1, 9))
            .RuleFor(x => x.ZNo, f => f.Random.Int(10000, 99999))
            .RuleFor(x => x.IsGisAddressManualyEntered, f => f.Random.Bool())
            .RuleFor(x => x.SubmitterQId, qId)
            .RuleFor(x => x.SubmitterMobile, f => f.Person.Phone)
            .RuleFor(x => x.SubmitterEmail, f => f.Person.Email)
            .RuleFor(x => x.SecondaryMobNo, f => f.Person.Phone)
            .RuleFor(x => x.Consent, f => f.Random.Bool(1))
            .RuleFor(x => x.Action, f => f.Internet.DomainWord())
            .RuleFor(x => x.MedInfo, new Faker<CreateMedicineInformation>()
                .RuleFor(x => x.MedicineName, p => p.Commerce.ProductName())
                .RuleFor(x => x.PrescriptionOrderId, RandomNumber(12).ToString)
                .RuleFor(x => x.PrescribedDate, p => p.Date.Past())
                .RuleFor(x => x.PrescriptionRefillDueDate, p => p.Date.Past())
                .RuleFor(x => x.LastDispenseDate, p => p.Date.Past())
                .RuleFor(x => x.SupplyDuration, p => p.Random.Int(1, 9).ToString())
                .RuleFor(x => x.LastDispensedLocation, p => p.PickRandom("MES", "MKH", "GHR"))
                .RuleFor(x => x.OrderByName, p => p.Person.FullName)
                .RuleFor(x => x.AttachId, NewGuid())
                .Generate(2))
            .Generate();

        var request = GetRestRequest("mhds");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateMHDS");
        request.AddJsonBody(mhdsRequest);
        return request;
    }
}