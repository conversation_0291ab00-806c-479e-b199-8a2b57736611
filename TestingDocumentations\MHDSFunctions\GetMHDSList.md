# Get MHDS List Tests

## Test Cases

### 1. TestGetMhdsListByQId

Tests retrieval of MHDS requests by QID with status filter and pagination.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetMhdsListByQId()
{
    // Arrange
    const long qId = 36298929958;
    var request = GetRestRequest("mhds/submitter-qid/{qId}");
    request.AddOrUpdateHeader(JwtClaimsQId, qId);
    request.AddUrlSegment("qId", qId);
    request.AddQueryParameter("status", InProcess);
    request.AddQueryParameter("skip", Skip);
    request.AddQueryParameter("take", "10");

    // Act
    var response = await _client.GetAsync<List<MHDSRequestList>>(request);

    // Assert
    True(response is not null);
    True(response.Count > 0);
    True(response[0].ReqNumber is not null);
    response.ForEach(x => NotEmpty(x.ReqNumber));
}
```

### 2. TestGetMhdsListByQIdWithInvalidQId

Tests handling of invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetMhdsListByQIdWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("mhds/submitter-qid/{qId}");
    request.AddUrlSegment("qId", 0);
    request.AddQueryParameter("status", InProcess);
    request.AddQueryParameter("skip", Skip);
    request.AddQueryParameter("take", "10");

    // Act
    var response = await _client.ExecuteAsync(request);

    // Assert
    Equal(Unauthorized, response.StatusCode);
}
```

### 3. TestGetMhdsListByQIdWithInvalidStatus

Tests handling of invalid status parameter.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetMhdsListByQIdWithInvalidStatus()
{
    // Arrange
    var request = GetRestRequest("mhds/submitter-qid/{qId}");
    request.AddOrUpdateHeader(JwtClaimsQId, 36298929958);
    request.AddUrlSegment("qId", 36298929958);
    request.AddQueryParameter("status", "InvalidStatus");
    request.AddQueryParameter("skip", Skip);
    request.AddQueryParameter("take", "10");

    // Act
    var response = await _client.GetAsync<List<MHDSRequestList>>(request);

    // Assert
    True(response is null);
}
```

## Request Details

### Endpoint
```
GET mhds/submitter-qid/{qId}
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `qId`: Submitter QID

### Query Parameters
- `status`: Request status (e.g., "InProcess")
- `skip`: Pagination offset
- `take`: Number of records to return

## Response Model

### MHDSRequestList
```csharp
public class MHDSRequestList
{
    public string ReqNumber { get; set; }
    // Other request details
}
```

## Test Data

### Success Case
- QID: 36298929958
- Status: InProcess
- Pagination: skip=0, take=10

### Error Cases
1. Invalid QID: 0
2. Invalid Status: "InvalidStatus"
3. Missing pagination parameters

## Validation Rules

### Request Validation
1. Valid QID format
2. Valid status value
3. Valid pagination parameters

### Response Validation
1. Success case:
   - Response not null
   - Contains requests
   - Valid request numbers

2. Error cases:
   - Unauthorized for invalid QID
   - Null response for invalid status

## Error Cases

1. **Invalid QID**
   - Zero or negative QID
   - Non-numeric QID
   - Unauthorized access

2. **Invalid Status**
   - Unknown status value
   - Case sensitivity
   - Empty status

3. **Pagination Errors**
   - Invalid skip value
   - Invalid take value
   - Missing parameters

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Pagination**
   - Default values if not specified
   - Maximum take limit
   - Skip validation

3. **Performance**
   - Filtered queries
   - Limited result set
   - Efficient data loading
