# Get Transfer Stats By QID

## Overview
Retrieves statistical information about transfer requests for a given submitter's QID.

## Endpoint
```http
GET /transfers/submitter-qid/{qid}/stats
```

## Authorization
- Function-level authorization
- QID-based access control
- Requires valid submitter QID

## Parameters

### Path Parameters
- `qId` (long, required): Submitter's QID

### Query Parameters
- `status` (string, optional): Filter by status category
  - InProcess: Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested
  - Archived: Approved, Cancelled, CancelledByEServ

## Response

### Success Response (200 OK)
Returns the count of transfer requests matching the criteria:
```json
{
  "count": "integer"
}
```

### No Content Response (204)
Returned when no records are found for the given QID.

### Error Responses
- 400 Bad Request: Invalid parameters
- 401 Unauthorized: Invalid QID or unauthorized access

## Implementation Details
- Uses Entity Framework Core with AsNoTracking for optimal performance
- Implements efficient counting mechanism
- Supports status-based filtering
- Provides real-time statistics
- Validates data existence before filtering

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control based on submitter QID

## Performance Optimization
- Uses AsNoTracking for read-only operations
- Implements efficient counting query
- Optimizes database queries using proper joins
- Efficient status-based filtering
- Early validation of data existence
