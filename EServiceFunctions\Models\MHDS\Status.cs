﻿namespace EServiceFunctions.Models.MHDS;

[ExcludeFromCodeCoverage]
public class Status
{
    public Status()
    {
        MHDSRequestDetails = new HashSet<MHDSRequestDetails>();
    }

    public string? Code { get; set; }
    public string? DescriptionEn { get; set; }
    public string? DescriptionAr { get; set; }
    public string? Category { get; set; }
    public string? NrkCategory { get; set; }
    public virtual ICollection<MHDSRequestDetails> MHDSRequestDetails { get; set; }
}