# GetEServRequestedAppointmentByReqNumber

## Overview
Retrieves a specific appointment request by its request number.

## Endpoint
- **Route**: `appointments/{reqNumber}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `reqNumber` (path, required): Appointment request number
- `EligibleClinicCodes` (header, required): List of eligible clinic codes

## Responses
- **200 OK**: Returns `GetAppointmentResponse`
  ```json
  {
    "ReqNumber": "string",
    "QId": "string",
    "Status": "string",
    "RequestType": "string",
    "Clinic": "string",
    "PrefDate": "date",
    "SubmittedAt": "datetime",
    ...
  }
  ```
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication failed
- **404 Not Found**: Appointment not found
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates request number format
2. Checks if appointment exists and belongs to eligible clinics
3. Maps appointment data to response model
4. Returns detailed appointment information

## Dependencies
- AppointmentContext
- Status entity
