﻿using EServiceFunctions.Models.GeneralWorks;
using EServiceFunctions.RequestResponseModels.GeneralWorks;

namespace EServiceFunctions.MapperModels;

public static class CovidVaccinationMapper
{
    public static CovidVaccination MapCovidVaccinationRequestToCovidVaccination(CovidVaccinationRequest covidVaccinationRequest)
    {
        var response = new CovidVaccination
        {
            IDType = covidVaccinationRequest.IDType,
            IDNumber = covidVaccinationRequest.IDNumber,
            FullNameEn = covidVaccinationRequest.FullNameEn,
            FullNameAr = covidVaccinationRequest.FullNameAr,
            MobileNumber = covidVaccinationRequest.MobileNumber,
            DOB = covidVaccinationRequest.DOB,
            Gender = covidVaccinationRequest.Gender,
            Nationality = covidVaccinationRequest.Nationality,
            Email = covidVaccinationRequest.Email               
        };
        return response;
    }
}