# Delete Transfer Tests

## Test Cases

### 1. TestDeleteTransferByReqNumber

Tests successful deletion of transfer by request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteTransferByReqNumber()
{
    // Arrange
    var reqNumber = await TestCreateTransfer();
    reqNumber.ThrowIfNull();
    await WaitAsync();
    var request = GetRestRequest("transfers/{reqNumber}");
    request.AddUrlSegment("reqNumber", reqNumber);
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.DeleteAsync(request);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.StatusCode == OK);
}
```

### 2. TestDeleteTransferByInvalidReqNumber

Tests deletion with invalid request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestDeleteTransferByInvalidReqNumber()
{
    // Arrange
    var request = GetRestRequest("transfers/{reqNumber}");
    request.AddUrlSegment("reqNumber", "InvalidReqNumber");
    request.Method = Method.Delete;
    
    // Act
    var response = await _client.ExecuteAsync(request);
    response.ThrowIfNull();

    // Assert
    True(response.StatusCode == BadRequest);
}
```

## Request Details

### Endpoint
```
DELETE transfers/{reqNumber}
```

### Headers
- `JwtClaimsQId`: User QID

### URL Parameters
- `reqNumber`: Transfer request number to delete

## Test Flow

1. **Create Transfer**
   - Creates new transfer
   - Gets request number
   - Waits for processing

2. **Delete Transfer**
   - Sends delete request
   - Validates response

## Response Validation

### Success Case
- Status Code: OK (200)
- Response not null

### Error Case
- Status Code: BadRequest (400)
- Response not null

## Test Data

### Success Case
1. **Create Request**
   - Generated test data
   - Valid transfer details

2. **Delete Request**
   - Valid request number
   - Proper authorization

### Error Case
- Request Number: "InvalidReqNumber"
- Expected: BadRequest status code

## Validation Rules

### Pre-deletion Checks
1. Request number exists
2. Request number not null
3. Wait period completed

### Success Case Validation
1. Response not null
2. Status code is OK

### Error Case Validation
1. Response not null
2. Status code is BadRequest

## Error Cases

1. **Invalid Request Number**
   - Non-existent request
   - Malformed number
   - Invalid format

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID
   - Unauthorized deletion

3. **System Errors**
   - Database errors
   - Service unavailable
   - Deletion conflicts

## Notes

1. **Test Flow**
   - Create then delete pattern
   - Asynchronous operations
   - Status verification

2. **Performance**
   - Wait time required
   - Deletion verification
   - Resource cleanup

3. **Error Handling**
   - Invalid request validation
   - Authorization checks
   - System error handling

4. **Special Considerations**
   - Request number validation
   - Authorization required
   - Wait period important
