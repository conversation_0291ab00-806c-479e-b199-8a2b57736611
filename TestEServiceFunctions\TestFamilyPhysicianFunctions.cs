﻿namespace TestEServiceFunctions;

[ExcludeFromCodeCoverage]
[Collection("FamilyPhysicianFunctions")]
public class TestFamilyPhysicianFunctions(IRestLibrary restLibrary, ITestOutputHelper testOutputHelper)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetFamilyPhysicianByPhysicianCode()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians/{phyCode}");
        request.AddUrlSegment("phyCode", "52332");

        // Act
        var response = await _client.GetAsync<GetFamilyPhysicianItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal("52332", response.PhyCode);
        Equal("Consultant", response.ClinicalTitleEn);
    }
    
    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetFamilyPhysicianByPhysicianCode_Should_Return_GetFamilyPhysicianItemResponse()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians/{phyCode}");
        request.AddUrlSegment("phyCode", "58707");

        // Act
        var response = await _client.GetAsync<GetFamilyPhysicianItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal("58707", response.PhyCode);
        Equal("Consultant", response.ClinicalTitleEn);
        Equal("استشاري", response.ClinicalTitleAr);
        Equal("Consultant Family Medicine", response.Title);
        Equal("د. محمد عبدالسلام محمد سليم", response.PhyNameAr);
        Equal("Dr. Mohammed Abdelsalam Mohammed Saliem", response.PhyNameEn);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetFamilyPhysicianByPhysicianCodeNotFound()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians/{phyCode}");
        request.AddUrlSegment("phyCode", "5233311");

        // Act
        var response = await _client.GetAsync<GetFamilyPhysicianItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetSpecialityList()
    {
        // Arrange
        var request = GetRestRequest("specialities");

        // Act
        var response = await _client.GetAsync<List<GetSpecialityResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();
        response = response.OrderBy(x => x.SpltyCode).ToList();

        // Assert
        NotNull(response);
        True(response.Count > 0);
        Equal("1  ", response[0].SpltyCode);
        Equal("Pediatrics", response[0].SpltyNameEn);
        Equal("طب الأطفال", response[0].SpltyNameAr);
        Equal("2  ", response[1].SpltyCode);
        Equal("Antenatal/ Postnatal", response[1].SpltyNameEn);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetFamilyPhysicianList()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians");
        request.AddQueryParameter("hcCode", "MES");
        request.AddQueryParameter("gender", 1);
        request.AddQueryParameter("language", "EN");
        request.AddQueryParameter("speciality", 7);
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", 10);

        // Act
        var response = await _client.GetAsync<List<GetFamilyPhysicianListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count > 1);
        Equal("52329", response[0].PhyCode);
        Equal("Senior Consultant Family Medicine", response[0].Title);
        Equal("Consultant", response[0].ClinicalTitleEn);
        Equal(" MRCGP - Family Medicine", response[0].Qualification);
        Equal("EN,HI,GU,MR,AR", response[0].Language);
        Equal("2,7,1,3", response[0].SpltyCode);
        Equal("1", response[0].Gender);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetFamilyPhysicianListWithoutSpecialityCode()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians");
        request.AddQueryParameter("hcCode", "MES");
        request.AddQueryParameter("gender", 1);
        request.AddQueryParameter("language", "EN");
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", Take);

        // Act
        var response = await _client.GetAsync<List<GetFamilyPhysicianListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count > 1);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetFamilyPhysicianListOnlyWithHcCode()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians");
        request.AddQueryParameter("hcCode", "MES");

        // Act
        var response = await _client.GetAsync<List<GetFamilyPhysicianListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count > 1);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetFamilyPhysicianListOnlyWithHcCodeTakeOne()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians");
        request.AddQueryParameter("hcCode", "MES");
        request.AddQueryParameter("take", 1);

        // Act
        var response = await _client.GetAsync<List<GetFamilyPhysicianListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count == 1);
    }


    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetFamilyPhysicianListWithInvalidQueryParameters()
    {
        // Arrange
        var request = GetRestRequest("familyphysicians");
        request.AddQueryParameter("hcCode", "MESS");
        request.AddQueryParameter("gender", "1");
        request.AddQueryParameter("language", "E");
        request.AddQueryParameter("speciality", "1,2,3,4");
        
        // Act
        var response = await _client.GetAsync<List<GetFamilyPhysicianListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }
}