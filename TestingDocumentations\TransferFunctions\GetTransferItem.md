# Get Transfer Item Tests

## Test Cases

### 1. TestGetTransferItemByReqNumber

Tests retrieval of transfer item by request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetTransferItemByReqNumber()
{
    // Arrange
    var request = GetRestRequest("transfers/{reqNumber}");
    request.AddUrlSegment("reqNumber", "XBM423S5S9A");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);

    // Act
    var response = await _client.GetAsync<GetTransferItemResponse>(request);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.QId == 36210462220);
    True(response.ReqNumber == "XBM423S5S9A");
}
```

### 2. TestGetTransferItemByInvalidReqNumber

Tests item retrieval with invalid request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetTransferItemByInvalidReqNumber()
{
    // Arrange 
    var request = GetRestRequest("transfers/{reqNumber}");
    request.Method = Method.Get;
    request.AddUrlSegment("reqNumber", "SV1ZCMN9RX2");
    
    // Act
    var response = await _client.ExecuteAsync(request);
    
    // Assert
    True(response.StatusCode == NoContent);
}
```

## Request Details

### Endpoint
```
GET transfers/{reqNumber}
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `reqNumber`: Transfer request number

## Response Model

### GetTransferItemResponse
```csharp
public class GetTransferItemResponse
{
    public string ReqNumber { get; set; }
    public long QId { get; set; }
    // Other transfer details
}
```

## Test Data

### Success Case
- Request Number: "XBM423S5S9A"
- JWT Claims QID: 22222222272
- Expected:
  * QId: 36210462220
  * ReqNumber: "XBM423S5S9A"

### Error Case
- Request Number: "SV1ZCMN9RX2"
- Expected: NoContent status code

## Validation Rules

### Success Case Validation
1. Response not null
2. QID matches expected value
3. Request number matches

### Error Case Validation
1. Status code is NoContent

## Error Cases

1. **Invalid Request Number**
   - Non-existent request
   - Malformed number
   - Invalid format

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - Unauthorized access

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID verification

2. **Response Format**
   - Complete transfer details
   - Request number validation
   - QID verification

3. **Performance**
   - Single record retrieval
   - Efficient query
   - Quick response time
