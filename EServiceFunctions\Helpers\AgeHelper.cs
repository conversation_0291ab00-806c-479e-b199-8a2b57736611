﻿namespace EServiceFunctions.Helpers;

[ExcludeFromCodeCoverage]
public static class AgeHelper
{
    public static bool IsAdult(DateTime dateOfBirth) => CalculateAge(dateOfBirth) >= 18;

    public static bool IsMinor(DateTime dateOfBirth) => !IsAdult(dateOfBirth);

    public static int CalculateAge(DateTime dateOfBirth)
    {
        var age = GetCurrentTime().Year - dateOfBirth.Year;
        if (GetCurrentTime().Month < dateOfBirth.Month
            || (GetCurrentTime().Month == dateOfBirth.Month
                && GetCurrentTime().Day < dateOfBirth.Day)) age--;
        return age;
    }
}