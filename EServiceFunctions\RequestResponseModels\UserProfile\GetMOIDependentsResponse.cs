﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.RequestResponseModels.UserProfile;

[ExcludeFromCodeCoverage]
public class GetMoiDependentsResponse
{
    [Key]
    [Column("DependentQID")]
    public long QId { get; set; }
    [Column("fNameAr")]
    public string? FNameAr { get; set; }
    [Column("mNameAr")]
    public string? MNameAr { get; set; }
    [Column("lNameAr")]
    public string? LNameAr { get; set; }
    [Column("fNameEn")]
    public string? FNameEn { get; set; }
    [Column("mNameEn")]
    public string? MNameEn { get; set; }
    [Column("lNameEn")]
    public string? LNameEn { get; set; }
    [NotMapped]
    public bool? Linked { get; set; }              
}