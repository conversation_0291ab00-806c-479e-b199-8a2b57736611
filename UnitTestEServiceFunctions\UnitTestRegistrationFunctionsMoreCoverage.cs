using EServiceFunctions.Models.Registration;
using EServiceFunctions.RequestResponseModels.Registration;
using static TestDoublesForUnitTest.MockHelpers;
using static MockDataLibrary.GenerateMoqData;

namespace UnitTestEServiceFunctions;

// Additional test methods for RegistrationFunctions to achieve 100% code coverage
public class UnitTestRegistrationFunctionsMoreCoverage(ITestOutputHelper output)
{
    private readonly ILogger<RegistrationFunctions> _logger = Mock.Of<ILogger<RegistrationFunctions>>();
        
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Return_BadRequest_For_Missing_RequestOrigin_Header()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        // Create request without X-RequestOrigin header
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Handle_DbUpdateException()
    {
        // Arrange
        // Create a mock context that will throw an exception when SaveChanges is called
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbCreate")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);
        mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Test exception", new Exception("Inner exception")));

        var mockDbSet = new Mock<DbSet<Registration>>();
        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var request = new CreateUpdateRegistrationRequest
        {
            QId =LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Handle_SaveChangesAsync_Returning_Zero()
    {
        // Arrange
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ZeroResultDb")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);
        mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(0); 

        var mockDbSet = new Mock<DbSet<Registration>>();
        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Return_BadRequest_For_Missing_RequestOrigin_Header()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0001";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        // Create request without X-RequestOrigin header
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Put);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Handle_DbUpdateException()
    {
        // Arrange
        // Create a mock context that will throw an exception when SaveChanges is called
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbUpdate")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);
        mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Test exception", new Exception("Inner exception")));

        // Setup a registration that will be found
        var registration = new Registration
        {
            Id = 101,
            ReqNumber = "REQ0004",
            Status = "Saved",
            SubmitterQId = LongQId
        };

        var mockRegistrations = new List<Registration> { registration }.AsQueryable();
        var mockDbSet = new Mock<DbSet<Registration>>();
        mockDbSet.As<IQueryable<Registration>>().Setup(m => m.Provider).Returns(mockRegistrations.Provider);
        mockDbSet.As<IQueryable<Registration>>().Setup(m => m.Expression).Returns(mockRegistrations.Expression);
        mockDbSet.As<IQueryable<Registration>>().Setup(m => m.ElementType).Returns(mockRegistrations.ElementType);
        using var enumerator = mockRegistrations.GetEnumerator();
        mockDbSet.As<IQueryable<Registration>>().Setup(m => m.GetEnumerator()).Returns(enumerator);

        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);
        // Create a queryable mock that will return our registration
        var mockQueryable = new Mock<IQueryable<Registration>>();
        mockQueryable.Setup(m => m.Provider).Returns(mockRegistrations.Provider);
        mockQueryable.Setup(m => m.Expression).Returns(mockRegistrations.Expression);
        mockQueryable.Setup(m => m.ElementType).Returns(mockRegistrations.ElementType);
        using var value = mockRegistrations.GetEnumerator();
        mockQueryable.Setup(m => m.GetEnumerator()).Returns(value);
            
        // Setup the context to throw exception on SaveChanges but return the registration when finding it
        mockContext.Setup(m => m.Set<Registration>()).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        const string reqNumber = "REQ0004";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Put);
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Handle_Exception()
    {
        // Arrange
        // Create a mock context that will throw an exception
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbList")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);

        // Setup to throw exception when querying
        var mockDbSet = new Mock<DbSet<Registration>>();
        mockDbSet.As<IQueryable<Registration>>().Setup(m => m.Provider).Throws(new Exception("Test exception"));

        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var mockHttpRequestData = CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationStatsByQId_Should_Handle_Exception()
    {
        // Arrange
        // Create a mock context that will throw an exception
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbStats")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);

        // Setup to throw exception when querying
        var mockDbSet = new Mock<DbSet<Registration>>();
        mockDbSet.As<IQueryable<Registration>>().Setup(m => m.Provider).Throws(new Exception("Test exception"));

        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var mockHttpRequestData = CreateHttpRequestData(query: "?status=InProcess");
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessRegistrationByQId_Should_Handle_Exception()
    {
        // Arrange
        // Create a mock context that will throw an exception
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbCheck")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);

        // Setup to throw exception when querying
        var mockDbSet = new Mock<DbSet<Registration>>();
        mockDbSet.As<IQueryable<Registration>>().Setup(m => m.Provider).Throws(new Exception("Test exception"));

        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var mockHttpRequestData = CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.CheckInProcessRegistrationByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationItemByReqNumber_Should_Handle_Exception()
    {
        // Arrange
        // Create a mock context that will throw an exception
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbItem")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);

        // Setup to throw exception when querying
        var mockDbSet = new Mock<DbSet<Registration>>();
        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);
            
        // Setup the DbSet to throw an exception when accessed
        mockDbSet.Setup(m => m.AsQueryable()).Throws(new Exception("Test exception"));

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var mockHttpRequestData = CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationItemByReqNumber(mockHttpRequestData, "REQ0001");
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }
}