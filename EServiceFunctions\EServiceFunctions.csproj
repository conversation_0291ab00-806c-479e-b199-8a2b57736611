<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <AzureFunctionsVersion>v4</AzureFunctionsVersion>
        <LangVersion>12</LangVersion>
        <OutputType>Exe</OutputType>
        <Nullable>enable</Nullable>
        <UserSecretsId>b47762cd-6971-43d9-9311-f39c53106cdc</UserSecretsId>
        <_FunctionsSkipCleanOutput>true</_FunctionsSkipCleanOutput>
        <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
        <DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
        <AssemblyVersion>1.1.0</AssemblyVersion>
        <FileVersion>1.1.0</FileVersion>
    </PropertyGroup>
    <PropertyGroup>
        <EnableNETAnalyzers>true</EnableNETAnalyzers>
        <AnalysisLevel>latest</AnalysisLevel>
        <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.OpenApi" Version="1.5.1"/>
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.5" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
        <PackageReference Include="Azure.Identity" Version="1.14.1" />
        <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.23.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.17" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.8.0" />
        <PackageReference Include="Bogus" Version="35.6.3" />
        <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="8.2.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.6" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
        <PackageReference Include="Microsoft.FeatureManagement" Version="4.1.0" />
        <PackageReference Include="Throw" Version="1.4.0" />
    </ItemGroup>
    <ItemGroup>
        <None Update="host.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="local.settings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\MockDataLibrary\MockDataLibrary.csproj" />
    </ItemGroup>
    <ItemGroup>
        <Folder Include="MapperModels\" />
        <Folder Include="Models\FamilyPhysician"/>
    </ItemGroup>
</Project>
