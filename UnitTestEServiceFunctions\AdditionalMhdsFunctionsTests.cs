using EServiceFunctions.Models.MHDS;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.MHDS;
using Microsoft.Azure.Functions.Worker.Http;
using static System.Text.Json.JsonSerializer;

namespace UnitTestEServiceFunctions;

[ExcludeFromCodeCoverage]
public class AdditionalMhdsFunctionsTests(ITestOutputHelper outputHelper)
{
    private readonly ILogger<MhdsFunctions> _logger = Mock.Of<ILogger<MhdsFunctions>>();

    private string LogResponse(ITestOutputHelper outputHelper, HttpResponseData response)
    {
        var result = response as MockHttpResponseData;
        var responseObject = result?.ReadBodyToEnd();
        outputHelper.WriteLine(responseObject);
        return responseObject ?? string.Empty;
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSStatsByQID_Should_Return_Unauthorized_When_QId_Not_Found()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsStatsByQId(request, LongQId + 2); // Use a QID that doesn't exist in the mock data
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSStatsByQID_Should_Return_Ok_With_Status_Filter()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData(query: "?status=InProcess");

        // Act
        var response = await mhdsFunctions.GetMhdsStatsByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseContent = DeserializeObject<GetMHDSStatsResponse>(responseObject);
        NotNull(responseContent);
        True(responseContent.Count > 0);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSStatsByQID_Should_Handle_Exception()
    {
        // Arrange
        var mockEdwDbContext = new Mock<IDbContextFactory<EDWDbContext>>();
        mockEdwDbContext.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var mockMhdsContext = new Mock<IDbContextFactory<MHDSContext>>();
        mockMhdsContext.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var mhdsFunctions = new MhdsFunctions(mockMhdsContext.Object, mockEdwDbContext.Object, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsStatsByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByHCNumber_Should_Return_NoContent_When_HealthCardNumbers_Is_Empty()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var requestBody = new RequestMHDSMedicineList
        {
            HealthCardNumbers = [] // Empty list
        };
        var request = MockHelpers.CreateHttpRequestData(payload: Serialize(requestBody));

        // Act
        var response = await mhdsFunctions.GetMhdsListByHcNumber(request);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }

    [Fact] // edge case
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByHCNumber_Should_Handle_Exception_In_EDW_Context()
    {
        // Arrange
        var mockEdwDbContext = new Mock<IDbContextFactory<EDWDbContext>>();
        mockEdwDbContext.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext.Object, _logger);
        var requestBody = new RequestMHDSMedicineList
        {
            HealthCardNumbers = [HcNumber1]
        };
        var request = MockHelpers.CreateHttpRequestData(payload: Serialize(requestBody));

        // Act
        var response = await mhdsFunctions.GetMhdsListByHcNumber(request);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_CreateMHDS_Should_Return_BadRequest_When_Missing_RequestOrigin_Header()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var requestBody = new CreateMHDSRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId
        };

        // Create request without X-RequestOrigin header
        var request = MockHelpers.CreateHttpRequestData(payload: Serialize(requestBody));
        request.Headers.Remove(RequestOrigin);

        // Act
        var response = await mhdsFunctions.CreateMhdsRequestAsync(request);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // edge case
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByQID_Should_Return_Unauthorized_When_QId_Not_Found()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsListByQId(request, LongQId + 2); // Use a QID that doesn't exist in the mock data
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByQID_Should_Return_Ok_With_Status_Filter()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData(query: "?status=InProcess");

        // Act
        var response = await mhdsFunctions.GetMhdsListByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseContent = DeserializeObject<List<MHDSRequestList>>(responseObject);
        NotNull(responseContent);
        True(responseContent.Count > 0);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSItemByReqNumber_Should_Return_BadRequest_When_ReqNumber_Is_Empty()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsItemByReqNumber(request, string.Empty);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteMHDSByReqNumber_Should_Return_BadRequest_When_Request_Not_Found()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds($"EDWDbContextDb_{Guid.NewGuid()}");
        var mockMhdsContext = new TestMhdsContext($"MHDSContextDb_{Guid.NewGuid()}");
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.DeleteMhdsByReqNumber(request, "NONEXISTENT");
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }
}
