﻿namespace EServiceFunctions.Models.Appointment;

public class Shift
{
    public Shift()
    {
        AppointmentAmPm = new HashSet<Appointment>();

        AppointmentPrefContactTime = new HashSet<Appointment>();
    }

    public string? Code { get; set; }
    public string? DescriptionEn { get; set; }
    public string? DescriptionAr { get; set; }

    public virtual ICollection<Appointment> AppointmentAmPm { get; set; }

    public virtual ICollection<Appointment> AppointmentPrefContactTime { get; set; }
}