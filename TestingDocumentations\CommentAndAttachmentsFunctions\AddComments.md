# Add Comments Tests

## Overview

These tests verify the functionality of adding comments to various entities in the system. The test suite covers different comment types, attachment handling, and validation scenarios.

## Test Cases

### TestAddComment_ValidRequest_ReturnsCreated

Tests the successful addition of a comment with valid data.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestAddComment_ValidRequest_ReturnsCreated()
{
    // Arrange
    var request = GetRestRequest("comments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var commentRequest = new AddCommentRequest
    {
        EntityId = "TEST-123",
        Content = "Test comment content",
        Type = "General"
    };
    request.AddJsonBody(commentRequest);

    // Act
    var response = await _client.ExecuteAsync<CommentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(Created, response.StatusCode);
    NotNull(response.Data);
    NotEmpty(response.Data.CommentId);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid entity ID
  - Comment content
  - Comment type
- **Expected Output**: 
  - 201 Created response
  - Comment details
- **Validation Points**:
  - Response success
  - Comment ID generation
  - Content verification

### TestAddComment_WithAttachment_ReturnsCreated

Tests adding a comment with an attached file.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestAddComment_WithAttachment_ReturnsCreated()
{
    // Arrange
    var request = GetRestRequest("comments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    
    // Add comment data
    request.AddParameter("entityId", "TEST-123");
    request.AddParameter("content", "Comment with attachment");
    request.AddParameter("type", "General");
    
    // Add file
    var filePath = CreateTestFile();
    request.AddFile("attachment", filePath);

    // Act
    var response = await _client.ExecuteAsync<CommentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(Created, response.StatusCode);
    NotNull(response.Data);
    NotEmpty(response.Data.Attachments);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Comment data
  - File attachment
- **Expected Output**: 
  - Created response
  - Attachment info
- **Validation Points**:
  - File upload success
  - Attachment linking
  - Content verification

## Request/Response Models

### AddCommentRequest
```csharp
public class AddCommentRequest
{
    public string EntityId { get; set; }
    public string Content { get; set; }
    public string Type { get; set; }
    public List<IFormFile>? Attachments { get; set; }
}
```

### CommentResponse
```csharp
public class CommentResponse
{
    public string CommentId { get; set; }
    public string EntityId { get; set; }
    public string Content { get; set; }
    public string Type { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<AttachmentInfo> Attachments { get; set; }
}
```

## Validation Rules

1. **Content Validation**
   - Not empty
   - Maximum length
   - Valid characters

2. **Attachment Validation**
   - File size limit
   - Allowed types
   - Maximum count

3. **Entity Validation**
   - Valid entity ID
   - User permission
   - Entity status

## Implementation Details

### 1. Comment Creation
```csharp
private async Task<CommentResponse> CreateComment(AddCommentRequest request)
{
    // Validate request
    await ValidateCommentRequest(request);

    // Create comment
    var comment = new Comment
    {
        Id = GenerateCommentId(),
        EntityId = request.EntityId,
        Content = request.Content,
        Type = request.Type,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = GetCurrentUser()
    };

    // Handle attachments
    if (request.Attachments?.Any() == true)
    {
        comment.Attachments = await ProcessAttachments(request.Attachments);
    }

    await _commentRepository.AddAsync(comment);
    return MapToResponse(comment);
}
```

### 2. Attachment Processing
```csharp
private async Task<List<AttachmentInfo>> ProcessAttachments(List<IFormFile> files)
{
    var attachments = new List<AttachmentInfo>();

    foreach (var file in files)
    {
        // Validate file
        await ValidateFile(file);

        // Process and store
        var attachment = await ProcessAndStoreFile(file);
        attachments.Add(attachment);
    }

    return attachments;
}
```

### 3. Validation Implementation
```csharp
private async Task ValidateCommentRequest(AddCommentRequest request)
{
    var errors = new List<string>();

    if (string.IsNullOrEmpty(request.Content))
        errors.Add("Content is required");

    if (request.Content?.Length > MaxContentLength)
        errors.Add($"Content exceeds maximum length of {MaxContentLength}");

    if (!await EntityExists(request.EntityId))
        errors.Add("Invalid entity ID");

    if (errors.Any())
        throw new ValidationException(errors);
}
```

## Error Handling

### 1. Content Errors
```csharp
private void ValidateContent(string content)
{
    if (string.IsNullOrEmpty(content))
        throw new ValidationException("Content cannot be empty");

    if (content.Length > MaxContentLength)
        throw new ValidationException($"Content exceeds maximum length of {MaxContentLength}");

    if (ContainsInvalidCharacters(content))
        throw new ValidationException("Content contains invalid characters");
}
```

### 2. File Errors
```csharp
private async Task ValidateFile(IFormFile file)
{
    if (file.Length > MaxFileSize)
        throw new ValidationException($"File size exceeds limit of {MaxFileSize} bytes");

    if (!AllowedFileTypes.Contains(file.ContentType))
        throw new ValidationException("File type not allowed");

    await ScanFileForViruses(file);
}
```

## Test Helper Methods

### 1. File Creation
```csharp
private string CreateTestFile(int sizeInBytes = 1024)
{
    var path = Path.GetTempFileName();
    using var fs = File.Create(path);
    fs.SetLength(sizeInBytes);
    return path;
}
```

### 2. Test Data Generation
```csharp
private AddCommentRequest CreateValidCommentRequest()
{
    return new AddCommentRequest
    {
        EntityId = "TEST-" + Random.Shared.Next(1000, 9999),
        Content = "Test comment " + Guid.NewGuid(),
        Type = "General"
    };
}
```

## Notes

- Handles both simple and complex comments
- Supports file attachments
- Validates all inputs
- Proper error handling
- Comprehensive logging

## Recommendations

1. **Content Management**
   - Implement content filtering
   - Support rich text
   - Handle special characters

2. **File Handling**
   - Implement virus scanning
   - Support large files
   - Handle multiple uploads

3. **Performance**
   - Optimize file processing
   - Implement caching
   - Handle concurrency
