﻿namespace EServiceFunctions.Models.GeneralWorks;

[ExcludeFromCodeCoverage]
public partial class GeneralWorksContext(DbContextOptions<GeneralWorksContext> options) : DbContext(options)
{
    public virtual DbSet<CovidVaccination>? CovidVaccination { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CovidVaccination>(entity =>
        {
            entity.HasKey(e => e.Id)
                .HasName("PK__CovidVac__3214EC066901FE73")
                .IsClustered(false);

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");

            entity.Property(e => e.CreatedAt).HasColumnType("datetime");

            entity.Property(e => e.DOB).HasColumnType("datetime");

            entity.Property(e => e.Email).HasMaxLength(255);

            entity.Property(e => e.FullNameAr).HasMaxLength(255);

            entity.Property(e => e.FullNameEn).HasMaxLength(255);

            entity.Property(e => e.Gender).HasMaxLength(255);

            entity.Property(e => e.IDNumber).HasMaxLength(255);

            entity.Property(e => e.IDType).HasMaxLength(255);

            entity.Property(e => e.MobileNumber).HasMaxLength(255);

            entity.Property(e => e.Nationality).HasMaxLength(255);

            entity.Property(e => e.SN).HasMaxLength(255);

            entity.Property(e => e.Status).HasMaxLength(255);

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}