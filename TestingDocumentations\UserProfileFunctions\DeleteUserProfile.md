# Delete User Profile Tests

## Test Cases

### 1. TestDeleteUserProfileByqId

Tests successful deletion of user profile.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteUserProfileByqId()
{
    // Arrange
    long qId;
    do
    {
        qId = await TestCreateUserProfile();
    } while (qId == 0);

    var request = GetRestRequest("UserProfiles/{qId}");
    request.AddUrlSegment("qId", qId);
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-DeleteUserProfileByQID");
    request.AddOrUpdateHeader(JwtClaimsQId, qId);

    // Act
    var response = await _client.DeleteAsync(request);
    response.ThrowIfNull();

    // Assert
    True(response.StatusCode == OK);
}
```

### 2. TestDeleteUserProfileByQIdWithInvalidQId

Tests deletion with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestDeleteUserProfileByQIdWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("UserProfiles/{qId}");
    request.AddUrlSegment("qId", 2323232323211);
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-DeleteUserProfileByQID");
    request.Method = Method.Delete;

    // Act
    var response = await _client.ExecuteAsync(request);
    var responseObject = DeserializeObject<ErrorResponse>(response.Content!);

    // Assert
    Equal(BadRequest, response.StatusCode);
    Equal("Invalid QID 2323232323211", responseObject?.Message);
}
```

## Request Details

### Endpoint
```
DELETE UserProfiles/{qId}
```

### Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: "UnitTest-DeleteUserProfileByQID"

### URL Parameters
- `qId`: QID of profile to delete

## Test Flow

1. **Create Profile**
   - Create new profile
   - Get QID
   - Ensure creation success

2. **Delete Profile**
   - Send delete request
   - Validate response

## Response Validation

### Success Case
- Status Code: OK (200)
- Response not null

### Error Case
- Status Code: BadRequest (400)
- Error message matches expected

## Test Data

### Success Case
1. **Create Request**
   - Generated test data
   - Valid profile details

2. **Delete Request**
   - Valid QID
   - Proper authorization

### Error Case
- QID: 2323232323211
- Expected: BadRequest with error message

## Validation Rules

### Pre-deletion Checks
1. Profile exists
2. QID not null
3. Valid authorization

### Success Case Validation
1. Response not null
2. Status code is OK

### Error Case Validation
1. Status code is BadRequest
2. Error message matches expected

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Invalid format

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID
   - Unauthorized deletion

3. **System Errors**
   - Database errors
   - Service unavailable
   - Deletion conflicts

## Notes

1. **Test Flow**
   - Create then delete pattern
   - Asynchronous operations
   - Status verification

2. **Performance**
   - Creation overhead
   - Deletion verification
   - Resource cleanup

3. **Error Handling**
   - Invalid QID validation
   - Authorization checks
   - System error handling

4. **Special Considerations**
   - QID validation
   - Authorization required
   - Resource cleanup
