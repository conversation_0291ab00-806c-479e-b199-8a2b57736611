# Assignment Functions

## Overview
The Assignment API provides comprehensive endpoints for managing healthcare provider assignments, including creating, updating, and retrieving assignment requests. This service supports multilingual content (English/Arabic) and maintains detailed status tracking.

## Endpoints

### 1. Get Assignment List By QID
- **Endpoint**: `GET /assignments/submitter-qid/{qId}`
- **Description**: Get the assignment list for a submitter's QID
- **Authorization**: Function level, requires QID authorization
- **Parameters**:
  - `qId`: Submitter's QID (path, required)
  - `status`: Filter by status (query, optional)
    - InProcess: Submitted, Rework, Reworked
    - Archived: Approved, Cancelled, CancelledByEServ
  - `skip`: Number of records to skip for pagination (query, optional)
  - `take`: Number of records to take per page (query, optional)
- **Response**:
  - `200 OK`: Returns list of GetAssignmentListResponse
    ```json
    [{
      "QId": "long",
      "ReqNumber": "string",
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "Status": "string",
      "StatusDescriptionEn": "string",
      "StatusDescriptionAr": "string",
      "SubmittedAt": "string"
    }]
    ```
  - `204 No Content`: No assignments found
  - `400 Bad Request`: Invalid parameters
  - `401 Unauthorized`: Invalid authorization

### 2. Get Assignment Stats By QID
- **Endpoint**: `GET /assignments/submitter-qid/{qId}/stats`
- **Description**: Get assignment statistics for a submitter's QID
- **Authorization**: Function level, requires QID authorization
- **Parameters**:
  - `qId`: Submitter's QID (path, required)
  - `status`: Filter by status (query, optional)
    - InProcess: Submitted, Rework, Reworked
    - Archived: Approved, Cancelled, CancelledByEServ
- **Response**:
  - `200 OK`: Returns GetAssignmentStatsResponse
    ```json
    {
      "Count": "integer"
    }
    ```
  - `400 Bad Request`: Invalid parameters
  - `401 Unauthorized`: Invalid authorization

### 3. Check In-Process Assignment By QID
- **Endpoint**: `GET /assignments/{qId}/inprocess-validation`
- **Description**: Check for existing in-process assignments for the given requester's QID
- **Authorization**: Function level
- **Parameters**:
  - `qId`: Requester's QID (path, required)
- **Response**:
  - `200 OK`: Returns CheckInProcessAssignmentResponse
    ```json
    {
      "IsInProcessRequestExist": "boolean"
    }
    ```
  - `400 Bad Request`: Invalid QID
  - `401 Unauthorized`: Invalid authorization

### 4. Get Assignment Item By Request Number
- **Endpoint**: `GET /assignments/{reqNumber}`
- **Description**: Get detailed assignment information by request number
- **Authorization**: Function level, requires submitter QID in claims
- **Parameters**:
  - `reqNumber`: Assignment Request Number (path, required)
- **Response**:
  - `200 OK`: Returns GetAssignmentItemResponse
  - `204 No Content`: Assignment not found
  - `400 Bad Request`: Invalid request number
  - `401 Unauthorized`: Invalid authorization

### 5. Create Assignment
- **Endpoint**: `POST /assignments`
- **Description**: Create new assignment request
- **Authorization**: Function level, requires QID authorization
- **Headers**:
  - `X-RequestOrigin`: Required source identifier
- **Request Body**: CreateUpdateAssignmentRequest
  ```json
  {
    "qId": "long",
    "submitterQId": "long",
    "personalDetails": {
      "fNameEn": "string",
      "mNameEn": "string",
      "lNameEn": "string",
      "fNameAr": "string",
      "mNameAr": "string",
      "lNameAr": "string"
    },
    "submitterEmail": "string",
    "submitterMobile": "string",
    "changeReason": "string"
  }
  ```
- **Response**:
  - `201 Created`: Successfully created assignment
  - `400 Bad Request`: Invalid request
  - `401 Unauthorized`: Invalid authorization
  - `500 Internal Server Error`: Server error

### 6. Update Assignment By Request Number
- **Endpoint**: `PUT /assignments/{reqNumber}`
- **Description**: Update existing assignment
- **Authorization**: Function level, requires QID authorization
- **Parameters**:
  - `reqNumber`: Assignment Request Number (path, required)
- **Headers**:
  - `X-RequestOrigin`: Required source identifier
- **Request Body**: Same as Create Assignment
- **Response**:
  - `200 OK`: Successfully updated assignment
  - `400 Bad Request`: Invalid request
  - `401 Unauthorized`: Invalid authorization
  - `500 Internal Server Error`: Server error

### 7. Delete Assignment By Request Number
- **Endpoint**: `DELETE /assignments/{reqNumber}`
- **Description**: Delete assignment (only saved/draft assignments)
- **Authorization**: Function level, requires QID authorization
- **Parameters**:
  - `reqNumber`: Assignment Request Number (path, required)
- **Response**:
  - `200 OK`: Successfully deleted assignment
  - `400 Bad Request`: Invalid request number
  - `401 Unauthorized`: Invalid authorization
  - `500 Internal Server Error`: Server error

## Function Documentation

### Assignment Queries
- [GetAssignmentListByQId](AssignmentFunctions/GetAssignmentListByQId.md)
- [GetAssignmentStatsByQId](AssignmentFunctions/GetAssignmentStatsByQId.md)
- [CheckInProcessAssignmentByQId](AssignmentFunctions/CheckInProcessAssignmentByQId.md)
- [GetAssignmentItemByReqNumber](AssignmentFunctions/GetAssignmentItemByReqNumber.md)

### Assignment Management
- [CreateAssignment](AssignmentFunctions/CreateAssignment.md)
- [UpdateAssignmentByReqNumber](AssignmentFunctions/UpdateAssignmentByReqNumber.md)
- [DeleteAssignmentByReqNumber](AssignmentFunctions/DeleteAssignmentByReqNumber.md)

## Technical Implementation Details

### Database Integration
- Uses `AssignmentContext` for data access
- Implements efficient query patterns with `AsNoTracking()`
- Supports pagination for large result sets
- Optimized database operations

### Security Features
- Function-level authorization
- QID-based access control
- Request origin validation
- Secure data access patterns

### Status Management
- Multiple status categories:
  - InProcess: Submitted, Rework, Reworked
  - Archived: Approved, Cancelled, CancelledByEServ
- Status change validation
- Status tracking with timestamps

### Performance Optimization
- Asynchronous operations
- Efficient query filtering
- Batch processing support
- Smart pagination implementation

### Error Handling
- Comprehensive exception handling
- Detailed error logging
- Standardized error responses
- Custom exception types

### Monitoring and Auditing
- Request tracking
- Status change logging
- Creation/modification timestamps
- Source tracking

### Integration Features
- External system integration
- Environment-specific configuration
- Health card validation
- Multilingual support