# Release of Information Functions API Documentation

## Overview
The Release of Information module provides a comprehensive set of Azure Functions for managing release requests in the EServices system. This module handles the creation, retrieval, updating, and deletion of release information requests with proper authorization and multilingual support.

## API Endpoints

### Get Release List By QID
- **Endpoint**: `GET /releases/submitter-qid/{qId}`
- **Description**: Retrieves a list of release requests for a specific submitter
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): Submitter's QID (long)
  - `status` (query, optional): Filter by status category
    - InProcess: Submitted, ConditionallyApproved, ResubmitOriginalsRequested
    - Archived: Approved, Cancelled, CancelledByEServ, Rejected
  - `skip` (query, optional): Number of records to skip
  - `take` (query, optional): Number of records to take
- **Response Format**:
  ```json
  [
    {
      "QId": "number",
      "ReqNumber": "string",
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "Status": "string",
      "StatusDescriptionEn": "string",
      "StatusDescriptionAr": "string",
      "SubmittedAt": "string"
    }
  ]
  ```
- **Responses**:
  - 200: Success (List<GetReleaseListResponse>)
  - 204: No Content
  - 400: Bad Request
  - 401: Unauthorized

### Get Release Stats By QID
- **Endpoint**: `GET /releases/submitter-qid/{qId}/stats`
- **Description**: Retrieves statistics about release requests for a specific submitter
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): Submitter's QID (long)
  - `status` (query, optional): Filter by status category
- **Response Format**:
  ```json
  {
    "Count": "number"
  }
  ```
- **Responses**:
  - 200: Success (GetReleaseStatsResponse)
  - 400: Bad Request
  - 401: Unauthorized

### Check InProcess Release By QID
- **Endpoint**: `GET /releases/{qId}/inprocess-validation`
- **Description**: Checks if there are any in-process release requests for a specific QID
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): Requester's QID (long)
- **Response Format**:
  ```json
  {
    "IsInprocessExist": "boolean"
  }
  ```
- **Responses**:
  - 200: Success (CheckInProcessReleaseResponse)
  - 400: Bad Request

### Get Release Item By Request Number
- **Endpoint**: `GET /releases/{reqNumber}`
- **Description**: Retrieves detailed information about a specific release request
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `reqNumber` (path, required): Release Request Number
- **Response Format**:
  ```json
  {
    "QId": "number",
    "ReqNumber": "string",
    "PersonalDetails": {
      "NameEn": "string",
      "NameAr": "string"
    },
    "Status": "string",
    "StatusDescriptionEn": "string",
    "StatusDescriptionAr": "string"
  }
  ```
- **Responses**:
  - 200: Success (GetReleaseItemResponse)
  - 204: No Content
  - 400: Bad Request
  - 401: Unauthorized

### Create Release
- **Endpoint**: `POST /releases`
- **Description**: Creates a new release request
- **Authorization**: Function-level authorization required
- **Headers**:
  - `X-RequestOrigin` (required): Source of the request
- **Request Body**:
  ```json
  {
    "SubmitterQId": "number",
    "QId": "number",
    "FNameEn": "string",
    "MNameEn": "string",
    "LNameEn": "string",
    "FNameAr": "string",
    "MNameAr": "string",
    "LNameAr": "string"
  }
  ```
- **Responses**:
  - 201: Created (Release object)
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error
- **Notes**:
  - Automatically assigns a unique request number
  - Sets initial status as "Saved"
  - Records submission timestamp
  - Validates submitter authorization

### Update Release By Request Number
- **Endpoint**: `PUT /releases/{reqNumber}`
- **Description**: Updates an existing release request
- **Authorization**: Function-level authorization required
- **Headers**:
  - `X-RequestOrigin` (required): Source of the request
- **Parameters**:
  - `reqNumber` (path, required): Release Request Number
- **Request Body**: Same as Create Release
- **Responses**:
  - 200: Success
  - 204: No Content
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error
- **Notes**:
  - Internal use only (OpenAPI ignore flag)
  - Updates modification timestamp
  - Maintains request history

### Delete Release By Request Number
- **Endpoint**: `DELETE /releases/{reqNumber}`
- **Description**: Deletes a release request
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `reqNumber` (path, required): Release Request Number
- **Responses**:
  - 200: Success
  - 204: No Content
  - 400: Bad Request
  - 401: Unauthorized
- **Notes**:
  - Performs soft delete
  - Updates deletion timestamp
  - Maintains request history

## Technical Implementation Details

### Database Schema
The Release of Information system uses Entity Framework Core with the following main entities:
- Release: Stores main release request information
- Status: Manages request status and workflow
- Audit: Tracks changes and maintains history

### Security Implementation
1. **Authorization**:
   - Function-level authorization
   - QID-based access control
   - Request origin validation

2. **Data Protection**:
   - Input sanitization
   - Secure error handling
   - Audit trail maintenance

### Performance Optimization
1. **Query Optimization**:
   - AsNoTracking queries for read operations
   - Efficient joins and includes
   - Pagination support

2. **Data Access**:
   - Connection pooling
   - Async operations
   - Efficient entity tracking

### Error Handling
1. **Validation**:
   - Request body validation
   - Header validation
   - Authorization validation

2. **Error Responses**:
   - Standardized error format
   - Appropriate HTTP status codes
   - Detailed error messages (when appropriate)

### Best Practices
1. **Code Organization**:
   - Separation of concerns
   - Clean architecture principles
   - Dependency injection

2. **Logging**:
   - Comprehensive error logging
   - Operation tracking
   - Performance monitoring

3. **Maintenance**:
   - Regular status cleanup
   - Audit trail maintenance
   - Performance monitoring

## Response Models

### GetReleaseListResponse
- Basic release information
- Status details
- Bilingual name fields
- Submission timestamp

### GetReleaseStatsResponse
- Release request count

### CheckInProcessReleaseResponse
- InProcess status flag

### GetReleaseItemResponse
- Detailed release information
- Status information
- Bilingual fields

### CreateUpdateReleaseRequest
- Submitter information
- Personal details
- Optional fields for updates

## Status Categories
- **InProcess**:
  - Submitted
  - ConditionallyApproved
  - ResubmitOriginalsRequested
- **Archived**:
  - Approved
  - Cancelled
  - CancelledByEServ
  - Rejected

## Dependencies
- Entity Framework Core
- Azure Functions
- OpenAPI/Swagger
- Custom extensions for request handling

## API Versioning
Current version: Implicit v1
Future considerations:
- URI versioning
- Header versioning
- Query parameter versioning