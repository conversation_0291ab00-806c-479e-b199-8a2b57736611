﻿using EServiceFunctions.Models.MHDS;
using EServiceFunctions.RequestResponseModels.MHDS;

namespace EServiceFunctions.MapperModels;

public static class MhdsMapper
{
    public static GetMHDSItemResponse MapMhdsRequestDetailsToGetMhdsItemResponse(MHDSRequestDetails mhdsRequestDetails)
    {
        var response = new GetMHDSItemResponse
        {
            ReqNumber = mhdsRequestDetails.ReqNumber,
            QId = mhdsRequestDetails.QId,
            FNameEn = mhdsRequestDetails.FNameEn,
            FNameAr = mhdsRequestDetails.FNameAr,
            MNameEn = mhdsRequestDetails.MNameEn,
            MNameAr = mhdsRequestDetails.MNameAr,
            LNameEn = mhdsRequestDetails.LNameEn,
            LNameAr = mhdsRequestDetails.LNameAr,
            //EServiceName = nameof(MhdsFunctions),
            HcNumber = mhdsRequestDetails.HcNumber,
            CurrentAssignedHc = mhdsRequestDetails.CurrentAssignedHc,
            Nationality = mhdsRequestDetails.Nationality,
            Dob = mhdsRequestDetails.Dob.ToUtcString(),
            BNo = mhdsRequestDetails.BNo,
            ZNo = mhdsRequestDetails.ZNo,
            SNo = mhdsRequestDetails.SNo,
            UNo = mhdsRequestDetails.UNo,
            IsGisAddressManualyEntered = mhdsRequestDetails.IsGisAddressManualyEntered,
            SubmitterQId = mhdsRequestDetails.SubmitterQId,
            SubmitterEmail = mhdsRequestDetails.SubmitterEmail,
            SubmitterMobile = mhdsRequestDetails.SubmitterMobile,
            SecondaryPhoneMobile = mhdsRequestDetails.SecondaryPhoneMobile,
            //MedicineList = mhdsRequestDetails.,
            Consent = mhdsRequestDetails.Consent,
            Status = mhdsRequestDetails.StatusNavigation?.Code,
            StatusDescriptionEn = mhdsRequestDetails.StatusNavigation?.DescriptionEn,
            StatusDescriptionAr = mhdsRequestDetails.StatusNavigation?.DescriptionAr,
            SubmittedAt = mhdsRequestDetails.SubmittedAt.ToUtcString(),
            CreatedAt = mhdsRequestDetails.CreatedAt.ToUtcString(),
            UpdatedAt = mhdsRequestDetails.UpdatedAt.ToUtcString()
        };
        return response;
    }        
}