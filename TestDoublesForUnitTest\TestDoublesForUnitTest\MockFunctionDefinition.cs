﻿using System.Collections.Immutable;

using Microsoft.Azure.Functions.Worker;

namespace TestDoublesForUnitTest;

public class MockFunctionDefinition : FunctionDefinition
{
    public static readonly string DefaultPathToAssembly = typeof(MockFunctionDefinition).Assembly.Location;
    public static readonly string DefaultEntryPoint = $"{nameof(MockFunctionDefinition)}.{nameof(DefaultEntryPoint)}";
    private const string DefaultId = "TestId";
    private const string DefaultName = "TestName";

    public MockFunctionDefinition(
        string? functionId = null,
        IDictionary<string, BindingMetadata>? inputBindings = null,
        IDictionary<string, BindingMetadata>? outputBindings = null,
        IEnumerable<FunctionParameter>? parameters = null)
    {
            if (!string.IsNullOrWhiteSpace(functionId))
            {
                Id = functionId;
            }

            Parameters = parameters?.ToImmutableArray() ?? ImmutableArray<FunctionParameter>.Empty;
            InputBindings = inputBindings?.ToImmutableDictionary() ?? ImmutableDictionary<string, BindingMetadata>.Empty;
            OutputBindings = outputBindings?.ToImmutableDictionary() ?? ImmutableDictionary<string, BindingMetadata>.Empty;
        }

    public override ImmutableArray<FunctionParameter> Parameters { get; }
    public override string PathToAssembly => DefaultPathToAssembly;
    public override string EntryPoint => DefaultEntryPoint;
    public override string Id { get; } = DefaultId;
    public override string Name => DefaultName;
    public override IImmutableDictionary<string, BindingMetadata> InputBindings { get; }
    public override IImmutableDictionary<string, BindingMetadata> OutputBindings { get; }
}