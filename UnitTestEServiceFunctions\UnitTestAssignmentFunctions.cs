using EServiceFunctions.Models.Assignment;
using EServiceFunctions.RequestResponseModels.Assignment;

using static UnitTestEServiceFunctions.MockDataForAssignmentFunctions;

namespace UnitTestEServiceFunctions;

public class EmptyAssignmentDbContext : IDbContextFactory<AssignmentContext>
{
    public AssignmentContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<AssignmentContext>()
            .UseInMemoryDatabase(databaseName: "EmptyAssignmentDb")
            .Options;
        var context = new AssignmentContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();
        return context;
    }
}

public class TestAssignmentDbContext : IDbContextFactory<AssignmentContext>
{
    public AssignmentContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<AssignmentContext>()
            .UseInMemoryDatabase(databaseName: "AssignmentDb")
            .Options;
        var context = new AssignmentContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.Assignment.AddRange(GetAssignments());
        context.Status.AddRange(GetAssignmentStatus());
        context.SaveChanges();
        return context;
    }
}

public static class MockDataForAssignmentFunctions
{
    public static IEnumerable<Status> GetAssignmentStatus()
    {
        var status = new List<Status>
        {
            new()
            {
                Code = "New",
                DescriptionEn = "New",
                DescriptionAr = "جديد",
                Category = "New"
            },
            new()
            {
                Code = InProcess,
                DescriptionEn = "In Process",
                DescriptionAr = "قيد الإجراء",
                Category = InProcess
            },
            new()
            {
                Code = "Completed",
                DescriptionEn = "Completed",
                DescriptionAr = "مكتمل",
                Category = "Completed"
            },
            new()
            {
                Code = Cancelled,
                DescriptionEn = Cancelled,
                DescriptionAr = "ملغى",
                Category = Cancelled
            },
            new()
            {
                Code = Rejected,
                DescriptionEn = Rejected,
                DescriptionAr = "مرفوض",
                Category = Rejected
            },
            new()
            {
                Code = Archived,
                DescriptionEn = Archived,
                DescriptionAr = "مؤرشف",
                Category = Archived
            }
        };
        return status;
    }

    public static IEnumerable<Assignment> GetAssignments()
    {
        var assignmentList = new List<Assignment>
        {
            new()
            {
                Id = 1,
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                ReqNumber = "REQ12345678",
                HCNumber = HcNumber1,
                Nationality = NationalityEn,
                Dob = new DateTime(1986, 09, 28),
                CurrentAssignedHC = HealthCenterNameEn,
                CurrentPhysician = "Dr. Mohamed Fazrin",
                SelectedPhysician = "Dr. Mohamed Fazrin",
                ChangeReason = "Change of physician",
                SubmittedBy = "Mohamed Fazrin",
                SubmittedAt = new DateTime(2023, 10, 10),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new DateTime(2023, 01, 10),
                UpdatedAt = new DateTime(2023, 10, 10),
                Status = InProcess,
                StatusInternal = InProcess,
                SN = "*********",
                CreateSource = "Unit-Test-Create",
                UpdateSource = "Unit-Test-Update"
            },
            new()
            {
                Id = 2,
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                ReqNumber = "REQ0001",
                HCNumber = HcNumber1,
                Nationality = NationalityEn,
                Dob = new DateTime(1986, 09, 28),
                CurrentAssignedHC = HealthCenterNameEn,
                CurrentPhysician = "Dr. Mohamed Fazrin",
                SelectedPhysician = "Dr. Mohamed Fazrin",
                ChangeReason = "Change of physician",
                SubmittedBy = "Mohamed Fazrin",
                SubmittedAt = new DateTime(2023, 10, 10),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new DateTime(2023, 01, 10),
                UpdatedAt = new DateTime(2023, 10, 10),
                Status = EmptyString,
                StatusInternal = EmptyString,
                StatusNavigation = null,
                SN = "*********",
                CreateSource = "Unit-Test-Create",
                UpdateSource = "Unit-Test-Update"
            }
        };

        return assignmentList;
    }
}

public class UnitTestAssignmentFunctions(ITestOutputHelper output)
{
    private readonly ILogger<AssignmentFunctions> _logger = Mock.Of<ILogger<AssignmentFunctions>>();

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_OkObjectResult_For_Valid_QId()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, 1);        

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.Unauthorized, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_NoContentResult_For_Invalid_Status()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess1&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_NoContentResult_For_Invalid_Skip()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip=1000&take={Take}";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        
        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_OkObjectResult_For_Invalid_Take()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=1000";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_OkObjectResult_For_Empty_Status()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_OkObjectResult_For_Empty_Skip()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = "?status=&skip=&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_OkObjectResult_For_Empty_Take()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=&skip={Skip}&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_OkObjectResult_For_Empty_QueryParameters()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = "?status=&skip=&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_OkObjectResult_For_No_QueryParameters()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentStatsByQId_Should_Return_OkObjectResult_And_One_Count_For_Valid_QId()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentStatsByQId_Should_Return_Zero_Count_For_Invalid_QId()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentStatsByQId(mockHttpRequestData, 1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessAssignmentByQId_Should_Return_True_For_Valid_QId()
    {
        //Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var mockHttpRequestData =  MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        //Act
        var response =  await assignmentFunctions.CheckInProcessAssignmentByQId(mockHttpRequestData, LongQId);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessAssignmentResponse>(result);
        True(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessAssignmentByQId_Should_Return_False_For_InvalidQId()
    {
        //Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        //Act
        var response = await assignmentFunctions.CheckInProcessAssignmentByQId(mockHttpRequestData, 123);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessAssignmentResponse>(result);
        False(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);
        const string reqNumber = "REQ12345678";        

        // Act
        var response = await assignmentFunctions.GetAssignmentItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetAssignmentItemResponse>(result);
        Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber_And_Valid_Status()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);        
        const string reqNumber = "REQ12345678";
        mockHttpRequestData.Query.Add(InProcess, InProcess);

        // Act
        var response = await assignmentFunctions.GetAssignmentItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetAssignmentItemResponse>(result);
        Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentItemByReqNumber_Should_Return_NoContentResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string reqNumber = "REQ123456789";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentItemByReqNumber_Should_Return_NoContentResult_For_Invalid_StatusNavigation()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string reqNumber = "REQ0001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAssignment_Should_Return_BadRequestObjectResult_For_Invalid_RequestObject()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var request = new CreateUpdateAssignmentRequest
        {
            QId = LongQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            HCNumber = HcNumber1,
            Nationality = NationalityEn,
            Dob = new DateTime(1986, 09, 28),
            CurrentAssignedHC = HealthCenterNameEn,
            CurrentPhysician = "Dr. Mohamed Fazrin",
            SelectedPhysician = "Dr. Mohamed Fazrin",
            AttachId1 = Guid.NewGuid(),
            AttachId2 = Guid.NewGuid(),
            AttachId3 = Guid.NewGuid(),
            AttachId4 = Guid.NewGuid(),
            AttachId5 = Guid.NewGuid(),
            AttachId6 = Guid.NewGuid(),
            ChangeReason = "Testing-Purpose",
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.CreateAssignment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAssignment_Should_Return_CreatedResult_For_Valid_Request()
    {
        // Arrange
        var mockAssignmentContext = new EmptyAssignmentDbContext();
        
        var request = new CreateUpdateAssignmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            HCNumber = HcNumber1,
            Nationality = NationalityEn,
            Dob = new DateTime(1986, 09, 28),
            CurrentAssignedHC = HealthCenterNameEn,
            CurrentPhysician = "Dr. Mohamed Fazrin",
            SelectedPhysician = "Dr. Mohamed Fazrin",
            AttachId1 = Guid.NewGuid(),
            AttachId2 = Guid.NewGuid(),
            AttachId3 = Guid.NewGuid(),
            AttachId4 = Guid.NewGuid(),
            AttachId5 = Guid.NewGuid(),
            AttachId6 = Guid.NewGuid(),
            ChangeReason = "Testing-Purpose",
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.CreateAssignment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAssignment_Should_Return_BadRequestResult_For_Invalid_Request()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var request = new CreateUpdateAssignmentRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.CreateAssignment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAssignment_Should_Return_BadRequestResult_For_Missing_Header()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        var request = new CreateUpdateAssignmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            HCNumber = HcNumber1,
            Nationality = NationalityEn,
            Dob = new DateTime(1986, 09, 28),
            CurrentAssignedHC = HealthCenterNameEn,
            CurrentPhysician = "Dr. Mohamed Fazrin",
            SelectedPhysician = "Dr. Mohamed Fazrin",
            AttachId1 = Guid.NewGuid(),
            AttachId2 = Guid.NewGuid(),
            AttachId3 = Guid.NewGuid(),
            AttachId4 = Guid.NewGuid(),
            AttachId5 = Guid.NewGuid(),
            AttachId6 = Guid.NewGuid(),
            ChangeReason = "Testing-Purpose",
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.CreateAssignment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAssignmentByReqNumber_Should_Return_OK_For_Valid_ReqNumber()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string reqNumber = "REQ12345678";

        var request = new CreateUpdateAssignmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            HCNumber = HcNumber1,
            Nationality = NationalityEn,
            Dob = new DateTime(1986, 09, 28),
            CurrentAssignedHC = HealthCenterNameEn,
            CurrentPhysician = "Dr. Mohamed Fazrin",
            SelectedPhysician = "Dr. Mohamed Fazrin",
            AttachId1 = Guid.NewGuid(),
            AttachId2 = Guid.NewGuid(),
            AttachId3 = Guid.NewGuid(),
            AttachId4 = Guid.NewGuid(),
            AttachId5 = Guid.NewGuid(),
            AttachId6 = Guid.NewGuid(),
            ChangeReason = "Testing-Purpose",
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.UpdateAssignmentByReqNumber(mockHttpRequestData, reqNumber);
        
        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAssignmentByReqNumber_Should_Return_BadRequestResult_For_Empty_RequestObject()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string reqNumber = "REQ12345678";
        var request = new CreateUpdateAssignmentRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.UpdateAssignmentByReqNumber(mockHttpRequestData,reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAssignmentByReqNumber_Should_Return_BadRequestResult_For_Missing_Headers()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string reqNumber = "REQ12345678";
        var request = new CreateUpdateAssignmentRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.UpdateAssignmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteAssignmentByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string reqNumber = "REQ12345678";
        var mockHttpRequestData =  MockHelpers.CreateHttpRequestData(method: Delete);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.DeleteAssignmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteAssignmentByReqNumber_Should_Return_BadRequestObjectResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string reqNumber = "REQ123456789";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.DeleteAssignmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_Should_Return_NoContentResult_For_Empty_Records()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_FiltersBySubmitterQId()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_FiltersByAssignmentStatus()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_HandlesSkipAndTake()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_MapsFieldsCorrectly()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_HandlesNoRecordsFound()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssignmentListByQId_OrdersByQId()
    {
        // Arrange
        var mockAssignmentContext = new TestAssignmentDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var assignmentFunctions = new AssignmentFunctions(mockAssignmentContext, _logger);

        // Act
        var response = await assignmentFunctions.GetAssignmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }
}