﻿using EServiceFunctions.Models.ReleaseOfInformation;
using EServiceFunctions.RequestResponseModels.ReleaseOfInformation;

namespace EServiceFunctions.MapperModels;

public static class ReleaseOfInformationMapper
{
    public static GetReleaseItemResponse MapReleaseToGetReleaseItemResponse(Release release)
    {
        return new GetReleaseItemResponse
        {
            QId = release.QId,
            FNameEn = release.FNameEn,
            MNameEn = release.MNameEn,
            LNameEn = release.LNameEn,
            FNameAr = release.FNameAr,
            MNameAr = release.MNameAr,
            LNameAr = release.LNameAr,
            ReqNumber = release.ReqNumber,
            HCNumber = release.HCNumber,
            Nationality = release.Nationality,
            Dob = release.Dob.ToUtcString(),
            CurrentAssignedHC = release.CurrentAssignedHC,
            AttendanceRptStartDate = release.AttendanceRptStartDate.ToUtcString(),
            AttendanceRptEndDate = release.AttendanceRptEndDate.ToUtcString(),
            ReleaseItems = release.ReleaseItems,
            OtherReleaseItems = release.OtherReleaseItems,
            EncounterStartDate = release.EncounterStartDate.ToUtcString(),
            EncounterEndDate = release.EncounterEndDate.ToUtcString(),
            PickupDelegated = release.PickupDelegated,
            AuthorizedQId = release.AuthorizedQId,
            AuthorizedPersonName = release.AuthorizedPersonName,
            AuthorizedPersonRelation = release.AuthorizedPersonRelation,
            DelegationOthers = release.DelegationOthers,
            AttachId1 = release.AttachId1,
            AttachId2 = release.AttachId2,
            AttachId3 = release.AttachId3,
            AttachId4 = release.AttachId4,
            AttachId5 = release.AttachId5,
            AttachId6 = release.AttachId6,
            SubmittedBy = release.SubmittedBy,
            SubmittedAt = release.SubmittedAt.ToUtcString(),
            SubmitterQId = release.SubmitterQId,
            SubmitterEmail = release.SubmitterEmail,
            SubmitterMobile = release.SubmitterMobile,
            CreatedAt = release.CreatedAt.ToUtcString(),
            UpdatedAt = release.UpdatedAt.ToUtcString(),
            Status = release.StatusNavigation?.Code,
            StatusDescriptionEn = release.StatusNavigation?.DescriptionEn,
            StatusDescriptionAr = release.StatusNavigation?.DescriptionAr,
            SN = release.SN
        };
    }

    public static Release MapCreateUpdateReleaseRequestToRelease(CreateUpdateReleaseRequest createUpdateReleaseRequest)
    {
        return new Release
        {
            QId = createUpdateReleaseRequest.QId,
            FNameEn = createUpdateReleaseRequest.FNameEn,
            MNameEn = createUpdateReleaseRequest.MNameEn,
            LNameEn = createUpdateReleaseRequest.LNameEn,
            FNameAr = createUpdateReleaseRequest.FNameAr,
            MNameAr = createUpdateReleaseRequest.MNameAr,
            LNameAr = createUpdateReleaseRequest.LNameAr,
            HCNumber = createUpdateReleaseRequest.HCNumber,
            Nationality = createUpdateReleaseRequest.Nationality,
            Dob = createUpdateReleaseRequest.Dob,
            CurrentAssignedHC = createUpdateReleaseRequest.CurrentAssignedHC,
            AttendanceRptStartDate = createUpdateReleaseRequest.AttendanceRptStartDate,
            AttendanceRptEndDate = createUpdateReleaseRequest.AttendanceRptEndDate,
            ReleaseItems = createUpdateReleaseRequest.ReleaseItems,
            OtherReleaseItems = createUpdateReleaseRequest.OtherReleaseItems,
            EncounterStartDate = createUpdateReleaseRequest.EncounterStartDate,
            EncounterEndDate = createUpdateReleaseRequest.EncounterEndDate,
            PickupDelegated = createUpdateReleaseRequest.PickupDelegated,
            AuthorizedQId = createUpdateReleaseRequest.AuthorizedQId,
            AuthorizedPersonName = createUpdateReleaseRequest.AuthorizedPersonName,
            AuthorizedPersonRelation = createUpdateReleaseRequest.AuthorizedPersonRelation,
            DelegationOthers = createUpdateReleaseRequest.DelegationOthers,
            AttachId1 = createUpdateReleaseRequest.AttachId1,
            AttachId2 = createUpdateReleaseRequest.AttachId2,
            AttachId3 = createUpdateReleaseRequest.AttachId3,
            AttachId4 = createUpdateReleaseRequest.AttachId4,
            AttachId5 = createUpdateReleaseRequest.AttachId5,
            AttachId6 = createUpdateReleaseRequest.AttachId6,
            SubmittedBy = createUpdateReleaseRequest.SubmittedBy,                
            SubmitterQId = createUpdateReleaseRequest.SubmitterQId,
            SubmitterEmail = createUpdateReleaseRequest.SubmitterEmail,
            SubmitterMobile = createUpdateReleaseRequest.SubmitterMobile
        };
    }
}