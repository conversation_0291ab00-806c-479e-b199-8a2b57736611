﻿namespace EServiceFunctions.Models.CommentAndAttachment;

public class CommentAndAttachmentContext(DbContextOptions<CommentAndAttachmentContext> options) : DbContext(options)
{
    public virtual DbSet<Attachment>? Attachment { get; set; }
    public virtual DbSet<Comment>? Comment { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<Attachment>(entity =>
        {
            entity.HasKey(e => e.Id)
                .HasName("PK__Attachme__3214EC06B3244C23")
                .IsClustered(false);

            entity.HasIndex(e => e.SubmitterQId)
                .HasDatabaseName("IDX_Attachment_SubmitterQId");

            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.Property(e => e.AttachmentName).HasMaxLength(255);

            entity.Property(e => e.AttachmentType).HasMaxLength(255);

            entity.Property(e => e.CreatedAt).HasColumnType("datetime");

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);
        });

        modelBuilder.Entity<Comment>(entity =>
        {
            entity.HasIndex(e => e.ReqNumber)
                .HasDatabaseName("IDX_Comment_ReqNumber");

            entity.HasIndex(e => e.SubmitterQId)
                .HasDatabaseName("IDX_Comment_SubmitterQId");

            entity.Property(e => e.Action).HasMaxLength(255);

            entity.Property(e => e.CommentText).HasMaxLength(1000);

            entity.Property(e => e.CommentType).HasMaxLength(255);

            entity.Property(e => e.CommenterName).HasMaxLength(1000);

            entity.Property(e => e.CreatedAt).HasColumnType("datetime");

            entity.Property(e => e.ReqNumber).HasMaxLength(255);

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);
        });
    }
}