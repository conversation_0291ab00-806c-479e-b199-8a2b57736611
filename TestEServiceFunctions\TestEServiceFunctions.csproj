<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <LangVersion>12</LangVersion>
        <UserSecretsId>1fa41be8-5b20-49c0-96e1-46dc005a24f9</UserSecretsId>
    </PropertyGroup>
	<PropertyGroup >
		<EnableNETAnalyzers>true</EnableNETAnalyzers>
		<AnalysisLevel>latest</AnalysisLevel>
		<TreatWarningsAsErrors>True</TreatWarningsAsErrors>
	</PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Bogus" Version="35.6.3" />
        <PackageReference Include="FluentAssertions" Version="8.3.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="RestSharp" Version="112.1.0" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="Xunit.DependencyInjection" Version="9.7.1" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.3">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\EServiceFunctions\EServiceFunctions.csproj" />
      <ProjectReference Include="..\MockDataLibrary\MockDataLibrary.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appconfigs.dev.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Remove="Properties\launchSettings.json" />
      <None Remove="HttpFiles\http-client.private.env.json" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Properties\" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="UnitTests\MockObjects.cs" />
    </ItemGroup>

</Project>
