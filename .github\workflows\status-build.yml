# runner: Github hosted being used - no tokens or secrets used
# This perform build checks before merging features into main branch 
name: PR StatusCheck
on:
    workflow_dispatch:  # remove this line once jobs are reflected in status check. 
    pull_request:
         types: [opened, synchronize]
         branches:
            - 'develop'
jobs:
    pr-jobs:
        name: PR 
        uses: public-health-care-center-CORP/WorkflowRepo/.github/workflows/ci-cd-eservices-restapi-pr.yml@main
        secrets: inherit
