# Medicine Home Delivery Service (MHDS) Functions Documentation

## Overview
The Medicine Home Delivery Service (MHDS) Functions module provides REST API endpoints for managing medicine home delivery service requests. This module enables users to request prescription refills, track delivery status, and manage their prescriptions through a secure and efficient delivery system. The system supports both individual and dependent requests with comprehensive tracking and status management, integrated with the Enterprise Data Warehouse (EDW) for accurate medication information.

## Core Features
1. **Medicine Refill Management**
   - Automated refill eligibility checking
   - Multiple prescription support
   - Dependent request handling
   - Bilingual support (Arabic/English)

2. **Request Tracking**
   - Real-time status updates
   - Comprehensive request history
   - Statistical reporting
   - Multi-status workflow

3. **Security**
   - QID-based authorization
   - Request origin validation
   - Secure data handling
   - Access control

## API Endpoints

### 1. Check In-Process MHDS by QID
- **Endpoint**: `GET /mhds/{qId}/inprocess-validation`
- **Description**: Validates if a user has any active MHDS requests in process
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `qId` (path, required): Requester's QID (long)
- **Response Format**:
  ```json
  {
    "IsInProcessExist": boolean    // True if in-process requests exist
  }
  ```
- **Response Codes**:
  - 200: Validation successful
  - 400: Invalid QID format
  - 500: Server error

### 2. Get MHDS Statistics by QID
- **Endpoint**: `GET /mhds/submitter-qid/{qId}/stats`
- **Description**: Retrieves request statistics for a specific submitter
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `qId` (path, required): Submitter's QID
  - `status` (query, optional): Status category filter
    - InProcess: [Submitted, PaymentReceived, ReturnedForPayment]
    - Archived: [Approved, Cancelled, Returned]
- **Response Format**:
  ```json
  {
    "Count": number    // Number of requests matching criteria
  }
  ```
- **Response Codes**:
  - 200: Statistics retrieved
  - 400: Invalid parameters
  - 401: Unauthorized access
  - 500: Server error

### 3. Get MHDS List by Health Card Number
- **Endpoint**: `POST /mhds/medicinelist`
- **Description**: Retrieves refillable medicines for specified health cards
- **Authorization**: Function-level authorization
- **Request Body**:
  ```json
  {
    "HealthCardNumbers": [         // Array of health card numbers
      "string"
    ]
  }
  ```
- **Response Format**:
  ```json
  [
    {
      "MedicineInformation": [
        {
          "MedicineName": "string",              // Medicine name
          "PrescribedDate": "string",            // ISO 8601 date
          "SupplyDuration": "string",            // Duration in days
          "LastDispenseDate": "string",          // ISO 8601 date
          "LastDispensedLocation": "string",     // Facility code
          "OrderByName": "string",               // Prescriber name
          "PrescriptionRefillDueDate": "string", // ISO 8601 date
          "PrescriptionOrderId": "string"        // Unique prescription ID
        }
      ]
    }
  ]
  ```
- **Environment Handling**:
  - Production: Uses MedicationRefillDetailsProd
  - Staging: Uses MedicationRefillDetailsStg
- **Response Codes**:
  - 200: Medicines found
  - 204: No refillable medicines
  - 400: Invalid request
  - 500: Server error

### 4. Create MHDS Request
- **Endpoint**: `POST /mhds`
- **Description**: Creates a new medicine delivery request
- **Authorization**: Function-level authorization with QID validation
- **Headers Required**:
  - X-RequestOrigin: Request source identifier
- **Request Body**:
  ```json
  {
    "QId": "number",                    // Patient QID
    "SubmitterQId": "number",           // Requester QID
    "FNameEn": "string",                // First name (English)
    "FNameAr": "string",                // First name (Arabic)
    "MNameEn": "string",                // Middle name (English)
    "MNameAr": "string",                // Middle name (Arabic)
    "LNameEn": "string",                // Last name (English)
    "LNameAr": "string",                // Last name (Arabic)
    "HCNumber": "string",               // Health card number
    "CurrentAssignedHC": "string",      // Assigned health center
    "Dob": "string",                    // Date of birth
    "Nationality": "string",            // Nationality code
    "IsGisAddressManualyEntered": bool, // Manual address flag
    "UNo": "string",                    // Unit number
    "BNo": "string",                    // Building number
    "SNo": "string",                    // Street number
    "ZNo": "string",                    // Zone number
    "SubmitterMobile": "string",        // Contact number
    "SecondaryMobNo": "string",         // Alternative number
    "SubmitterEmail": "string",         // Email address
    "Consent": bool,                    // Consent flag
    "Action": "string",                 // Request action
    "MedInfo": [                        // Medicine information
      {
        "MedicineName": "string",
        "PrescriptionOrderId": "string",
        "PrescribedDate": "string",
        "PrescriptionRefillDueDate": "string",
        "LastDispenseDate": "string",
        "SupplyDuration": "string",
        "LastDispensedLocation": "string",
        "OrderByName": "string",
        "AttachId": "string"
      }
    ]
  }
  ```
- **Response Format**:
  ```json
  {
    "ReqNumber": "string",              // Generated request number
    "Status": "string",                 // Initial status (Submitted)
    "CreatedAt": "string",              // Creation timestamp
    "CreateSource": "string"            // Request origin
  }
  ```
- **Response Codes**:
  - 201: Request created
  - 400: Invalid request
  - 401: Unauthorized
  - 500: Server error

### 5. Update MHDS by Request Number
- **Endpoint**: `PUT /mhds/{reqNumber}`
- **Description**: Updates an existing MHDS request
- **Authorization**: Function-level authorization with QID validation
- **Headers Required**:
  - X-RequestOrigin: Request source identifier
- **Parameters**:
  - `reqNumber` (path, required): MHDS request number
- **Request Body**:
  ```json
  {
    "QId": "number",                    // Patient QID
    "SubmitterQId": "number",           // Requester QID
    "IsGisAddressManualyEntered": bool, // Manual address flag
    "UNo": "string",                    // Unit number (if manual)
    "BNo": "string",                    // Building number (if manual)
    "SNo": "string",                    // Street number (if manual)
    "ZNo": "string"                     // Zone number (if manual)
  }
  ```
- **Response Codes**:
  - 200: Update successful
  - 400: Invalid request
  - 401: Unauthorized
  - 404: Request not found
  - 500: Server error

### 6. Get MHDS List by QID
- **Endpoint**: `GET /mhds/submitter-qid/{qId}`
- **Description**: Retrieves all MHDS requests for a submitter
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `qId` (path, required): Submitter's QID
  - `status` (query, optional): Status filter
  - `skip` (query, optional): Pagination offset
  - `take` (query, optional): Page size
- **Response Format**:
  ```json
  [
    {
      "QId": "number",                  // Patient QID
      "ReqNumber": "string",            // Request number
      "FNameEn": "string",              // First name (English)
      "MNameEn": "string",              // Middle name (English)
      "LNameEn": "string",              // Last name (English)
      "FNameAr": "string",              // First name (Arabic)
      "MNameAr": "string",              // Middle name (Arabic)
      "LNameAr": "string",              // Last name (Arabic)
      "Status": "string",               // Status code
      "StatusDescriptionEn": "string",   // Status description (English)
      "StatusDescriptionAr": "string",   // Status description (Arabic)
      "SubmittedAt": "string"           // Submission timestamp
    }
  ]
  ```
- **Response Codes**:
  - 200: Requests found
  - 204: No requests
  - 400: Invalid parameters
  - 401: Unauthorized
  - 500: Server error

### 7. Get MHDS Item by Request Number
- **Endpoint**: `GET /mhds/{reqNumber}`
- **Description**: Retrieves detailed information for a specific MHDS request
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `reqNumber` (path, required): MHDS request number
- **Response Format**:
  ```json
  {
    "ReqNumber": "string",
    "QId": "long",
    "SubmitterQId": "long",
    "Status": "string",
    "StatusDescriptionEn": "string",
    "StatusDescriptionAr": "string",
    "PersonalInfo": {
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "HCNumber": "string",
      "CurrentAssignedHC": "string",
      "Dob": "datetime",
      "Nationality": "string"
    },
    "AddressInfo": {
      "UNo": "string",
      "BNo": "string",
      "SNo": "string",
      "ZNo": "string",
      "IsGisAddressManualyEntered": "boolean"
    },
    "ContactInfo": {
      "SubmitterMobile": "string",
      "SecondaryMobNo": "string",
      "SubmitterEmail": "string"
    },
    "MedicineList": [
      {
        "MedicineName": "string",
        "PrescriptionOrderId": "string",
        "PrescribedDate": "datetime",
        "LastDispenseDate": "datetime",
        "SupplyDuration": "string",
        "LastDispensedLocation": "string",
        "OrderByName": "string",
        "PrescriptionRefillDueDate": "datetime",
        "AttachId": "string"
      }
    ]
  }
  ```
- **Response Codes**:
  - 200: Request details retrieved
  - 204: Request not found
  - 400: Invalid request number
  - 401: Unauthorized
  - 500: Server error

### 8. Delete MHDS by Request Number (Internal)
- **Endpoint**: `DELETE /mhds/{reqNumber}`
- **Description**: Deletes an MHDS request and associated medicine list (Internal use only)
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `reqNumber` (path, required): MHDS request number
- **Response Codes**:
  - 204: Successfully deleted
  - 400: Invalid request or not found
  - 401: Unauthorized
  - 500: Server error

## Data Models

### Status Categories
1. InProcess
   - Submitted
   - PaymentReceived
   - ReturnedForPayment

2. Archived
   - Approved
   - Cancelled
   - Returned

### Database Context
- **MHDSContext**
  - MHDSRequestDetails
  - MHDSRequestMedicineList
  - Status

- **EDWContext**
  - MedicationRefillDetailsProd
  - MedicationRefillDetailsStg

## Security Implementation
1. Function-level authorization
2. QID-based access control
3. Request origin validation
4. Secure data transmission
5. Audit logging

## Error Handling
1. Standard HTTP status codes
2. Detailed error messages
3. Exception logging
4. Transaction management
5. Data validation

## Performance Optimization
1. AsNoTracking for read operations
2. Efficient joins and filtering
3. Pagination implementation
4. Selective field projection
5. Query optimization

## Multilingual Support
1. Bilingual personal information
2. Arabic/English status descriptions
3. Consistent naming conventions
4. Cultural considerations

## Best Practices
1. Clean architecture
2. Dependency injection
3. Repository pattern
4. Async/await operations
5. Proper resource disposal
6. Comprehensive logging