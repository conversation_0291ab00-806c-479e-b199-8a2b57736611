﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.RequestResponseModels.Enrollment;

[ExcludeFromCodeCoverage]
[Keyless]
public class GetUserDetailsResponse
{
    [Column("FIRST_NAME_EN")]
    public string? FNameEn { get; set; }
    [Column("MIDDLE_NAME_EN")]
    public string? MNameEn { get; set; }
    [Column("LAST_NAME_EN")]
    public string? LNameEn { get; set; }
    [Column("FIRST_NAME_AR")]
    public string? FNameAr { get; set; }
    [Column("MIDDLE_NAME_AR")]
    public string? MNameAr { get; set; }
    [Column("LAST_NAME_AR")]
    public string? LNameAr { get; set; }
    [Column("NATIONALITY_CODE")]
    public string? NationalityCode { get; set; }
    [Column("HC_NUMBER")]
    public string? HcNumber { get; set; }
    [Column("HC_EXPIRY_DATE")]
    public string? HcExpiryDate { get; set; }

}