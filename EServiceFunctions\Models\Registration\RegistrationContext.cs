﻿namespace EServiceFunctions.Models.Registration;

public class RegistrationContext(DbContextOptions<RegistrationContext> options) : DbContext(options)
{
    public virtual DbSet<Registration>? Registration { get; set; }
    public virtual DbSet<Status>? Status { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<Registration>(entity =>
        {
            entity.HasIndex(e => e.QId)
                .HasDatabaseName("IDX_Registration_QId");

            entity.HasIndex(e => e.ReqNumber)
                .HasDatabaseName("UQ__Registra__237F3423E00F7DA3")
                .IsUnique();

            entity.HasIndex(e => e.SubmitterQId)
                .HasDatabaseName("IDX_Registration_SubmitterQId");

            entity.Property(e => e.CatchmentHC).HasMaxLength(255);

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.Property(e => e.Dob).HasColumnType("date");

            entity.Property(e => e.Education).HasMaxLength(255);

            entity.Property(e => e.FNameAr).HasMaxLength(255);

            entity.Property(e => e.FNameEn).HasMaxLength(255);

            entity.Property(e => e.Gender).HasMaxLength(255);

            entity.Property(e => e.HomeTel).HasMaxLength(255);

            entity.Property(e => e.LNameAr).HasMaxLength(255);

            entity.Property(e => e.LNameEn).HasMaxLength(255);

            entity.Property(e => e.MNameAr).HasMaxLength(255);

            entity.Property(e => e.MNameEn).HasMaxLength(255);

            entity.Property(e => e.MaritalStatus).HasMaxLength(255);

            entity.Property(e => e.MobileNo).HasMaxLength(255);

            entity.Property(e => e.Nationality).HasMaxLength(255);

            entity.Property(e => e.NextOfKinLandLine).HasMaxLength(255);

            entity.Property(e => e.NextOfKinName).HasMaxLength(255);

            entity.Property(e => e.Occupation).HasMaxLength(255);

            entity.Property(e => e.OfficeTel).HasMaxLength(255);

            entity.Property(e => e.PrefComMode).HasMaxLength(255);

            entity.Property(e => e.PrefHC).HasMaxLength(255);

            entity.Property(e => e.PrefSMSLang).HasMaxLength(255);

            entity.Property(e => e.ReqNumber).HasMaxLength(255);

            entity.Property(e => e.SN).HasMaxLength(255);

            entity.Property(e => e.SponsorAddress).HasMaxLength(255);

            entity.Property(e => e.SponsorName).HasMaxLength(255);

            entity.Property(e => e.Status).HasMaxLength(255);

            entity.Property(e => e.StatusInternal).HasMaxLength(255);

            entity.Property(e => e.SubmittedAt).HasColumnType("datetime");

            entity.Property(e => e.SubmittedBy).HasMaxLength(255);

            entity.Property(e => e.SubmitterEmail).HasMaxLength(255);

            entity.Property(e => e.SubmitterMobile).HasMaxLength(255);

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");

            entity.Property(e => e.VisaType).HasMaxLength(255);

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);

            entity.HasOne(d => d.StatusNavigation)
                .WithMany(p => p.Registration)
                .HasForeignKey(d => d.Status)
                .HasConstraintName("FK__Registrat__Statu__6319B466");
        });

        modelBuilder.Entity<Status>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Status__A25C5AA68EAE98DF");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);

            entity.Property(e => e.Category).HasMaxLength(255);
        });
    }
}