﻿--WI#1447
INSERT INTO Status
Values ('Ready', 'Saved', N'تم حفظ الطلب', 'InProcess')
GO

--WI#1442
IF OBJECT_ID('dbo.SP_GetMyTaskStatus', 'P') IS NOT NULL
    DROP PROCEDURE dbo.SP_GetMyTaskStatus;
GO
CREATE PROCEDURE dbo.SP_GetMyTaskStatus @SubmitterQId BIGINT
AS
BEGIN
    -- declare table variable
    DECLARE @MyTaskStatusTable TABLE
                               (
                                   [Service] nvarchar(50)  NOT NULL,
                                   [Status]  nvarchar(255) NOT NULL
                               );

    INSERT INTO @MyTaskStatusTable
    SELECT 'Appointments' as [Service], Status
    FROM Appointment
    WHERE SubmitterQId = @SubmitterQId
    UNION ALL
    SELECT 'Assignments' as [Service], Status
    FROM Assignment
    WHERE SubmitterQId = @SubmitterQId
    UNION ALL
    SELECT 'Enrollments' as [Service], Status
    FROM Enrollment
    WHERE SubmitterQId = @SubmitterQId
    UNION ALL
    SELECT 'Registrations' as [Service], Status
    FROM Registration
    WHERE SubmitterQId = @SubmitterQId
    UNION ALL
    SELECT 'Releases' as [Service], Status
    FROM Release
    WHERE SubmitterQId = @SubmitterQId
    UNION ALL
    SELECT 'Transfers' as [Service], Status
    FROM Transfer
    WHERE SubmitterQId = @SubmitterQId

    --SELECT * FROM @MyTaskStatusTable 

    SELECT [Service], [Status], COUNT([Status]) AS 'StatusCount' FROM @MyTaskStatusTable GROUP BY [Service], [Status];

END
GO

