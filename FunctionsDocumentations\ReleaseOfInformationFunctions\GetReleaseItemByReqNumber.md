# Get Release Item By Request Number

## Overview
Retrieves detailed information about a specific release request using its request number.

## Endpoint
```http
GET /releases/{reqNumber}
```

## Authorization
- Function-level authorization
- QID-based access control
- Requires valid submitter QID

## Parameters

### Path Parameters
- `reqNumber` (string, required): Release Request Number

## Response

### Success Response (200 OK)
Returns detailed release request information:
```json
{
  "reqNumber": "string",
  "qId": "long",
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string",
  "status": "string",
  "statusDescriptionEn": "string",
  "statusDescriptionAr": "string",
  "submittedAt": "string (UTC)"
  // Additional fields based on release request model
}
```

### No Content Response (204)
Returned when no record is found for the given request number.

### Error Responses
- 400 Bad Request: Invalid request number format
- 401 Unauthorized: Invalid QID or unauthorized access

## Implementation Details
- Uses Entity Framework Core with AsNoTracking for optimal performance
- Implements eager loading for status information
- Supports multilingual content (English/Arabic)
- Includes detailed status descriptions

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control based on submitter QID
- Validates request number format

## Performance Optimization
- Uses AsNoTracking for read-only operations
- Implements efficient single record retrieval
- Optimizes database queries using proper includes
- Efficient status mapping
