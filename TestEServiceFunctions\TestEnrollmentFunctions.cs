﻿namespace TestEServiceFunctions;

[Collection("EnrollmentFunctions")]
public class TestEnrollmentFunctions(ITestOutputHelper testOutputHelper, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetEnrollmentListByQId()
    {
        // Arrange
        var request = GetRestRequest("enrollments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
        request.AddUrlSegment("qId", 22222222270);
        request.AddHeader("IsApp", true);

        // Act
        var response = await _client.GetAsync<List<GetEnrollmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        NotNull(response[0]);
        Equal("TEST1", response[0].FNameEn);
        Equal(33322222245, response[0].QId);
        Equal(Approved, response[0].Status);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetEnrollmentStatsByQId()
    {
        // Arrange
        var request = GetRestRequest("enrollments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
        request.AddUrlSegment("qId", 22222222270);
        request.AddHeader("IsApp", true);

        // Act
        var response = await _client.GetAsync<GetEnrollmentStatsResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count > 0);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessEnrollmentFalse()
    {
        // Arrange
        var request = GetRestRequest("enrollments/inprocess-validation/{applicantQId}/{submitterQId}");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
        request.AddUrlSegment("applicantQId", "33322222291");
        request.AddUrlSegment("submitterQId", "22222222270");
        
        // Act
        var response = await _client.GetAsync<CheckInProcessEnrollmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        False(response.IsInprocessExist);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessEnrollmentTrue()
    {
        // Arrange
        var request = GetRestRequest("enrollments/inprocess-validation/{applicantQId}/{submitterQId}");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
        request.AddUrlSegment("applicantQId", "9876543210");
        request.AddUrlSegment("submitterQId", "22222222270");

        // Act
        var response = await _client.GetAsync<CheckInProcessEnrollmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.IsInprocessExist);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetEnrollmentItemByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("enrollments/{reqNumber}");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222267");
        request.AddUrlSegment("reqNumber", "52696VUZW");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222267);

        // Act
        var response = await _client.GetAsync<GetEnrollmentItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal("TEST1", response.FNameEn);
        Equal(33322222291, response.QId);
        Equal(CancelledByEServ, response.Status);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetEnrollmentItemByReqNumberNotFound()
    {
        // Arrange
        var request = GetRestRequest("enrollments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "52696VUZ");

        // Act
        var response = await _client.GetAsync<GetEnrollmentItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCreateEnrollment()
    {
        // Arrange
        var request = CreateEnrollment();
        
        // Act
        var response = await _client.PostAsync<CreateEnrollmentResponse>(request);
       testOutputHelper.LogToConsole(response);

        //  Assert
        NotNull(response);
        True(response.ReqNumber.Length > 0);
        True(response.ReqNumber.Length == 11);
    }

    private static RestRequest CreateEnrollment()
    {
        var request = GetRestRequest("enrollments");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateEnrollment");
        return request.AddJsonBody(GenerateEnrollment());
    }

    private static CreateUpdateEnrollmentRequest GenerateEnrollment()
    {
        var enrollment = new Faker<CreateUpdateEnrollmentRequest>()
            .RuleFor(x => x.QId, RandomNumber(11))
            .RuleFor(x => x.HCNumber, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.FNameEn, f => f.Name.FirstName())
            .RuleFor(x => x.MNameEn, MiddleNameEnglish)
            .RuleFor(x => x.LNameEn, f => f.Name.LastName())
            .RuleFor(x => x.FNameAr, FirstNameArabic)
            .RuleFor(x => x.MNameAr, MiddleNameArabic)
            .RuleFor(x => x.LNameAr, LastNameArabic)
            .RuleFor(x => x.Nationality, "634")
            .RuleFor(x => x.AttachId1, NewGuid())
            .RuleFor(x => x.AttachId2, NewGuid())
            .RuleFor(x => x.AttachId3, NewGuid())
            .RuleFor(x => x.AttachId4, NewGuid())
            .RuleFor(x => x.AttachId5, NewGuid())
            .RuleFor(x => x.AttachId6, NewGuid())
            .RuleFor(x => x.SubmittedBy, f => f.Name.FullName())
            .RuleFor(x => x.SubmitterQId, LongQId)
            .RuleFor(x => x.SubmitterEmail, "<EMAIL>")
            .RuleFor(x => x.SubmitterMobile, "+97470559257")
            .RuleFor(x => x.SubmitterNationality, "634")
            .RuleFor(x => x.SubmitterHCNumber, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.SubmitterHCntCode, "RYN")
            .RuleFor(x => x.Consent, f => f.Random.Bool())
            .Generate();

        return enrollment;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateEnrollmentByReqNumber_Should_Return_OK()
    {
        // Arrange
        var request = GetRestRequest("enrollments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "18708IJIB");
        var enrollment = GenerateEnrollment();
        enrollment.SubmitterQId = 22222222270;
        request.AddJsonBody(enrollment);
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateEnrollmentByReqNumber");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
        
        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        NotNull(response);
        Equal(OK, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteEnrollmentByReqNumber()
    {
        // Arrange
        var req = CreateEnrollment();
        var reqNumber = await _client.PostAsync<CreateEnrollmentResponse>(req);
        testOutputHelper.LogToConsole(reqNumber.ReqNumber);
        reqNumber.ReqNumber.ThrowIfNull();
        reqNumber.ThrowIfNull();
        await WaitAsync();

        // Act
        var request = GetRestRequest("enrollments/{reqNumber}");
        request.AddUrlSegment("reqNumber", reqNumber.ReqNumber);

        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        True(response is not null);
        True(response.IsSuccessStatusCode);
        Equal(OK, response.StatusCode);
    }
    
    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestDeleteEnrollmentByReqNumber_Should_Return_BadRequest()
    {
        // Arrange
        var request = GetRestRequest("enrollments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "VIJRANG0581");
        request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
        request.Method = Method.Delete;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        NotNull(response);
        Equal(BadRequest, response.StatusCode);
    }
}