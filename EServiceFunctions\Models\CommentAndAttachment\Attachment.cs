﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.CommentAndAttachment;

public class Attachment
{
    public Guid Id { get; set; }
    public string? AttachmentName { get; set; }
    public string? AttachmentType { get; set; }
    public string? AttachmentData { get; set; }
    public long? SubmitterQId { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreateSource { get; set; }
    public string? UpdateSource { get; set; }
}