# GetAppointmentsListByQIdV1

## Overview
Retrieves appointments for a given list of QIDs. Only returns appointments that the submitter is authorized to view.

## Endpoint
- **Route**: `appointments/bookings`
- **Method**: POST
- **Authorization**: Function level

## Request Body
```json
{
  "QIds": ["string"]
}
```

## Headers
- `EligibleClinicCodes`: List of eligible clinic codes
- Authorization header containing submitter's QID

## Responses
- **200 OK**: Returns list of `AppointmentResponse`
  ```json
  [{
    "QId": "string",
    "HcNumber": "string",
    "AppointmentId": "string",
    "AppointmentDateTime": "datetime",
    "AppointmentType": "string",
    "AppointmentStatus": "string",
    "AppointmentHCnt": "string"
  }]
  ```
- **204 No Content**: No appointments found
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication failed
- **403 Forbidden**: Access denied
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates submitter's QID from claims
2. Checks authorization for requested QIDs
3. Retrieves eligible appointment types
4. Returns appointments from the last 7 days that match the criteria

## Dependencies
- AppointmentContext
- HmcLiveFeedContext
