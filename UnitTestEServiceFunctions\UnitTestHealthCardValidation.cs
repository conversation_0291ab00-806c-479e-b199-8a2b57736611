using System.Text;
using EServiceFunctions.Models.Appointment;
using EServiceFunctions.Models.Assignment;
using EServiceFunctions.Models.Transfer;
using EServiceFunctions.Models.MHDS;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.Appointment;
using EServiceFunctions.RequestResponseModels.Assignment;
using EServiceFunctions.RequestResponseModels.Transfer;
using EServiceFunctions.RequestResponseModels.MHDS;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using static System.Text.Json.JsonSerializer;
using static EServiceFunctions.Helpers.UtilityHelper;

namespace UnitTestEServiceFunctions;

[ExcludeFromCodeCoverage]
public class UnitTestHealthCardValidation(ITestOutputHelper outputHelper)
{
    private readonly ILogger<AppointmentFunctions> _appointmentLogger = Mock.Of<ILogger<AppointmentFunctions>>();
    private readonly ILogger<AssignmentFunctions> _assignmentLogger = Mock.Of<ILogger<AssignmentFunctions>>();
    private readonly ILogger<TransferFunctions> _transferLogger = Mock.Of<ILogger<TransferFunctions>>();
    private readonly ILogger<MhdsFunctions> _mhdsLogger = Mock.Of<ILogger<MhdsFunctions>>();

    #region Health Card Validation Tests for Appointment Functions

    [Fact]
    public async Task CreateAppointment_WithMissingHealthCard_ShouldReturnBadRequest()
    {
        // Arrange
        var appointmentDbContextFactory = CreateMockDbContextFactory<AppointmentContext>();
        var edwDbContextFactory = CreateMockDbContextFactory<EDWDbContext>();
        var hmcLiveFeedsContextFactory = CreateMockDbContextFactory<EServiceFunctions.Models.HmcLiveFeeds.HmcLiveFeedContext>();

        var appointmentFunctions = new AppointmentFunctions(
            appointmentDbContextFactory.Object,
            edwDbContextFactory.Object,
            hmcLiveFeedsContextFactory.Object,
            _appointmentLogger);

        var request = CreateMockHttpRequest(CreateAppointmentRequestWithoutHealthCard());

        // Act
        var response = await appointmentFunctions.CreateAppointment(request);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No is missing for QID", responseBody);
        Assert.Contains("and request name Appointment", responseBody);
        
        LogResponse(outputHelper, response);
    }

    [Fact]
    public async Task CreateAppointment_WithInvalidHealthCardFormat_ShouldReturnBadRequest()
    {
        // Arrange
        var appointmentDbContextFactory = CreateMockDbContextFactory<AppointmentContext>();
        var edwDbContextFactory = CreateMockDbContextFactory<EDWDbContext>();
        var hmcLiveFeedsContextFactory = CreateMockDbContextFactory<EServiceFunctions.Models.HmcLiveFeeds.HmcLiveFeedContext>();

        var appointmentFunctions = new AppointmentFunctions(
            appointmentDbContextFactory.Object,
            edwDbContextFactory.Object,
            hmcLiveFeedsContextFactory.Object,
            _appointmentLogger);

        var request = CreateMockHttpRequest(CreateAppointmentRequestWithInvalidHealthCard());

        // Act
        var response = await appointmentFunctions.CreateAppointment(request);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No format is invalid for QID", responseBody);
        Assert.Contains("and request name Appointment", responseBody);
        
        LogResponse(outputHelper, response);
    }

    [Fact]
    public async Task UpdateAppointmentByReqNumber_WithMissingHealthCard_ShouldReturnBadRequest()
    {
        // Arrange
        var appointmentDbContextFactory = CreateMockDbContextFactory<AppointmentContext>();
        var edwDbContextFactory = CreateMockDbContextFactory<EDWDbContext>();
        var hmcLiveFeedsContextFactory = CreateMockDbContextFactory<EServiceFunctions.Models.HmcLiveFeeds.HmcLiveFeedContext>();

        var appointmentFunctions = new AppointmentFunctions(
            appointmentDbContextFactory.Object,
            edwDbContextFactory.Object,
            hmcLiveFeedsContextFactory.Object,
            _appointmentLogger);

        var request = CreateMockHttpRequest(CreateAppointmentRequestWithoutHealthCard());

        // Act
        var response = await appointmentFunctions.UpdateAppointmentByReqNumber(request, "TEST123");

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No is missing for QID", responseBody);
        
        LogResponse(outputHelper, response);
    }

    #endregion

    #region Health Card Validation Tests for Assignment Functions

    [Fact]
    public async Task CreateAssignment_WithMissingHealthCard_ShouldReturnBadRequest()
    {
        // Arrange
        var assignmentDbContextFactory = CreateMockDbContextFactory<AssignmentContext>();
        var assignmentFunctions = new AssignmentFunctions(assignmentDbContextFactory.Object, _assignmentLogger);

        var request = CreateMockHttpRequest(CreateAssignmentRequestWithoutHealthCard());

        // Act
        var response = await assignmentFunctions.CreateAssignment(request);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No is missing for QID", responseBody);
        Assert.Contains("and request name Assignment", responseBody);
        
        LogResponse(outputHelper, response);
    }

    [Fact]
    public async Task CreateAssignment_WithInvalidHealthCardFormat_ShouldReturnBadRequest()
    {
        // Arrange
        var assignmentDbContextFactory = CreateMockDbContextFactory<AssignmentContext>();
        var assignmentFunctions = new AssignmentFunctions(assignmentDbContextFactory.Object, _assignmentLogger);

        var request = CreateMockHttpRequest(CreateAssignmentRequestWithInvalidHealthCard());

        // Act
        var response = await assignmentFunctions.CreateAssignment(request);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No format is invalid for QID", responseBody);
        Assert.Contains("and request name Assignment", responseBody);
        
        LogResponse(outputHelper, response);
    }

    #endregion

    #region Health Card Validation Tests for Transfer Functions

    [Fact]
    public async Task CreateTransfer_WithMissingHealthCard_ShouldReturnBadRequest()
    {
        // Arrange
        var transferDbContextFactory = CreateMockDbContextFactory<TransferContext>();
        var transferFunctions = new TransferFunctions(transferDbContextFactory.Object, _transferLogger);

        var request = CreateMockHttpRequest(CreateTransferRequestWithoutHealthCard());

        // Act
        var response = await transferFunctions.CreateTransfer(request);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No is missing for QID", responseBody);
        Assert.Contains("and request name Transfer", responseBody);
        
        LogResponse(outputHelper, response);
    }

    [Fact]
    public async Task UpdateTransferByReqNumber_WithMissingHealthCard_ShouldReturnBadRequest()
    {
        // Arrange
        var transferDbContextFactory = CreateMockDbContextFactory<TransferContext>();
        var transferFunctions = new TransferFunctions(transferDbContextFactory.Object, _transferLogger);

        var request = CreateMockHttpRequest(CreateTransferRequestWithoutHealthCard());

        // Act
        var response = await transferFunctions.UpdateTransferByReqNumber(request, "TEST123");

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No is missing for QID", responseBody);
        
        LogResponse(outputHelper, response);
    }

    #endregion

    #region Health Card Validation Tests for MHDS Functions

    [Fact]
    public async Task CreateMhdsRequestAsync_WithMissingHealthCard_ShouldReturnBadRequest()
    {
        // Arrange
        var mhdsDbContextFactory = CreateMockDbContextFactory<MHDSContext>();
        var edwDbContextFactory = CreateMockDbContextFactory<EDWDbContext>();
        var mhdsFunctions = new MhdsFunctions(mhdsDbContextFactory.Object, edwDbContextFactory.Object, _mhdsLogger);

        var request = CreateMockHttpRequest(CreateMHDSRequestWithoutHealthCard());

        // Act
        var response = await mhdsFunctions.CreateMhdsRequestAsync(request);

        // Assert
        Assert.NotNull(response);
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
        
        var responseBody = await GetResponseBody(response);
        Assert.Contains("Health Card No is missing for QID", responseBody);
        Assert.Contains("and request name MHDS", responseBody);
        
        LogResponse(outputHelper, response);
    }

    #endregion

    #region Health Card Format Validation Tests

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task ValidateHealthCardNumberAsync_WithEmptyOrNullHealthCard_ShouldReturnInvalid(string? healthCard)
    {
        // Arrange
        var functionContext = Mock.Of<FunctionContext>();
        var request = new MockHttpRequestData(functionContext);
        request.Headers.Add("X-RequestOrigin", "UnitTest");
        var logger = Mock.Of<ILogger<object>>();

        // Act
        var result = await ValidateHealthCardNumberAsync(healthCard, 12345678901, "Test", request, logger);

        // Assert
        Assert.False(result.IsValid);
        Assert.NotNull(result.ErrorResponse);
    }

    [Theory]
    [InlineData("1234")] // Too short
    [InlineData("123456789012345678901")] // Too long
    [InlineData("HC@123")] // Invalid characters
    [InlineData("HC 123")] // Contains space
    public async Task ValidateHealthCardNumberAsync_WithInvalidFormat_ShouldReturnInvalid(string healthCard)
    {
        // Arrange
        var functionContext = Mock.Of<FunctionContext>();
        var request = new MockHttpRequestData(functionContext);
        request.Headers.Add("X-RequestOrigin", "UnitTest");
        var logger = Mock.Of<ILogger<object>>();

        // Act
        var result = await ValidateHealthCardNumberAsync(healthCard, 12345678901, "Test", request, logger);

        // Assert
        Assert.False(result.IsValid);
        Assert.NotNull(result.ErrorResponse);
    }

    [Theory]
    [InlineData("HC123456")]
    [InlineData("12345678")]
    [InlineData("ABC123DEF")]
    [InlineData("HC02288616")]
    public async Task ValidateHealthCardNumberAsync_WithValidFormat_ShouldReturnValid(string healthCard)
    {
        // Arrange
        var functionContext = Mock.Of<FunctionContext>();
        var request = new MockHttpRequestData(functionContext);
        request.Headers.Add("X-RequestOrigin", "UnitTest");
        var logger = Mock.Of<ILogger<object>>();

        // Act
        var result = await ValidateHealthCardNumberAsync(healthCard, 12345678901, "Test", request, logger);

        // Assert
        Assert.True(result.IsValid);
        Assert.Null(result.ErrorResponse);
    }

    #endregion

    #region Logging Verification Tests

    [Fact]
    public async Task CreateAppointment_WithMissingHealthCard_ShouldLogValidationFailure()
    {
        // Arrange
        var appointmentDbContextFactory = CreateMockDbContextFactory<AppointmentContext>();
        var edwDbContextFactory = CreateMockDbContextFactory<EDWDbContext>();
        var hmcLiveFeedsContextFactory = CreateMockDbContextFactory<EServiceFunctions.Models.HmcLiveFeeds.HmcLiveFeedContext>();

        var mockLogger = new Mock<ILogger<AppointmentFunctions>>();
        var appointmentFunctions = new AppointmentFunctions(
            appointmentDbContextFactory.Object,
            edwDbContextFactory.Object,
            hmcLiveFeedsContextFactory.Object,
            mockLogger.Object);

        var request = CreateMockHttpRequest(CreateAppointmentRequestWithoutHealthCard());

        // Act
        await appointmentFunctions.CreateAppointment(request);

        // Assert
        mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Health Card validation failed - Missing")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task CreateAssignment_WithInvalidHealthCard_ShouldLogValidationFailure()
    {
        // Arrange
        var assignmentDbContextFactory = CreateMockDbContextFactory<AssignmentContext>();
        var mockLogger = new Mock<ILogger<AssignmentFunctions>>();
        var assignmentFunctions = new AssignmentFunctions(assignmentDbContextFactory.Object, mockLogger.Object);

        var request = CreateMockHttpRequest(CreateAssignmentRequestWithInvalidHealthCard());

        // Act
        await assignmentFunctions.CreateAssignment(request);

        // Assert
        mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Health Card validation failed - Invalid Format")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion

    #region Error Message Format Tests

    [Fact]
    public async Task CreateTransfer_WithMissingHealthCard_ShouldReturnCorrectErrorMessageFormat()
    {
        // Arrange
        var transferDbContextFactory = CreateMockDbContextFactory<TransferContext>();
        var transferFunctions = new TransferFunctions(transferDbContextFactory.Object, _transferLogger);

        var testQId = 98765432109L;
        var requestBody = new CreateUpdateTransferRequest
        {
            QId = testQId,
            SubmitterQId = testQId,
            HCNumber = null,
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678"
        };

        var request = CreateMockHttpRequest(requestBody);

        // Act
        var response = await transferFunctions.CreateTransfer(request);

        // Assert
        var responseBody = await GetResponseBody(response);
        var expectedMessage = $"Health Card No is missing for QID {testQId} and request name Transfer";
        Assert.Contains(expectedMessage, responseBody);
    }

    [Fact]
    public async Task CreateMhdsRequestAsync_WithInvalidHealthCard_ShouldReturnCorrectErrorMessageFormat()
    {
        // Arrange
        var mhdsDbContextFactory = CreateMockDbContextFactory<MHDSContext>();
        var edwDbContextFactory = CreateMockDbContextFactory<EDWDbContext>();
        var mhdsFunctions = new MhdsFunctions(mhdsDbContextFactory.Object, edwDbContextFactory.Object, _mhdsLogger);

        var testQId = 11111111111L;
        var requestBody = new CreateMHDSRequest
        {
            QId = testQId,
            SubmitterQId = testQId,
            HCNumber = "XX", // Invalid format
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678",
            Consent = true
        };

        var request = CreateMockHttpRequest(requestBody);

        // Act
        var response = await mhdsFunctions.CreateMhdsRequestAsync(request);

        // Assert
        var responseBody = await GetResponseBody(response);
        var expectedMessage = $"Health Card No format is invalid for QID {testQId} and request name MHDS";
        Assert.Contains(expectedMessage, responseBody);
    }

    #endregion

    #region Helper Methods

    private static Mock<IDbContextFactory<T>> CreateMockDbContextFactory<T>() where T : DbContext
    {
        return new Mock<IDbContextFactory<T>>();
    }

    private static HttpRequestData CreateMockHttpRequest(object requestBody)
    {
        var functionContext = Mock.Of<FunctionContext>();
        var json = Serialize(requestBody);
        var bodyStream = new MemoryStream(Encoding.UTF8.GetBytes(json));
        
        var request = new MockHttpRequestData(functionContext, bodyStream, "POST", "https://localhost/test");
        request.Headers.Add("Content-Type", "application/json");
        request.Headers.Add("X-RequestOrigin", "UnitTest");
        request.Headers.Add("X-JWT-Claims-QID", "12345678901");
        request.Headers.Add("X-ClinicCodes", "TEST");
        request.Headers.Add("IsAuthValReq", "true");
        
        return request;
    }

    private static async Task<string> GetResponseBody(HttpResponseData response)
    {
        response.Body.Position = 0;
        using var reader = new StreamReader(response.Body);
        return await reader.ReadToEndAsync();
    }

    private static CreateUpdateAppointmentRequest CreateAppointmentRequestWithoutHealthCard()
    {
        return new CreateUpdateAppointmentRequest
        {
            QId = 12345678901,
            SubmitterQId = 12345678901,
            HcNumber = null, // Missing health card
            Clinic = "TEST",
            RequestType = "New",
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678"
        };
    }

    private static CreateUpdateAppointmentRequest CreateAppointmentRequestWithInvalidHealthCard()
    {
        return new CreateUpdateAppointmentRequest
        {
            QId = 12345678901,
            SubmitterQId = 12345678901,
            HcNumber = "123", // Invalid format (too short)
            Clinic = "TEST",
            RequestType = "New",
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678"
        };
    }

    private static CreateUpdateAssignmentRequest CreateAssignmentRequestWithoutHealthCard()
    {
        return new CreateUpdateAssignmentRequest
        {
            QId = 12345678901,
            SubmitterQId = 12345678901,
            HCNumber = null, // Missing health card
            ChangeReason = "Test reason",
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678"
        };
    }

    private static CreateUpdateAssignmentRequest CreateAssignmentRequestWithInvalidHealthCard()
    {
        return new CreateUpdateAssignmentRequest
        {
            QId = 12345678901,
            SubmitterQId = 12345678901,
            HCNumber = "AB", // Invalid format (too short)
            ChangeReason = "Test reason",
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678"
        };
    }

    private static CreateUpdateTransferRequest CreateTransferRequestWithoutHealthCard()
    {
        return new CreateUpdateTransferRequest
        {
            QId = 12345678901,
            SubmitterQId = 12345678901,
            HCNumber = null, // Missing health card
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678"
        };
    }

    private static CreateMHDSRequest CreateMHDSRequestWithoutHealthCard()
    {
        return new CreateMHDSRequest
        {
            QId = 12345678901,
            SubmitterQId = 12345678901,
            HCNumber = null, // Missing health card
            SubmitterEmail = "<EMAIL>",
            SubmitterMobile = "12345678",
            Consent = true
        };
    }

    #endregion
}
