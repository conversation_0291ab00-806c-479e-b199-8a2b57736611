﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.FamilyPhysician;

[Table("MRD_Physician_Specialty")]
public class MRD_Physician_Specialty
{
    [Key]
    [StringLength(3)]
    [Unicode(false)]
    public string SPECIALTY_CODE { get; set; } = null!;
    [StringLength(100)]
    [Unicode(false)]
    public string? SPECIALTY_NAME_EN { get; set; }
    [StringLength(100)]
    public string? SPECIALTY_NAME_ARB { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LAST_LOAD_DATE { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LAST_UPDATE_DATE { get; set; }
}