# Attachment Validation Tests

## Overview

These tests verify the validation rules and error handling for attachment-related operations. The test suite covers file validation, security checks, and storage constraints.

## Test Cases

### TestUploadAttachment_InvalidSize_ReturnsBadRequest

Tests validation of file size limits.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnhappyPath)]
public async Task TestUploadAttachment_InvalidSize_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest("attachments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    
    var filePath = CreateLargeTestFile(MaxFileSize + 1024);
    request.AddFile("file", filePath, "application/pdf");
    request.AddParameter("entityId", "TEST-123");

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    False(response.IsSuccessful);
    Equal(BadRequest, response.StatusCode);
    Contains($"File size exceeds maximum limit of {MaxFileSize} bytes", response.Content);
}
```

### TestUploadAttachment_InvalidType_ReturnsBadRequest

Tests validation of file type restrictions.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnhappyPath)]
public async Task TestUploadAttachment_InvalidType_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest("attachments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    
    var filePath = CreateTestFile("test.exe");
    request.AddFile("file", filePath, "application/x-msdownload");
    request.AddParameter("entityId", "TEST-123");

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    False(response.IsSuccessful);
    Equal(BadRequest, response.StatusCode);
    Contains("File type not allowed", response.Content);
}
```

## Validation Rules

### 1. File Validation
```csharp
public class AttachmentValidator : AbstractValidator<IFormFile>
{
    public AttachmentValidator()
    {
        RuleFor(x => x.Length)
            .NotNull()
            .WithMessage("File is required")
            .GreaterThan(0)
            .WithMessage("File cannot be empty")
            .LessThanOrEqualTo(MaxFileSize)
            .WithMessage($"File size cannot exceed {MaxFileSize} bytes");

        RuleFor(x => x.ContentType)
            .NotEmpty()
            .WithMessage("Content type is required")
            .Must(BeAllowedContentType)
            .WithMessage("File type not allowed");

        RuleFor(x => x.FileName)
            .NotEmpty()
            .WithMessage("Filename is required")
            .Must(BeValidFileName)
            .WithMessage("Invalid filename");
    }

    private bool BeAllowedContentType(string contentType)
    {
        return AllowedContentTypes.Contains(contentType.ToLower());
    }

    private bool BeValidFileName(string fileName)
    {
        return !string.IsNullOrEmpty(fileName) &&
               fileName.Length <= MaxFileNameLength &&
               !fileName.Contains("..") &&
               !Path.GetInvalidFileNameChars().Any(c => fileName.Contains(c));
    }
}
```

### 2. Security Validation
```csharp
public class AttachmentSecurityValidator
{
    private readonly IAntiVirusService _antiVirusService;
    private readonly IFileSignatureValidator _signatureValidator;

    public async Task ValidateFileSecurity(IFormFile file)
    {
        // Check for malware
        var scanResult = await _antiVirusService.ScanFileAsync(file);
        if (!scanResult.IsClean)
            throw new SecurityException($"File failed security scan: {scanResult.Reason}");

        // Validate file signature
        if (!await _signatureValidator.ValidateSignature(file))
            throw new SecurityException("File signature validation failed");

        // Check for executable content
        if (await ContainsExecutableContent(file))
            throw new SecurityException("File contains executable content");

        // Validate file extension matches content
        if (!await ValidateFileExtension(file))
            throw new SecurityException("File extension does not match content");
    }

    private async Task<bool> ContainsExecutableContent(IFormFile file)
    {
        using var stream = file.OpenReadStream();
        var buffer = new byte[4096];
        var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

        // Check for executable signatures
        return buffer.Take(bytesRead).Any(b => ExecutableSignatures.Contains(b));
    }

    private async Task<bool> ValidateFileExtension(IFormFile file)
    {
        var extension = Path.GetExtension(file.FileName).ToLower();
        var contentType = file.ContentType.ToLower();

        return ContentTypeMap.TryGetValue(extension, out var allowedTypes) &&
               allowedTypes.Contains(contentType);
    }
}
```

### 3. Storage Validation
```csharp
public class StorageValidator
{
    private readonly IStorageService _storageService;
    private readonly IAttachmentRepository _attachmentRepository;

    public async Task ValidateStorage(IFormFile file)
    {
        // Check total storage usage
        var currentUsage = await _storageService.GetCurrentUsage();
        if (currentUsage + file.Length > MaxTotalStorage)
            throw new StorageException("Storage quota exceeded");

        // Check user storage quota
        var user = GetCurrentUser();
        var userUsage = await _storageService.GetUserUsage(user.Id);
        if (userUsage + file.Length > UserStorageQuota)
            throw new StorageException("User storage quota exceeded");

        // Check entity storage limit
        var entityId = GetEntityId();
        var entityUsage = await _storageService.GetEntityUsage(entityId);
        if (entityUsage + file.Length > EntityStorageLimit)
            throw new StorageException("Entity storage limit exceeded");

        // Validate storage availability
        if (!await _storageService.HasAvailableSpace(file.Length))
            throw new StorageException("Insufficient storage space");
    }
}
```

## Error Handling

### 1. Validation Error Handler
```csharp
public class AttachmentValidationErrorHandler
{
    private readonly ILogger<AttachmentValidationErrorHandler> _logger;

    public async Task<ErrorResponse> HandleValidationError(
        ValidationException ex)
    {
        _logger.LogWarning(
            "Attachment validation failed: {Message}",
            ex.Message
        );

        return new ErrorResponse
        {
            Code = "VALIDATION_ERROR",
            Message = "File validation failed",
            Details = ex.Errors
                .Select(e => new ValidationError
                {
                    Field = e.PropertyName,
                    Message = e.ErrorMessage
                })
                .ToList()
        };
    }

    public async Task<ErrorResponse> HandleSecurityError(
        SecurityException ex)
    {
        _logger.LogError(
            "Attachment security check failed: {Message}",
            ex.Message
        );

        await NotifySecurityViolation(ex);

        return new ErrorResponse
        {
            Code = "SECURITY_ERROR",
            Message = "File security check failed"
        };
    }

    public async Task<ErrorResponse> HandleStorageError(
        StorageException ex)
    {
        _logger.LogError(
            "Storage validation failed: {Message}",
            ex.Message
        );

        await NotifyStorageIssue(ex);

        return new ErrorResponse
        {
            Code = "STORAGE_ERROR",
            Message = ex.Message
        };
    }
}
```

### 2. Error Response Models
```csharp
public class ValidationError
{
    public string Field { get; set; }
    public string Message { get; set; }
}

public class ErrorResponse
{
    public string Code { get; set; }
    public string Message { get; set; }
    public List<ValidationError> Details { get; set; }
}
```

## Test Helper Methods

### 1. File Generation
```csharp
public class AttachmentTestHelper
{
    public string CreateTestFile(long size, string contentType = "application/pdf")
    {
        var path = Path.GetTempFileName();
        using (var fs = File.Create(path))
        {
            fs.SetLength(size);
            
            // Add file signature based on content type
            var signature = GetFileSignature(contentType);
            fs.Write(signature, 0, signature.Length);
        }
        return path;
    }

    public string CreateMaliciousFile()
    {
        var path = Path.GetTempFileName();
        using (var fs = File.Create(path))
        {
            var content = Encoding.UTF8.GetBytes("<script>alert('xss')</script>");
            fs.Write(content, 0, content.Length);
        }
        return path;
    }

    private byte[] GetFileSignature(string contentType)
    {
        return contentType switch
        {
            "application/pdf" => new byte[] { 0x25, 0x50, 0x44, 0x46 },
            "image/jpeg" => new byte[] { 0xFF, 0xD8, 0xFF },
            "image/png" => new byte[] { 0x89, 0x50, 0x4E, 0x47 },
            _ => Array.Empty<byte>()
        };
    }
}
```

### 2. Validation Testing
```csharp
public class AttachmentValidationTester
{
    private readonly AttachmentValidator _validator;
    private readonly AttachmentSecurityValidator _securityValidator;
    private readonly StorageValidator _storageValidator;

    public async Task<ValidationResult> ValidateAttachment(IFormFile file)
    {
        try
        {
            // Basic validation
            var validationResult = await _validator.ValidateAsync(file);
            if (!validationResult.IsValid)
                return validationResult;

            // Security validation
            await _securityValidator.ValidateFileSecurity(file);

            // Storage validation
            await _storageValidator.ValidateStorage(file);

            return ValidationResult.Success;
        }
        catch (Exception ex)
        {
            return ValidationResult.Fail(ex.Message);
        }
    }
}
```

## Notes

1. **File Validation**
   - Check file size
   - Validate content type
   - Verify file integrity

2. **Security**
   - Scan for malware
   - Check file signatures
   - Prevent malicious content

3. **Storage**
   - Monitor quotas
   - Track usage
   - Handle cleanup

## Recommendations

1. **Validation Strategy**
   - Implement strict validation
   - Use proper error handling
   - Add comprehensive logging

2. **Security Measures**
   - Regular security updates
   - Multiple validation layers
   - Proper error handling

3. **Performance**
   - Optimize validation
   - Handle large files
   - Implement caching
