# Get Registration Item Tests

## Test Cases

### 1. TestGetRegistrationItemByReqNumber

Tests retrieval of registration item by request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetRegistrationItemByReqNumber()
{
    // Arrange
    var request = GetRestRequest("registrations/{reqNumber}");
    request.AddUrlSegment("reqNumber", "396OAJU24");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

    // Act
    var response = await _client.GetAsync<GetRegistrationItemResponse>(request);
    response.ThrowIfNull();

    // Assert
    Equal("396OAJU24", response.ReqNumber);
    True(response.QId > 0);
}
```

### 2. TestGetRegistrationItemByReqNumberWithInvalidReqNumber

Tests item retrieval with invalid request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetRegistrationItemByReqNumberWithInvalidReqNumber()
{
    // Arrange
    var request = GetRestRequest("registrations/{reqNumber}");
    request.AddUrlSegment("reqNumber", "REG123UMAR1");

    // Act
    var response = await _client.GetAsync<GetRegistrationItemResponse>(request);

    // Assert
    Null(response);
}
```

## Request Details

### Endpoint
```
GET registrations/{reqNumber}
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `reqNumber`: Registration request number

## Response Model

### GetRegistrationItemResponse
```csharp
public class GetRegistrationItemResponse
{
    public string ReqNumber { get; set; }
    public long QId { get; set; }
    // Other registration details
}
```

## Test Data

### Success Case
- Request Number: "396OAJU24"
- JWT Claims QID: 22222222270
- Expected:
  * Matching request number
  * Valid QID

### Error Case
- Request Number: "REG123UMAR1"
- Expected: Null response

## Validation Rules

### Success Case Validation
1. Response not null
2. Request number matches
3. QID is valid

### Error Case Validation
1. Response is null

## Error Cases

1. **Invalid Request Number**
   - Non-existent request
   - Malformed number
   - Invalid format

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - Unauthorized access

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID verification

2. **Response Format**
   - Complete registration details
   - Request number validation
   - QID verification

3. **Performance**
   - Single record retrieval
   - Efficient query
   - Quick response time
