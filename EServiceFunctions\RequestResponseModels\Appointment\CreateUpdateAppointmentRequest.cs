﻿namespace EServiceFunctions.RequestResponseModels.Appointment;

public class CreateUpdateAppointmentRequest
{
	public long QId { get; set; }
	public string? FNameEn { get; set; }
	public string? MNameEn { get; set; }
	public string? LNameEn { get; set; }
	public string? FNameAr { get; set; }
	public string? MNameAr { get; set; }
	public string? LNameAr { get; set; }
	public string? Gender { get; set; }
	public string? SecondaryPhoneMobile { get; set; }
	public bool CancellationCall { get; set; }
	public bool Consent { get; set; }
	public string? Nationality { get; set; }
	public DateTime? Dob { get; set; }
	public string? HcNumber { get; set; }
	public string? Clinic { get; set; }  
	public string? ClinicTime { get; set; }
	public DateTime? PrefDate { get; set; }
	public string? AmPm { get; set; }
	public string? SelectedHc { get; set; }
	public string? RequestType { get; set; }
	public string? PrefContactTime { get; set; }
	public string? SubmittedBy { get; set; }
	public DateTime? SubmittedAt { get; set; } = GetCurrentTime();
	public long? SubmitterQId { get; set; }
	public string? SubmitterEmail { get; set; }
	public string? SubmitterMobile { get; set; }
	public long? AppointmentId { get; set; }
}