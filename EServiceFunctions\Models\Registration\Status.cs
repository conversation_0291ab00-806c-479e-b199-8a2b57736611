﻿namespace EServiceFunctions.Models.Registration;

public class Status
{
    public Status()
    {
        Registration = new HashSet<Registration>();
    }

    public string? Code { get; set; }
    public string? DescriptionEn { get; set; }
    public string? DescriptionAr { get; set; }
    public string? Category { get; set; }

    public virtual ICollection<Registration> Registration { get; set; }
}