using EServiceFunctions.Models.ReleaseItem;
using EServiceFunctions.RequestResponseModels.ReleaseItem;

namespace EServiceFunctions.Functions;

public class ReleaseItemFunctions(
    IDbContextFactory<ReleaseItemContext> dbContextFactory,
    ILogger<ReleaseItemFunctions> logger)
{
    #region ReleaseItemFunctions
    
    [Function("GetReleaseItemList")]
    [OpenApiOperation(operationId: "GetReleaseItemList", tags: ["DropDownList"],
        Summary = "Get Release Item List", Description = " Get the release item list")]
    [OpenApiParameter("itemCode", Description = "Release Item Code", In = ParameterLocation.Query, Required = false,
        Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetReleaseItemListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> ReleaseItem(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "releaseitems")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Getting Release Item List");

            string itemCode = req.GetCleanedQueryString("itemCode"); 
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.ReleaseItem!.AsQueryable();

            if (!string.IsNullOrWhiteSpace(itemCode))
            {
                query = query.Where(item => item.ItemCode! == itemCode);
            }

            var response = await query.Select(item => new GetReleaseItemListResponse
            {
                ItemCode = item.ItemCode,
                ItemNameEn = item.ItemNameEn,
                ItemNameAr = item.ItemNameAr,
                CategoryCode = item.CategoryCode,
                CategoryNameEn = item.CategoryNameEn,
                CategoryNameAr = item.CategoryNameAr
            }).AsNoTracking().OrderBy(p => p.ItemCode).Skip(skip).Take(take).ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found");
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Release Item List ({ResponseCount})", response.Count);
            
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}