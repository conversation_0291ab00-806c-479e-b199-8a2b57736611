# Transfer Functions API Documentation

## Overview
The Transfer module provides a comprehensive set of Azure Functions for managing transfer requests in the EServices system. This module handles the creation, retrieval, updating, and deletion of transfer requests with proper authorization, multilingual support, and transfer reason management.

## API Endpoints

### Get Transfer List By QID
- **Endpoint**: `GET /transfers/submitter-qid/{qId}`
- **Description**: Retrieves a list of transfer requests for a specific submitter
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): Submitter's QID (long)
  - `status` (query, optional): Filter by status category
    - InProcess: Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested
    - Archived: Approved, Cancelled, CancelledByEServ
  - `skip` (query, optional): Number of records to skip
  - `take` (query, optional): Number of records to take
- **Response Format**:
  ```json
  [
    {
      "QId": "number",
      "ReqNumber": "string",
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "Status": "string",
      "StatusDescriptionEn": "string",
      "StatusDescriptionAr": "string",
      "SubmittedAt": "string"
    }
  ]
  ```
- **Responses**:
  - 200: Success (List<GetTransferListResponse>)
  - 204: No Content
  - 400: Bad Request
  - 401: Unauthorized

### Get Transfer Stats By QID
- **Endpoint**: `GET /transfers/submitter-qid/{qId}/stats`
- **Description**: Retrieves statistics about transfer requests for a specific submitter
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): Submitter's QID (long)
  - `status` (query, optional): Filter by status category
- **Response Format**:
  ```json
  {
    "Count": "number"
  }
  ```
- **Responses**:
  - 200: Success (GetTransferStatsResponse)
  - 204: No Content
  - 400: Bad Request
  - 401: Unauthorized

### Check InProcess Transfer By QID
- **Endpoint**: `GET /transfers/{qId}/inprocess-validation`
- **Description**: Checks if there are any in-process transfer requests for a specific QID
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): Requester's QID (long)
- **Response Format**:
  ```json
  {
    "IsInprocessExist": "boolean"
  }
  ```
- **Responses**:
  - 200: Success (CheckInProcessTransferResponse)
  - 400: Bad Request

### Get Transfer Item By Request Number
- **Endpoint**: `GET /transfers/{reqNumber}`
- **Description**: Retrieves detailed information about a specific transfer request
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `reqNumber` (path, required): Transfer Request Number
- **Response Format**:
  ```json
  {
    "QId": "number",
    "ReqNumber": "string",
    "PersonalDetails": {
      "NameEn": "string",
      "NameAr": "string"
    },
    "TransferReason": "string",
    "TransferReasonDescriptionEn": "string",
    "TransferReasonDescriptionAr": "string",
    "Status": "string",
    "StatusDescriptionEn": "string",
    "StatusDescriptionAr": "string"
  }
  ```
- **Responses**:
  - 200: Success (GetTransferItemResponse)
  - 204: No Content
  - 400: Bad Request
  - 401: Unauthorized

### Create Transfer
- **Endpoint**: `POST /transfers`
- **Description**: Creates a new transfer request
- **Authorization**: Function-level authorization required
- **Headers**:
  - `X-RequestOrigin` (required): Source of the request
- **Request Body**:
  ```json
  {
    "SubmitterQId": "number",
    "PersonalDetails": {
      "NameEn": "string",
      "NameAr": "string"
    },
    "TransferReason": "string"
  }
  ```
- **Responses**:
  - 201: Created (Transfer)
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error
- **Implementation Notes**:
  - Automatically generates request number
  - Sets initial status as "Saved"
  - Records creation timestamp and source

### Update Transfer By Request Number
- **Endpoint**: `PUT /transfers/{reqNumber}`
- **Description**: Updates an existing transfer request
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `reqNumber` (path, required): Transfer Request Number
- **Headers**:
  - `X-RequestOrigin` (required): Source of the request
- **Request Body**: Same as Create Transfer
- **Responses**:
  - 200: Success
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error
- **Notes**:
  - Maintains audit trail with update timestamps
  - Preserves original creation metadata

### Delete Transfer By Request Number
- **Endpoint**: `DELETE /transfers/{reqNumber}`
- **Description**: Deletes a transfer request (Internal use only)
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `reqNumber` (path, required): Transfer Request Number
- **Responses**:
  - 200: Success ("Deleted Successfully")
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error
- **Notes**:
  - OpenAPI ignore flag for internal use
  - Complete removal from database

### Get Transfer Reason List
- **Endpoint**: `GET /transfersreason`
- **Description**: Retrieves list of available transfer reasons
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `reasonCode` (query, optional): Filter by specific reason code
  - `skip` (query, optional): Number of records to skip
  - `take` (query, optional): Number of records to take
- **Response Format**:
  ```json
  [
    {
      "ReasonCode": "string",
      "ReasonEn": "string",
      "ReasonAr": "string"
    }
  ]
  ```
- **Responses**:
  - 200: Success (List<GetTransferReasonListResponse>)
  - 204: No Content
  - 400: Bad Request

## Technical Implementation Details

### Data Sources
- Transfer Context
  - Transfer table
  - Status table
  - TransferReason table

### Security
- QID-based authorization
- Request origin validation
- Submitter validation
- Internal-only endpoints protection

### Error Handling
- Comprehensive exception logging
- Standardized error responses
- Database transaction handling
- Input validation

### Performance Considerations
- Asynchronous operations
- No-tracking queries for read operations
- Pagination support
- Efficient status filtering
- Database transaction optimization

## Response Models

### GetTransferListResponse
- Basic transfer information
- Status details
- Bilingual name fields
- Submission timestamp

### GetTransferStatsResponse
- Transfer request count

### CheckInProcessTransferResponse
- InProcess status flag

### GetTransferItemResponse
- Detailed transfer information
- Transfer reason details
- Status information
- Bilingual fields

### GetTransferReasonListResponse
- Transfer reason code
- Bilingual reason descriptions

### CreateUpdateTransferRequest
- Submitter information
- Personal details
- Transfer reason
- Optional fields for updates

## Status Categories
- **InProcess**:
  - Submitted
  - Rework
  - Reworked
  - ConditionallyApproved
  - ResubmitOriginalsRequested
- **Archived**:
  - Approved
  - Cancelled
  - CancelledByEServ

## Dependencies
- Entity Framework Core
- Azure Functions
- OpenAPI/Swagger
- Custom extensions for request handling

## Design Patterns
- Clean Architecture principles
- SOLID principles
- Repository Pattern
- Dependency Injection
- Async/await patterns

## Multilingual Support
- Bilingual descriptions (English/Arabic)
- Name fields in both languages
- Status descriptions in both languages
- Transfer reason descriptions in both languages
- Consistent naming conventions

## Performance Features
- Connection pooling
- Efficient data projections
- AsNoTracking for read operations
- Proper resource disposal
- Optimized queries
- Early validation
- Efficient pagination

## Error Handling
- Standardized error responses
- Comprehensive logging
- User-friendly messages
- Exception tracking
- Transaction management
- Proper null handling

## Security Features
- QID validation
- Claims validation
- Request origin verification
- Input sanitization
- Authorization checks
- Data access control

## Future Improvements
1. Enhanced caching mechanism
2. Batch operation support
3. Advanced search capabilities
4. Real-time notifications
5. Audit trail enhancements
6. Performance monitoring
7. Advanced analytics

## Best Practices
1. Consistent error handling
2. Proper logging
3. Input validation
4. Security checks
5. Performance optimization
6. Resource management
7. Transaction handling
8. Claims-based security

## API Versioning
Current version: Implicit v1
Future considerations:
- URI versioning
- Header versioning
- Query parameter versioning