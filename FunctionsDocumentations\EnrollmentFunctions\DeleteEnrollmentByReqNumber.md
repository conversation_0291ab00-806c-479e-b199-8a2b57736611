# DeleteEnrollmentByReqNumber

## Overview
Deletes an enrollment for a given request number.

## Endpoint
- **Route**: `enrollments/{reqNumber}`
- **Method**: DELETE
- **Authorization**: Function level with submitter QId validation
- **Note**: This endpoint is marked with OpenApiIgnore

## Parameters
- **reqNumber** (path, required): Enrollment Request Number

## Response
- **200 OK**: Returns "Deleted Successfully"
- **400 Bad Request**: Invalid request number
- **401 Unauthorized**: Invalid submitter QId access
- **500 Internal Server Error**: Database operation failure

## Business Logic
1. Extracts submitter claims from request
2. Retrieves enrollment by request number
3. Validates enrollment exists
4. Validates ownership (submitter QId match)
5. Deletes enrollment record
6. Returns success message

## Security Considerations
- Validates submitter authorization
- Ensures only owner can delete
- Protected by function-level authorization

## Error Handling
- Validates request number existence
- Proper DB exception handling
- Appropriate error responses
