﻿--WI#1004
IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Shift'
             AND type = 'U')
    DROP TABLE [Shift]; -- MD
GO
CREATE TABLE [Shift]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255)
)
GO
INSERT [dbo].[Shift] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (N'0', N'AM', N'صباح')
GO
INSERT [dbo].[Shift] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (N'1', N'PM', N'مساء')
GO
INSERT [dbo].[Shift] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (N'2', N'BOTH', N'اي وقت')
GO

ALTER TABLE [Appointment]
    ADD FOREIGN KEY ([AmPm]) REFERENCES [Shift] (Code); -- FK__Appointmen__AmPm__269AB60B
GO

ALTER TABLE [Appointment]
    ADD FOREIGN KEY ([PrefContactTime]) REFERENCES [Shift] (Code); -- FK__Appointme__PrefC__278EDA44
GO

update [dbo].[Appointment]
set [AmPm]='0'
where [AmPm] = 'am'
update [dbo].[Appointment]
set [AmPm]='1'
where [AmPm] = 'pm'
update [dbo].[Appointment]
set [AmPm]=NULL
where [AmPm] = 'string'

update [dbo].[Appointment]
set [PrefContactTime]='0'
where [PrefContactTime] = 'am'
update [dbo].[Appointment]
set [PrefContactTime]='1'
where [PrefContactTime] = 'pm'
update [dbo].[Appointment]
set [PrefContactTime]=NULL
where [PrefContactTime] = 'string'