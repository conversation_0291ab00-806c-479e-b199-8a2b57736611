using EServiceFunctions.Models.Registration;
using EServiceFunctions.RequestResponseModels.Registration;

using static EServiceFunctions.MapperModels.RegistrationMapper;

namespace EServiceFunctions.Functions;

public class RegistrationFunctions(
    IDbContextFactory<RegistrationContext> dbContextFactory,
    ILogger<RegistrationFunctions> logger)
{
    private const string ErrorMessageTemplate = "Error: {Message}";
    private const string UnauthorizedQIdAccessTemplate = "Unauthorized ({QId}) Access!!!";

    #region GetRegistrationListByQID

    /// <summary>
    /// Get the registration list for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>        
    [Function("GetRegistrationListByQID")]
    [OpenApiOperation(operationId: "GetRegistrationListByQID", tags: ["Registration"],
        Summary = "Get Registration List By Submitter's QID",
        Description = " Get the registration list for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested} or Archived {Approved, Cancelled, CancelledByEServ}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetRegistrationListResponse>),
        Description =
            "Type of requests to return viz. Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested, Approved, Cancelled or CancelledByEServ.")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetRegistrationListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "registrations/submitter-qid/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Registrations List By Submitter's QId {QId}", qId);
            
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string registrationStatus = req.GetCleanedQueryString("status");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Registration!.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (registration, status) => new
                {
                    registration, status
                }).AsQueryable().AsNoTracking();

            query = query.Where(item => item.registration.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(registrationStatus))
            {
                query = query.Where(item =>
                    item.status.Category! == registrationStatus || item.registration.Status! == registrationStatus);
            }

            var response = await query.Select(item => new GetRegistrationListResponse
            {
                QId = item.registration.QId,
                ReqNumber = item.registration.ReqNumber,
                FNameEn = item.registration.FNameEn,
                MNameEn = item.registration.MNameEn,
                LNameEn = item.registration.LNameEn,
                FNameAr = item.registration.FNameAr,
                MNameAr = item.registration.MNameAr,
                LNameAr = item.registration.LNameAr,
                Status = item.registration.StatusNavigation!.Code,
                StatusDescriptionEn = item.registration.StatusNavigation.DescriptionEn,
                StatusDescriptionAr = item.registration.StatusNavigation.DescriptionAr,
                SubmittedAt = item.registration.SubmittedAt.ToUtcString()
            }).OrderBy(p => p.Status).Skip(skip).Take(take).ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for the given QID {QId} and Status {Status}", qId, registrationStatus);
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Registrations List ({Count}) By Submitter's QID {QId}", response.Count, qId);

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetRegistrationStatsByQID

    /// <summary>
    /// Get the registration stats for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetRegistrationStatsByQID")]
    [OpenApiOperation(operationId: "GetRegistrationStatsByQID", tags: ["Registration"],
        Summary = "Get Registration Stats By Submitter's QID",
        Description = "Get the registration stats for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested} or Archived {Approved, Cancelled, CancelledByEServ}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetRegistrationStatsResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetRegistrationStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "registrations/submitter-qid/{qId}/stats")]
        HttpRequestData req, long qId)
    {
        try
        {
            if (req.Url.Query.Length == 0)
            {
                logger.LogInformation("The HTTP request is null");
                return await req.WriteErrorResponseAsync(BadRequest, "The HTTP request is null");
            }

            logger.LogInformation("Getting Registrations Stats By Submitter's QID {QId}", qId);
            
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string registrationStatus = req.GetCleanedQueryString("status");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Registration?.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (registration, status) => new { registration, status }).AsQueryable().AsNoTracking();

            query = query?.Where(item => item.registration.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(registrationStatus))
            {
                query = query?.Where(item => item.registration.Status != null
                                             && item.status.Category != null
                                             && (item.status.Category == registrationStatus
                                                 || item.registration.Status == registrationStatus));
            }

            var response = await query!.CountAsync();

            logger.LogInformation("Returning Registrations count {Count} By Submitter's QID {QId} & Status {Status}", response, qId, registrationStatus);
            
            return await req.WriteOkResponseAsync(new GetRegistrationStatsResponse
            {
                Count = response
            });
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CheckInProcessRegistrationByQID

    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("CheckInProcessRegistrationByQID")]
    [OpenApiOperation(operationId: "CheckInProcessRegistrationByQID", tags: ["Registration"],
        Summary = "Check InProcess Registration(s) By Requester's QID",
        Description = "Check inprocess Registration(s) for the given requester's QId")]
    [OpenApiParameter("qId", Description = "Requester's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessRegistrationResponse),
        Description = "Returns True Or False")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckInProcessRegistrationByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "registrations/{qId}/inprocess-validation")]
        HttpRequestData req, long qId)
    {
        try
        {
            if (qId <= 0 || qId.ToString().Length < 11)
            {
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid QID {qId}");
            }

            logger.LogInformation("Checking InProcess Registration(s) By Requester's QID {QId}", qId);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Registration?.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (registration, status) => new { registration, status }).AsQueryable().AsNoTracking();

            var isInProcessRequestExist = false;
            if (query is not null)
            {
                isInProcessRequestExist = await query.AnyAsync(item =>
                    item.registration.QId == qId && item.status.Category == InProcess);
            }

            logger.LogInformation("Returning InProcess Registration(s) Validation Result {Result} By Requester's QID {QId}", isInProcessRequestExist, qId);

            return await req.WriteOkResponseAsync(new CheckInProcessRegistrationResponse
            {
                IsInprocessExist = isInProcessRequestExist
            });
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetRegistrationItemByReqNumber

    /// <summary>
    /// Get registration item for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Registration Request Number</param>
    /// <returns></returns>
    [Function("GetRegistrationItemByReqNumber")]
    [OpenApiOperation(operationId: "GetRegistrationItemByReqNumber", tags: ["Registration"],
        Summary = "Get Registration Item By ReqNumber",
        Description = "Get registration item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Registration Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetRegistrationItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetRegistrationItemByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "registrations/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();
            if (string.IsNullOrWhiteSpace(reqNumber))
            {
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            logger.LogInformation("Getting Registration Item By Request Number {ReqNumber}", reqNumber);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.Registration?.Include(s => s.StatusNavigation).AsNoTracking()
                .SingleOrDefaultAsync(item => item.ReqNumber == reqNumber)!;
            if (response == null || IsEmptyObject(response))
            {
                logger.LogInformation("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            if (submitter.QId is not null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var getResponse = MapRegistrationToGetRegistrationItemResponse(response);            

            getResponse.Status = response.StatusNavigation?.Code;
            getResponse.StatusDescriptionEn = response.StatusNavigation?.DescriptionEn;
            getResponse.StatusDescriptionAr = response.StatusNavigation?.DescriptionAr;

            logger.LogInformation("Returning Registration Item By Request Number {ReqNumber}", getResponse.ReqNumber);
            
            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CreateRegistration

    /// <summary>
    /// Create new registration request
    /// </summary>
    /// <param name="req">New Registration Create Model</param>
    /// <returns></returns>
    [Function("CreateRegistration")]
    [OpenApiOperation(operationId: "CreateRegistration", tags: ["Registration"],
        Summary = "Create Registration", Description = "Create new registration request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateRegistrationRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(Registration))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateRegistration(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "registrations")]
        HttpRequestData req)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateRegistrationRequest>(requestBody);
            
            if (!req.IsAuthorized(request?.SubmitterQId ?? 0))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, request.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (request is null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }

            if (IsEmptyObject(request) || request.SubmitterQId is null
                || request.SubmitterEmail is null || request.SubmitterMobile is null)
            {
                return await req.WriteErrorResponseAsync();
            }

            logger.LogInformation("Creating An Registration");

            Registration create = MapCreateUpdateRegistrationRequestToRegistration(request);

            create.ReqNumber = RandomPassword();
            create.SubmittedAt = GetCurrentTime();
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;
            create.Status = Saved;

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            // Check for existing in-process registration requests
            var existingInProcessQuery = dbContext.Registration!
                .Join(dbContext.Status!, r => r.Status, s => s.Code, (registration, status) => new { registration, status })
                .AsQueryable();

            var isInProcessRequestExist = await existingInProcessQuery.AnyAsync(item =>
                item.registration.QId == request.QId && item.status.Category! == InProcess);

            if (isInProcessRequestExist)
            {
                logger.LogWarning("Duplicate registration request detected for QId {QId}", request.QId);
                return await req.WriteErrorResponseAsync(BadRequest, "An in-process registration request already exists for this applicant.");
            }

            try
            {
                if (dbContext.Registration is not null)
                {
                    await dbContext.Registration.AddAsync(create);
                    var result = await dbContext.SaveChangesAsync();
                    if (result == 0)
                    {
                        logger.LogError(ErrorMessageTemplate, result);
                        return await req.WriteErrorResponseAsync(InternalServerError, "Error while saving the registration");
                    }
                }
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError, ex.InnerException?.Message);
            }

            logger.LogInformation("Created A New Registration With Request Number {ReqNumber}", create.ReqNumber);
            
            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UpdateRegistrationByReqNumber

    /// <summary>
    /// Update registration for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Registration Request Number </param>
    /// <returns></returns>       
    [Function("UpdateRegistrationByReqNumber")]
    [OpenApiOperation(operationId: "UpdateRegistrationByReqNumber", tags: ["Registration"],
        Summary = "Update Registration By ReqNumber",
        Description = "Update registration item for the given request number")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateRegistrationRequest))]
    [OpenApiParameter("reqNumber", Description = "Registration Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateRegistrationByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "registrations/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateRegistrationRequest>(requestBody);
            
            if (!req.IsAuthorized(request?.SubmitterQId ?? 0))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, request.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (request is null || request.SubmitterQId.IsNullOrDefault())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body.");
            }

            logger.LogInformation("Updating An Registration");
            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            Registration? response;
            reqNumber.ThrowIfNullOrWhiteSpace();
            try
            {
                response = await dbContext.Registration!.SingleOrDefaultAsync(item =>
                    item.ReqNumber == reqNumber.Trim());
            }
            catch (Exception e)
            {
                logger.LogError(e, ErrorMessageTemplate, e.Message);
                return await req.WriteNoContentResponseAsync();
            }

            if (response is null)
            {
                logger.LogWarning("Invalid Request Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Request Number {reqNumber}");
            }
            
            var mapperResponse = MapCreateUpdateRegistrationRequestToRegistration(request);
            
            mapperResponse.Id = response.Id;
            mapperResponse.ReqNumber = response.ReqNumber;
            mapperResponse.Status = response.Status;
            mapperResponse.StatusInternal = response.StatusInternal;
            mapperResponse.SN = response.SN;
            mapperResponse.CreatedAt = response.CreatedAt;
            mapperResponse.UpdatedAt = GetCurrentTime();
            mapperResponse.CreateSource = response.CreateSource;
            mapperResponse.UpdateSource = requestOriginSource;
            dbContext.Entry(response).CurrentValues.SetValues(mapperResponse);

            try
            {
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError, ex.InnerException?.Message);
            }
            
            logger.LogInformation("Updated A New Registration With Request Number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync(mapperResponse);

        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region DeleteRegistrationByReqNumber

    /// <summary>
    /// Delete registration for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Registration Request Number </param>
    /// <returns></returns>
    [Function("DeleteRegistrationByReqNumber")]
    [OpenApiOperation(operationId: "DeleteRegistrationByReqNumber", tags: ["Registration"],
        Summary = "Delete Registration By ReqNumber",
        Description = "Delete registration item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Registration Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteRegistrationByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "registrations/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            logger.LogInformation("Deleting An Registration");
            var submitter = req.GetClaims();

            if (reqNumber.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Request Number {reqNumber}");
            }

            Registration? response;

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            try
            {
                response = await dbContext.Registration?.SingleOrDefaultAsync(item =>
                    item.ReqNumber == reqNumber.Trim() && item.Status == Saved)!;
            }
            catch (Exception e)
            {
                logger.LogError(e, ErrorMessageTemplate, e.Message);
                return await req.WriteNoContentResponseAsync();
            }

            if (response == null || IsEmptyObject(response))
            {
                logger.LogWarning("Invalid Request Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Request Number {reqNumber}");
            }

            submitter.QId.ThrowIfNullOrDefault();

            if (response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            try
            {
                dbContext.Registration.ThrowIfNullOrDefault();
                dbContext.Registration.Remove(response);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogCritical(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError, ex.InnerException?.Message);
            }

            logger.LogInformation("Deleted Registration By Request Number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync("Successfully Deleted");
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}