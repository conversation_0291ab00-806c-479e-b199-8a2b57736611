# Get MOI Dependents

## Overview
Retrieves dependent information from the Ministry of Interior (MOI) for a given QID. This endpoint provides details about potential dependents and their current linkage status in the EServices system.

## Endpoint
```http
GET /userprofiles/moi-dependents/{qId}
```

## Authorization
- Function-level authorization
- QID-based access control
- Claims validation

## Headers
- `X-PhccEnvironment` (optional): Environment identifier for EDW data retrieval

## Parameters

### Path Parameters
- `qId` (long, required): User QID

### Query Parameters
- `isLinked` (boolean, optional): Filter for linked/unlinked dependents
  * `true`: Return only linked dependents
  * `false`: Return only unlinked dependents
  * not provided: Return all dependents

## Response

### Success Response (200 OK)
Returns a list of dependents:
```json
[
  {
    "qId": "long",
    "fNameEn": "string",
    "mNameEn": "string",
    "lNameEn": "string",
    "fNameAr": "string",
    "mNameAr": "string",
    "lNameAr": "string",
    "linked": "boolean"
  }
]
```

### No Content Response (204)
Returned when no dependents are found.

### Error Responses
- 401 Unauthorized: Invalid or missing authorization
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Retrieves data from EDW database
- Uses stored procedure for MOI data processing
- Handles nationality validation
- Manages linkage status
- Optimized live feeds data retrieval with ordering by event date
- Efficient XML handling for MOI data processing
- Implements proper error handling
- Supports multilingual content

## Business Logic
- Validates QID authorization
- Processes nationality XML
- Manages dependent status
- Handles data filtering
- Validates relationships
- Maintains data consistency

## Data Integration
### EDW Integration
- Uses stored procedure
- XML data handling
- Environment routing
- Data transformation
- Error handling

### MOI Data
- Dependent information
- Relationship validation
- Status tracking
- Data synchronization
- Validation rules

## Security Considerations
- Validates user authorization
- Implements function-level security
- Ensures data access control
- Protects sensitive data
- Proper error handling
- Secure data exchange

## Performance Optimization
- Efficient database queries
- XML data optimization
- Proper connection handling
- Resource management
- Early validation
- Caching considerations
