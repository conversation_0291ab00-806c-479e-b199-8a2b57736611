﻿using EServiceFunctions.Models.Transfer;
using EServiceFunctions.RequestResponseModels.Assignment;
using EServiceFunctions.RequestResponseModels.Transfer;
using static System.Guid;
using static UnitTestEServiceFunctions.MockDataForTransferFunctions;

namespace UnitTestEServiceFunctions;

[ExcludeFromCodeCoverage]
public class TestTransferDbContext : IDbContextFactory<TransferContext>
{
    public TransferContext CreateDbContext()
    {
        var optionsBuilder = new DbContextOptionsBuilder<TransferContext>();
        optionsBuilder.UseInMemoryDatabase("TestTransferContext");
        var context = new TransferContext(optionsBuilder.Options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();
        context.Status.AddRange(GetStatusForTransfer());
        context.Transfer.AddRange(GetTransfers());
        context.TransferReason.AddRange(GetTransferReasons());
        context.SaveChanges();
        return context;
    }
}

public class EmptyTransferDbContext : IDbContextFactory<TransferContext>
{
    public TransferContext CreateDbContext()
    {
        var optionsBuilder = new DbContextOptionsBuilder<TransferContext>();
        optionsBuilder.UseInMemoryDatabase("EmptyTransferContext");
        var context = new TransferContext(optionsBuilder.Options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();
        context.Status.AddRange(GetStatusForTransfer());
        context.TransferReason.AddRange(GetTransferReasons());
        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public static class MockDataForTransferFunctions
{
    public static IEnumerable<Status> GetStatusForTransfer()
    {
        var statusList = new List<Status>
        {
            new()
            {
                Code = "New",
                DescriptionEn = "New",
                DescriptionAr = "جديد",
                Category = "New"
            },
            new()
            {
                Code = Approved,
                DescriptionEn = "Request Completed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = Cancelled,
                DescriptionEn = "Request Cancelled",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = CancelledByEServ,
                DescriptionEn = "Cancelled by EServices",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = "CancelledByRegistration",
                DescriptionEn = "Cancelled by Registration",
                DescriptionAr = "ألغيت بالتسجيل",
                Category = Archived
            },
            new()
            {
                Code = CancelledFrom107,
                DescriptionEn = "Cancelled from 107",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = ConditionallyApproved,
                DescriptionEn = "Request Tentatively Approved",
                DescriptionAr = "تمت الموافقة المبدئية للطلب",
                Category = InProcess
            },
            new()
            {
                Code = "Confirmed",
                DescriptionEn = "Confirmed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = InProcess,
                DescriptionEn = "Request In Progress",
                DescriptionAr = "الطلب قيد الاجراء",
                Category = InProcess
            },
            new()
            {
                Code = "PaymentReceived",
                DescriptionEn = "Payment Received",
                DescriptionAr = "تمت عملية الدفع",
                Category = InProcess
            },
            new()
            {
                Code = PendingOrder,
                DescriptionEn = "Request Pending",
                DescriptionAr = "الطلب قيد الإنتظار",
                Category = InProcess
            },
            new()
            {
                Code = "ProceedToRegistration",
                DescriptionEn = "Proceed to Registration",
                DescriptionAr = "الشروع في التسجيل",
                Category = InProcess
            }
        };

        return statusList;
    }

    public static IEnumerable<Transfer> GetTransfers()
    {
        var transfersList = new List<Transfer>
        {
            new()
            {
                Id = 1,
                QId = LongQId,
                ReqNumber = "REQ0000001",
                HCNumber = HcNumber1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Nationality = NationalityEn,
                AttachId1 = NewGuid(),
                AttachId2 = NewGuid(),
                AttachId3 = NewGuid(),
                AttachId4 = NewGuid(),
                AttachId5 = NewGuid(),
                AttachId6 = NewGuid(),
                SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
                SubmittedAt = GetCurrentTime(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                Consent = true,
                CreatedAt = GetCurrentTime(),
                UpdatedAt = GetCurrentTime(),
                Status = InProcess,
                StatusInternal = InProcess,
                SN = "SN",
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest",
                TransferReason = "TR1",
                CurrentHC = HcNumber1,
                CatchmentHC = HcNumber1,
                PrefHC = HcNumber2,
                Dob = new DateTime(1990, 1, 1),
                BNo = new Random().Next(1, 100),
                ZNo = new Random().Next(1, 100),
                SNo = new Random().Next(1, 100),
                UNo = new Random().Next(1, 100),
                GisAddressUpdatedAt = GetCurrentTime(),
                IsGisAddressManualyEntered = true
            },
            new()
            {
                Id = 2,
                QId = LongQId,
                ReqNumber = "REQ0000002",
                HCNumber = HcNumber2,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Nationality = NationalityEn,
                AttachId1 = NewGuid(),
                AttachId2 = NewGuid(),
                AttachId3 = NewGuid(),
                AttachId4 = NewGuid(),
                AttachId5 = NewGuid(),
                AttachId6 = NewGuid(),
                SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
                SubmittedAt = GetCurrentTime(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                Consent = true,
                CreatedAt = GetCurrentTime(),
                UpdatedAt = GetCurrentTime(),
                Status = InProcess,
                StatusInternal = InProcess,
                SN = "SN",
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest",
                TransferReason = "TransferReason",
                CurrentHC = HcNumber2,
                CatchmentHC = HcNumber2,
                PrefHC = HcNumber1,
                Dob = new DateTime(1990, 1, 1),
                BNo = new Random().Next(1, 100),
                ZNo = new Random().Next(1, 100),
                SNo = new Random().Next(1, 100),
                UNo = new Random().Next(1, 100),
                GisAddressUpdatedAt = GetCurrentTime(),
                IsGisAddressManualyEntered = true
            },
            new()
            {
                Id = 3,
                QId = LongQId2,
                ReqNumber = "REQ0000003",
                HCNumber = HcNumber3,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Nationality = NationalityEn,
                AttachId1 = NewGuid(),
                AttachId2 = NewGuid(),
                AttachId3 = NewGuid(),
                AttachId4 = NewGuid(),
                AttachId5 = NewGuid(),
                AttachId6 = NewGuid(),
                SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
                SubmittedAt = GetCurrentTime(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                Consent = true,
                CreatedAt = GetCurrentTime(),
                UpdatedAt = GetCurrentTime(),
                Status = InProcess,
                StatusInternal = InProcess,
                SN = "SN",
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest",
                TransferReason = "TransferReason",
                CurrentHC = HcNumber3,
                CatchmentHC = HcNumber3,
                PrefHC = HcNumber2,
                Dob = new DateTime(1990, 1, 1),
                BNo = new Random().Next(1, 100),
                ZNo = new Random().Next(1, 100),
                SNo = new Random().Next(1, 100),
                UNo = new Random().Next(1, 100),
                GisAddressUpdatedAt = GetCurrentTime(),
                IsGisAddressManualyEntered = true
            }
        };
        return transfersList;
    }

    public static IEnumerable<TransferReason> GetTransferReasons()
    {
        var transferReasonsList = new List<TransferReason>
        {
            new()
            {
                ReasonCode = "TR1",
                ReasonEn = "Resident address Changed",
                ReasonAr = "تم تغيير عنوان الاقامة",
                CreatedAt = GetCurrentTime()
            },
            new()
            {
                ReasonCode = "TR2",
                ReasonEn = "Family status Changed",
                ReasonAr = "تم تغيير الحالة الاجتماعية",
                CreatedAt = GetCurrentTime()
            },
            new()
            {
                ReasonCode = "TR3",
                ReasonEn = "Change of Sponsor",
                ReasonAr = "تغيير جهة العمل",
                CreatedAt = GetCurrentTime()
            },
            new()
            {
                ReasonCode = "TR4",
                ReasonEn = "Other",
                ReasonAr = "اخرى",
                CreatedAt = GetCurrentTime()
            }
        };
        return transferReasonsList;
    }
}

public class UnitTestTransferFunctions(ITestOutputHelper testOutputHelper)
{
    private readonly ILogger<TransferFunctions> _logger = Mock.Of<ILogger<TransferFunctions>>();

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_OkObjectResult_For_Valid_QId()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, 1);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        testOutputHelper.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.Unauthorized, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_NoContentResult_For_Invalid_Status()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = $"?status=InProcess1&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        testOutputHelper.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_NoContentResult_For_Invalid_Skip()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = $"?status=InProcess&skip=1000&take={Take}";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        testOutputHelper.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_OkObjectResult_For_Invalid_Take()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=1000";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_OkObjectResult_For_Empty_Status()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = $"?status=&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_OkObjectResult_For_Empty_Skip()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = "?status=&skip=&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_OkObjectResult_For_Empty_Take()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = $"?status=&skip={Skip}&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_OkObjectResult_For_Empty_QueryParameters()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = "?status=&skip=&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Return_OkObjectResult_For_No_QueryParameters()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferStatsByQId_Should_Return_OkObjectResult_And_One_Count_For_Valid_QId()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferStatsByQId(mockHttpRequestData, LongQId);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferStatsByQId_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferStatsByQId(mockHttpRequestData, 1);

        // Assert
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessTransferByQId_Should_Return_True_For_Valid_QId()
    {
        //Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        //Act
        var response = await transferFunctions.CheckInProcessTransferByQId(mockHttpRequestData, LongQId);
        var result = response.ReadBodyToEnd();
        testOutputHelper.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessTransferResponse>(result);
        True(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessTransferByQId_Should_Return_False_For_InvalidQId()
    {
        //Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        //Act
        var response = await transferFunctions.CheckInProcessTransferByQId(mockHttpRequestData, 123);
        var result = response.ReadBodyToEnd();
        testOutputHelper.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessTransferResponse>(result);
        False(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);
        const string reqNumber = "REQ0000001";

        // Act
        var response = await transferFunctions.GetTransferItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        testOutputHelper.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetTransferItemResponse>(result);
        Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber_And_Valid_Status()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);
        const string reqNumber = "REQ0000001";
        mockHttpRequestData.Query.Add(InProcess, InProcess);

        // Act
        var response = await transferFunctions.GetTransferItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        testOutputHelper.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetTransferItemResponse>(result);
        Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferItemByReqNumber_Should_Return_NoContentResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string reqNumber = "REQ0000000";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        testOutputHelper.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferItemByReqNumber_Should_Return_NoContentResult_For_Invalid_StatusNavigation()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string reqNumber = "REQ0001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        testOutputHelper.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateTransfer_Should_Return_CreatedResult_For_Valid_Request()
    {
        // Arrange
        var mockTransferContext = new EmptyTransferDbContext();
        var createTransferRequest = new CreateUpdateTransferRequest
        {
            QId = LongQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = "144",
            Dob = new DateTime(1990, 1, 1),
            Consent = true,
            HCNumber = HcNumber1,
            BNo = new Random().Next(1, 100),
            ZNo = new Random().Next(1, 100),
            SNo = new Random().Next(1, 100),
            UNo = new Random().Next(1, 100),
            CurrentHC = HcNumber1,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            TransferReason = "TR1",
            SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            GisAddressUpdatedAt = GetCurrentTime(),
            IsGisAddressManualyEntered = true
        };

        var json = SerializeObject(createTransferRequest);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.CreateTransfer(mockHttpRequestData);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateTransfer_Should_Return_BadRequestResult_For_Invalid_Request()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var request = new CreateUpdateTransferRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.CreateTransfer(mockHttpRequestData);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateTransfer_Should_Return_BadRequestResult_For_Missing_Header()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var createTransferRequest = new CreateUpdateTransferRequest
        {
            QId = LongQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = "144",
            Dob = new DateTime(1990, 1, 1),
            Consent = true,
            HCNumber = HcNumber1,
            BNo = new Random().Next(1, 100),
            ZNo = new Random().Next(1, 100),
            SNo = new Random().Next(1, 100),
            UNo = new Random().Next(1, 100),
            CurrentHC = HcNumber1,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            TransferReason = "TR1",
            SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            GisAddressUpdatedAt = GetCurrentTime(),
            IsGisAddressManualyEntered = true
        };

        var json = SerializeObject(createTransferRequest);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.CreateTransfer(mockHttpRequestData);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateTransferByReqNumber_Should_Return_OK_For_Valid_ReqNumber()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string reqNumber = "REQ0000001";
        var createTransferRequest = new CreateUpdateTransferRequest
        {
            QId = LongQId2,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = "144",
            Dob = new DateTime(1990, 1, 1),
            Consent = true,
            HCNumber = HcNumber3,
            BNo = new Random().Next(1, 100),
            ZNo = new Random().Next(1, 100),
            SNo = new Random().Next(1, 100),
            UNo = new Random().Next(1, 100),
            CurrentHC = HcNumber3,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            TransferReason = "TR3",
            SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            GisAddressUpdatedAt = GetCurrentTime(),
            IsGisAddressManualyEntered = true
        };

        var json = SerializeObject(createTransferRequest);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.UpdateTransferByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        testOutputHelper.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateTransferByReqNumber_Should_Return_BadRequestResult_For_Empty_RequestObject()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string reqNumber = "REQ0000001";
        var request = new CreateUpdateTransferRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.UpdateTransferByReqNumber(mockHttpRequestData, reqNumber);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateTransferByReqNumber_Should_Return_BadRequestResult_For_Missing_Headers()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string reqNumber = "REQ0000001";
        var request = new CreateUpdateAssignmentRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.UpdateTransferByReqNumber(mockHttpRequestData, reqNumber);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteTransferByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string reqNumber = "REQ0000001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.DeleteTransferByReqNumber(mockHttpRequestData, reqNumber);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteTransferByReqNumber_Should_Return_BadRequestObjectResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string reqNumber = "REQ0000000";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.DeleteTransferByReqNumber(mockHttpRequestData, reqNumber);
        testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    #region GetTransferReasonList Tests

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferReasonList_Should_Return_OkObjectResult_For_All_Reasons()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferReasonList(mockHttpRequestData);
        var result = response.ReadBodyToEnd();
        testOutputHelper.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
        var responseObject = DeserializeObject<List<GetTransferReasonListResponse>>(result);
        Equal(4, responseObject.Count);
        Contains(responseObject, x => x.ReasonCode == "TR1");
        Contains(responseObject, x => x.ReasonCode == "TR2");
        Contains(responseObject, x => x.ReasonCode == "TR3");
        Contains(responseObject, x => x.ReasonCode == "TR4");
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferReasonList_Should_Return_OkObjectResult_For_Specific_ReasonCode()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = "?reasonCode=TR1";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferReasonList(mockHttpRequestData);
        var result = response.ReadBodyToEnd();
        testOutputHelper.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
        var responseObject = DeserializeObject<List<GetTransferReasonListResponse>>(result);
        Single(responseObject);
        Equal("TR1", responseObject[0].ReasonCode);
        Equal("Resident address Changed", responseObject[0].ReasonEn);
        Equal("تم تغيير عنوان الاقامة", responseObject[0].ReasonAr);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferReasonList_Should_Return_NoContentResult_For_Invalid_ReasonCode()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = "?reasonCode=InvalidCode";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferReasonList(mockHttpRequestData);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var noContentResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, noContentResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferReasonList_Should_Return_OkObjectResult_With_Pagination()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = "?skip=1&take=2";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferReasonList(mockHttpRequestData);
        var result = response.ReadBodyToEnd();
        testOutputHelper.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
        var responseObject = DeserializeObject<List<GetTransferReasonListResponse>>(result);
        Equal(2, responseObject.Count);
        Equal("TR2", responseObject[0].ReasonCode);
        Equal("TR3", responseObject[1].ReasonCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferReasonList_Should_Return_NoContentResult_When_Skip_Exceeds_Available_Items()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        const string queryParams = "?skip=10&take=5";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);

        // Act
        var response = await transferFunctions.GetTransferReasonList(mockHttpRequestData);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var noContentResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, noContentResponse.StatusCode);
    }

    #endregion

    #region Exception Handling Tests

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferListByQId_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<TransferContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var transferFunctions = new TransferFunctions(mockDbContextFactory.Object, _logger);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await transferFunctions.GetTransferListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferStatsByQId_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<TransferContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var transferFunctions = new TransferFunctions(mockDbContextFactory.Object, _logger);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await transferFunctions.GetTransferStatsByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessTransferByQId_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<TransferContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var transferFunctions = new TransferFunctions(mockDbContextFactory.Object, _logger);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await transferFunctions.CheckInProcessTransferByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferItemByReqNumber_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<TransferContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var transferFunctions = new TransferFunctions(mockDbContextFactory.Object, _logger);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        const string reqNumber = "REQ0000001";

        // Act
        var response = await transferFunctions.GetTransferItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferReasonList_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<TransferContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var transferFunctions = new TransferFunctions(mockDbContextFactory.Object, _logger);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await transferFunctions.GetTransferReasonList(mockHttpRequestData);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateTransfer_Should_Handle_DbUpdateException()
    {
        // Arrange
        var mockDbContext = new Mock<TransferContext>(new DbContextOptions<TransferContext>());
        var mockDbSet = new Mock<DbSet<Transfer>>();

        mockDbContext.Setup(m => m.Transfer).Returns(mockDbSet.Object);
        mockDbSet.Setup(m => m.AddAsync(It.IsAny<Transfer>(), It.IsAny<CancellationToken>()))
            .Callback(() => throw new DbUpdateException("Test DB exception", new Exception()));

        var mockDbContextFactory = new Mock<IDbContextFactory<TransferContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockDbContext.Object);

        var transferFunctions = new TransferFunctions(mockDbContextFactory.Object, _logger);

        var createTransferRequest = new CreateUpdateTransferRequest
        {
            QId = LongQId,
            FNameEn = FirstNameEn,
            SubmitterQId = LongQId
        };

        var json = SerializeObject(createTransferRequest);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);

        // Act
        var response = await transferFunctions.CreateTransfer(mockHttpRequestData);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateTransferByReqNumber_Should_Handle_DbUpdateException()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<TransferContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Test DB exception", new Exception()));

        var transferFunctions = new TransferFunctions(mockDbContextFactory.Object, _logger);

        var updateTransferRequest = new CreateUpdateTransferRequest
        {
            QId = LongQId,
            FNameEn = FirstNameEn,
            SubmitterQId = LongQId
        };

        var json = SerializeObject(updateTransferRequest);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Put);
        const string reqNumber = "REQ0000001";

        // Act
        var response = await transferFunctions.UpdateTransferByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    #endregion

    #region Additional Edge Cases

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferItemByReqNumber_Should_Return_BadRequest_For_Empty_ReqNumber()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);
        const string reqNumber = "";

        // Act
        var response = await transferFunctions.GetTransferItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetTransferItemByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Set a different QID in the header to simulate unauthorized access
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, (LongQId + 1).ToString());

        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);
        const string reqNumber = "REQ0000001";

        // Act
        var response = await transferFunctions.GetTransferItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var unauthorizedResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, unauthorizedResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteTransferByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockTransferContext = new TestTransferDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);

        // Set a different QID in the header to simulate unauthorized access
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, (LongQId + 1).ToString());

        var transferFunctions = new TransferFunctions(mockTransferContext, _logger);
        const string reqNumber = "REQ0000001";

        // Act
        var response = await transferFunctions.DeleteTransferByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var unauthorizedResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, unauthorizedResponse.StatusCode);
    }

    #endregion
}