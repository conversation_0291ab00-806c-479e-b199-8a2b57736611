<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Core" Version="2.0.0" />
        <PackageReference Include="Microsoft.Azure.Core.NewtonsoftJson" Version="2.0.0" />
        <PackageReference Include="Moq" Version="4.20.72" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\MockDataLibrary\MockDataLibrary.csproj" />
    </ItemGroup>

</Project>
