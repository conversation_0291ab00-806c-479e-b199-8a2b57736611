﻿using EServiceFunctions.Models.FamilyPhysician;
using Newtonsoft.Json.Linq;
using static UnitTestEServiceFunctions.MockDataForFamilyPhysicianFunctions;

namespace UnitTestEServiceFunctions;

public class TestMrdDbContextForFamilyPhysicians : IDbContextFactory<MRDDbContext>
{
    public MRDDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<MRDDbContext>()
            .UseInMemoryDatabase(databaseName: "TestMrdDbContextForFamilyPhysicians")
            .Options;
        var context = new MRDDbContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.MAP_MOI_OccupationTypes.AddRange(GetOccupationTypes());
        context.MAP_MOI_VisaTypes.AddRange(GetVisaType());
        context.MRD_Physician_Specialties.AddRange(GetPhysicianSpecialties());
        context.MRD_Nationalities.AddRange(GetNationalities());
        context.MRD_Family_Physicians.AddRange(GetFamilyPhysicians());

        context.SaveChanges();

        return context;
    }
}

public static class MockDataForFamilyPhysicianFunctions
{
    public static IEnumerable<MAP_MOI_OccupationType> GetOccupationTypes()
    {
        var occupationType = new MAP_MOI_OccupationType
        {
            OCCUPATION_CODE = OccupationCode,
            DESCRIPTION_ENGLISH = OccupationDescriptionEn,
            DESCRIPTION_ARABIC_MALE = OccupationDescriptionAr,
            DESCRIPTION_ARABIC_FEMALE = OccupationDescriptionAr,
            LAST_UPDATED_DATE = new DateTime(2023, 10, 10)
        };
        return new List<MAP_MOI_OccupationType> { occupationType };
    }

    public static IEnumerable<MAP_MOI_VisaType> GetVisaType()
    {
        var visaType = new MAP_MOI_VisaType
        {
            VISA_TYPE = VisaCode,
            DESCRIPTION_ENGLISH = VisaDescriptionEn,
            DESCRIPTION_ARABIC = VisaDescriptionAr,
            LAST_UPDATED_DATE = new DateTime(2023, 10, 10)
        };
        return new List<MAP_MOI_VisaType> { visaType };
    }

    public static IEnumerable<MRD_Nationality> GetNationalities()
    {
        var nationality = new MRD_Nationality
        {
            COUNTRY_CODE = NationalityCode,
            COUNTRY_NAME_EN = NationalityEn,
            COUNTRY_NAME_ARB = NationalityAr,
            ALPHA2_CODE = "LK",
            ALPHA3_CODE = "LKA",
            STANDARD_SYSTEM = "ISO 3166-1 alpha-3",
            IS_QATARI = "N",
            IS_GCC = "N",
            IS_MENA = "N",
            REGION = "Asia",
            ACTIVE_FLG = 1,
            LAST_UPDATE_DATE = new DateTime(2023, 10, 10),
            NATIONALITY = NationalityEn
        };

        return new List<MRD_Nationality> { nationality };
    }

    public static IEnumerable<MRD_Physician_Specialty> GetPhysicianSpecialties()
    {
        var physicianSpecialty = new MRD_Physician_Specialty
        {
            SPECIALTY_CODE = "001",
            SPECIALTY_NAME_EN = "Gynaecologist",
            SPECIALTY_NAME_ARB = "طبيب نسائية",
            LAST_LOAD_DATE = new DateTime(2023, 10, 10),
            LAST_UPDATE_DATE = new DateTime(2023, 01, 10)
        };

        return new List<MRD_Physician_Specialty> { physicianSpecialty };
    }

    public static IEnumerable<MRD_Family_Physician> GetFamilyPhysicians()
    {
        var familyPhysician1 = new MRD_Family_Physician
        {
            CORP_ID = "123456",
            NAME_EN = "Mohamed Rayyan",
            NAME_ARB = "محمد ريان",
            PHCC_EXP_YRS = 5,
            POSITION_EN = "Senior Software Engineer",
            POSITION_ARB = "مهندس برمجيات كبير",
            CLINICAL_TITLE_EN = "Dr.",
            CLINICAL_TITLE_ARB = "د.",
            FAC_CODE = "OBK",
            HEALTH_CENTER = "Omar Bin Khattab",
            QUALIFICATIONS = "MSc in Software Engineering",
            GENDER_CODE = GenderCode,
            LANGUAGE_CODE = "001",
            TOTAL_EXPERIENCE = 5,
            PHYSICIAN_PHOTO = "https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png",
            SPECIALTY_CODE = "001",
            LAST_UPDATE_DATE = new DateTime(2023, 10, 10),
            LAST_LOAD_DATE = new DateTime(2023, 01, 10),
            OTHER_LANGUAGES = "English",
            OTHER_SPECIALITY = "Gynaecologist",
            STATUS = true,
            PHOTO_CONSENT = true,
            PHYSICIAN_PHOTO_BINARY = [0x20, 0x20, 0x20, 0x20]
        };

        // Add a physician with gender code "2" (mapped to "0" - Female)
        var familyPhysician2 = new MRD_Family_Physician
        {
            CORP_ID = "654321",
            NAME_EN = "Sarah Johnson",
            NAME_ARB = "سارة جونسون",
            PHCC_EXP_YRS = 8,
            POSITION_EN = "Pediatrician",
            POSITION_ARB = "طبيب أطفال",
            CLINICAL_TITLE_EN = "Dr.",
            CLINICAL_TITLE_ARB = "د.",
            FAC_CODE = "OBK",
            HEALTH_CENTER = "Omar Bin Khattab",
            QUALIFICATIONS = "MD in Pediatrics",
            GENDER_CODE = "2", // Will be mapped to "0" (Female)
            LANGUAGE_CODE = "001",
            TOTAL_EXPERIENCE = 8,
            SPECIALTY_CODE = "001",
            STATUS = true,
            PHOTO_CONSENT = false, // No photo consent
            PHYSICIAN_PHOTO_BINARY = [0x20, 0x20, 0x20, 0x20]
        };

        // Add a physician with gender code "0" (mapped to "2" - Unknown)
        var familyPhysician3 = new MRD_Family_Physician
        {
            CORP_ID = "789012",
            NAME_EN = "Alex Smith",
            NAME_ARB = "أليكس سميث",
            PHCC_EXP_YRS = 3,
            POSITION_EN = "General Practitioner",
            POSITION_ARB = "طبيب عام",
            CLINICAL_TITLE_EN = "Dr.",
            CLINICAL_TITLE_ARB = "د.",
            FAC_CODE = "OBK",
            HEALTH_CENTER = "Omar Bin Khattab",
            QUALIFICATIONS = "MBBS",
            GENDER_CODE = "0", // Will be mapped to "2" (Unknown)
            LANGUAGE_CODE = "001",
            TOTAL_EXPERIENCE = 3,
            SPECIALTY_CODE = "001",
            STATUS = true,
            PHOTO_CONSENT = true,
            PHYSICIAN_PHOTO_BINARY = null // Null photo binary
        };

        // Add a physician with null values for various fields
        var familyPhysician4 = new MRD_Family_Physician
        {
            CORP_ID = "246810",
            NAME_EN = null,
            NAME_ARB = null,
            PHCC_EXP_YRS = null,
            POSITION_EN = null,
            POSITION_ARB = null,
            CLINICAL_TITLE_EN = null,
            CLINICAL_TITLE_ARB = null,
            FAC_CODE = "OBK",
            HEALTH_CENTER = "Omar Bin Khattab",
            QUALIFICATIONS = null,
            GENDER_CODE = null, // Null gender code
            LANGUAGE_CODE = null,
            TOTAL_EXPERIENCE = null,
            SPECIALTY_CODE = null,
            STATUS = true,
            PHOTO_CONSENT = true,
            PHYSICIAN_PHOTO_BINARY = [0x20, 0x20, 0x20, 0x20]
        };

        return new List<MRD_Family_Physician> { familyPhysician1, familyPhysician2, familyPhysician3, familyPhysician4 };
    }
}

public class UnitTestFamilyPhysicianFunctions
{
    private readonly ILogger<FamilyPhysicianFunctions> _logger = Mock.Of<ILogger<FamilyPhysicianFunctions>>();
    private readonly ITestOutputHelper _testOutputHelper;
    private readonly FamilyPhysicianFunctions _attachmentFunctions;

    public UnitTestFamilyPhysicianFunctions(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        var mockDbContext = new TestMrdDbContextForFamilyPhysicians();
        _attachmentFunctions = new FamilyPhysicianFunctions(mockDbContext, _logger);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Return_OkObjectResult_For_Valid_PhysicianCode()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "123456";

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(result.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Return_NoContentResult_For_Invalid_PhysicianCode()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "1234567";

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Return_NoContentResult_For_Empty_Request()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, EmptyString);
        _testOutputHelper.WriteLine(response.StatusCode.ToString());

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetSpecialityList_Should_Return_OkObjectResult_For_Valid_SpecialityCode()
    {
        // Arrange
        const string queryParams = "?spltyCode=001&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetSpecialityList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetSpecialityList_Should_Return_NoContent_For_Invalid_Speciality_Code()
    {
        // Arrange
        const string queryParams = "?spltyCode=003&skip=0&take=1";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetSpecialityList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetSpecialityList_Should_Return_OkObjectResult_For_Valid_SpecialityCode_And_No_Skip_Take()
    {
        // Arrange
        const string queryParams = "?spltyCode=001";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetSpecialityList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetSpecialityList_Should_Return_OkObjectResult_For_No_SpecialityCode_And_Valid_Skip_Take()
    {
        // Arrange
        const string queryParams = $"?spltyCode=&skip={Skip}&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetSpecialityList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetSpecialityList_Should_Return_NoContentResult_For_Valid_SpecialityCode_But_No_Records_In_Db()
    {
        // Arrange
        const string queryParams = $"?spltyCode=002&skip={Skip}&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetSpecialityList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetSpecialityList_Should_Return_NoContent_For_Take_0()
    {
        // Arrange
        const string queryParams = "?spltyCode=001&skip=0&take=0";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);
        var mockDbContext = new TestMrdDbContextForFamilyPhysicians();
        var attachmentFunctions = new FamilyPhysicianFunctions(mockDbContext, _logger);

        // Act
        var response = await attachmentFunctions.GetSpecialityList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_OkObjectResult_For_Valid_Input()
    {
        // Arrange
        const string queryParams = $"?hcCode={HealthCenterCode}&gender={GenderCode}&language=001&speciality=001&skip={Skip}&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(result.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_NoContent_For_Invalid_Speciality_Code()
    {
        // Arrange
        const string queryParams = $"?hcCode={HealthCenterCode}&gender={GenderCode}&language=001&speciality=003&skip={Skip}&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_NoContent_For_Take_0()
    {
        // Arrange
        const string queryParams = $"?hcCode={HealthCenterCode}&gender={GenderCode}&language=001&speciality=001&skip={Skip}&take={Skip}";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_BadRequestObjectResult_For_Empty_Request()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_NoContentResult_For_NonExisting_SpecialityCode()
    {
        // Arrange
        const string hcCode = "OBK";
        const string language = "001";
        const string speciality = "002";
        const string queryParams = $"?hcCode={hcCode}&gender={GenderCode}&language={language}&speciality={speciality}&skip={Skip}&take={Take}";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_NoContentResult_For_NonExisting_HcCode()
    {
        // Arrange
        const string hcCode = "OBK123";
        const string language = "001";
        const string speciality = "001";
        const string queryParams = $"?hcCode={hcCode}&gender={GenderCode}&language={language}&speciality={speciality}&skip={Skip}&take={Take}";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_NoContentResult_For_NonExisting_LanguageCode()
    {
        // Arrange
        const string hcCode = "OBK";
        const string language = "0011";
        const string speciality = "001";
        const string queryParams = $"?hcCode={hcCode}&gender={GenderCode}&language={language}&speciality={speciality}&skip={Skip}&take={Take}";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_NoContentResult_For_Empty_Speciality_Code()
    {
        // Arrange
        const string hcCode = "OBK123";
        const string language = "001";
        const string queryParams = $"?hcCode={hcCode}&gender={GenderCode}&language={language}&speciality={EmptyString}&skip={Skip}&take={Take}";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_NoContentResult_For_Empty_Language_Code()
    {
        // Arrange
        const string hcCode = "OBK123";
        const string speciality = "001";
        const string queryParams = $"?hcCode={hcCode}&gender={GenderCode}&language={EmptyString}&speciality={speciality}&skip={Skip}&take={Take}";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Return_BadRequestObjectResult_For_Empty_HcCode()
    {
        // Arrange
        const string language = "001";
        const string speciality = "001";
        const string queryParams = $"?hcCode={EmptyString}&gender={GenderCode}&language={language}&speciality={speciality}&skip={Skip}&take={Take}";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    #region Additional Tests for 100% Code Coverage

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContext = new Mock<IDbContextFactory<MRDDbContext>>();
        mockDbContext.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var functions = new FamilyPhysicianFunctions(mockDbContext.Object, _logger);
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "123456";

        // Act
        var response = await functions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetSpecialityList_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContext = new Mock<IDbContextFactory<MRDDbContext>>();
        mockDbContext.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var functions = new FamilyPhysicianFunctions(mockDbContext.Object, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await functions.GetSpecialityList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContext = new Mock<IDbContextFactory<MRDDbContext>>();
        mockDbContext.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var functions = new FamilyPhysicianFunctions(mockDbContext.Object, _logger);
        const string queryParams = $"?hcCode=OBK&gender={GenderCode}&language=001&speciality=001&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await functions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Return_Physician_With_Gender_Code_2()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "654321"; // Physician with gender code "2"

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        var responseBody = result.ReadBodyToEnd();
        _testOutputHelper.WriteLine(responseBody);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        Contains("\"gender\":", responseBody); // Verify gender field exists
        Contains("0", responseBody); // Gender code "2" should be mapped to "0" (Female)
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Return_Physician_With_Gender_Code_0()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "789012"; // Physician with gender code "0"

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        var responseBody = result.ReadBodyToEnd();
        _testOutputHelper.WriteLine(responseBody);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        Contains("\"gender\":", responseBody); // Verify gender field exists
        Contains("2", responseBody); // Gender code "0" should be mapped to "2" (Unknown)
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Return_Physician_With_Null_Values()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "246810"; // Physician with null values

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        var responseBody = result.ReadBodyToEnd();
        _testOutputHelper.WriteLine(responseBody);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        Contains("\"phyNameEn\":", responseBody); // Verify phyNameEn field exists
        // Check that phyNameEn is empty
        var nameField = JObject.Parse(responseBody)["phyNameEn"].ToString();
        True(string.IsNullOrEmpty(nameField), "phyNameEn should be empty");
        Contains("\"gender\":", responseBody); // Verify gender field exists
        Contains("1", responseBody); // Null GENDER_CODE should default to "1" (Male)
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Not_Include_Photo_Without_Consent()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "654321"; // Physician without photo consent

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        var responseBody = result.ReadBodyToEnd();
        _testOutputHelper.WriteLine(responseBody);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        Contains("\"image\":", responseBody); // Verify image field exists
        // The image should be empty when no consent
        var imageField = JObject.Parse(responseBody)["image"].ToString();
        True(string.IsNullOrEmpty(imageField), "Image should be empty when no consent");
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianByPhysicianCode_Should_Handle_Null_Photo_Binary()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        const string phyCode = "789012"; // Physician with null photo binary

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianByPhysicianCode(request, phyCode);
        var result = response as MockHttpResponseData;
        var responseBody = result.ReadBodyToEnd();
        _testOutputHelper.WriteLine(responseBody);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        Contains("\"image\":", responseBody); // Verify image field exists
        // The image should be empty when photo binary is null
        var imageField = JObject.Parse(responseBody)["image"].ToString();
        True(string.IsNullOrEmpty(imageField), "Image should be empty when photo binary is null");
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_With_Gender_Filter_Female()
    {
        // Arrange
        const string queryParams = "?hcCode=OBK&gender=0&language=001&speciality=001&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        var responseBody = result.ReadBodyToEnd();
        _testOutputHelper.WriteLine(responseBody);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        // Should include the physician with gender code "2" (mapped to "0" - Female)
        Contains("654321", responseBody);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetFamilyPhysicianList_With_Gender_Filter_Unknown()
    {
        // Arrange
        const string queryParams = "?hcCode=OBK&gender=2&language=001&speciality=001&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryParams);

        // Act
        var response = await _attachmentFunctions.GetFamilyPhysicianList(request);
        var result = response as MockHttpResponseData;
        var responseBody = result.ReadBodyToEnd();
        _testOutputHelper.WriteLine(responseBody);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        // Should include the physician with gender code "0" (mapped to "2" - Unknown)
        Contains("789012", responseBody);
    }

    #endregion
}