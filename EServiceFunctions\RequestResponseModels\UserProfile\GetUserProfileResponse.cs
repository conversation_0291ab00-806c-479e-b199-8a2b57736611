﻿namespace EServiceFunctions.RequestResponseModels.UserProfile;

public class UserProfile
{
    public long QId { get; set; }
    public string? QIdExpiryDt { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? Dob { get; set; }
    public string? PrefSMSLang { get; set; }
    public string? PrefComMode { get; set; }
    public string? NationalityCode { get; set; }
    public string? NationalityEn { get; set; }
    public string? NationalityAr { get; set; }
    public string? GenderCode { get; set; }
    public string? GenderEn { get; set; }
    public string? GenderAr { get; set; }
    public string? VisaCode { get; set; }
    public string? VisaDescriptionEn { get; set; }
    public string? VisaDescriptionAr { get; set; }
    public string? OccupationCode { get; set; }
    public string? OccupationDescriptionEn { get; set; }
    public string? OccupationDescriptionAr { get; set; }
    public string? PhoneMobile { get; set; }
    public string? SecondaryPhoneMobile { get; set; }
    public string? AssignedHealthCenter { get; set; }
    public string? AssignedHealthCenterEn { get; set; }
    public string? AssignedHealthCenterAr { get; set; }
    public string? HcNumber { get; set; }
    public string? HcExpiryDate { get; set; }
    public string? GisAddressStreet { get; set; }
    public string? GisAddressBuilding { get; set; }
    public string? GisAddressZone { get; set; }
    public string? GisAddressUnit { get; set; }
    public string? GisAddressUpdatedAt { get; set; }
    public string? PhysicianId { get; set; }
    public string? PhysicianFullNameEn { get; set; }
    public string? PhysicianFullNameAr { get; set; }
    public bool? Consent { get; set; }
    public bool? IsStaff { get; set; }
    public bool? IsCitizen { get; set; }
    public bool? IsOGCC { get; set; }
}

public class GetUserProfileResponse : UserProfile
{
    public List<UserProfile>? Dependents { get; set; }
}