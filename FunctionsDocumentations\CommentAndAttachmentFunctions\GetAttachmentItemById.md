# GetAttachmentItemById

## Overview
Retrieves an attachment by its unique identifier (ID).

## Endpoint
- **Route**: `attachments/{id}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `id` (path, required): Attachment ID in GUID format
- `X-SendData` (header, optional): Boolean flag to include attachment data in response

## Responses
- **200 OK**: Returns attachment details
  ```json
  {
    "Id": "guid",
    "AttachmentName": "string",
    "AttachmentType": "string",
    "AttachmentData": "base64string",
    "SubmitterQId": "long",
    "CreatedAt": "datetime",
    "CreateSource": "string",
    "UpdatedAt": "datetime",
    "UpdateSource": "string"
  }
  ```
- **400 Bad Request**: Invalid ID format
- **401 Unauthorized**: QID mismatch
- **204 No Content**: Attachment not found

## Business Logic
1. Validates attachment ID format
2. Checks QID authorization
3. Retrieves attachment from database
4. Optionally includes attachment data based on X-SendData header

## Dependencies
- CommentAndAttachmentContext
- Authorization middleware
