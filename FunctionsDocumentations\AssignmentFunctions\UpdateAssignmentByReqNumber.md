# UpdateAssignmentByReqNumber

## Overview
Updates an existing assignment based on the request number.

## Endpoint
- **Route**: `assignments/{reqNumber}`
- **Method**: PUT
- **Authorization**: Function level

## Parameters
- `reqNumber` (path, required): Assignment request number

## Headers
- `X-RequestOrigin`: Required header indicating request origin

## Request Body
```json
{
  "QId": "long",
  "SubmitterQId": "long",
  "SubmitterEmail": "string",
  "SubmitterMobile": "string",
  "FNameEn": "string",
  "MNameEn": "string",
  "LNameEn": "string",
  "FNameAr": "string",
  "MNameAr": "string",
  "LNameAr": "string"
}
```

## Responses
- **200 OK**: Returns updated Assignment
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication failed
- **404 Not Found**: Assignment not found
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates request origin header
2. Validates request number
3. Verifies submitter's authorization
4. Updates assignment fields
5. Records update time and source

## Dependencies
- AssignmentContext

## Helper Methods
### MapCreateUpdateAssignmentRequestToAssignment
Converts CreateUpdateAssignmentRequest to Assignment entity

### GetCurrentTime
Returns current UTC time
