using EServiceFunctions.Functions;

namespace EServiceFunctions.RequestResponseModels.MHDS;

[ExcludeFromCodeCoverage]
public class GetMHDSItemResponse
{
    public string? ReqNumber { get; set; }
    public long? QId { get; set; }
    public string? FNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameEn { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameEn { get; set; }
    public string? LNameAr { get; set; }
    ///<summary>
    ///Static string of Name of the service to display in UI - {E-services Name)
    ///</summary>
    public string? EServiceName { get; set; } = nameof(MhdsFunctions);
    public string? HcNumber { get; set; }
    public string? CurrentAssignedHc { get; set; }
    public string? Nationality { get; set; }
    public string? Dob { get; set; }
    public int? BNo { get; set; }
    public int? ZNo { get; set; }
    public int? SNo { get; set; }
    public int? UNo { get; set; }
    public bool? IsGisAddressManualyEntered { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }
    public string? SecondaryPhoneMobile { get; set; }
    public List<MedicineInformation>? MedicineList { get; set; }
    public bool Consent { get; set; }
    public string? Status { get; set; }
    public string? StatusDescriptionEn { get; set; }
    public string? StatusDescriptionAr { get; set; }
    public string? SubmittedAt { get; set; }
    public string? CreatedAt { get; set; }
    public string? UpdatedAt { get; set; }
}