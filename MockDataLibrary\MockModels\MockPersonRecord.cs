﻿namespace MockDataLibrary.MockModels;

public static class MockPersonRecord
{
    public const string QId = "28614415181";
    public const long LongQId = 28614415181;
    public const string QId1 = "28614415182";
    public const long LongQId1 = 28614415182;
    public const string QId2 = "28614415183";
    public const long LongQId2 = 28614415183;
    public const string HcNumber1 = "**********";
    public const string HcNumber2 = "**********";
    public const string HcNumber3 = "**********";
    public const string FirstNameEn = "Mohamed";
    public const string MiddleNameEn = "Fazrin";
    public const string LastNameEn = "Farook";
    public const string FirstNameAr = "محمد";
    public const string MiddleNameAr = "فضرين";
    public const string LastNameAr = "فاروق";
    public const int Age = 36;
    public const string GenderCode = "1";
    public const string GenderEn = "Male";
    public const string GenderAr = "ذكر";
    public const string HealthCenterCode = "OBK";
    public const string HealthCenterNameEn = "<PERSON>";
    public const string NationalityCode = "144";
    public const string NationalityEn = "Sri Lanka";
    public const string NationalityAr = " سريلانكا";
    public const string VisaCode = "001";
    public const string VisaDescriptionEn = "Resident";
    public const string VisaDescriptionAr = "مقيم";
    public const string OccupationCode = "256";
    public const string OccupationDescriptionEn = "Senior Software Engineer";
    public const string OccupationDescriptionAr = "مهندس برمجيات كبير";
    public const string MobilePhone = "33253203";
    public const string SecondaryMobilePhone = "772049123";
    public const string UserEmail = "<EMAIL>";
}