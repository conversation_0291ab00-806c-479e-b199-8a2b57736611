﻿namespace EServiceFunctions.RequestResponseModels.Registration;

public class GetRegistrationItemResponse
{
     public long? QId { get; set; }
    public string? ReqNumber { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? Nationality { get; set; }
    public string? Dob { get; set; }
    public string? Gender { get; set; }
    public string? MaritalStatus { get; set; }
    public string? Education { get; set; }
    public string? Occupation { get; set; }
    public string? HomeTel { get; set; }
    public string? OfficeTel { get; set; }
    public string? MobileNo { get; set; }
    public string? NextOfKinName { get; set; }
    public string? NextOfKinLandLine { get; set; }
    public string? SponsorName { get; set; }
    public string? SponsorAddress { get; set; }
    public string? VisaType { get; set; }
    public int? BNo { get; set; }
    public int? ZNo { get; set; }
    public int? SNo { get; set; }
    public int? UNo { get; set; }
    public string? CatchmentHC { get; set; }
    public string? PrefHC { get; set; }
    public string? PrefSMSLang { get; set; }
    public string? PrefComMode { get; set; }
    public Guid? AttachId1 { get; set; }
    public Guid? AttachId2 { get; set; }
    public Guid? AttachId3 { get; set; }
    public Guid? AttachId4 { get; set; }
    public Guid? AttachId5 { get; set; }
    public Guid? AttachId6 { get; set; }
    public string? EmergencyContactID { get; set; }
    public string? SubmittedBy { get; set; }
    public string? SubmittedAt { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }
    public string? CreatedAt { get; set; }
    public string? UpdatedAt { get; set; }
    public string? Status { get; set; }
    public string? StatusDescriptionEn { get; set; }
    public string? StatusDescriptionAr { get; set; }
    public string? SN { get; set; }
    public string? GisAddressUpdatedAt { get; set; }
    public bool? IsGisAddressManualyEntered { get; set; }
}