using EServiceFunctions.Models.EServiceGeneric;
using EServiceFunctions.RequestResponseModels.Miscellaneous;
using System.Reflection; // Added for Assembly.GetExecutingAssembly()

namespace EServiceFunctions.Functions;

[ExcludeFromCodeCoverage]
public class MiscellaneousFunctions(
    IDbContextFactory<EServiceDbContext> dbContextFactory,
    ILogger<MiscellaneousFunctions> logger)
{
    #region GetRequestStatusStatsByQID

    /// <summary>
    /// Get the enrollment list for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetRequestStatusStatsByQID")]
    [OpenApiOperation(operationId: "GetRequestStatusStatsByQID", tags: ["Miscellaneous"],
        Summary = "Get My Tasks Count By Submitter's QID", Description = "Get My Tasks Count By Submitter's QID")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<MyTaskStatusStatsResponse>),
        Description = EmptyString)]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetRequestStatusStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "requeststatus-stats/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting My Task Status Stats By Submitter's QId {QId}", qId);

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning("Unauthorized ({QId}) Access!!!", qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            
            var response = await dbContext.MyTaskStatusStat!
                .FromSqlInterpolated($"EXEC dbo.SP_GetMyTaskStatusStats @SubmitterQId = {qId}")
                .AsNoTracking().ToListAsync();
            
            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for a given QID {QId}", qId);
                return await req.WriteNoContentResponseAsync();
            }
            
            var services = response.Select(i => i.Service).Distinct();
            var statuses = response.Select(i => i.Status).Distinct();
            var lstResponse = new List<MyTaskStatusStatsResponse>();
            
            foreach (var service in services)
            {
                Dictionary<string, int> dictionary = new();
                foreach (var unknown in from i in response where i.Service! == service select new { i.Status, Count = i.StatusCount })
                {
                    dictionary.TryAdd(unknown.Status!, unknown.Count);
                }
            
                MyTaskStatusStatsResponse item = new()
                {
                    EService = service,
                    ReqCount = dictionary.Values.Sum(),
                    Stats = dictionary
                };
            
                lstResponse.Add(item);
            }
            
            MyTaskStatusStatsResponse allServices = new()
            {
                EService = "All",
                Stats = new Dictionary<string, int>()
            };
            
            foreach (var status in statuses)
            {
                var statusCount = response
                    .Where(i => i.Status! == status)
                    .Sum(i => i.StatusCount);
                allServices.Stats.TryAdd(status!, statusCount);                
            }

            if(allServices.Stats.Count > 0)
            {
                allServices.ReqCount = allServices.Stats.Values.Sum();
            }               

            lstResponse.Add(allServices);
            logger.LogInformation("Returning My Task Status Stats List ({Count}) By Submitter's QID {QId}", lstResponse.Count, qId);
            
            return await req.WriteOkResponseAsync(lstResponse);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetAllStaticDDLs

    /// <summary>
    /// Get all static dropdown list values
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetAllStaticDDLs")]
    [OpenApiOperation(operationId: "GetAllStaticDDLs", tags: ["Miscellaneous"],
        Summary = "Get all static dropdown list values", Description = "Get all static dropdown list values")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetAllStaticDDLsResponse>),
        Description = EmptyString)]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetAllStaticDDLs(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "staticddls")]
        HttpRequestData req)
    {
        try
        {
            var submitter = req.GetClaims();
            logger.LogInformation("Getting all the static drop down list values of Submitter with QId: {QId}", submitter.QId);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.StaticDDLs!.FromSqlRaw("EXEC dbo.SP_GetAllStaticDropDownList")
                .ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("No data found");
                return await req.WriteNoContentResponseAsync();
            }

            var categories = response.Select(i => i.Category).Distinct();
            var lstResponse = new List<GetAllStaticDDLsResponse>();

            foreach (var category in categories)
            {
                GetAllStaticDDLsResponse item = new()
                {
                    Category = category,
                    Data = response.Where(i => i.Category! == category).Select(i => new DropdownData
                    {
                        Code = i.Code,
                        DescriptionEn = i.DescriptionEn,
                        DescriptionAr = i.DescriptionAr
                    }).ToList()
                };
                lstResponse.Add(item);
            }

            logger.LogInformation("Returning all the static drop down list values");
            
            return await req.WriteOkResponseAsync(lstResponse);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetApiHealth

    /// <summary>
    /// Check the API health status
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetApiHealth")]
    [OpenApiOperation(operationId: "GetApiHealth", tags: ["Miscellaneous"],
        Summary = "Check API Health", Description = "Check the health status of the API")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(string),
        Description = "API is healthy")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetApiHealth(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "health")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Checking API health status");
            return await req.WriteOkResponseAsync("API is healthy");
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetAssemblyVersion

    /// <summary>
    /// Get the assembly version of the EServiceFunctions
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetAssemblyVersion")]
    [OpenApiOperation(operationId: "GetAssemblyVersion", tags: ["Miscellaneous"],
        Summary = "Get Assembly Version", Description = "Retrieve the assembly version of EServiceFunctions")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(string),
        Description = "Assembly version")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetAssemblyVersion(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "assembly-version")]
        HttpRequestData req)
    {
        try
        {
            var version = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "Version not found";
            logger.LogInformation("Returning Assembly Version: {Version}", version);
            return await req.WriteOkResponseAsync(version);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}