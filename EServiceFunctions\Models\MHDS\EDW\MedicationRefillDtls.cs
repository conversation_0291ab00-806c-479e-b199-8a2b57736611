﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.MHDS.EDW;

[ExcludeFromCodeCoverage]
[Table("MEDICATION_REFILL_DTLS")]
public class MedicationRefillDtls
{
    [Key]
    [Column("PERSON_ID")]
    public long? PersonId { get; set; }
    [Column("QID")]
    [StringLength(50)]
    public string? QId { get; set; }
    [Column("HC_NUMBER")]
    [StringLength(50)]
    public string? HcNumber { get; set; }
    [Column("NAME_FIRST")]
    [StringLength(100)]
    public string? NameFirst { get; set; }
    [Column("NAME_MIDDLE")]
    [StringLength(100)]
    public string? NameMiddle { get; set; }
    [Column("NAME_LAST")]
    [StringLength(100)]
    public string? NameLast { get; set; }
    [Column("AGE_YEARS")]
    public int? AgeYears { get; set; }
    [Column("MOBILE_PHONE")]
    [StringLength(50)]
    public string? MobilePhone { get; set; }
    [Column("REFILL_DUE_DATE", TypeName = "date")]
    public DateTime? PrescriptionRefillDueDate { get; set; }
    [Column("LAST_DISPENSE_DATE", TypeName = "date")]
    public DateTime? LastDispenseDate { get; set; }
    [Column("LAST_DISPENSE_FAC_CODE")]
    [StringLength(50)]
    public string? LastDispenseFacCode { get; set; }
    [Column("PRESCRIPTION_DATE", TypeName = "date")]
    public DateTime? PrescriptionDate { get; set; }
    [Column("PRESCRIPTION_FAC_CODE")]
    [StringLength(50)]
    public string? PrescriptionFacCode { get; set; }
    [Column("PRESCRIBED_MEDICATION")]
    [StringLength(200)]
    public string? PrescribedMedication { get; set; }
    [Column("DAYS_SUPPLY")]
    [StringLength(50)]
    public int SupplyDuration { get; set; }
    [Column("PRESCRIPTION_ORDER_ID")]
    public long? PrescriptionOrderId { get; set; }
    [Column("PRESCRIBED_BY_CORP_ID")]
    [StringLength(10)]
    public string? PrescribedByCorpId { get; set; }
    [Column("PRESCRIBED_BY_NAME_ENG")]
    [StringLength(250)]
    public string? PrescribedByNameEng { get; set; }
    [Column("PRESCRIBED_BY_NAME_ARA")]
    [StringLength(300)]
    public string? PrescribedByNameAra { get; set; }
    [Column("REFILLS_REMAINING")]
    public int? RefillsRemaining { get; set; }
    [Column("ETL_LOAD_DATETIME", TypeName = "datetime")]
    public DateTime? EtlLoadDatetime { get; set; }
}