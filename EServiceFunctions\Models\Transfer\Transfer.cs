﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.Transfer;
public class Transfer
{
    [Column("Id")]
    public int Id { get; set; }
    [Column("QId")]
    public long? QId { get; set; }
    [Column("FNameEn")]
    public string? FNameEn { get; set; }
    [Column("MNameEn")]
    public string? MNameEn { get; set; }
    [Column("LNameEn")]
    public string? LNameEn { get; set; }
    [Column("FNameAr")]
    public string? FNameAr { get; set; }
    [Column("MNameAr")]
    public string? MNameAr { get; set; }
    [Column("LNameAr")]
    public string? LNameAr { get; set; }
    [Column("ReqNumber")]
    public string? ReqNumber { get; set; }
    [Column("Nationality")]
    public string? Nationality { get; set; }
    [Column("Dob")]
    public DateTime? Dob { get; set; }
    [Column("Consent")]
    public bool? Consent { get; set; }
    [Column("HCNumber")]
    public string? HCNumber { get; set; }
    [Column("BNo")]
    public int? BNo { get; set; }
    [Column("ZNo")]
    public int? ZNo { get; set; }
    [Column("SNo")]
    public int? SNo { get; set; }
    [Column("UNo")]
    public int? UNo { get; set; }
    [Column("CurrentHC")]
    public string? CurrentHC { get; set; }
    [Column("CatchmentHC")]
    public string? CatchmentHC { get; set; }
    [Column("PrefHC")]
    public string? PrefHC { get; set; }
    [Column("AttachId1")]
    public Guid? AttachId1 { get; set; }
    [Column("AttachId2")]
    public Guid? AttachId2 { get; set; }
    [Column("AttachId3")]
    public Guid? AttachId3 { get; set; }
    [Column("AttachId4")]
    public Guid? AttachId4 { get; set; }
    [Column("AttachId5")]
    public Guid? AttachId5 { get; set; }
    [Column("AttachId6")]
    public Guid? AttachId6 { get; set; }
    [Column("TransferReason")]
    public string? TransferReason { get; set; }
    [Column("SubmittedBy")]
    public string? SubmittedBy { get; set; }
    [Column("SubmittedAt")]
    public DateTime? SubmittedAt { get; set; }
    [Column("SubmitterQId")]
    public long? SubmitterQId { get; set; }
    [Column("SubmitterEmail")]
    public string? SubmitterEmail { get; set; }
    [Column("SubmitterMobile")]
    public string? SubmitterMobile { get; set; }
    [Column("CreatedAt")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    [Column("UpdatedAt")]
    public DateTime? UpdatedAt { get; set; }
    [Column("Status")]
    public string? Status { get; set; }
    [Column("StatusInternal")]
    public string? StatusInternal { get; set; }
    [Column("SN")]
    public string? SN { get; set; }
    [Column("GisAddressUpdatedAt")]
    public DateTime? GisAddressUpdatedAt { get; set; }
    [Column("IsGisAddressManualyEntered")]
    public bool? IsGisAddressManualyEntered { get; set; }
    [Column("CreateSource")]
    public string? CreateSource { get; set; }
    [Column("UpdateSource")]
    public string? UpdateSource { get; set; }
    [Column("StatusNavigation")]
    public Status? StatusNavigation { get; set; }
    [Column("TransferReasonNavigation")]
    public TransferReason? TransferReasonNavigation { get; set; }
}