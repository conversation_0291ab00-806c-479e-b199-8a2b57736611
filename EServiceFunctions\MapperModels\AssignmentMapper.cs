﻿using EServiceFunctions.Models.Assignment;
using EServiceFunctions.RequestResponseModels.Assignment;

namespace EServiceFunctions.MapperModels;

public static class AssignmentMapper
{
    public static GetAssignmentItemResponse MapAssignmentToGetAssignmentItemResponse(Assignment assignment)
    {
        return new GetAssignmentItemResponse
        {
            QId = assignment.QId,
            FNameEn = assignment.FNameEn,
            MNameEn = assignment.MNameEn,
            LNameEn = assignment.LNameEn,
            FNameAr = assignment.FNameAr,
            MNameAr = assignment.MNameAr,
            LNameAr = assignment.LNameAr,
            ReqNumber = assignment.ReqNumber,
            HcNumber = assignment.HCNumber,
            Nationality = assignment.Nationality,
            Dob = assignment.Dob.ToUtcString(),
            CurrentAssignedHc = assignment.CurrentAssignedHC,
            CurrentPhysician = assignment.CurrentPhysician,
            SelectedPhysician = assignment.SelectedPhysician,
            AttachId1 = assignment.AttachId1,
            AttachId2 = assignment.AttachId2,
            AttachId3 = assignment.AttachId3,
            AttachId4 = assignment.AttachId4,
            AttachId5 = assignment.AttachId5,
            AttachId6 = assignment.AttachId6,
            ChangeReason = assignment.ChangeReason,
            SubmittedBy = assignment.SubmittedBy,
            SubmittedAt = assignment.SubmittedAt.ToUtcString(),
            SubmitterQId = assignment.SubmitterQId,
            SubmitterEmail = assignment.SubmitterEmail,
            SubmitterMobile = assignment.SubmitterMobile,
            CreatedAt = assignment.CreatedAt.ToUtcString(),
            UpdatedAt = assignment.UpdatedAt.ToUtcString(),
            Status = assignment.StatusNavigation?.Code,
            StatusDescriptionEn = assignment.StatusNavigation!.DescriptionEn,
            StatusDescriptionAr = assignment.StatusNavigation!.DescriptionAr,
            Sn = assignment.SN
        };
    }

    public static Assignment MapCreateUpdateAssignmentRequestToAssignment(
        CreateUpdateAssignmentRequest createUpdateAssignmentRequest)
    {
        return new Assignment
        {
            QId = createUpdateAssignmentRequest.QId,
            FNameEn = createUpdateAssignmentRequest.FNameEn,
            MNameEn = createUpdateAssignmentRequest.MNameEn,
            LNameEn = createUpdateAssignmentRequest.LNameEn,
            FNameAr = createUpdateAssignmentRequest.FNameAr,
            MNameAr = createUpdateAssignmentRequest.MNameAr,
            LNameAr = createUpdateAssignmentRequest.LNameAr,
            HCNumber = createUpdateAssignmentRequest.HCNumber,
            Nationality = createUpdateAssignmentRequest.Nationality,
            Dob = createUpdateAssignmentRequest.Dob,
            CurrentAssignedHC = createUpdateAssignmentRequest.CurrentAssignedHC,
            CurrentPhysician = createUpdateAssignmentRequest.CurrentPhysician,
            SelectedPhysician = createUpdateAssignmentRequest.SelectedPhysician,
            AttachId1 = createUpdateAssignmentRequest.AttachId1,
            AttachId2 = createUpdateAssignmentRequest.AttachId2,
            AttachId3 = createUpdateAssignmentRequest.AttachId3,
            AttachId4 = createUpdateAssignmentRequest.AttachId4,
            AttachId5 = createUpdateAssignmentRequest.AttachId5,
            AttachId6 = createUpdateAssignmentRequest.AttachId6,
            ChangeReason = createUpdateAssignmentRequest.ChangeReason,
            SubmittedBy = createUpdateAssignmentRequest.SubmittedBy,
            SubmitterQId = createUpdateAssignmentRequest.SubmitterQId,
            SubmitterEmail = createUpdateAssignmentRequest.SubmitterEmail,
            SubmitterMobile = createUpdateAssignmentRequest.SubmitterMobile
        };
    }
}