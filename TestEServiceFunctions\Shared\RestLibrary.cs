﻿namespace TestEServiceFunctions.Shared;

[ExcludeFromCodeCoverage]
public class RestLibrary : IRestLibrary
{
    public RestLibrary()
    {
        var isDev = ReadFromConfiguration("IsDev");
        var baseUrl = ReadFromConfiguration(isDev == "true" ? "DevBaseUrl" : "LocalBaseUrl");

        var restClientOptions = new RestClientOptions
        {
            BaseUrl = new Uri(baseUrl!)
        };
        
        ServicePointManager.ServerCertificateValidationCallback += ValidateCertificate!;
        Client = new RestClient(restClientOptions);
    }

    private static bool ValidateCertificate(object sender, X509Certificate certificate, X509Chain chain,
        SslPolicyErrors sslPolicyErrors) => true;

    public RestClient Client { get; }
}
