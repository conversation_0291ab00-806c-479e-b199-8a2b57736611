global using System.Diagnostics.CodeAnalysis;
global using System.Net;

global using Bogus;

global using EServiceFunctions.Functions;

global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Logging;

global using Moq;

global using SixLabors.ImageSharp;
global using SixLabors.ImageSharp.Formats.Png;
global using SixLabors.ImageSharp.PixelFormats;
global using SixLabors.ImageSharp.Processing;

global using TestDoublesForUnitTest;

global using Xunit;
global using Xunit.Abstractions;

global using static System.DateTime;

global using static UnitTestEServiceFunctions.MockDataForAppointmentFunctions;

global using static Xunit.Assert;

global using static MockDataLibrary.GlobalConstants;
global using static MockDataLibrary.GlobalStatus;
global using static MockDataLibrary.MockModels.MockPersonRecord;

global using static Newtonsoft.Json.JsonConvert;

global using static UnitTestEServiceFunctions.GlobalFunctions;

using Microsoft.Azure.Functions.Worker.Http;

using System.Text;

using static Newtonsoft.Json.Formatting;

namespace UnitTestEServiceFunctions;

public static class GlobalFunctions
{

    public static string LogResponse(ITestOutputHelper outputHelper, HttpResponseData? data)
    {
        try
        {
            if (data is null) return "data is null";

            // Save the current position
            long originalPosition = data.Body.Position;

            // Reset to beginning of stream
            data.Body.Position = 0;

            // Use a non-disposing reader to avoid closing the stream
            var reader = new StreamReader(data.Body, leaveOpen: true);
            string json = reader.ReadToEnd();

            // Reset position back to where it was
            data.Body.Position = originalPosition;

            try
            {
                var jsonObject = DeserializeObject(json);
                string formatted = SerializeObject(jsonObject, Indented);
                outputHelper.WriteLine(formatted);
                return formatted;
            }
            catch
            {
                // If we can't deserialize as JSON, return the raw string
                outputHelper.WriteLine(json);
                return json;
            }
        }
        catch (Exception exception)
        {
            outputHelper.WriteLine(exception.Message + "\n" + exception.StackTrace);
            return string.Empty;
        }
    }
    public static DateTime GetCurrentTime() => UtcNow.AddHours(3);

    public static string ReadBodyToEnd(this HttpResponseData response)
    {
        // Save the current position
        long originalPosition = response.Body.Position;

        // Reset to the beginning of stream
        response.Body.Position = 0;

        // Use a non-disposing reader to avoid closing the stream
        var reader = new StreamReader(response.Body, leaveOpen: true);
        string json = reader.ReadToEnd();

        // Reset position back to where it was
        response.Body.Position = originalPosition;

        try
        {
            var jsonObject = DeserializeObject(json);
            return SerializeObject(jsonObject, Indented);
        }
        catch
        {
            // If we can't deserialize as JSON, return the raw string
            return json;
        }
    }

    public static string GetResponseText(MockHttpResponseData response)
    {
        // Save the current position
        long position = response.Body.Position;

        // Reset to beginning of stream
        response.Body.Position = 0;

        // Use a non-disposing reader to avoid closing the stream
        var reader = new StreamReader(response.Body, Encoding.UTF8, leaveOpen: true);
        string text = reader.ReadToEnd();

        // Reset position back to where it was
        response.Body.Position = position;

        try
        {
            var jsonObject = DeserializeObject(text);
            return SerializeObject(jsonObject, Indented);
        }
        catch
        {
            // If we can't deserialize as JSON, return the raw string
            return text;
        }
    }
}