# Get Medicine List Tests

## Test Cases

### 1. TestingGetMhdsListByHcNumber

Tests retrieval of medicine list by health card numbers.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestingGetMhdsListByHcNumber()
{
    // Arrange
    var hcNumberList = new RequestMHDSMedicineList
    {
        HealthCardNumbers = new List<string> { "HC02288616", "HC02222743", "HC02286889" }
    };
    var request = GetRestRequest("mhds/medicinelist");
    request.AddJsonBody(hcNumberList);

    // Act
    var response = await _client.PostAsync<List<GetMedicineList>>(request);

    // Assert
    True(response is not null);
    True(response.Count > 0);
    True(response[0].MedicineInformation?.Count > 0);
    response.ForEach(x => x.MedicineInformation?.ForEach(q => NotEmpty(q.MedicineName!)));
}
```

### 2. TestingGetMhdsListByHcNumberWithInvalidHcNumber

Tests medicine list retrieval with invalid health card numbers.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestingGetMhdsListByHcNumberWithInvalidHcNumber()
{
    // Arrange
    var hcNumberList = new RequestMHDSMedicineList
    {
        HealthCardNumbers = new List<string> { "HC0228861", "HC0222274", "HC0228688" }
    };
    var request = GetRestRequest("mhds/medicinelist");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);
    request.AddJsonBody(hcNumberList);

    // Act
    var response = await _client.PostAsync<List<GetMedicineList>>(request);

    // Assert
    Null(response);
}
```

## Request Details

### Endpoint
```
POST mhds/medicinelist
```

### Headers
- `JwtClaimsQId`: "22222222270" (for error case)

### Request Body
```csharp
public class RequestMHDSMedicineList
{
    public List<string> HealthCardNumbers { get; set; }
}
```

## Response Model

### GetMedicineList
```csharp
public class GetMedicineList
{
    public List<MedicineInformation> MedicineInformation { get; set; }
}

public class MedicineInformation
{
    public string MedicineName { get; set; }
    // Other medicine details
}
```

## Test Data

### Success Case
Valid Health Card Numbers:
- "HC02288616"
- "HC02222743"
- "HC02286889"

### Error Case
Invalid Health Card Numbers:
- "HC0228861"
- "HC0222274"
- "HC0228688"

## Validation Rules

### Success Case Validation
1. Response not null
2. Contains medicine lists
3. Medicine information present
4. Medicine names not empty

### Error Case Validation
1. Response is null

## Error Cases

1. **Invalid Health Card Numbers**
   - Wrong format
   - Non-existent numbers
   - Invalid length

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID
   - Insufficient permissions

3. **Data Errors**
   - No medicines found
   - System unavailable
   - Database errors

## Notes

1. **Request Format**
   - Multiple HC numbers
   - Batch processing
   - Efficient retrieval

2. **Response Format**
   - List of medicine lists
   - Nested information
   - Complete details

3. **Performance**
   - Batch processing
   - Multiple records
   - Efficient queries
