using EServiceFunctions.Functions;

namespace EServiceFunctions.RequestResponseModels.MHDS;

[ExcludeFromCodeCoverage]
public class MHDSMedicineListByRequestNumber: MHDSRequestList
{
    public List<MedicineInformation>? MedicineList { get; set; }
}

[ExcludeFromCodeCoverage]
public class MHDSRequestList
{
    public string? ReqNumber { get; set; }
    public long? QId { get; set; }
    public string? FNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameEn { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameEn { get; set; }
    public string? LNameAr { get; set; }
    ///<summary>
    ///Static string of Name of the service to display in UI - {E-services Name)
    ///</summary>
    public string? EServiceName { get; set; } = nameof(MhdsFunctions);             
    public string? Status { get; set; }
    public string? StatusDescriptionEn { get; set; }
    public string? StatusDescriptionAr { get; set; }
    public string? SubmittedAt { get; set; }

}