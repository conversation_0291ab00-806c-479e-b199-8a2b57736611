﻿using System.Net;

using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;

namespace TestDoublesForUnitTest;

public class MockHttpResponseData(FunctionContext functionContext, HttpStatusCode status)
    : HttpResponseData(functionContext)
{
    public sealed override HttpStatusCode StatusCode { get; set; } = status;
    public override HttpHeadersCollection Headers { get; set; } = [];
    public override Stream Body { get; set; } = new MemoryStream();
    public override HttpCookies Cookies { get; }
}