# Get Transfer List Tests

## Test Cases

### 1. TestGetTransferListByQId

Tests retrieval of transfer list by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetTransferListByQId()
{
    // Arrange
    var request = GetRestRequest("transfers/submitter-qid/{qId}");
    request.AddUrlSegment("qId", GetMoqUser().UserQId);
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.GetAsync<List<GetTransferListResponse>>(request);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.Count > 0);
    Contains(response, x => x.QId == 94733427819);
    Contains(response, x => x.ReqNumber == "SV1ZCMN9RX1");
    Contains(response, x => x.FNameEn == "Jett");
}
```

### 2. TestGetTransferListByInvalidQId

Tests list retrieval with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetTransferListByInvalidQId()
{
    // Arrange
    var request = GetRestRequest("transfers/submitter-qid/{qId}");
    request.Method = Method.Get;
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222273);
    request.AddUrlSegment("qId", 22222222273);
    
    // Act
    var response = await _client.ExecuteAsync(request);

    // Assert
    True(response.StatusCode == NoContent);
}
```

## Request Details

### Endpoint
```
GET transfers/submitter-qid/{qId}
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `qId`: Submitter QID

## Response Model

### GetTransferListResponse
```csharp
public class GetTransferListResponse
{
    public long QId { get; set; }
    public string ReqNumber { get; set; }
    public string FNameEn { get; set; }
    // Other transfer details
}
```

## Test Data

### Success Case
- QID: From GetMoqUser().UserQId
- Expected:
  * Count > 0
  * Contains QID: 94733427819
  * Contains ReqNumber: "SV1ZCMN9RX1"
  * Contains FNameEn: "Jett"

### Error Case
- QID: 22222222273
- Expected: NoContent status code

## Validation Rules

### Success Case Validation
1. Response not null
2. Response count greater than 0
3. Contains specific QID
4. Contains specific request number
5. Contains specific name

### Error Case Validation
1. Status code is NoContent

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Unauthorized access

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - QID mismatch

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Response Format**
   - List of transfers
   - Complete transfer details
   - Personal information

3. **Performance**
   - Multiple record retrieval
   - Efficient query
   - Status filtering
