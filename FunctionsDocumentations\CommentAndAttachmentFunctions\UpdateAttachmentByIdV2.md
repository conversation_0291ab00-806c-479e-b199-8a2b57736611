# UpdateAttachmentByIdV2

## Overview
Updates an existing attachment using multipart/form-data format.

## Endpoint
- **Route**: `attachments-v2/{id}`
- **Method**: PUT
- **Authorization**: Function level

## Parameters
- `id` (path, required): Attachment ID in GUID format

## Headers
- `X-RequestOrigin`: Required header indicating request origin
- `Content-Type`: Must be multipart/form-data

## Request Body
- Form data containing file
- File must be included in the form data

## Responses
- **200 OK**: Returns updated attachment
- **400 Bad Request**: 
  - Invalid ID format
  - Invalid form data
  - Invalid file type
  - File size exceeds limit
  - Missing file in form data
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates attachment ID format
2. Validates request origin header
3. Processes multipart form data
4. Extracts file content and metadata
5. Validates file extension and size
6. Updates attachment record
7. Returns updated attachment

## Validation Rules
- Maximum file size: 5MB
- File extension must be valid
- Request origin must be provided
- File must be present in form data
- Attachment must exist

## Dependencies
- CommentAndAttachmentContext
- MultipartReader
- File validation utilities
