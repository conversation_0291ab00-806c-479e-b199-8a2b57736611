# GetClinicList

## Overview
Retrieves a filtered list of clinics with support for appointment-related filtering.

## Endpoint
- **Route**: `clinics`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **hcCode** (query, optional): Filter by health center code
- **clinicCode** (query, optional): Filter by clinic code
- **isNewAppointment** (query, optional): Filter clinics available for new appointments
- **isEditable** (query, optional): Filter clinics that allow appointment changes
- **skip** (query, optional): Number of records to skip
- **take** (query, optional): Number of records to take

## Response
- **200 OK**: Returns list of clinics
  ```json
  [
    {
      "ClinicCode": "string",
      "ClinicNameEn": "string",
      "ClinicNameAr": "string"
    }
  ]
  ```
- **204 No Content**: No clinics found

## Business Logic
1. Applies filters:
   - Health center code
   - Clinic code
   - New appointment availability (IS_SELF_REFERRAL = 1)
   - Appointment editability (IS_RESCHEDULE = 1 or IS_CANCEL = 1)
2. Only returns active clinics (ACTIVE_IND = 1)
3. Implements pagination
4. Returns distinct results

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient filtering with Where clauses
- Pagination implementation
- Distinct results to avoid duplicates

## Appointment Features
- Self-referral support
- Rescheduling capability
- Cancellation support

## Multilingual Support
- Bilingual clinic names (English/Arabic)
