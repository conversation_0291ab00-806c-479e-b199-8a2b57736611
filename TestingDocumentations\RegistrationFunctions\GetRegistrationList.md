# Get Registration List Tests

## Test Cases

### 1. TestGetRegistrationListByQId

Tests retrieval of registration list by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetRegistrationListByQId()
{
    // Arrange
    var request = GetRestRequest("registrations/submitter-qid/{qId}");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);
    request.AddUrlSegment("qId", 22222222270);

    // Act
    var response = await _client.GetAsync<List<GetRegistrationListResponse>>(request);

    // Assert
    True(response.Count > 2);
    Contains(response, item => item.QId == 33322222237);
    Contains(response, item => item.Status == Cancelled);
}
```

### 2. TestGetRegistrationListByQIdWithInvalidQId

Tests list retrieval with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetRegistrationListByQIdWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("registrations/submitter-qid/{qId}");
    request.AddOrUpdateHeader(JwtClaimsQId, 33322222230);
    request.AddUrlSegment("qId", 33322222230);

    // Act
    var response = await _client.GetAsync<List<GetRegistrationListResponse>>(request);

    // Assert
    Null(response);
}
```

## Request Details

### Endpoint
```
GET registrations/submitter-qid/{qId}
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `qId`: Submitter QID

## Response Model

### GetRegistrationListResponse
```csharp
public class GetRegistrationListResponse
{
    public long QId { get; set; }
    public string Status { get; set; }
    // Other registration details
}
```

## Test Data

### Success Case
- QID: 22222222270
- Expected:
  * Count > 2
  * Contains QID: 33322222237
  * Contains Status: Cancelled

### Error Case
- QID: 33322222230
- Expected: Null response

## Validation Rules

### Success Case Validation
1. Response count greater than 2
2. Contains specific QID
3. Contains specific status

### Error Case Validation
1. Response is null

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Unauthorized access

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - QID mismatch

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Response Format**
   - List of registrations
   - Complete registration details
   - Status information

3. **Performance**
   - Multiple record retrieval
   - Efficient query
   - Status filtering
