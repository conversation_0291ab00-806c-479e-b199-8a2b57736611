﻿namespace EServiceFunctions.Models.MHDS;

public class MHDSContext(DbContextOptions<MHDSContext> options) : DbContext(options)
{
    public virtual DbSet<MHDSRequestDetails>? MHDSRequestDetails { get; set; }
    public virtual DbSet<MHDSRequestMedicineList>? MHDSRequestMedicineList { get; set; }
    public virtual DbSet<Status>? Status { get; set; }
        

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<MHDSRequestDetails>(entity =>
        {
            entity.Property(e => e.Id).IsRequired();
            entity.Property(e => e.QId).HasMaxLength(255);
            entity.Property(e => e.FNameEn).HasMaxLength(255);
            entity.Property(e => e.MNameEn).HasMaxLength(255);
            entity.Property(e => e.LNameEn).HasMaxLength(255);
            entity.Property(e => e.FNameAr).HasMaxLength(255);
            entity.Property(e => e.MNameAr).HasMaxLength(255);
            entity.Property(e => e.LNameAr).HasMaxLength(255);
            entity.Property(e => e.ReqNumber).HasMaxLength(255);
            entity.Property(e => e.HcNumber).HasMaxLength(255);
            entity.Property(e => e.Nationality).HasMaxLength(255);
            entity.Property(e => e.Dob).HasColumnType("date");
            entity.Property(e => e.CurrentAssignedHc).HasMaxLength(255);
            entity.Property(e => e.Relationship).HasMaxLength(255);
            entity.Property(e => e.SecondaryPhoneMobile).HasMaxLength(255);
            entity.Property(e => e.Status).HasMaxLength(255);
            entity.Property(e => e.StatusInternal).HasMaxLength(255);
            entity.Property(e => e.SubmittedAt).HasColumnType("datetime");
            entity.Property(e => e.SubmittedBy).HasMaxLength(255);
            entity.Property(e => e.SubmitterEmail).HasMaxLength(255);
            entity.Property(e => e.SubmitterMobile).HasMaxLength(255);
            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");
            entity.Property(e => e.CreateSource).HasMaxLength(255);
            entity.Property(e => e.UpdateSource).HasMaxLength(255);

            entity.HasOne(d => d.StatusNavigation)
                .WithMany(p => p.MHDSRequestDetails)
                .HasForeignKey(d => d.Status)
                .HasConstraintName("MHDSRequestDetails_Status_Code_fk");

            entity.HasIndex(e => e.QId)
                .HasDatabaseName("IDX_MHDS_QId");

            entity.HasIndex(e => e.ReqNumber)
                .HasDatabaseName("UQ__MHDSRequ__237F34231E8BB0B2")
                .IsUnique();

            entity.HasIndex(e => e.SubmitterQId)
                .HasDatabaseName("IDX_MHDS_SubmitterQId");
        });

        modelBuilder.Entity<MHDSRequestMedicineList>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedOnAdd();

            entity.HasOne(d => d.RequestNumberNavigation)
                .WithMany()
                .HasPrincipalKey(p => p.ReqNumber)
                .HasForeignKey(d => d.RequestNumber)
                .HasConstraintName("FK__MHDSReque__Reque__6F7F8B4B");
        });


        modelBuilder.Entity<Status>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Status__A25C5AA697217193");

            entity.Property(e => e.Code).HasMaxLength(255);
            entity.Property(e => e.Category).HasMaxLength(255);
            entity.Property(e => e.DescriptionAr).HasMaxLength(255);
            entity.Property(e => e.DescriptionEn).HasMaxLength(255);
            entity.Property(e => e.NrkCategory).HasMaxLength(255);
        });
    }
}