# GetAppointmentStatsByQId

## Overview
Retrieves appointment statistics for a given QID, including counts of appointments in different states.

## Endpoint
- **Route**: `appointments/stats/{qId}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `qId` (path, required): QID to get statistics for
- `EligibleClinicCodes` (header, required): List of eligible clinic codes

## Responses
- **200 OK**: Returns `AppointmentStatsResponse`
  ```json
  {
    "InProcessCount": integer,
    "ArchivedCount": integer
  }
  ```
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication failed
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates submitter's authorization
2. Counts appointments in "InProcess" status (Submitted, Rework, Reworked)
3. Counts appointments in "Archived" status (Approved, Cancelled, CancelledByEServ, Rejected)
4. Returns combined statistics

## Dependencies
- AppointmentContext
- Status entity
