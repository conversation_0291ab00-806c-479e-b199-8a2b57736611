﻿namespace TestEServiceFunctions;

[Collection("CovidVaccineFunctions")]
public class TestCovidVaccineFunctions(ITestOutputHelper testOutputHelper, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCreateCovidVaccinationRequest()
    {
        // Arrange
        var nationalityCodeList = MockNationality().Select(c => c.NatCode).ToList();
        var request = GetRestRequest("covidvaccinationrequest");

        var vaccineRequest = new Faker<CovidVaccinationRequest>()
            .RuleFor(x => x.IDType, "QID")
            .RuleFor(x => x.IDNumber, "28435668645")
            .RuleFor(x => x.FullNameEn, FullNameEnglish)
            .RuleFor(x => x.FullNameAr, FullNameArabic)
            .RuleFor(x => x.<PERSON>Number, "50110272")
            .RuleFor(x => x.DOB, f => f.Date.Past(30).AddYears(-18))
            .RuleFor(x => x.Gender, f => f.PickRandom("Male", "Female"))
            .RuleFor(x => x.Nationality, f => f.PickRandom(nationalityCodeList))
            .RuleFor(x => x.Email, f => f.Internet.Email()).Generate();

        request.AddJsonBody(vaccineRequest);
        testOutputHelper.LogToConsole(request);
        request.Method = Method.Post;
        
        // Act
        var response = await _client.ExecuteAsync<CreateCovidVaccinationResponse>(request);
        
        // Assert.
        True(response is not null); //Not in use 
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCreateCovidVaccinationRequestWithInvalidObject()
    {
        // Arrange
        var request = GetRestRequest("covidvaccinationrequest");
        var vaccine = new CovidVaccinationRequest();
        request.AddJsonBody(vaccine);
        request.Method = Method.Post;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        response.ThrowIfNull();
        Equal(BadRequest, response.StatusCode);
    }
}