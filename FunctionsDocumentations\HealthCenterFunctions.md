# Health Center Functions Documentation

## Overview
The Health Center Functions module provides REST API endpoints for managing and retrieving information about health centers, clinics, and their operational schedules. This module enables searching and filtering of health centers and clinics based on various criteria, including purpose (Regular/Covid19), clinic codes, and shift timings. It is designed to support the Primary Health Care Corporation's (PHCC) facility management system with comprehensive bilingual support.

## API Endpoints

### 1. Get Health Center List
- **Endpoint**: `GET /healthcenters`
- **Description**: Retrieves a filtered list of active PHCC health centers
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `hcCode` (query, optional): Health Center Code for specific facility lookup
  - `hcPurpose` (query, optional): Health Center Purpose
    - "Regular": Standard health centers
    - "Covid19": COVID-19 designated facilities
  - `clinicCode` (query, optional): Filter by specific clinic availability
  - `skip` (query, optional): Pagination offset
  - `take` (query, optional): Page size
- **Response Format**:
  ```json
  [
    {
      "HCntCode": "string",       // Unique health center identifier
      "HCntNameEn": "string",     // English name
      "HCntNameAr": "string",     // Arabic name
      "HCntPurpose": "string"     // "Regular" or "Covid19"
    }
  ]
  ```
- **Responses**:
  - 200: Success with health center list
  - 204: No matching health centers found
  - 400: Invalid parameters or request
  - 500: Server error
- **Filters**:
  - Organization: PHCC only
  - Facility Status: Active only
  - Facility Type: Health Center only
  - Region: Must be specified

### 2. Get Clinic List
- **Endpoint**: `GET /clinics`
- **Description**: Retrieves a list of clinics with appointment scheduling capabilities
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `hcCode` (query, optional): Filter by health center
  - `clinicCode` (query, optional): Specific clinic lookup
  - `isNewAppointment` (query, optional): 
    - `true`: Only clinics accepting new appointments
    - `false`: All clinics
  - `isEditable` (query, optional):
    - `true`: Only clinics allowing schedule modifications
    - `false`: All clinics
  - `skip` (query, optional): Pagination offset
  - `take` (query, optional): Page size
- **Response Format**:
  ```json
  [
    {
      "ClinicCode": "string",     // Unique clinic identifier
      "ClinicNameEn": "string",   // English name
      "ClinicNameAr": "string"    // Arabic name
    }
  ]
  ```
- **Response Details**:
  - Active clinics only (ACTIVE_IND = 1)
  - Self-referral status for new appointments
  - Rescheduling/cancellation capabilities
- **Responses**:
  - 200: Success with clinic list
  - 204: No matching clinics found
  - 500: Server error

### 3. Get Clinic Shift List By Health Center Code
- **Endpoint**: `GET /healthcenters/{hcCode}/clinic-shift`
- **Description**: Retrieves available clinic shifts for a specific health center
- **Authorization**: Function-level authorization with QID validation
- **Parameters**:
  - `hcCode` (path, required): Health Center Code
  - `clinicCode` (query, optional): Filter by specific clinic
  - `workDay` (query, optional): Day of week filter
    - 1: Sunday
    - 2: Monday
    - 3: Tuesday
    - 4: Wednesday
    - 5: Thursday
    - 6: Friday
    - 7: Saturday
  - `shift` (query, optional): Time of day filter
    - 0: Morning (AM)
    - 1: Afternoon (PM)
    - 2: Full day (BOTH)
- **Response Format**:
  ```json
  [
    {
      "ClinicCode": "string",     // Clinic identifier
      "ClinicNameEn": "string",   // English name
      "ClinicNameAr": "string",   // Arabic name
      "WorkDays": "string",       // Available days (e.g., "135" for Sun,Tue,Thu)
      "ShiftTime": "string"       // Shift code (0,1,2)
    }
  ]
  ```
- **Validation Rules**:
  - WorkDay: Single digit 1-7
  - Shift: Single digit 0-2
- **Responses**:
  - 200: Success with shift list
  - 204: No shifts found
  - 400: Invalid day or shift parameter
  - 500: Server error

### 4. Get Clinic Day Shift List By Health Center And Clinic Code
- **Endpoint**: `GET /clinic-day-shift/{hcCode}/{clinicCode}`
- **Description**: Retrieves detailed shift schedule for specific health center and clinic
- **Authorization**: Function-level authorization
- **Parameters**:
  - `hcCode` (path, required): Health Center Code
  - `clinicCode` (path, required): Clinic Code
- **Response Format**:
  ```json
  [
    {
      "AvailableDate": "string",          // UTC date string
      "ShiftCode": "string",              // Shift identifier
      "ShiftDescriptionEn": "string",     // English description
      "ShiftDescriptionAr": "string"      // Arabic description
    }
  ]
  ```
- **Schedule Range**: 
  - Start: Current date
  - End: Last day of 5th month from current date
- **Responses**:
  - 200: Success with shift schedule
  - 204: No shifts found
  - 500: Server error

## Technical Implementation

### Database Architecture
1. **Context Management**
   - HealthCenterContext
     - Primary health center data
     - Clinic associations
     - Shift configurations
   - EServiceContext
     - Shift definitions
     - System configurations

2. **Entity Relationships**
   - HealthCenter → Clinic (1:Many)
   - Clinic → ClinicShift (1:Many)
   - ClinicShift → ClinicDaysShift (1:Many)

### Performance Optimization
1. **Query Efficiency**
   - Asynchronous operations
   - No-tracking queries
   - Efficient projections
   - Pagination implementation
   - Distinct result sets

2. **Data Access Patterns**
   - Async/await throughout
   - Proper connection management
   - Efficient resource disposal
   - Minimal data transfer

### Security Implementation
1. **Authorization**
   - Function-level security
   - QID validation
   - Claims verification
   - Access logging

2. **Data Protection**
   - Input sanitization
   - Parameter validation
   - Secure error handling
   - Audit logging

### Error Management
1. **Exception Handling**
   - Structured error responses
   - Comprehensive logging
   - Proper status codes
   - Detailed error messages

2. **Validation**
   - Parameter sanitization
   - Business rule validation
   - Data integrity checks
   - Format verification

## Best Practices
1. **Code Organization**
   - Clean architecture
   - Separation of concerns
   - Consistent naming
   - Proper documentation

2. **Performance**
   - Efficient queries
   - Connection pooling
   - Resource management
   - Caching strategies

3. **Maintenance**
   - Regular monitoring
   - Performance tracking
   - Error analysis
   - Usage analytics

4. **Security**
   - Regular audits
   - Access control
   - Data protection
   - Compliance checks

## Dependencies
- Entity Framework Core
- Azure Functions
- System.Text.Json
- Microsoft.Azure.WebJobs
- Application Insights