namespace EServiceFunctions.RequestResponseModels;

/// <summary>
/// Represents the result of Health Card number validation
/// </summary>
[ExcludeFromCodeCoverage]
public class HealthCardValidationResult
{
    /// <summary>
    /// Indicates whether the Health Card number validation passed
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// The error response to return if validation failed, null if validation passed
    /// </summary>
    public HttpResponseData? ErrorResponse { get; }

    /// <summary>
    /// Initializes a new instance of HealthCardValidationResult
    /// </summary>
    /// <param name="isValid">Whether validation passed</param>
    /// <param name="errorResponse">Error response if validation failed</param>
    public HealthCardValidationResult(bool isValid, HttpResponseData? errorResponse)
    {
        IsValid = isValid;
        ErrorResponse = errorResponse;
    }
}
