﻿using EServiceFunctions.Models.Registration;
using EServiceFunctions.RequestResponseModels.Registration;

namespace EServiceFunctions.MapperModels;

public static class RegistrationMapper
{
    public static GetRegistrationItemResponse MapRegistrationToGetRegistrationItemResponse(Registration registration)
    {
        return new GetRegistrationItemResponse
        {
            QId = registration.QId,
            ReqNumber = registration.ReqNumber,
            FNameEn = registration.FNameEn,
            MNameEn = registration.MNameEn,
            LNameEn = registration.LNameEn,
            FNameAr = registration.FNameAr,
            MNameAr = registration.MNameAr,
            LNameAr = registration.LNameAr,
            Nationality = registration.Nationality,
            Dob = registration.Dob.ToUtcString(),
            Gender = registration.Gender,
            MaritalStatus = registration.MaritalStatus,
            Education = registration.Education,
            Occupation = registration.Occupation,
            HomeTel = registration.HomeTel,
            OfficeTel = registration.OfficeTel,
            MobileNo = registration.MobileNo,
            NextOfKinName = registration.NextOfKinName,
            NextOfKinLandLine = registration.NextOfKinLandLine,
            SponsorName = registration.SponsorName,
            SponsorAddress = registration.SponsorAddress,
            VisaType = registration.VisaType,
            BNo = registration.BNo,
            ZNo = registration.ZNo,
            SNo = registration.SNo,
            UNo = registration.UNo,
            CatchmentHC = registration.CatchmentHC,
            PrefHC = registration.PrefHC,
            PrefSMSLang = registration.PrefSMSLang,
            PrefComMode = registration.PrefComMode,
            AttachId1 = registration.AttachId1,
            AttachId2 = registration.AttachId2,
            AttachId3 = registration.AttachId3,
            AttachId4 = registration.AttachId4,
            AttachId5 = registration.AttachId5,
            AttachId6 = registration.AttachId6,
            EmergencyContactID = registration.EmergencyContactID,
            SubmittedBy = registration.SubmittedBy,
            SubmittedAt = registration.SubmittedAt.ToUtcString(),
            SubmitterQId = registration.SubmitterQId,
            SubmitterEmail = registration.SubmitterEmail,
            SubmitterMobile = registration.SubmitterMobile,
            CreatedAt = registration.CreatedAt.ToUtcString(),
            UpdatedAt = registration.UpdatedAt.ToUtcString(),
            Status = registration.StatusNavigation?.Code,
            StatusDescriptionEn = registration.StatusNavigation?.DescriptionEn,
            StatusDescriptionAr = registration.StatusNavigation?.DescriptionAr,
            SN = registration.SN,
            GisAddressUpdatedAt = registration.GisAddressUpdatedAt.ToUtcString(),
            IsGisAddressManualyEntered = registration.IsGisAddressManualyEntered
        };
    }

    public static Registration MapCreateUpdateRegistrationRequestToRegistration(CreateUpdateRegistrationRequest createUpdateRegistrationRequest)
    {
        return new Registration
        {
            QId = createUpdateRegistrationRequest.QId,
            FNameEn = createUpdateRegistrationRequest.FNameEn,
            MNameEn = createUpdateRegistrationRequest.MNameEn,
            LNameEn = createUpdateRegistrationRequest.LNameEn,
            FNameAr = createUpdateRegistrationRequest.FNameAr,
            MNameAr = createUpdateRegistrationRequest.MNameAr,
            LNameAr = createUpdateRegistrationRequest.LNameAr,
            Nationality = createUpdateRegistrationRequest.Nationality,
            Dob = createUpdateRegistrationRequest.Dob,
            Gender = createUpdateRegistrationRequest.Gender,
            MaritalStatus = createUpdateRegistrationRequest.MaritalStatus,
            Education = createUpdateRegistrationRequest.Education,
            Occupation =  createUpdateRegistrationRequest.Occupation,
            HomeTel = createUpdateRegistrationRequest.HomeTel,
            OfficeTel = createUpdateRegistrationRequest.OfficeTel,
            MobileNo = createUpdateRegistrationRequest.MobileNo,
            NextOfKinName = createUpdateRegistrationRequest.NextOfKinName,
            NextOfKinLandLine = createUpdateRegistrationRequest.NextOfKinLandLine,
            SponsorName = createUpdateRegistrationRequest.SponsorName,
            SponsorAddress = createUpdateRegistrationRequest.SponsorAddress,
            VisaType = createUpdateRegistrationRequest.VisaType,
            BNo = createUpdateRegistrationRequest.BNo,
            ZNo = createUpdateRegistrationRequest.ZNo,
            SNo = createUpdateRegistrationRequest.SNo,
            UNo = createUpdateRegistrationRequest.UNo,
            CatchmentHC = createUpdateRegistrationRequest.CatchmentHC,
            PrefHC = createUpdateRegistrationRequest.PrefHC,
            PrefSMSLang = createUpdateRegistrationRequest.PrefSMSLang,
            PrefComMode = createUpdateRegistrationRequest.PrefComMode,
            AttachId1 = createUpdateRegistrationRequest.AttachId1,
            AttachId2 = createUpdateRegistrationRequest.AttachId2,
            AttachId3 = createUpdateRegistrationRequest.AttachId3,
            AttachId4 = createUpdateRegistrationRequest.AttachId4,
            AttachId5 = createUpdateRegistrationRequest.AttachId5,
            AttachId6 = createUpdateRegistrationRequest.AttachId6,
            EmergencyContactID = createUpdateRegistrationRequest.EmergencyContactID,
            SubmittedBy = createUpdateRegistrationRequest.SubmittedBy,
            SubmitterQId = createUpdateRegistrationRequest.SubmitterQId,
            SubmitterEmail = createUpdateRegistrationRequest.SubmitterEmail,
            SubmitterMobile = createUpdateRegistrationRequest.SubmitterMobile,
            GisAddressUpdatedAt = createUpdateRegistrationRequest.GisAddressUpdatedAt,
            IsGisAddressManualyEntered = createUpdateRegistrationRequest.IsGisAddressManualyEntered
        };
    }
}