﻿using EServiceFunctions.Models.MHDS;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.Appointment;
using EServiceFunctions.RequestResponseModels.EDWDropDownList;
using EServiceFunctions.RequestResponseModels.MHDS;
using EServiceFunctions.RequestResponseModels.UserProfile;

using static UnitTestEServiceFunctions.MockDataForMhdsFunctions;

namespace UnitTestEServiceFunctions;

public class EmptyMhdsContext : IDbContextFactory<MHDSContext>
{
    public MHDSContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<MHDSContext>()
            .UseInMemoryDatabase("EmptyMHDSContext")
            .Options;
        var context = new MHDSContext(options);
        context.Status!.AddRange(GetMhdsStatus());
        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public class TestEdwDbContextForMhds(string? databaseName = null) : IDbContextFactory<EDWDbContext>
{
    private readonly string _databaseName = databaseName ?? $"EDWDbContextDb_{Guid.NewGuid()}";

    public EDWDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<EDWDbContext>()
            .UseInMemoryDatabase(_databaseName)
            .Options;
        var context = new EDWDbContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.PersonMoiDetail.AddRange(GetPersonMoiDetailsForMhdsEdw());
        context.UserProfileResponseFromEDW!.AddRange(GetUserProfileResponseFromEdw());
        context.GetMoiDependentsResponse.AddRange(GetGetMoiDependentsResponse());
        context.GetLanguageListResponse.AddRange(GetGetLanguageListResponse());
        context.UpcomingConfirmedAppointmentListResponseFromEDW.AddRange(
            GetGetUpcomingConfirmedAppointmentListResponseFromEdw());
        context.MedicationRefillDetailsProd!.AddRange(GetMedicationRefillDetailsProd());
        context.MedicationRefillDetailsStg!.AddRange(GetMedicationRefillDetailsStg());

        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public class TestMhdsContext(string? databaseName = null) : IDbContextFactory<MHDSContext>
{
    private readonly string _databaseName = databaseName ?? $"MHDSContextDb_{Guid.NewGuid()}";

    public MHDSContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<MHDSContext>()
            .UseInMemoryDatabase(_databaseName)
            .Options;
        var context = new MHDSContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.MHDSRequestDetails!.AddRange(GetMhdsRequestDetailsList());
        context.MHDSRequestMedicineList!.AddRange(GetMhdsRequestMedicineList());
        context.Status!.AddRange(GetMhdsStatus());

        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public static class MockDataForMhdsFunctions
{
    public static IEnumerable<PersonMoiDetail> GetPersonMoiDetailsForMhdsEdw()
    {
        var personMoiDetail = new PersonMoiDetail
        {
            QId = QId,
            Dob = new DateTime(1986, 09, 28),
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<PersonMoiDetail> { personMoiDetail };
    }

    public static IEnumerable<MHDSRequestMedicineList> GetMhdsRequestMedicineList()
    {
        var medicineList = new List<MHDSRequestMedicineList>
        {
            new()
            {
                Id = 1,
                RequestNumber = "REQ00001",
                MedicineName = "Medicine 1",
                PrescriptionOrderId = "PO00001",
                PrescribedDate = new Faker().Date.Past(),
                PrescriptionDueDate = new Faker().Date.Past(),
                LastDispenseDate = new Faker().Date.Past(),
                SupplyDuration = "1",
                LastDispensedLocation = "OBK",
                OrderByName = FirstNameEn + " " + LastNameEn,
                AttachId = Guid.NewGuid()
            },
            new()
            {
                Id = 2,
                RequestNumber = "REQ00002",
                MedicineName = "Medicine 2",
                PrescriptionOrderId = "PO00002",
                PrescribedDate = new Faker().Date.Past(),
                PrescriptionDueDate = new Faker().Date.Past(),
                LastDispenseDate = new Faker().Date.Past(),
                SupplyDuration = "1",
                LastDispensedLocation = "OBK",
                OrderByName = FirstNameEn + " " + LastNameEn,
                AttachId = Guid.NewGuid()
            },
            new()
            {
                Id = 3,
                RequestNumber = "REQ00002",
                MedicineName = "Medicine 3",
                PrescriptionOrderId = "PO00003",
                PrescribedDate = new Faker().Date.Past(),
                PrescriptionDueDate = new Faker().Date.Past(),
                LastDispenseDate = new Faker().Date.Past(),
                SupplyDuration = "1",
                LastDispensedLocation = "OBK",
                OrderByName = FirstNameEn + " " + LastNameEn,
                AttachId = Guid.NewGuid()
            },
            new()
            {
                Id = 4,
                RequestNumber = "REQ00003",
                MedicineName = "Medicine 4",
                PrescriptionOrderId = "PO00004",
                PrescribedDate = new Faker().Date.Past(),
                PrescriptionDueDate = new Faker().Date.Past(),
                LastDispenseDate = new Faker().Date.Past(),
                SupplyDuration = "1",
                LastDispensedLocation = "OBK",
                OrderByName = FirstNameEn + " " + LastNameEn,
                AttachId = Guid.NewGuid()
            }
        };
        return medicineList;
    }

    public static IEnumerable<Status> GetMhdsStatus()
    {
        var statusList = new List<Status>
        {
            new()
            {
                Code = InProgress,
                DescriptionEn = "Request In Progress",
                DescriptionAr = "الطلب قيد الاجراء",
                Category = InProcess,
                NrkCategory = InProgress
            },
            new()
            {
                Code = Approved,
                DescriptionEn = "Request Completed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived,
                NrkCategory = Archived
            },
            new()
            {
                Code = Cancelled,
                DescriptionEn = "Request Cancelled",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived,
                NrkCategory = Archived
            },
            new()
            {
                Code = CancelledByEServ,
                DescriptionEn = "Cancelled by EServices",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived,
                NrkCategory = Archived
            },
            new()
            {
                Code = "CancelledByRegistration",
                DescriptionEn = "Cancelled by Registration",
                DescriptionAr = "ألغيت بالتسجيل",
                Category = Archived,
                NrkCategory = Archived
            },
            new()
            {
                Code = CancelledFrom107,
                DescriptionEn = "Cancelled from 107",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived,
                NrkCategory = Archived
            },
            new()
            {
                Code = ConditionallyApproved,
                DescriptionEn = "Request Tentatively Approved",
                DescriptionAr = "تمت الموافقة المبدئية للطلب",
                Category = InProcess,
                NrkCategory = "MyTasks"
            },
            new()
            {
                Code = "Confirmed",
                DescriptionEn = "Confirmed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived,
                NrkCategory = EmptyString
            },
            new()
            {
                Code = "PaymentReceived",
                DescriptionEn = "Payment Received",
                DescriptionAr = "تمت عملية الدفع",
                Category = InProcess,
                NrkCategory = InProgress
            },
            new()
            {
                Code = PendingOrder,
                DescriptionEn = "Request Pending",
                DescriptionAr = "الطلب قيد الإنتظار",
                Category = InProcess,
                NrkCategory = InProgress
            },
            new()
            {
                Code = "ProceedToRegistration",
                DescriptionEn = "Proceed to Registration",
                DescriptionAr = "الشروع في التسجيل",
                Category = InProcess,
                NrkCategory = InProgress
            },
            new()
            {
                Code = "Ready",
                DescriptionEn = "Saved",
                DescriptionAr = "تم حفظ الطلب",
                Category = EmptyString,
                NrkCategory = EmptyString
            },
            new()
            {
                Code = "Re-AssignedForVerification",
                DescriptionEn = "Re-Assigned for Verification",
                DescriptionAr = "إعادة التعيين للتحقق",
                Category = InProcess,
                NrkCategory = InProgress
            },
            new()
            {
                Code = Rejected,
                DescriptionEn = "Request Rejected",
                DescriptionAr = "تم رفض الطلب",
                Category = Archived,
                NrkCategory = Archived
            },
            new()
            {
                Code = ResubmitOriginalsRequested,
                DescriptionEn = "Resubmit Originals Requested",
                DescriptionAr = "تمت الموافقة المبدئية للطلب",
                Category = InProcess,
                NrkCategory = "MyTasks"
            },
            new()
            {
                Code = Returned,
                DescriptionEn = Returned,
                DescriptionAr = "تم الارجاع",
                Category = Archived,
                NrkCategory = Archived
            },
            new()
            {
                Code = "ReturnedForPayment",
                DescriptionEn = "Returned for Payment",
                DescriptionAr = "تم الارجاع للدفع",
                Category = InProcess,
                NrkCategory = "MyTasks"
            },
            new()
            {
                Code = Rework,
                DescriptionEn = "Request Returned for Correction",
                DescriptionAr = "تم إعادة الطلب للتعديل",
                Category = InProcess,
                NrkCategory = "MyTasks"
            },
            new()
            {
                Code = Reworked,
                DescriptionEn = "Request Resubmitted",
                DescriptionAr = "تم إستلام الطلب",
                Category = InProcess,
                NrkCategory = InProgress
            },
            new()
            {
                Code = "Saved",
                DescriptionEn = "Saved",
                DescriptionAr = "تم حفظ الطلب",
                Category = InProcess,
                NrkCategory = "MyTasks"
            },
            new()
            {
                Code = Submitted,
                DescriptionEn = "Request Received",
                DescriptionAr = "تم إستلام الطلب",
                Category = InProcess,
                NrkCategory = InProgress
            }
        };
        return statusList;
    }

    public static IEnumerable<MHDSRequestDetails> GetMhdsRequestDetailsList()
    {
        var mhdsRequestList = new List<MHDSRequestDetails>
        {
            new()
            {
                Id = 1,
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                ReqNumber = "REQ00001",
                HcNumber = HcNumber1,
                Nationality = NationalityEn,
                Dob = new Faker().Date.Past(),
                CurrentAssignedHc = "OBK",
                Relationship = "Self",
                BNo = 12,
                ZNo = 38,
                SNo = 12,
                UNo = 1,
                IsGisAddressManualyEntered = true,
                Action = "Create",
                Consent = true,
                SubmittedBy = FirstNameEn + " " + LastNameEn,
                SubmittedAt = new Faker().Date.Past(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                CreatedAt = new Faker().Date.Past(),
                UpdatedAt = new Faker().Date.Past(),
                Status = InProgress,
                StatusInternal = InProgress,
                LastActionBy = FirstNameEn + " " + LastNameEn,
                CreateSource = "EServ Unit test",
                UpdateSource = "EServ Unit test"
            },
            new()
            {
                Id = 2,
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                ReqNumber = "REQ00002",
                HcNumber = HcNumber1,
                Nationality = NationalityEn,
                Dob = new Faker().Date.Past(),
                CurrentAssignedHc = new Faker().Address.City(),
                Relationship = "Self",
                BNo = new Faker().Random.Int(1, 100),
                ZNo = new Faker().Random.Int(1, 100),
                SNo = new Faker().Random.Int(1, 100),
                UNo = new Faker().Random.Int(1, 100),
                IsGisAddressManualyEntered = true,
                Action = "Create",
                Consent = true,
                SubmittedBy = new Faker().Name.FullName(),
                SubmittedAt = new Faker().Date.Past(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                SecondaryPhoneMobile = new Faker().Phone.PhoneNumber(),
                CreatedAt = new Faker().Date.Past(),
                UpdatedAt = new Faker().Date.Past(),
                Status = InProcess,
                StatusInternal = InProcess,
                LastActionBy = new Faker().Name.FullName(),
                CreateSource = "EServ Unit test",
                UpdateSource = "EServ Unit test"
            },
            new()
            {
                Id = 3,
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                ReqNumber = "REQ00003",
                HcNumber = HcNumber1,
                Nationality = NationalityEn,
                Dob = new Faker().Date.Past(),
                CurrentAssignedHc = new Faker().Address.City(),
                Relationship = "Self",
                BNo = new Faker().Random.Int(1, 100),
                ZNo = new Faker().Random.Int(1, 100),
                SNo = new Faker().Random.Int(1, 100),
                UNo = new Faker().Random.Int(1, 100),
                IsGisAddressManualyEntered = true,
                Action = "Create",
                Consent = true,
                SubmittedBy = new Faker().Name.FullName(),
                SubmittedAt = new Faker().Date.Past(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                SecondaryPhoneMobile = new Faker().Phone.PhoneNumber(),
                CreatedAt = new Faker().Date.Past(),
                UpdatedAt = new Faker().Date.Past(),
                Status = Submitted,
                StatusInternal = Submitted,
                LastActionBy = new Faker().Name.FullName(),
                CreateSource = "EServ Unit test",
                UpdateSource = "EServ Unit test"
            }
        };
        return mhdsRequestList;
    }

    public static IEnumerable<UserProfileFromEDW> GetUserProfileResponseFromEdw()
    {
        var userProfileFromEdw = new List<UserProfileFromEDW>
        {
            new()
            {
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber1,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            },
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber2,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            }
        };
        return userProfileFromEdw;
    }

    public static IEnumerable<GetMoiDependentsResponse> GetGetMoiDependentsResponse()
    {
        var getMoiDependentsResponse = new List<GetMoiDependentsResponse>
        {
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 2,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 3,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            }
        };
        return getMoiDependentsResponse;
    }

    public static IEnumerable<GetLanguageListResponse> GetGetLanguageListResponse()
    {
        var getLanguageListResponse = new List<GetLanguageListResponse>
        {
            new()
            {
                NatCode = "en",
                NatNameEn = "English",
                NatNameAr = "الإنجليزية"
            },
            new()
            {
                NatCode = "ar",
                NatNameEn = "Arabic",
                NatNameAr = "العربية"
            },
            new()
            {
                NatCode = "fr",
                NatNameEn = "French",
                NatNameAr = "الفرنسية"
            }
        };
        return getLanguageListResponse;
    }

    public static IEnumerable<GetUpcomingConfirmedAppointmentListResponseFromEdw>
        GetGetUpcomingConfirmedAppointmentListResponseFromEdw()
    {
        var getUpcomingConfirmedAppointmentListResponseFromEdw =
            new List<GetUpcomingConfirmedAppointmentListResponseFromEdw>
            {
                new()
                {
                    AppointmentId = "AP00001",
                    QId = LongQId,
                    QIdExpiryDt = new Faker().Date.Future(),
                    FNameEn = FirstNameEn,
                    MNameEn = MiddleNameEn,
                    LNameEn = LastNameEn,
                    FNameAr = FirstNameAr,
                    MNameAr = MiddleNameAr,
                    LNameAr = LastNameAr,
                    NationalityCode = NationalityCode,
                    NationalityEn = NationalityEn,
                    NationalityAr = NationalityAr,
                    Dob = new Faker().Date.Past(),
                    HCNumber = HcNumber1,
                    HCExpiryDate = new Faker().Date.Future(),
                    GenderCode = GenderCode,
                    GenderEn = GenderEn,
                    GenderAr = GenderAr,
                    PhoneMobile = MobilePhone,
                    AggignedHCntCode = "OBK",
                    AggignedHCntNameEn = "Omar Bin Khattab",
                    AggignedHCntNameAr = "عمر بن الخطاب",
                    AppointmentHCntCode = "OBK",
                    AppointmentHCntNameEn = "Omar Bin Khattab",
                    AppointmentHCntNameAr = "عمر بن الخطاب",
                    ClinicCode = "OBK001",
                    ClinicNameEn = "OBK Clinic 1",
                    ClinicNameAr = "عيادة عمر بن الخطاب 1",
                    GisAddressStreet = new Faker().Address.StreetAddress(),
                    GisAddressBuilding = new Faker().Address.BuildingNumber(),
                    GisAddressZone = new Faker().Address.City(),
                    GisAddressUnit = new Faker().Address.City(),
                    PhysicianId = "123456",
                    PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                    PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                    BookedDateTime = new Faker().Date.Past(),
                    AppointmentDateTime = new Faker().Date.Future(),
                    AppointmentType = "Appointment",
                    AppointmentStatus = "Confirmed",
                    AppointmentLocation = "OBK",
                    AppointmentFacility = "OBK"
                }
            };

        return getUpcomingConfirmedAppointmentListResponseFromEdw;
    }

    public static IEnumerable<EServPersonasMedicationRefill> GetMedicationRefillDetailsStg()
    {
        var medicationRefillDetailsList = new List<EServPersonasMedicationRefill>
        {
            new()
            {
                QId = QId,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 4",
                SupplyDuration = 1,
                PrescriptionOrderId = 1,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId1,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 5",
                SupplyDuration = 1,
                PrescriptionOrderId = 2,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId2,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 6",
                SupplyDuration = 1,
                PrescriptionOrderId = 3,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            }
        };
        return medicationRefillDetailsList;
    }

    public static IEnumerable<MedicationRefillDtls> GetMedicationRefillDetailsProd()
    {
        var medicationRefill = new MedicationRefillDtls
        {
            PersonId = 1,
            QId = QId,
            HcNumber = HcNumber1,
            NameFirst = FirstNameEn,
            NameMiddle = MiddleNameEn,
            NameLast = LastNameEn,
            AgeYears = Age,
            MobilePhone = MobilePhone,
            PrescriptionRefillDueDate = new DateTime(2023, 10, 10),
            LastDispenseDate = new DateTime(2023, 08, 10),
            LastDispenseFacCode = "WAJ",
            PrescriptionDate = new DateTime(2023, 10, 10),
            PrescriptionFacCode = "WAJ",
            PrescribedMedication = "mometasone nasaL",
            SupplyDuration = 15,
            PrescriptionOrderId = 8448095541,
            PrescribedByCorpId = "2683232",
            PrescribedByNameEng = "Dr. Aktham Shraiba",
            PrescribedByNameAra = "د. أكثم شريبة",
            RefillsRemaining = 10,
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<MedicationRefillDtls> { medicationRefill };
    }
}

public class UnitTestMhdsFunctions(ITestOutputHelper outputHelper)
{
    private readonly ILogger<MhdsFunctions> _logger = Mock.Of<ILogger<MhdsFunctions>>();

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessMHDSByQID_Should_Return_Ok()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);

        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.CheckInProcessMhdsByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseContent = DeserializeObject<CheckInProcessMHDSResponse>(responseObject);
        True(responseContent!.IsInProcessExist);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessMHDSByQID_Should_Return_Ok_When_No_InProcess_Requests_Exist()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);

        // Act
        var response = await mhdsFunctions.CheckInProcessMhdsByQId(request, LongQId + 1);
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseContent = DeserializeObject<CheckInProcessMHDSResponse>(responseObject);
        False(responseContent!.IsInProcessExist);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSStatsByQID_Should_Return_Ok()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsStatsByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseContent = DeserializeObject<GetMHDSStatsResponse>(responseObject);
        Equal(2, responseContent!.Count);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSStatsByQID_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsStatsByQId(request, LongQId + 1);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByHCNumber_Should_Return_Ok()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var requestBody = new RequestMHDSMedicineList
        {
            HealthCardNumbers = [HcNumber1]
        };
        var requestBodyString = SerializeObject(requestBody);
        var request = MockHelpers.CreateHttpRequestData(payload: requestBodyString);

        // Act
        var response = await mhdsFunctions.GetMhdsListByHcNumber(request);
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);

        var responseContent = DeserializeObject<List<GetMedicineList>>(responseObject);
        Single(responseContent!);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByHCNumber_Should_Return_NoContent_When_No_Requests_Exist()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var requestBody = new RequestMHDSMedicineList
        {
            HealthCardNumbers = [HcNumber2]
        };
        var requestBodyString = SerializeObject(requestBody);
        var request = MockHelpers.CreateHttpRequestData(payload: requestBodyString);

        // Act
        var response = await mhdsFunctions.GetMhdsListByHcNumber(request);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByHCNumber_Should_Return_BadRequest_When_Request_Is_Null()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData(payload: null);

        // Act
        var response = await mhdsFunctions.GetMhdsListByHcNumber(request);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_CreateMHDS_Should_Return_Created()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new EmptyMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var requestBody = new Faker<CreateMHDSRequest>()
            .RuleFor(x => x.QId, LongQId)
            .RuleFor(x => x.SubmitterQId, LongQId)
            .RuleFor(x => x.FNameEn, FirstNameEn)
            .RuleFor(x => x.FNameAr, FirstNameAr)
            .RuleFor(x => x.MNameEn, MiddleNameEn)
            .RuleFor(x => x.MNameAr, MiddleNameAr)
            .RuleFor(x => x.LNameEn, LastNameEn)
            .RuleFor(x => x.LNameAr, LastNameAr)
            .RuleFor(x => x.HCNumber, HcNumber1)
            .RuleFor(x => x.CurrentAssignedHC, "OBK")
            .RuleFor(x => x.Dob, new Faker().Date.Past())
            .RuleFor(x => x.Nationality, NationalityEn)
            .RuleFor(x => x.SubmitterMobile, MobilePhone)
            .RuleFor(x => x.SecondaryMobNo, SecondaryMobilePhone)
            .RuleFor(x => x.SubmitterEmail, UserEmail)
            .RuleFor(x => x.Consent, true)
            .RuleFor(x => x.Action, "Create")
            .RuleFor(x => x.IsGisAddressManualyEntered, true)
            .RuleFor(x => x.UNo, new Faker().Random.Int(1, 100))
            .RuleFor(x => x.BNo, new Faker().Random.Int(1, 100))
            .RuleFor(x => x.SNo, new Faker().Random.Int(1, 100))
            .RuleFor(x => x.ZNo, new Faker().Random.Int(1, 100))
            .RuleFor(x => x.MedInfo, [
                new()
                {
                    MedicineName = "Medicine 1",
                    PrescriptionOrderId = "1",
                    PrescribedDate = new Faker().Date.Past(),
                    PrescriptionRefillDueDate = new Faker().Date.Future(),
                    LastDispenseDate = new Faker().Date.Past(),
                    SupplyDuration = "1",
                    LastDispensedLocation = "OBK",
                    OrderByName = "Dr. " + FirstNameEn + " " + LastNameEn,
                    AttachId = Guid.NewGuid()
                },

                new()
                {
                    MedicineName = "Medicine 2",
                    PrescriptionOrderId = " 2",
                    PrescribedDate = new Faker().Date.Past(),
                    PrescriptionRefillDueDate = new Faker().Date.Future(),
                    LastDispenseDate = new Faker().Date.Past(),
                    SupplyDuration = "2",
                    LastDispensedLocation = "OBK",
                    OrderByName = "Dr. " + FirstNameEn + " " + LastNameEn,
                    AttachId = Guid.NewGuid()
                },

                new()
                {
                    MedicineName = "Medicine 3",
                    PrescriptionOrderId = "3",
                    PrescribedDate = new Faker().Date.Past(),
                    PrescriptionRefillDueDate = new Faker().Date.Future(),
                    LastDispenseDate = new Faker().Date.Past(),
                    SupplyDuration = "3",
                    LastDispensedLocation = "OBK",
                    OrderByName = "Dr. " + FirstNameEn + " " + LastNameEn,
                    AttachId = Guid.NewGuid()
                }
            ]).Generate();

        var requestBodyString = SerializeObject(requestBody);
        var request = MockHelpers.CreateHttpRequestData(payload: requestBodyString);

        // Act
        var response = await mhdsFunctions.CreateMhdsRequestAsync(request);
        var result = response as MockHttpResponseData;

        // Assert
        LogResponse(outputHelper, response);
        Equal(HttpStatusCode.Created, result.StatusCode);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_CreateMHDS_Should_Return_BadRequest_When_Request_Is_Null()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData(payload: null);

        // Act
        var response = await mhdsFunctions.CreateMhdsRequestAsync(request);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateMHDSByReqNumber_Should_Return_OK_When_Request_Is_Valid()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var requestBody = new Faker<UpdateMHDSRequest>()
            .RuleFor(x => x.QId, LongQId)
            .RuleFor(x => x.SubmitterQId, LongQId)
            .RuleFor(x => x.IsGisAddressManualyEntered, true)
            .RuleFor(x => x.UNo, f => f.Random.Int(10000, 20000))
            .RuleFor(x => x.BNo, f => f.Random.Int(1000, 2000))
            .RuleFor(x => x.SNo, f => f.Random.Int(1, 9))
            .RuleFor(x => x.ZNo, f => f.Random.Int(10000, 99999))
            .Generate();

        var requestBodyString = SerializeObject(requestBody);
        var request = MockHelpers.CreateHttpRequestData(payload: requestBodyString, method:Put);
        try
        {
            // Act
            var response = await mhdsFunctions.UpdateMhdsByReqNumber(request, "REQ00001");

            // Assert
            var okResponse = response as MockHttpResponseData;
            outputHelper.WriteLine(GetResponseText(okResponse));
            Equal(HttpStatusCode.OK, okResponse.StatusCode);
        }
        catch
        {
            var response = await mhdsFunctions.UpdateMhdsByReqNumber(request, "REQ00001");

            // Assert
            var okResponse = response as MockHttpResponseData;
            outputHelper.WriteLine(GetResponseText(okResponse));
            Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
        }
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateMHDSByReqNumber_Should_Return_BadRequest_When_Request_Is_Null()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData(payload: null);

        // Act
        var response = await mhdsFunctions.UpdateMhdsByReqNumber(request, "REQ00001");
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByQID_Should_Return_Ok()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsListByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseContent = DeserializeObject<List<MHDSRequestList>>(responseObject);
        Equal(2, responseContent!.Count);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSListByQID_Should_Return_OK_For_Valid_QId()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsListByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSItemByReqNumber_Should_Return_Ok()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsItemByReqNumber(request, "REQ00001");
        var result = response as MockHttpResponseData;
        var responseObject = LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseContent = DeserializeObject<GetMHDSItemResponse>(responseObject);
        Equal("REQ00001", responseContent!.ReqNumber);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSItemByReqNumber_Should_Return_NoContent_When_No_Requests_Exist()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsItemByReqNumber(request, "REQ006");
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetMHDSItemByReqNumber_Should_Return_BadRequest_When_Request_Number_Is_Empty()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.GetMhdsItemByReqNumber(request, string.Empty);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteMHDSByReqNumber_Should_Return_NoContent()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.DeleteMhdsByReqNumber(request, "REQ00001");
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }

    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteMHDSByReqNumber_Should_Return_BadRequest_When_Request_Number_Is_Empty()
    {
        // Arrange
        var mockEdwDbContext = new TestEdwDbContextForMhds();
        var mockMhdsContext = new TestMhdsContext();
        var mhdsFunctions = new MhdsFunctions(mockMhdsContext, mockEdwDbContext, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await mhdsFunctions.DeleteMhdsByReqNumber(request, string.Empty);
        var result = response as MockHttpResponseData;
        LogResponse(outputHelper, response);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }
}