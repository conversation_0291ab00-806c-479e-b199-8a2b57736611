# Create MHDS Request Tests

## Test Case: TestCreateMhdsRequestAsync

Tests the creation of a new MHDS (Medicine Home Delivery Service) request.

### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCreateMhdsRequestAsync()
{
    // Arrange
    var request = CreateMhdsRequest();
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

    // Act
    var response = await _client.PostAsync<CreateMHDSResponse>(request) ?? null;
    
    // Assert
    NotNull(response);
    True(response.ReqNumber.Length > 0);
}
```

## Request Details

### Endpoint
```
POST mhds
```

### Headers
- `JwtClaimsQId`: "22222222270"
- `RequestOrigin`: "UnitTest-CreateMHDS"

## Request Model

### CreateMHDSRequest
```csharp
public class CreateMHDSRequest
{
    public long QId { get; set; }
    public string FNameEn { get; set; }
    public string FNameAr { get; set; }
    public string MNameEn { get; set; }
    public string MNameAr { get; set; }
    public string LNameEn { get; set; }
    public string LNameAr { get; set; }
    public string HCNumber { get; set; }
    public string CurrentAssignedHC { get; set; }
    public DateTime Dob { get; set; }
    public string Nationality { get; set; }
    public int UNo { get; set; }
    public int BNo { get; set; }
    public int SNo { get; set; }
    public int ZNo { get; set; }
    public bool IsGisAddressManualyEntered { get; set; }
    public long SubmitterQId { get; set; }
    public string SubmitterMobile { get; set; }
    public string SubmitterEmail { get; set; }
    public string SecondaryMobNo { get; set; }
    public bool Consent { get; set; }
    public string Action { get; set; }
    public List<CreateMedicineInformation> MedInfo { get; set; }
}
```

### CreateMedicineInformation
```csharp
public class CreateMedicineInformation
{
    public string MedicineName { get; set; }
    public string PrescriptionOrderId { get; set; }
    public DateTime PrescribedDate { get; set; }
    public DateTime PrescriptionRefillDueDate { get; set; }
    public DateTime LastDispenseDate { get; set; }
    public string SupplyDuration { get; set; }
    public string LastDispensedLocation { get; set; }
    public string OrderByName { get; set; }
    public Guid AttachId { get; set; }
}
```

## Test Data Generation

### Personal Information
- QID: 11-digit random number
- Names: English and Arabic versions
- Health Card Numbers: From predefined list
- DOB: Past date within 50 years
- Nationality: Random 1-digit number

### Address Information
- UNo: 10000-20000
- BNo: 1000-2000
- SNo: 1-9
- ZNo: 10000-99999

### Contact Information
- Submitter QID: 22222222270
- Mobile numbers: Random phone numbers
- Email: Random valid email

### Medicine Information
- Generated list of 2 medicines
- Random product names
- Random dates for prescriptions
- Random supply duration
- Location from ["MES", "MKH", "GHR"]
- Unique attachment IDs

## Validation Rules

### Request Validation
1. All required fields present
2. Valid QID formats
3. Valid health card numbers
4. Valid address format
5. Valid contact information
6. At least one medicine entry

### Response Validation
1. Response not null
2. Request number generated
3. Request number not empty

## Error Cases

1. **Invalid Data Format**
   - Invalid QID
   - Invalid HC Number
   - Invalid email format
   - Invalid mobile format

2. **Missing Required Fields**
   - Missing names
   - Missing contact info
   - Missing medicine info
   - Missing address details

3. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - Insufficient permissions

## Notes

1. **Test Data**
   - Uses Faker for realistic data
   - Consistent format for IDs
   - Valid test email domain

2. **Medicine Information**
   - Multiple medicine entries
   - Unique attachment IDs
   - Valid prescription dates

3. **Performance**
   - Async operation
   - Multiple service validations
   - Database operations
