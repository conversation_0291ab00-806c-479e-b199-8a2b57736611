﻿namespace EServiceFunctions.RequestResponseModels;

[ExcludeFromCodeCoverage]
[DataContract]
public class ErrorResponse(HttpStatusCode code, string? message = default)
{
    [DataMember(Name = "code")] public int Code { get; private set; } = (int)code;
    [DataMember(Name = "message")] public string? Message { get; set; } = message ?? DefaultMessages.GetValueOrDefault(code, "Unknown Error!");

    private static readonly Dictionary<HttpStatusCode, string> DefaultMessages = new()
    {
        { InternalServerError, "Internal Server Error Occurred" },
        { BadRequest, "Bad Request, Please check your request and try again" },
        { Unauthorized, "Unauthorized Access" },
        { Forbidden, "Access Forbidden" },
        { NotFound, "Resource Not Found" },
        { MethodNotAllowed, "Request Method Not Allowed" },
        { NotAcceptable, "Request Not Acceptable" },
        { RequestTimeout, "Request Timeout Error, Please try again" },
        { Conflict, "Request could not be carried out because of a conflict on the server" },
        { Gone, "Request resource is no longer available" },
        { LengthRequired, "Required Content-length header is missing" },
        { PreconditionFailed, "Precondition Failed Error" },
        { RequestEntityTooLarge, "Request Entity Too Large Error" },
        { RequestUriTooLong, "Request Uri Too Long Error" },
        { UnsupportedMediaType, "Unsupported Media Type Error" },
        { RequestedRangeNotSatisfiable, "Requested Range Not Satisfiable Error" },
        { ExpectationFailed, "Expectation Failed Error" },
        { UpgradeRequired, "Upgrade Required Error" },
        { NotImplemented, "Server does not support the requested function" },
        { BadGateway, "Bad Gateway Error" },
        { ServiceUnavailable, "Service temporarily unavailable or overloaded" },
        { GatewayTimeout, "Gateway proxy server timed out while waiting for a response" },
        { HttpVersionNotSupported, "Http Version Not Supported Error" }
    };
}