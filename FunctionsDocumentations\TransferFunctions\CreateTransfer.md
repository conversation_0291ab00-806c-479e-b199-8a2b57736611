# Create Transfer

## Overview
Creates a new transfer request in the system.

## Endpoint
```http
POST /transfers
```

## Authorization
- Function-level authorization
- QID-based access control
- Requires valid submitter QID

## Headers
- `X-RequestOrigin` (required): Source of the request

## Request Body
```json
{
  "submitterQId": "long",
  "qId": "long",
  "HCNumber": "string", // REQUIRED - Health Card Number
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string",
  "transferReason": "string",
  "submitterEmail": "string",
  "submitterMobile": "string"
}
```

### Required Fields
- **HCNumber**: Health Card Number is mandatory for all transfer requests. Must be a valid alphanumeric string between 5-20 characters.

## Response

### Success Response (201 Created)
Returns the created transfer request with generated request number:
```json
{
  "reqNumber": "string",
  "submitterQId": "long",
  "qId": "long",
  "status": "string",
  "submittedAt": "string (UTC)",
  "createdAt": "string (UTC)",
  "createSource": "string"
  // Additional fields from request
}
```

### Error Responses
- **400 Bad Request**: Invalid request body, missing required fields, or validation failures
  - Missing Health Card Number:
    ```json
    {
      "code": 400,
      "message": "Health Card No is missing for QID {QID} and request name Transfer"
    }
    ```
  - Invalid Health Card Format:
    ```json
    {
      "code": 400,
      "message": "Health Card No format is invalid for QID {QID} and request name Transfer"
    }
    ```
  - Missing request origin or other validation errors
- **401 Unauthorized**: Invalid QID or unauthorized access
- **500 Internal Server Error**: Database operation failure

## Implementation Details
- Generates unique request number
- Sets initial status as "Saved"
- Records creation timestamp and source
- Implements proper database transaction handling
- Supports multilingual content (English/Arabic)
- Validates request origin

## Business Logic
- Initial Status: Saved (Draft mode)
- Auto-generates request number
- Records submission and creation timestamps
- Tracks request origin source
- Validates submitter authorization
- Validates transfer reason

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control
- Validates request origin
- Sanitizes input data
- Proper error handling

## Performance Optimization
- Efficient database transaction handling
- Proper error handling and logging
- Optimized data insertion
- Secure request number generation
- Input validation optimization
