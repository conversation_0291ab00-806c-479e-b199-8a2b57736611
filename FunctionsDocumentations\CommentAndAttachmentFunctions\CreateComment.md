# CreateComment

## Overview
Creates a new comment for a request.

## Endpoint
- **Route**: `comments`
- **Method**: POST
- **Authorization**: Function level

## Headers
- `X-RequestOrigin`: Required header indicating request origin

## Request Body
```json
{
  "ReqNumber": "string",
  "CommentText": "string",
  "Action": "string",
  "SubmitterQId": "long",
  "DisplayToSubmitter": "boolean"
}
```

## Responses
- **201 Created**: Returns created comment
- **400 Bad Request**: 
  - Invalid request body
  - Invalid request number
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates request origin header
2. Validates request body
3. Validates request number
4. Creates comment record
5. Returns created comment

## Validation Rules
- Request number is required
- Request origin must be provided
- Comment text should not be empty

## Dependencies
- CommentAndAttachmentContext
- Authorization middleware
