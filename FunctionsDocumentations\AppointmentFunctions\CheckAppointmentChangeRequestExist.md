# CheckAppointmentChangeRequestExist

## Overview
Checks if there is an existing change request for a specific appointment.

## Endpoint
- **Route**: `appointments/{appointmentId}/change-request-exists`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `appointmentId` (path, required): ID of the appointment to check
- `EligibleClinicCodes` (header, required): List of eligible clinic codes

## Responses
- **200 OK**: Returns boolean indicating if change request exists
  ```json
  {
    "exists": boolean
  }
  ```
- **400 Bad Request**: Invalid appointment ID
- **401 Unauthorized**: Authentication failed
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates appointment ID
2. Checks for any existing change requests in the system
3. Returns true if a change request exists, false otherwise

## Dependencies
- AppointmentContext
- HmcLiveFeedContext
