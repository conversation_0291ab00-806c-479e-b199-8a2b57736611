using EServiceFunctions.Models.FamilyPhysician;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.EDWDropDownList;
using static EServiceFunctions.Functions.EdwDropDownListConstants;

namespace EServiceFunctions.Functions;

public static class EdwDropDownListConstants
{
    public const string ItemsNotFound = "Items not found";
    public const string RequestedUrlTemplate = "Requested URL: {Url}";
}

public class EdwDropDownListFunctions(
    IDbContextFactory<EDWDbContext> edwDbContextFactory,
    IDbContextFactory<MRDDbContext> mrdFamilyPhyDbContextFactory,
    ILogger<EdwDropDownListFunctions> logger)
{
    #region GetOccupationList

    /// <summary>
    /// Get the list of Occupations from EDW
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetOccupationList")]
    [OpenApiOperation(operationId: "GetOccupationList", tags: ["DropDownList"], Summary = "Get Occupation List",
        Description = " Get the occupation list")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetOccupationListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    public async Task<HttpResponseData> GetOccupationList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "occupations")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation(RequestedUrlTemplate, req.LogRequestedUrl());
            logger.LogInformation("Getting Occupation List");

            await using var mrdFamilyPhyDbContext = await mrdFamilyPhyDbContextFactory.CreateDbContextAsync();

            var response = await mrdFamilyPhyDbContext.MAP_MOI_OccupationTypes.Select(c =>
                new GetOccupationListResponse
                {
                    PRF_CODE = c.OCCUPATION_CODE,
                    PRF_ENDSC = c.DESCRIPTION_ENGLISH,
                    PRF_ARDSCM = c.DESCRIPTION_ARABIC_MALE
                }).AsNoTracking().ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation(ItemsNotFound);
                return await req.WriteNoContentResponseAsync();
            }

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting occupation list");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetNationalityList

    /// <summary>
    /// Get the list of Nationalities from EDW
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetNationalityList")]
    [OpenApiOperation(operationId: "GetNationalityList", tags: ["DropDownList"],
        Summary = "Get Nationality List", Description = " Get the nationality list")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetNationalityListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> GetNationalityList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "nationalities")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation(RequestedUrlTemplate, req.LogRequestedUrl());
            logger.LogInformation("Getting Nationality List");

            await using var mrdDbContext = await mrdFamilyPhyDbContextFactory.CreateDbContextAsync();

            var response = await mrdDbContext.MRD_Nationalities
                .Select(c => new GetNationalityListResponse
                {
                    NatCode = c.COUNTRY_CODE,
                    NatNamrEn = c.COUNTRY_NAME_EN,
                    NatNameAr = c.COUNTRY_NAME_ARB
                }).AsNoTracking().ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation(ItemsNotFound);
                return await req.WriteNoContentResponseAsync();
            }

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting nationality list");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetVisaTypeList

    /// <summary>
    ///  Get the list of Visa Types from EDW
    ///  </summary>
    ///  <param name="req"></param>
    ///  <returns></returns>
    [Function("GetVisaTypeList")]
    [OpenApiOperation(operationId: "GetVisaTypeList", tags: ["DropDownList"],
        Summary = "Get Visa Type List",
        Description = " Get the visa type list")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetVisaTypeListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> GetVisaTypeList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "visatypes")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation(RequestedUrlTemplate, req.LogRequestedUrl());
            logger.LogInformation("Getting Visa Type List");

            await using var mrdDbContext = await mrdFamilyPhyDbContextFactory.CreateDbContextAsync();

            var response = await mrdDbContext.MAP_MOI_VisaTypes
                .Select(c => new GetVisaTypeListResponse
                {
                    VisaTypeCode = c.VISA_TYPE,
                    VisaDescriptionEn = c.DESCRIPTION_ENGLISH,
                    VisaDescriptionAr = c.DESCRIPTION_ARABIC
                }).AsNoTracking().ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation(ItemsNotFound);
                return await req.WriteNoContentResponseAsync();
            }

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting visa type list");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetLanguageList

    /// <summary>
    /// Get the list of languages
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [ExcludeFromCodeCoverage]
    [Function("GetLanguageList")]
    [OpenApiOperation(operationId: "GetLanguageList", tags: ["DropDownList"], Summary = "Get Language List",
        Description = " Get the language list")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetLanguageListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    public async Task<HttpResponseData> GetLanguageList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "languages")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation(RequestedUrlTemplate, req.LogRequestedUrl());
            logger.LogInformation("Getting Language List");

            await using var edwDbContext = await edwDbContextFactory.CreateDbContextAsync();

            try
            {
                var response = await edwDbContext.GetLanguageListResponse
                    .FromSqlRaw("EXEC dbo.SP_GET_PHYSICIAN_LANG_CODES").ToListAsync();

                if (response.Count == 0)
                {
                    logger.LogInformation(ItemsNotFound);
                    return await req.WriteNoContentResponseAsync();
                }

                return await req.WriteOkResponseAsync(response);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while getting language list");
                return ex.DefaultExceptionBehaviour(req, logger);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred");
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}