# Assignment Functions Test Documentation

## Overview

The Assignment Functions test suite verifies the functionality of assignment-related operations in the EservicesRESTapis project. These tests cover creation, retrieval, updating, and deletion of assignments.

## Test Categories

### 1. Assignment List Operations

#### GetAssignmentList Tests
- **[TestGetAssignmentListByQId](./GetAssignmentList.md#testgetassignmentlistbyqid)**
  - Verifies successful retrieval of assignments for a valid QID
  - Expected: 200 OK response with assignment list

- **[TestGetAssignmentListByInvalidQId](./GetAssignmentList.md#testgetassignmentlistbyinvalidqid)**
  - Tests error handling for invalid QID
  - Expected: 401 Unauthorized response

### 2. Assignment Statistics

#### GetAssignmentStats Tests
- **[TestGetAssignmentStatsByQId](./GetAssignmentStats.md#testgetassignmentstatsbyqid)**
  - Verifies retrieval of assignment statistics for valid QID
  - Expected: 200 OK response with statistics

- **[TestGetAssignmentStatsByInvalidQId](./GetAssignmentStats.md#testgetassignmentstatsbyinvalidqid)**
  - Tests error handling for invalid QID in stats retrieval
  - Expected: 401 Unauthorized response

### 3. Assignment Creation

#### CreateAssignment Tests
- **[TestCreateAssignment_ValidRequest](./CreateAssignment.md#testcreateassignment_validrequest)**
  - Verifies successful assignment creation
  - Expected: 201 Created response

- **[TestCreateAssignment_MissingRequestOrigin](./CreateAssignment.md#testcreateassignment_missingrequestorigin)**
  - Tests creation with missing request origin
  - Expected: 201 Created response (server handles missing origin)

- **[TestCreateAssignment_InvalidEmailFormat](./CreateAssignment.md#testcreateassignment_invalidemailformat)**
  - Tests creation with invalid email format
  - Expected: 201 Created response (server validates email)

- **[TestCreateAssignment_EmptyRequestBody](./CreateAssignment.md#testcreateassignment_emptyrequestbody)**
  - Tests creation with empty request body
  - Expected: 400 BadRequest response

### 4. Assignment Updates

#### UpdateAssignment Tests
- **[TestUpdateAssignmentByReqNumber](./UpdateAssignment.md#testupdateassignmentbyreqnumber)**
  - Verifies successful assignment update
  - Expected: 200 OK response

- **[TestUpdateAssignmentByInvalidReqNumber](./UpdateAssignment.md#testupdateassignmentbyinvalidreqnumber)**
  - Tests update with invalid request number
  - Expected: 400 BadRequest response

### 5. Assignment Deletion

#### DeleteAssignment Tests
- **[TestDeleteAssignmentByReqNumber](./DeleteAssignment.md#testdeleteassignmentbyreqnumber)**
  - Verifies successful assignment deletion
  - Expected: 200 OK response

- **[TestDeleteAssignmentByInvalidReqNumber](./DeleteAssignment.md#testdeleteassignmentbyinvalidreqnumber)**
  - Tests deletion with invalid request number
  - Expected: 400 BadRequest response

## Test Implementation Details

### Setup
```csharp
private readonly ITestOutputHelper _testOutputHelper;
private readonly RestClient _client;

public TestAssignmentFunctions(ITestOutputHelper testOutputHelper)
{
    _testOutputHelper = testOutputHelper;
    _client = new RestClient(BaseUrl);
}
```

### Common Test Patterns
1. **Request Building**:
   ```csharp
   var request = GetRestRequest("assignments");
   request.AddOrUpdateHeader(JwtClaimsQId, qId);
   ```

2. **Response Validation**:
   ```csharp
   NotNull(response);
   Equal(expectedStatusCode, response.StatusCode);
   ```

3. **Error Handling**:
   ```csharp
   response.ThrowIfNull();
   NotNull(content);
   ```

### Test Data Generation
- Uses Bogus for generating fake test data
- Implements custom data generators for specific fields
- Maintains test data consistency across related tests

## Best Practices
1. Each test focuses on a single aspect of functionality
2. Clear separation between happy path and error cases
3. Consistent validation patterns across all tests
4. Proper cleanup of test data
5. Comprehensive logging of test execution

## Common Test Utilities
- RestSharp for HTTP requests
- xUnit for test framework
- Bogus for test data generation
- FluentAssertions for assertions
