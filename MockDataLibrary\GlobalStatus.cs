﻿namespace MockDataLibrary;

public static class GlobalStatus
{
    public const string New = "New";
    public const string Reschedule = "Reschedule";
    public const string Cancel = "Cancel";
    public const string InProcess = "InProcess";
    public const string InProgress = "InProgress";
    public const string Saved = "Saved";
    public const string Submitted = "Submitted";
    public const string PendingOrder = "PendingOrder";
    public const string Archived = "Archived";
    public const string CancelledFrom107 = "CancelledFrom107";
    public const string Confirmed = "Confirmed";
    public const string Cancelled = "Cancelled";
    public const string Rework = "Rework";
    public const string Reworked = "Reworked";
    public const string Approved = "Approved";
    public const string CancelledByEServ = "CancelledByEServ";
    public const string Rejected = "Rejected";
    public const string ReturnedForPayment = "ReturnedForPayment";
    public const string PaymentReceived = "PaymentReceived";
    public const string Returned = "Returned";
    public const string ConditionallyApproved = "ConditionallyApproved";
    public const string ResubmitOriginalsRequested = "ResubmitOriginalsRequested";
    public const string Ready = "Ready";
    public const string Completed = "Completed";    
}