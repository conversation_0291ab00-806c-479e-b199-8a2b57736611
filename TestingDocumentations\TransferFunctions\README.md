# Transfer Functions Test Documentation

## Overview
This documentation covers the test suite for Transfer API functions in the EServices REST API project. The test suite validates various transfer operations including creation, retrieval, updates, and deletion of transfer requests.

## Test Categories

### 1. CRUD Operations
- [Create Transfer](./CreateTransfer.md)
- [Get Transfer Item](./GetTransferItem.md)
- [Update Transfer](./UpdateTransfer.md)
- [Delete Transfer](./DeleteTransfer.md)

### 2. List and Statistics
- [Get Transfer List](./GetTransferList.md)
- [Get Transfer Stats](./GetTransferStats.md)

### 3. Validation
- [Check In-Process Transfer](./CheckInProcessTransfer.md)

## Common Components

### Authentication
- JWT-based authentication
- QID claims validation
- Request origin tracking

### Test Data Generation
- Uses Faker/Bogus library
- Generates realistic test data including:
  * QID (11 digits)
  * Names (English/Arabic)
  * Health Card Numbers
  * Health Centers
  * Transfer Reasons
  * Address Information
  * Attachments

### Request Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: Request source identifier

### Response Models
1. `CreateTransferResponse`
   - ReqNumber (string)

2. `GetTransferItemResponse`
   - ReqNumber (string)
   - QId (long)
   - Other transfer details

3. `GetTransferListResponse`
   - List of transfers
   - Status information
   - QId details

4. `GetTransferStatsResponse`
   - Count (int)

5. `CheckInProcessTransferResponse`
   - IsInprocessExist (bool)

### Mock Data
1. Nationalities
2. Health Centers
3. Transfer Reasons
4. User Information

### Test Categories
- Happy Path: Valid test scenarios
- Unhappy Path: Error handling and validation scenarios

## Best Practices
1. **Data Generation**
   - Use consistent test data patterns
   - Generate realistic values
   - Handle both English and Arabic content

2. **Validation**
   - Verify response status codes
   - Check null responses
   - Validate data integrity
   - Test boundary conditions

3. **Error Handling**
   - Test invalid inputs
   - Verify error responses
   - Check authorization failures

4. **Performance**
   - Efficient test execution
   - Proper resource cleanup
   - Async operation handling

## Test Environment
- .NET Test Framework: xUnit
- HTTP Client: RestSharp
- Test Data Generation: Bogus
- Authentication: JWT Tokens
