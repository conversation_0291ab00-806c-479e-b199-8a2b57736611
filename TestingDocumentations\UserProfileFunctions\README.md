# User Profile Functions Test Documentation

## Overview
This documentation covers the test suite for User Profile API functions in the EServices REST API project. The test suite validates various user profile operations including creation, retrieval, updates, and deletion of user profiles, as well as QID validation and dependent management.

## Test Categories

### 1. User Profile Operations
- [Create User Profile](./CreateUserProfile.md)
- [Update User Profile](./UpdateUserProfile.md)
- [Delete User Profile](./DeleteUserProfile.md)

### 2. User Information Retrieval
- [Get User and Dependents](./GetUserAndDependents.md)
- [Validate QID](./ValidateQID.md)

## Common Components

### Authentication
- JWT-based authentication
- QID claims validation
- Request origin tracking
- PHCC Environment specification

### Test Data Generation
- Uses Faker/Bogus library
- Generates realistic test data including:
  * QID (11 digits)
  * Communication Preferences
  * Phone Numbers
  * Consent Information

### Request Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: Request source identifier
- `PhccEnvironment`: Environment specification (e.g., "STG")

### Response Models
1. `GetUserProfileResponse`
   - User information
   - List of dependents

2. `ValidateQIDFlags`
   - Multiple validation flags
   - Status indicators
   - Eligibility checks

3. `CreateUpdateUserProfileRequest`
   - QID
   - Communication preferences
   - Contact information
   - Consent status

4. `ErrorResponse`
   - Error message
   - Status details

### Test Categories
- Happy Path: Valid test scenarios
- Unhappy Path: Error handling and validation scenarios

## Best Practices
1. **Data Generation**
   - Use consistent test data patterns
   - Generate realistic values
   - Handle communication preferences

2. **Validation**
   - Verify response status codes
   - Check null responses
   - Validate data integrity
   - Test boundary conditions

3. **Error Handling**
   - Test invalid inputs
   - Verify error responses
   - Check authorization failures

4. **Performance**
   - Efficient test execution
   - Proper resource cleanup
   - Async operation handling

## Test Environment
- .NET Test Framework: xUnit
- HTTP Client: RestSharp
- Test Data Generation: Bogus
- Authentication: JWT Tokens
- Environment: Staging (STG)
