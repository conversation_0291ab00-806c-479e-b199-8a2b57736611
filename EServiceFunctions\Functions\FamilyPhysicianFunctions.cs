using EServiceFunctions.Models.FamilyPhysician;
using EServiceFunctions.RequestResponseModels.FamilyPhysician;

namespace EServiceFunctions.Functions;

public class FamilyPhysicianFunctions(
    IDbContextFactory<MRDDbContext> dbContextFactory,
    ILogger<FamilyPhysicianFunctions> logger)
{
    #region GetFamilyPhysicianByPhysicianCode

    /// <param name="req"></param>
    /// <param name="phyCode"></param>
    /// <returns></returns>
    [Function("GetFamilyPhysicianByPhysicianCode")]
    [OpenApiOperation(operationId: "GetFamilyPhysicianByPhysicianCode", tags: ["FamilyPhysician"],
        Summary = "Get Family Physician Item By Physician Code",
        Description = " Get the family physician details for the given physician code")]
    [OpenApiParameter("phyCode", Description = "Physician Code", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetFamilyPhysicianItemResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetFamilyPhysicianByPhysicianCode(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "familyphysicians/{phyCode}")]
        HttpRequestData req, string phyCode)
    {
        try
        {
            var submitter = req.GetClaims();
            logger.LogInformation("Getting Family Physician Details {SubmitterQId}", submitter.QId);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.MRD_Family_Physicians
                .Where(item => item.CORP_ID == phyCode)
                .Select(item => new GetFamilyPhysicianItemResponse
                {
                    PhyCode = item.CORP_ID ?? string.Empty,
                    PhyNameEn = item.NAME_EN ?? string.Empty,
                    PhyNameAr = item.NAME_ARB ?? string.Empty,
                    Title = item.POSITION_EN ?? string.Empty,
                    ClinicalTitleEn = item.CLINICAL_TITLE_EN ?? string.Empty,
                    ClinicalTitleAr = item.CLINICAL_TITLE_ARB ?? string.Empty,
                    Language = item.LANGUAGE_CODE ?? string.Empty,
                    Qualification = item.QUALIFICATIONS ?? string.Empty,
                    TotalExp = item.TOTAL_EXPERIENCE ?? 0,
                    SpltyCode = item.SPECIALTY_CODE ?? string.Empty,
                    Gender = item.GENDER_CODE == "2" ? "0" : item.GENDER_CODE == "0" ? "2" : "1",
                    Image = item.PHOTO_CONSENT == true && item.PHYSICIAN_PHOTO_BINARY != null
                        ? ToBase64String(item.PHYSICIAN_PHOTO_BINARY)
                        : string.Empty,
                    HCntCode = item.FAC_CODE ?? string.Empty,
                    OtherLanguage = item.OTHER_LANGUAGES ?? string.Empty,
                    OtherSpecialty = item.OTHER_SPECIALITY ?? string.Empty
                }).AsNoTracking()
                .FirstOrDefaultAsync();

            if (response == null || IsEmptyObject(response))
            {
                logger.LogInformation("Items not found");
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Family Physician Details For Physician Code ({ResponsePhyCode})", response.PhyCode);

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetSpecialityList

    [Function("GetSpecialityList")]
    [OpenApiOperation(operationId: "GetSpecialityList", tags: ["FamilyPhysician"],
        Summary = "Get Speciality List", Description = " Get the speciality list")]
    [OpenApiParameter("spltyCode", Description = "Speciality Code", In = ParameterLocation.Query, Required = false,
        Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetSpecialityResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetSpecialityList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "specialities")]
        HttpRequestData req)
    {
        try
        {            
            logger.LogInformation($"Getting Speciality List");
            string specialityCode = req.GetCleanedQueryString("spltyCode");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            var response = new List<GetSpecialityResponse>();
            
            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var query = dbContext.MRD_Physician_Specialties.AsQueryable().AsNoTracking();

            if (!string.IsNullOrWhiteSpace(specialityCode))
            {
                query = query.Where(s => s.SPECIALTY_CODE == specialityCode);
            }

            query = query.OrderBy(s => s.SPECIALTY_CODE).Skip(skip).Take(take);

            var specialties = await query.ToListAsync();

            if (specialties.Count == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Row count {SpecialtiesCount}", specialties.Count);

            foreach (var specialty in specialties)
            {
                var item = new GetSpecialityResponse
                {
                    SpltyCode = specialty.SPECIALTY_CODE,
                    SpltyNameEn = specialty.SPECIALTY_NAME_EN,
                    SpltyNameAr = specialty.SPECIALTY_NAME_ARB
                };

                response.Add(item);
            }

            logger.LogInformation("Returning Speciality List ({ResponseCount})", response.Count);

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetFamilyPhysicianList
    
    [Function("GetFamilyPhysicianList")]
    [OpenApiOperation(operationId: "GetFamilyPhysicianList", tags: ["FamilyPhysician"],
        Summary = "Get Family Physician List By Health Center Code",
        Description =
            " Get the family physician list for the given Health Center Code (required) & Gender/Language/Speciality (query params).")]
    [OpenApiParameter("hcCode", Description = "Health Center Code", In = ParameterLocation.Query, Required = true,
        Type = typeof(string))]
    [OpenApiParameter("gender", Description = "Gender Code. 0-Female, 1-Male, 2-Unknown.", In = ParameterLocation.Query,
        Required = false, Type = typeof(string))]
    [OpenApiParameter("language", Description = "Language Code. ex: AR OR EN etc. for Arabic or English",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("speciality",
        Description = "Speciality Code. ex: 1 OR 2 OR 3 etc. for NCD or Postnatal/Antenatal�or Pediatrics",
        In = ParameterLocation.Query, Required = false, Type = typeof(int))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetFamilyPhysicianListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    public async Task<HttpResponseData> GetFamilyPhysicianList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "familyphysicians")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation($"Getting Family Physician List");

            string hcCode = req.GetCleanedQueryString("hcCode");
            string gender = req.GetCleanedQueryString("gender");
            string language = req.GetCleanedQueryString("language");
            string speciality = req.GetCleanedQueryString("speciality");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            logger.LogInformation("hcCode: {HcCode}, gender: {Gender}, language: {Language}, speciality: {Speciality}, skip: {Skip}, take: {Take}", hcCode, gender, language, speciality, skip, take);

            if (string.IsNullOrWhiteSpace(hcCode))
            {
                return await req.WriteErrorResponseAsync(BadRequest,
                    $"Please provide a valid health center code: {hcCode}");
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.MRD_Family_Physicians.AsNoTracking().Where(p => p.STATUS == true);

            if (hcCode.IsNotNullOrWhiteSpace())
            {
                query = query.Where(p => p.FAC_CODE == hcCode);
            }

            if (speciality.IsNotNullOrWhiteSpace())
            {
                query = query.Where(p => p.SPECIALTY_CODE.Contains(speciality));
            }

            if (language.IsNotNullOrWhiteSpace())
            {
                query = query.Where(p => p.LANGUAGE_CODE.Contains(language));
            }

            if (gender.IsNotNullOrWhiteSpace())
            {
                gender = gender switch
                {
                    "2" => "0",
                    "0" => "2",
                    _ => "1"
                };  
                query = query.Where(p => p.GENDER_CODE == gender);
            }

            var response = await query
                .OrderBy(p => p.CORP_ID)
                .Skip(skip)
                .Take(take)
                .Select(item => new GetFamilyPhysicianListResponse
                {
                    PhyCode = item.CORP_ID ?? string.Empty,
                    PhyNameEn = item.NAME_EN ?? string.Empty,
                    PhyNameAr = item.NAME_ARB ?? string.Empty,
                    Title = item.POSITION_EN ?? string.Empty,
                    ClinicalTitleEn = item.CLINICAL_TITLE_EN ?? string.Empty,
                    ClinicalTitleAr = item.CLINICAL_TITLE_ARB ?? string.Empty,
                    Language = item.LANGUAGE_CODE ?? string.Empty,
                    Qualification = item.QUALIFICATIONS ?? string.Empty,
                    TotalExp = item.TOTAL_EXPERIENCE ?? 0,
                    SpltyCode = item.SPECIALTY_CODE ?? string.Empty,
                    Gender = item.GENDER_CODE == "2" ? "0" : item.GENDER_CODE == "0" ? "2" : "1",
                    Image = item.PHYSICIAN_PHOTO_BINARY != null && item.PHOTO_CONSENT == true
                        ? ToBase64String(item.PHYSICIAN_PHOTO_BINARY)
                        : string.Empty,
                    OtherLanguage = item.OTHER_LANGUAGES ?? string.Empty,
                    OtherSpecialty = item.OTHER_SPECIALITY ?? string.Empty
                })
                .ToListAsync();

            if (response.Count == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}