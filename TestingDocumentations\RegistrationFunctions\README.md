# Registration Functions Test Documentation

## Overview
This documentation covers the test suite for Registration API functions in the EServices REST API project. The test suite validates various registration operations including creation, retrieval, updates, and deletion of registration requests.

## Test Categories

### 1. CRUD Operations
- [Create Registration](./CreateRegistration.md)
- [Get Registration Item](./GetRegistrationItem.md)
- [Update Registration](./UpdateRegistration.md)
- [Delete Registration](./DeleteRegistration.md)

### 2. List and Statistics
- [Get Registration List](./GetRegistrationList.md)
- [Get Registration Stats](./GetRegistrationStats.md)

### 3. Validation
- [Check In-Process Registration](./CheckInProcessRegistration.md)

## Common Components

### Authentication
- JWT-based authentication
- QID claims validation
- Request origin tracking

### Test Data Generation
- Uses Faker/Bogus library
- Generates realistic test data including:
  * QID (11 digits)
  * Names (English/Arabic)
  * Personal Information
  * Contact Details
  * Address Information
  * Attachments

### Request Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: Request source identifier

### Response Models
1. `CreateRegistrationResponse`
   - ReqNumber (string)

2. `GetRegistrationItemResponse`
   - ReqNumber (string)
   - QId (long)
   - Other registration details

3. `GetRegistrationListResponse`
   - List of registrations
   - Status information
   - QId details

4. `GetRegistrationStatsResponse`
   - Count (int)

5. `CheckInProcessRegistrationResponse`
   - IsInprocessExist (bool)

### Mock Data
1. Occupations
2. Nationalities
3. Visa Types
4. Languages
5. Health Centers

### Test Categories
- Happy Path: Valid test scenarios
- Unhappy Path: Error handling and validation scenarios

## Best Practices
1. **Data Generation**
   - Use consistent test data patterns
   - Generate realistic values
   - Handle both English and Arabic content

2. **Validation**
   - Verify response status codes
   - Check null responses
   - Validate data integrity
   - Test boundary conditions

3. **Error Handling**
   - Test invalid inputs
   - Verify error responses
   - Check authorization failures

4. **Performance**
   - Efficient test execution
   - Proper resource cleanup
   - Async operation handling

## Test Environment
- .NET Test Framework: xUnit
- HTTP Client: RestSharp
- Test Data Generation: Bogus
- Authentication: JWT Tokens
