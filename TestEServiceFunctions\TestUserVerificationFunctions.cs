﻿namespace TestEServiceFunctions;

[Collection("UserVerificationFunctions")]
public class TestUserVerificationFunctions(IRestLibrary restLibrary, ITestOutputHelper testOutputHelper)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetUserVerificationById()
    {
        // Arrange
        var request = GetRestRequest("userVerification/isValid/{qId}");
        request.AddUrlSegment("qId", 31363403437);
        request.AddOrUpdateHeader(JwtClaimsQId, 31363403437);

        // Act
        var response = await _client.GetAsync<UserDetailsResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Age > 0);
        False(response.IsAdult);
        True(response.IsAlive);
        Equal(11, response.Age);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetUserVerificationByIdWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("userVerification/isValid/{qId}");
        request.AddUrlSegment("qId", 2323232323211);
        request.AddOrUpdateHeader(JwtClaimsQId, 2323232323211);
        request.Method = Method.Get;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(NoContent == response.StatusCode);
    }
}