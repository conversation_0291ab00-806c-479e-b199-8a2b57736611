# Family Physician Functions Documentation

## Overview
The Family Physician Functions module provides REST API endpoints for managing and retrieving information about family physicians in the healthcare system. This module facilitates searching and retrieving physician information based on various criteria such as health center, gender, language, and specialty, with comprehensive support for bilingual data (Arabic/English) and integration with the Medical Records Database (MRD).

## API Endpoints

### 1. Get Family Physician By Physician Code
- **Endpoint**: `GET /familyphysicians/{phyCode}`
- **Description**: Retrieves detailed information about a specific family physician
- **Authorization**: Function level, Claims-based
- **Parameters**:
  - `phyCode` (path, required): Physician's unique identifier code
- **Response**:
  ```json
  {
    "phyCode": "string",           // Physician's unique identifier
    "phyNameEn": "string",         // English name
    "phyNameAr": "string",         // Arabic name
    "title": "string",             // Professional title
    "clinicalTitleEn": "string",   // Clinical title in English
    "clinicalTitleAr": "string",   // Clinical title in Arabic
    "language": "string",          // Primary language code
    "qualification": "string",      // Professional qualifications
    "totalExp": "integer",         // Years of experience
    "spltyCode": "string",         // Specialty code
    "gender": "string",            // Gender code (0-Female, 1-Male, 2-Unknown)
    "image": "string",             // Base64 encoded photo (if consent given)
    "hCntCode": "string",          // Health center code
    "otherLanguage": "string",     // Additional languages
    "otherSpecialty": "string"     // Additional specialties
  }
  ```
- **Error Responses**:
  - 204: Physician not found
  - 400: Invalid physician code
  - 500: Database error

### 2. Get Speciality List
- **Endpoint**: `GET /specialities`
- **Description**: Retrieves a paginated list of medical specialties
- **Authorization**: Function level
- **Parameters**:
  - `spltyCode` (query, optional): Filter by specialty code
  - `skip` (query, optional): Number of records to skip (pagination)
  - `take` (query, optional): Number of records to take (pagination)
- **Response**:
  ```json
  [
    {
      "spltyCode": "string",      // Specialty code
      "spltyNameEn": "string",    // English name
      "spltyNameAr": "string"     // Arabic name
    }
  ]
  ```
- **Error Responses**:
  - 204: No specialties found
  - 500: Database error

### 3. Get Family Physician List
- **Endpoint**: `GET /familyphysicians`
- **Description**: Searches for family physicians using multiple filter criteria
- **Authorization**: Function level
- **Parameters**:
  - `hcCode` (query, required): Health Center Code
  - `gender` (query, optional): Gender Code
    - "0": Female
    - "1": Male
    - "2": Unknown
  - `language` (query, optional): Language Code (e.g., AR, EN)
  - `speciality` (query, optional): Specialty Code
  - `skip` (query, optional): Pagination offset
  - `take` (query, optional): Page size
- **Response**:
  ```json
  [
    {
      "phyCode": "string",           // Physician's unique identifier
      "phyNameEn": "string",         // English name
      "phyNameAr": "string",         // Arabic name
      "title": "string",             // Professional title
      "clinicalTitleEn": "string",   // Clinical title in English
      "clinicalTitleAr": "string",   // Clinical title in Arabic
      "language": "string",          // Primary language code
      "qualification": "string",      // Professional qualifications
      "totalExp": "integer",         // Years of experience
      "spltyCode": "string",         // Specialty code
      "gender": "string",            // Gender code
      "image": "string",             // Base64 encoded photo
      "otherLanguage": "string",     // Additional languages
      "otherSpecialty": "string"     // Additional specialties
    }
  ]
  ```
- **Error Responses**:
  - 204: No physicians found
  - 400: Invalid health center code
  - 500: Database error

## Technical Implementation

### Database Integration
1. **Context Management**
   - Uses `MRDDbContext` for Medical Records database access
   - Implements DbContextFactory pattern for efficient connection management
   - Proper context disposal with async/await pattern

2. **Query Optimization**
   - No-tracking queries for read-only operations
   - Efficient projection using Select statements
   - Proper indexing on frequently queried fields
   - Pagination for large result sets

3. **Data Access Patterns**
   - Repository pattern implementation
   - Asynchronous database operations
   - Efficient connection management
   - Proper error handling and logging

### Security Implementation
1. **Authorization**
   - Function-level security
   - Claims-based authentication
   - Proper error handling for unauthorized access
   - Access logging and monitoring

2. **Data Protection**
   - Photo consent validation
   - Secure image handling
   - Input validation and sanitization
   - Proper error message handling

### Performance Features
1. **Query Optimization**
   - Efficient filtering mechanisms
   - Proper use of indexes
   - Minimized data transfer
   - Optimized joins

2. **Resource Management**
   - Connection pooling
   - Efficient memory usage
   - Proper resource disposal
   - Caching considerations

### Data Handling
1. **Multilingual Support**
   - Arabic/English field pairs
   - Proper character encoding
   - Language-specific validation
   - RTL/LTR text handling

2. **Image Processing**
   - Base64 encoding/decoding
   - Photo consent tracking
   - Efficient binary data handling
   - Image size optimization

## Response Models

### GetFamilyPhysicianItemResponse
```json
{
  "phyCode": "string",           // Required
  "phyNameEn": "string",         // Required
  "phyNameAr": "string",         // Required
  "title": "string",             // Optional
  "clinicalTitleEn": "string",   // Optional
  "clinicalTitleAr": "string",   // Optional
  "language": "string",          // Required
  "qualification": "string",     // Optional
  "totalExp": "integer",         // Required
  "spltyCode": "string",         // Required
  "gender": "string",            // Required
  "image": "string",             // Optional
  "hCntCode": "string",         // Required
  "otherLanguage": "string",     // Optional
  "otherSpecialty": "string"     // Optional
}
```

### GetSpecialityResponse
```json
{
  "spltyCode": "string",      // Required
  "spltyNameEn": "string",    // Required
  "spltyNameAr": "string"     // Required
}
```

## Error Handling

### Error Response Format
```json
{
  "message": "string",     // Error message
  "details": "string",     // Additional details
  "stackTrace": "string"   // Development only
}
```

### Common Error Scenarios
1. **Validation Errors**
   - Invalid health center code
   - Invalid physician code
   - Invalid pagination parameters
   - Invalid filter criteria

2. **Database Errors**
   - Connection failures
   - Query timeout
   - Constraint violations
   - Data integrity issues

3. **Security Errors**
   - Unauthorized access
   - Invalid claims
   - Missing headers
   - Invalid permissions

## Best Practices
1. **Error Handling**
   - Use appropriate HTTP status codes
   - Provide meaningful error messages
   - Implement proper logging
   - Handle all exceptions

2. **Performance**
   - Implement efficient pagination
   - Use proper indexing
   - Optimize database queries
   - Consider caching strategies

3. **Security**
   - Validate all inputs
   - Implement proper authorization
   - Handle sensitive data securely
   - Follow least privilege principle

4. **Maintenance**
   - Follow consistent coding standards
   - Maintain comprehensive documentation
   - Regular performance monitoring
   - Security reviews and updates

## Dependencies
- Entity Framework Core
- Azure Functions
- System.Text.Json
- Microsoft.Azure.WebJobs
- Application Insights