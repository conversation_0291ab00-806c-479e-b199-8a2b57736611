# Delete Attachments Tests

## Overview

These tests verify the functionality of deleting file attachments from the system. The test suite covers various deletion scenarios including single file deletion, batch deletion, and cleanup processes.

## Test Cases

### TestDeleteAttachment_ValidId_ReturnsOk

Tests the successful deletion of a valid attachment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteAttachment_ValidId_ReturnsOk()
{
    // Arrange
    var attachmentId = await UploadTestFile();
    var request = GetRestRequest($"attachments/{attachmentId}");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);

    // Verify file is deleted
    var fileExists = await CheckFileExists(attachmentId);
    False(fileExists);
}
```

### TestDeleteMultipleAttachments_ValidIds_ReturnsOk

Tests the deletion of multiple attachments in a single request.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteMultipleAttachments_ValidIds_ReturnsOk()
{
    // Arrange
    var attachmentIds = new[]
    {
        await UploadTestFile("test1.pdf", "application/pdf"),
        await UploadTestFile("test2.jpg", "image/jpeg")
    };

    var request = GetRestRequest("attachments/batch");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    request.AddJsonBody(new { AttachmentIds = attachmentIds });

    // Act
    var response = await _client.ExecuteAsync<BatchDeleteResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
    NotNull(response.Data);
    Equal(attachmentIds.Length, response.Data.SuccessfulDeletes.Count);
    Empty(response.Data.FailedDeletes);

    // Verify all files are deleted
    foreach (var id in attachmentIds)
    {
        var fileExists = await CheckFileExists(id);
        False(fileExists);
    }
}
```

## Request/Response Models

### DeleteRequest
```csharp
public class DeleteRequest
{
    public string AttachmentId { get; set; }
    public bool ForceDelete { get; set; }
}
```

### BatchDeleteRequest
```csharp
public class BatchDeleteRequest
{
    public List<string> AttachmentIds { get; set; }
    public bool ForceDelete { get; set; }
}
```

### BatchDeleteResponse
```csharp
public class BatchDeleteResponse
{
    public List<string> SuccessfulDeletes { get; set; }
    public List<FailedDelete> FailedDeletes { get; set; }
}
```

## Implementation Details

### 1. Single File Deletion
```csharp
private async Task DeleteAttachment(string attachmentId)
{
    var attachment = await GetAttachmentOrThrow(attachmentId);
    await ValidateDeletePermission(attachment);

    // Delete physical file
    var filePath = GetStoragePath(attachment);
    if (File.Exists(filePath))
        File.Delete(filePath);

    // Remove from database
    await _attachmentRepository.DeleteAsync(attachment);

    // Clear cache
    await InvalidateCache(attachmentId);
}
```

### 2. Batch Deletion
```csharp
private async Task<BatchDeleteResponse> DeleteMultiple(
    List<string> attachmentIds,
    bool forceDelete = false)
{
    var response = new BatchDeleteResponse
    {
        SuccessfulDeletes = new List<string>(),
        FailedDeletes = new List<FailedDelete>()
    };

    foreach (var id in attachmentIds)
    {
        try
        {
            await DeleteAttachment(id);
            response.SuccessfulDeletes.Add(id);
        }
        catch (Exception ex) when (!forceDelete)
        {
            response.FailedDeletes.Add(new FailedDelete
            {
                AttachmentId = id,
                Error = ex.Message
            });
        }
    }

    return response;
}
```

### 3. Cleanup Process
```csharp
private async Task CleanupAttachment(Attachment attachment)
{
    // Remove thumbnails
    await DeleteThumbnails(attachment);

    // Remove from search index
    await _searchService.RemoveFromIndex(attachment);

    // Update references
    await UpdateReferences(attachment);

    // Log deletion
    await LogDeletion(attachment);
}
```

## Security Considerations

### 1. Permission Validation
```csharp
private async Task ValidateDeletePermission(Attachment attachment)
{
    var user = GetCurrentUser();
    
    if (attachment.CreatedBy != user.Id && !IsAdmin(user))
        throw new UnauthorizedException("Not authorized to delete this attachment");

    if (await IsAttachmentLocked(attachment))
        throw new ValidationException("Attachment is locked and cannot be deleted");
}
```

### 2. Deletion Tracking
```csharp
private async Task LogDeletion(Attachment attachment)
{
    await _auditLogger.LogAsync(new AuditLog
    {
        Action = "Delete",
        EntityType = "Attachment",
        EntityId = attachment.Id,
        UserId = GetCurrentUser().Id,
        Timestamp = DateTime.UtcNow,
        Details = new
        {
            FileName = attachment.FileName,
            Size = attachment.Size,
            ContentType = attachment.ContentType
        }
    });
}
```

## Error Handling

### 1. Delete Validation
```csharp
private async Task ValidateDelete(string attachmentId)
{
    var attachment = await GetAttachmentOrThrow(attachmentId);

    if (!File.Exists(GetStoragePath(attachment)))
        throw new FileNotFoundException($"File not found for attachment {attachmentId}");

    if (IsFileLocked(GetStoragePath(attachment)))
        throw new IOException("File is currently in use");

    if (await HasActiveReferences(attachment))
        throw new ValidationException("Attachment has active references");
}
```

### 2. Reference Handling
```csharp
private async Task UpdateReferences(Attachment attachment)
{
    var references = await _referenceRepository.GetReferences(attachment.Id);
    
    foreach (var reference in references)
    {
        try
        {
            await _referenceRepository.RemoveReference(reference);
            await NotifyReferenceRemoval(reference);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                "Failed to update reference {ReferenceId}: {Error}",
                reference.Id,
                ex.Message
            );
        }
    }
}
```

## Performance Optimization

### 1. Batch Processing
```csharp
private async Task<BatchDeleteResponse> DeleteMultipleParallel(
    List<string> attachmentIds)
{
    var tasks = attachmentIds.Select(id => DeleteAttachment(id));
    var results = await Task.WhenAll(
        tasks.Select(t => Task.Run(async () =>
        {
            try
            {
                await t;
                return new { Success = true, Id = id };
            }
            catch (Exception ex)
            {
                return new { Success = false, Id = id, Error = ex.Message };
            }
        }))
    );

    return new BatchDeleteResponse
    {
        SuccessfulDeletes = results.Where(r => r.Success).Select(r => r.Id).ToList(),
        FailedDeletes = results.Where(r => !r.Success)
            .Select(r => new FailedDelete { AttachmentId = r.Id, Error = r.Error })
            .ToList()
    };
}
```

### 2. Cache Management
```csharp
private async Task InvalidateCache(string attachmentId)
{
    var cacheKeys = new[]
    {
        $"file_{attachmentId}",
        $"metadata_{attachmentId}",
        $"thumbnail_{attachmentId}"
    };

    foreach (var key in cacheKeys)
    {
        await _cache.RemoveAsync(key);
    }
}
```

## Notes

1. **File System**
   - Handle concurrent access
   - Verify file removal
   - Clean up temporary files

2. **Database**
   - Maintain referential integrity
   - Handle cascading deletes
   - Track deletion history

3. **Security**
   - Validate permissions
   - Log all deletions
   - Handle sensitive data

## Recommendations

1. **Deletion Strategy**
   - Consider soft delete
   - Implement recovery
   - Track dependencies

2. **Security Measures**
   - Add deletion approval
   - Track unusual patterns
   - Backup critical files

3. **Performance**
   - Optimize batch size
   - Handle large volumes
   - Manage resources
