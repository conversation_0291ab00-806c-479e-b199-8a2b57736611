# Token Generator Functions API Documentation

This document provides detailed information about the Token Generator Functions API endpoints, which handle JWT token generation, validation, and RSA encryption/decryption operations for the Ehtheraz system.

## API Endpoints

### 1. Generate JWT Token
- **Endpoint**: `POST /generate-token`
- **Description**: Generates a JWT token with a 30-minute expiration time for Ehtheraz request validation
- **Authorization**: Function-level authorization required
- **Request Body**:
  ```json
  {
    "QId": "string",
    "Email": "string",
    "Mobile": "string"
  }
  ```
- **Request Validation**:
  - Email must be valid format
  - QID must be valid format
  - Mobile number must be valid format
- **Response Format**:
  ```json
  {
    "token": "string"  // JWT token string
  }
  ```
- **Response Codes**:
  - 200 (OK): Successfully generated token
  - 400 (Bad Request): Invalid input data
  - 500 (Internal Server Error): Server-side error

### 2. Validate JWT Token
- **Endpoint**: `GET /validate-token/{token}`
- **Description**: Validates a JWT token and returns the decoded credentials
- **Authorization**: Anonymous access allowed
- **Parameters**:
  - `token` (path, required): JWT token to validate
- **Response Format**:
  ```json
  {
    "QId": "string",
    "Email": "string",
    "Mobile": "string"
  }
  ```
- **Response Codes**:
  - 202 (Accepted): Token successfully validated
  - 400 (Bad Request): Invalid token
  - 500 (Internal Server Error): Validation error

### 3. RSA Decrypt
- **Endpoint**: `POST /rsa/decrypt`
- **Description**: Decrypts RSA encrypted cipher text containing app credentials
- **Authorization**: Anonymous access allowed
- **Request Body**:
  ```json
  {
    "CipherText": "string"  // URL-encoded RSA encrypted text
  }
  ```
- **Response Format**:
  ```json
  {
    "DecryptedText": "string"  // Decrypted data containing app credentials
  }
  ```
- **Response Codes**:
  - 202 (Accepted): Successfully decrypted data
  - 400 (Bad Request): Invalid cipher text
  - 500 (Internal Server Error): Decryption error

### 4. Decrypt Text
- **Endpoint**: `GET /decrypt-text/{base64CipherText}`
- **Description**: Decrypts base64 encoded cipher text
- **Authorization**: Anonymous access allowed
- **Parameters**:
  - `base64CipherText` (path, required): Base64 encoded cipher text
- **Response Format**:
  - String containing decrypted data
- **Response Codes**:
  - 202 (Accepted): Successfully decrypted text
  - 400 (Bad Request): Invalid cipher text
  - 500 (Internal Server Error): Decryption error

## Technical Implementation Details

### Security Features

#### JWT Token Generation
1. Token Properties:
   - 30-minute expiration time
   - HMAC SHA256 algorithm
   - Secure key storage in Azure Key Vault
   - Includes transaction ID for tracking

2. Claims Included:
   - Issuer (iss)
   - Audience (aud)
   - Expiration time (exp)
   - User QID
   - User mobile number
   - User email
   - Transaction ID

#### Encryption
1. RSA Encryption:
   - Asymmetric encryption for sensitive data
   - URL-safe encoding/decoding
   - JSON validation of decrypted data

2. Base64 Encryption:
   - Support for legacy systems
   - URL-safe character handling

### Data Validation

1. Input Validation:
   - Email format verification
   - QID format checking
   - Mobile number format validation
   - JSON structure validation

2. Token Validation:
   - Signature verification
   - Expiration time checking
   - Claim presence verification

### Error Handling
1. Comprehensive error management:
   - Invalid input detection
   - Decryption failures
   - Token validation errors
   - JSON parsing errors

2. Standardized error responses:
   - Consistent error message format
   - Appropriate HTTP status codes
   - Detailed logging for troubleshooting

## Request/Response Models

### TokenCredentialsModel
- **Purpose**: Input model for token generation
- **Fields**:
  - `QId`: Qatar ID number
  - `Email`: User's email address
  - `Mobile`: User's mobile number

### TokenResponse
- **Purpose**: JWT token response
- **Fields**:
  - `token`: Generated JWT token string

### DecryptRequest
- **Purpose**: RSA decryption request
- **Fields**:
  - `CipherText`: Encrypted data string

### DecryptedResponse
- **Purpose**: Decryption result
- **Fields**:
  - `DecryptedText`: Decrypted data string

## Dependencies
- JWT.NET library
  - Token generation and validation
  - HMAC SHA256 algorithm support
- Azure Key Vault
  - Secure key storage
  - Configuration management
- Newtonsoft.Json
  - JSON parsing and validation
- Custom Extensions
  - Input validation helpers
  - Error handling utilities

## Best Practices
1. Always validate input data
2. Use secure key storage
3. Implement proper error handling
4. Log security-related events
5. Use appropriate authorization levels
6. Validate JSON structure
7. Handle URL-safe encoding

## Security Considerations
1. Token expiration management
2. Secure key storage
3. Input sanitization
4. Authorization checks
5. Encryption strength
6. Secure communication

## Logging
- Request logging
- Error logging
- Security event logging
- Performance monitoring

## Future Enhancements
1. Additional encryption methods
2. Enhanced token validation
3. Rate limiting implementation
4. Improved error reporting
5. Extended security features
6. Performance optimizations