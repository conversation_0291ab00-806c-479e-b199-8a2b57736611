﻿using EServiceFunctions.Models.ReleaseOfInformation;
using EServiceFunctions.RequestResponseModels.ReleaseOfInformation;

namespace UnitTestEServiceFunctions;

[ExcludeFromCodeCoverage]
public class TestReleaseContext(string dbName = "TestReleaseOfInformation") : IDbContextFactory<ReleaseContext>
{
    private readonly DbContextOptions<ReleaseContext> _options = new DbContextOptionsBuilder<ReleaseContext>()
        .UseInMemoryDatabase(databaseName: dbName)
        .Options;

    public ReleaseContext CreateDbContext()
    {
        var context = new ReleaseContext(_options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.AddRange(MockDataForReleaseOfInformation.GetReleaseItems());
        context.AddRange(MockDataForReleaseOfInformation.GetReleaseStatus());

        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public static class MockDataForReleaseOfInformation
{
    public static IEnumerable<Status> GetReleaseStatus()
    {
        var statusList = new List<Status>
        {
            new()
            {
                Code = "New",
                DescriptionEn = "New",
                DescriptionAr = "جديد",
                Category = "New"
            },
            new()
            {
                Code = Approved,
                DescriptionEn = "Request Completed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = Cancelled,
                DescriptionEn = "Request Cancelled",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = CancelledByEServ,
                DescriptionEn = "Cancelled by EServices",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = "CancelledByRegistration",
                DescriptionEn = "Cancelled by Registration",
                DescriptionAr = "ألغيت بالتسجيل",
                Category = PendingOrder
            },
            new()
            {
                Code = CancelledFrom107,
                DescriptionEn = "Cancelled from 107",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = ConditionallyApproved,
                DescriptionEn = "Request Tentatively Approved",
                DescriptionAr = "تمت الموافقة المبدئية للطلب",
                Category = InProcess
            },
            new()
            {
                Code = "Confirmed",
                DescriptionEn = "Confirmed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = InProgress,
                DescriptionEn = "Request In Progress",
                DescriptionAr = "الطلب قيد الاجراء",
                Category = InProcess
            },
            new()
            {
                Code = "PaymentReceived",
                DescriptionEn = "Payment Received",
                DescriptionAr = "تمت عملية الدفع",
                Category = InProcess
            },
            new()
            {
                Code = PendingOrder,
                DescriptionEn = "Request Pending",
                DescriptionAr = "الطلب قيد الإنتظار",
                Category = InProcess
            },
            new()
            {
                Code = "ProceedToRegistration",
                DescriptionEn = "Proceed to Registration",
                DescriptionAr = "الشروع في التسجيل",
                Category = InProcess
            }
        };
        return statusList;
    }

    public static IEnumerable<Release> GetReleaseItems()
    {
        var releaseItemList = new List<Release>
        {
            new()
            {
                Id = 1,
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                ReqNumber = "REQ0001",
                HCNumber = HcNumber1,
                Nationality = NationalityEn,
                Dob = new Faker().Date.Past().AddYears(-18),
                CurrentAssignedHC = HcNumber1,
                AttendanceRptStartDate = new Faker().Date.Past().AddDays(-10),
                AttendanceRptEndDate = new Faker().Date.Past(),
                ReleaseItems = "ReleaseItems1",
                OtherReleaseItems = "OtherReleaseItems1",
                EncounterStartDate = new Faker().Date.Past().AddDays(-10),
                EncounterEndDate = new Faker().Date.Past(),
                PickupDelegated = false,
                AuthorizedQId = LongQId,
                AuthorizedPersonName = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
                AuthorizedPersonRelation = "Father",
                DelegationOthers = "DelegationOthers1",
                AttachId1 = Guid.NewGuid(),
                AttachId2 = Guid.NewGuid(),
                AttachId3 = Guid.NewGuid(),
                AttachId4 = Guid.NewGuid(),
                AttachId5 = Guid.NewGuid(),
                AttachId6 = Guid.NewGuid(),
                SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
                SubmittedAt = new Faker().Date.Past(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new Faker().Date.Past(),
                UpdatedAt = GetCurrentTime(),
                Status = InProgress,
                StatusInternal = InProgress,
                SN = "SN1",
                CreateSource = "CreateSource1",
                UpdateSource = "UpdateSource1"
            },
            new()
            {
                Id = 2,
                QId = LongQId,
                FNameEn = new Faker().Name.FirstName(),
                MNameEn = new Faker().Name.FirstName(),
                LNameEn = new Faker().Name.LastName(),
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                ReqNumber = "REQ0002",
                HCNumber = HcNumber2,
                Nationality = "144",
                Dob = new Faker().Date.Past().AddYears(-18),
                CurrentAssignedHC = HcNumber2,
                AttendanceRptStartDate = new Faker().Date.Past().AddDays(-10),
                AttendanceRptEndDate = new Faker().Date.Past(),
                ReleaseItems = "ReleaseItems2",
                OtherReleaseItems = "OtherReleaseItems2",
                EncounterStartDate = new Faker().Date.Past().AddDays(-10),
                EncounterEndDate = new Faker().Date.Past(),
                PickupDelegated = false,
                AuthorizedQId = LongQId2,
                AuthorizedPersonName = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
                AuthorizedPersonRelation = "Father",
                DelegationOthers = "DelegationOthers2",
                AttachId1 = Guid.NewGuid(),
                AttachId2 = Guid.NewGuid(),
                AttachId3 = Guid.NewGuid(),
                AttachId4 = Guid.NewGuid(),
                AttachId5 = Guid.NewGuid(),
                AttachId6 = Guid.NewGuid(),
                SubmittedBy = FirstNameEn + " " + MiddleNameEn + " " + LastNameEn,
                SubmittedAt = new Faker().Date.Past(),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new Faker().Date.Past(),
                UpdatedAt = GetCurrentTime(),
                Status = "New",
                StatusInternal = "New",
                SN = "SN2",
                CreateSource = "CreateSource2",
                UpdateSource = "UpdateSource2"
            }
        };
        return releaseItemList;
    }
}

public class UnitTestReleaseOfInformationFunctions
{
    private readonly ILogger<ReleaseOfInformationFunctions> _logger = Mock.Of<ILogger<ReleaseOfInformationFunctions>>();
    private readonly ReleaseOfInformationFunctions _releaseOfInformationFunctions;
    private readonly ITestOutputHelper _output;

    public UnitTestReleaseOfInformationFunctions(ITestOutputHelper output)
    {
        _output = output;
        _releaseOfInformationFunctions = new ReleaseOfInformationFunctions(new TestReleaseContext(), _logger);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseListByQID_Should_Return_OkObjectResult_For_Valid_QId()
    {
        // Arrange
        var queryString = "?status=InProcess&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseOfInformationFunctions.GetReleaseListByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        var responseObject = DeserializeObject<List<GetReleaseListResponse>>(responseString);
        NotNull(responseObject);
        Single(responseObject);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseListByQID_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var queryString = "?status=InProcess&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseOfInformationFunctions.GetReleaseListByQId(request, LongQId2);

        // Assert
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }
    
    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseStatsByQID_Should_Return_OkObjectResult_For_Valid_QId()
    {
        // Arrange
        var queryString = "?status=InProcess";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseOfInformationFunctions.GetReleaseStatsByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        var responseObject = DeserializeObject<GetReleaseStatsResponse>(responseString);
        NotNull(responseObject);
        Equal(1, responseObject.Count);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseStatsByQID_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        const string queryString = "?status=InProcess";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseOfInformationFunctions.GetReleaseStatsByQId(request, 123456789);

        // Assert
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessReleaseByQID_Should_Return_OkObjectResult_With_True_For_Valid_QId()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _releaseOfInformationFunctions.CheckInProcessReleaseByQId(request, LongQId);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        var responseObject = DeserializeObject<CheckInProcessReleaseResponse>(responseString);
        NotNull(responseObject);
        True(responseObject.IsInprocessExist);
    }
    
    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessReleaseByQID_Should_Return_OkObjectResult_With_False_For_Invalid_QId()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _releaseOfInformationFunctions.CheckInProcessReleaseByQId(request, LongQId2 + 11);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        var responseObject = DeserializeObject<CheckInProcessReleaseResponse>(responseString);
        NotNull(responseObject);
        False(responseObject.IsInprocessExist);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _releaseOfInformationFunctions.GetReleaseItemByReqNumber(request, "REQ0001");
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        var responseObject = DeserializeObject<GetReleaseItemResponse>(responseString);
        NotNull(responseObject);
        Equal("REQ0001", responseObject.ReqNumber);
    }
    
    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemByReqNumber_Should_Return_NoContentResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _releaseOfInformationFunctions.GetReleaseItemByReqNumber(request, "REQ0009");
        var result = response as MockHttpResponseData;
        LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }
    
    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRelease_Should_Return_Created_For_Valid_Request()
    {
        // Arrange
        var createRelease = new Faker<CreateUpdateReleaseRequest>()
            .RuleFor(r => r.QId, LongQId)
            .RuleFor(r => r.FNameEn, FirstNameEn)
            .RuleFor(r => r.MNameEn, MiddleNameEn)
            .RuleFor(r => r.LNameEn, LastNameEn)
            .RuleFor(r => r.FNameAr, FirstNameAr)
            .RuleFor(r => r.MNameAr, MiddleNameAr)
            .RuleFor(r => r.LNameAr, LastNameAr)
            .RuleFor(r => r.HCNumber, HcNumber1)
            .RuleFor(r => r.Nationality, NationalityEn)
            .RuleFor(r => r.Dob, new Faker().Date.Past().AddYears(-18))
            .RuleFor(r => r.CurrentAssignedHC, HcNumber1)
            .RuleFor(r => r.AttendanceRptStartDate, new Faker().Date.Past().AddDays(-10))
            .RuleFor(r => r.AttendanceRptEndDate, new Faker().Date.Past())
            .RuleFor(r => r.ReleaseItems, "ReleaseItems1")
            .RuleFor(r => r.OtherReleaseItems, "OtherReleaseItems1")
            .RuleFor(r => r.EncounterStartDate, new Faker().Date.Past().AddDays(-10))
            .RuleFor(r => r.EncounterEndDate, new Faker().Date.Past())
            .RuleFor(r => r.PickupDelegated, false)
            .RuleFor(r => r.AuthorizedQId, LongQId)
            .RuleFor(r => r.AuthorizedPersonName, FirstNameEn + " " + MiddleNameEn + " " + LastNameEn)
            .RuleFor(r => r.AuthorizedPersonRelation, "Father")
            .RuleFor(r => r.DelegationOthers, "DelegationOthers1")
            .RuleFor(r => r.AttachId1, Guid.NewGuid())
            .RuleFor(r => r.AttachId2, Guid.NewGuid())
            .RuleFor(r => r.AttachId3, Guid.NewGuid())
            .RuleFor(r => r.AttachId4, Guid.NewGuid())
            .RuleFor(r => r.AttachId5, Guid.NewGuid())
            .RuleFor(r => r.AttachId6, Guid.NewGuid())
            .RuleFor(r => r.SubmittedBy, FirstNameEn + " " + MiddleNameEn + " " + LastNameEn)
            .RuleFor(r => r.SubmittedAt, new Faker().Date.Past())
            .RuleFor(r => r.SubmitterQId, LongQId)
            .RuleFor(r => r.SubmitterEmail, UserEmail)
            .RuleFor(r => r.SubmitterMobile, MobilePhone)
            .Generate();

        var serializeObject = SerializeObject(createRelease);
        var request = MockHelpers.CreateHttpRequestData(payload: serializeObject);

        // Act
        var response = await _releaseOfInformationFunctions.CreateRelease(request);
        var result = response as MockHttpResponseData;
        LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.Created, response.StatusCode);
    }
    
    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRelease_Should_Return_BadRequest_For_Invalid_Request()
    {
        // Arrange
        var request = MockHelpers.CreateHttpRequestData(payload: "Invalid Request");

        // Act
        var response = await _releaseOfInformationFunctions.CreateRelease(request);

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }
    
    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateReleaseByReqNumber_Should_Return_OK_For_Valid_ReqNumber()
    {
        // Arrange
        const string requestNumber = "REQ0001";

        var updateRelease = new Faker<CreateUpdateReleaseRequest>()
            .RuleFor(r => r.QId, LongQId)
            .RuleFor(r => r.FNameEn, FirstNameEn)
            .RuleFor(r => r.MNameEn, MiddleNameEn)
            .RuleFor(r => r.LNameEn, LastNameEn)
            .RuleFor(r => r.FNameAr, FirstNameAr)
            .RuleFor(r => r.MNameAr, MiddleNameAr)
            .RuleFor(r => r.LNameAr, LastNameAr)
            .RuleFor(r => r.HCNumber, HcNumber1)
            .RuleFor(r => r.Nationality, NationalityEn)
            .RuleFor(r => r.Dob, new Faker().Date.Past().AddYears(-18))
            .RuleFor(r => r.CurrentAssignedHC, HcNumber1)
            .RuleFor(r => r.AttendanceRptStartDate, new Faker().Date.Past().AddDays(-10))
            .RuleFor(r => r.AttendanceRptEndDate, new Faker().Date.Past())
            .RuleFor(r => r.ReleaseItems, "ReleaseItems1")
            .RuleFor(r => r.OtherReleaseItems, "OtherReleaseItems1")
            .RuleFor(r => r.EncounterStartDate, new Faker().Date.Past().AddDays(-10))
            .RuleFor(r => r.EncounterEndDate, new Faker().Date.Past())
            .RuleFor(r => r.PickupDelegated, false)
            .RuleFor(r => r.AuthorizedQId, LongQId)
            .RuleFor(r => r.AuthorizedPersonName, FirstNameEn + " " + MiddleNameEn + " " + LastNameEn)
            .RuleFor(r => r.AuthorizedPersonRelation, "Father")
            .RuleFor(r => r.DelegationOthers, "DelegationOthers1")
            .RuleFor(r => r.AttachId1, Guid.NewGuid())
            .RuleFor(r => r.AttachId2, Guid.NewGuid())
            .RuleFor(r => r.AttachId3, Guid.NewGuid())
            .RuleFor(r => r.AttachId4, Guid.NewGuid())
            .RuleFor(r => r.AttachId5, Guid.NewGuid())
            .RuleFor(r => r.AttachId6, Guid.NewGuid())
            .RuleFor(r => r.SubmittedBy, FirstNameEn + " " + MiddleNameEn + " " + LastNameEn)
            .RuleFor(r => r.SubmittedAt, new Faker().Date.Past())
            .RuleFor(r => r.SubmitterQId, LongQId)
            .RuleFor(r => r.SubmitterEmail, UserEmail)
            .RuleFor(r => r.SubmitterMobile, MobilePhone)
            .Generate();

        var serializeObject = SerializeObject(updateRelease);
        var request = MockHelpers.CreateHttpRequestData(payload: serializeObject);

        // Act
        var response = await _releaseOfInformationFunctions.UpdateReleaseByReqNumber(request, requestNumber);
        var result = response as MockHttpResponseData;
        LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }
    
    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateReleaseByReqNumber_Should_Return_Unauthorized_For_Invalid_ReqNumber()
    {
        // Arrange
        const string requestNumber = "REQ0009";
        var updateRelease = new CreateUpdateReleaseRequest();
        var serializeObject = SerializeObject(updateRelease);
        var request = MockHelpers.CreateHttpRequestData(payload: serializeObject);

        // Act
        var response = await _releaseOfInformationFunctions.UpdateReleaseByReqNumber(request, requestNumber);

        // Assert
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }
    
    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteReleaseByReqNumber_Should_Return_OK_For_Valid_ReqNumber()
    {
        // Arrange
        const string requestNumber = "REQ0001";
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _releaseOfInformationFunctions.DeleteReleaseByReqNumber(request, requestNumber);
        var result = response as MockHttpResponseData;
        LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }
    
    [Fact] // unhappy path
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteReleaseByReqNumber_Should_Return_BadRequest_For_Invalid_ReqNumber()
    {
        // Arrange
        const string requestNumber = "REQ0009";
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _releaseOfInformationFunctions.DeleteReleaseByReqNumber(request, requestNumber);

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }
}