﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.MHDS.EDW;

[ExcludeFromCodeCoverage]
[Table("ESERV_PERSONAS_MEDICATION_REFILL")]
public class EServPersonasMedicationRefill
{   
    [Key]
    [Column("QID")]
    [StringLength(50)]
    public string? QId { get; set; }        
        
    [Column("REFILL_DUE_DATE", TypeName = "date")]
    public DateTime? PrescriptionRefillDueDate { get; set; }

    [Column("LAST_DISPENSE_DATE", TypeName = "date")]
    public DateTime? LastDispenseDate { get; set; }

    [Column("LAST_DISPENSE_FAC_CODE")]
    [StringLength(50)]
    public string? LastDispenseFacCode { get; set; }

    [Column("PRESCRIPTION_DATE", TypeName = "date")]
    public DateTime? PrescriptionDate { get; set; }

    [Column("PRESCRIPTION_FAC_CODE")]
    [StringLength(50)]
    public string? PrescriptionFacCode { get; set; }

    [Column("PRESCRIBED_MEDICATION")]
    [StringLength(200)]
    public string? PrescribedMedication { get; set; }

    [Column("DAYS_SUPPLY")]
    [StringLength(50)]
    public int SupplyDuration { get; set; }

    [Column("PRESCRIPTION_ORDER_ID")]
    public long? PrescriptionOrderId { get; set; }

    [Column("PRESCRIBED_BY_CORP_ID")]
    [StringLength(10)]
    public string? PrescribedByCorpId { get; set; }

    [Column("PRESCRIBED_BY_NAME_ENG")]
    [StringLength(250)]
    public string? PrescribedByNameEng { get; set; }

    [Column("PRESCRIBED_BY_NAME_ARA")]
    [StringLength(300)]
    public string? PrescribedByNameAra { get; set; }

    [Column("ETL_LOAD_DATETIME", TypeName = "datetime")]
    public DateTime? EtlLoadDatetime { get; set; }

    [Column("HC_NUMBER")]
    [StringLength(50)]
    public string? HcNumber { get; set; }

    [Column("REFILLS_REMAINING")]
    public int? RefillsRemaining { get; set; }
       
}