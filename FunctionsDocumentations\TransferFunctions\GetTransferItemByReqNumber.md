# Get Transfer Item By Request Number

## Overview
Retrieves detailed information about a specific transfer request using its request number.

## Endpoint
```http
GET /transfers/{reqNumber}
```

## Authorization
- Function-level authorization
- QID-based access control via claims
- Requires valid submitter QID

## Parameters

### Path Parameters
- `reqNumber` (string, required): Transfer Request Number

## Response

### Success Response (200 OK)
Returns detailed transfer request information:
```json
{
  "reqNumber": "string",
  "qId": "long",
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string",
  "transferReason": "string",
  "transferReasonDescriptionEn": "string",
  "transferReasonDescriptionAr": "string",
  "status": "string",
  "statusDescriptionEn": "string",
  "statusDescriptionAr": "string",
  "submittedAt": "string (UTC)"
}
```

### No Content Response (204)
Returned when no record is found for the given request number.

### Error Responses
- 400 Bad Request: Invalid request number format
- 401 Unauthorized: Invalid QID or unauthorized access

## Implementation Details
- Uses Entity Framework Core with AsNoTracking for optimal performance
- Implements eager loading for status and transfer reason information
- Supports multilingual content (English/Arabic)
- Includes detailed status and reason descriptions
- Validates request number format

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control based on submitter QID
- Claims-based authorization
- Request number validation

## Performance Optimization
- Uses AsNoTracking for read-only operations
- Implements efficient single record retrieval
- Optimizes database queries using proper includes
- Efficient status and reason mapping
- Early validation of request number
