﻿namespace TestEServiceFunctions;

[Collection("CommentAndAttachmentsFunctions")]
public class TestCommentAndAttachmentsFunctions(
    IRestLibrary restLibrary,
    ITestOutputHelper testOutputHelper)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task CreateAttachmentV2()
    {
        // Act
        var response = await CreateAttachmentForV2();

        // Assert
        NotNull(response);
        True(response.Id != Guid.Empty);

        // Cleanup
        await WaitAsync();
        await DeleteByIdAsync(response.Id.ToString());
    }

    private async Task<CreateAttachmentResponse> CreateAttachmentForV2()
    {
        using var outputStream = new MemoryStream();
        var sampleImageFile = GenerateSampleImage(1800, 1800);
        await sampleImageFile.SaveAsPngAsync(outputStream);
        
        var request = GetRestRequest("attachments-v2");
        request.AddFile("file", outputStream.ToArray(), $"test{new Faker().Name.FirstName()}.png", "image/png");
        request.AddOrUpdateHeader(RequestOrigin, "Test-CreateAttachmentV2");

        var response = await _client.PostAsync<CreateAttachmentResponse>(request);
        testOutputHelper.LogToConsole(response);
        return response!;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteAttachmentById()
    {
        // Arrange
        var createdAttachment = await CreateAttachmentForV2();
        await WaitAsync();

        // Act
        var response = await DeleteByIdAsync(createdAttachment.Id.ToString());

        // Assert
        NotNull(response);
        True(response.StatusCode == OK);
    }

    [ExcludeFromCodeCoverage]
    private async Task<RestResponse> DeleteByIdAsync(string id)
    {
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", id);

        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);

        return response;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetAttachmentByIdWithout_X_SendDataHeader()
    {
        // Arrange
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", "707b7d6f-dd25-40b2-a2a1-e06c7948c219");
        request.AddHeader("X-SendData", "");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);

        // Act
        var response = await _client.GetAsync<GetAttachmentItemResponse>(request);
        response.ThrowIfNull();
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.AttachmentName == "test.png");
        True(response.AttachmentType == "png");
        True(response.AttachmentData is null);
    }
    
    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAttachmentByIdWith_X_SendDataHeader_As_Boolean()
    {
        // Arrange
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", "707b7d6f-dd25-40b2-a2a1-e06c7948c219");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
        request.AddHeader("X-SendData", true);

        // Act
        var response = await _client.GetAsync<GetAttachmentItemResponse>(request);
        response.ThrowIfNull();
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.AttachmentName == "test.png");
        True(response.AttachmentType == "png");
        True(response.AttachmentData is not null);
    }
    
    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAttachmentByIdWith_X_SendDataHeader_As_String()
    {
        // Arrange
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", "707b7d6f-dd25-40b2-a2a1-e06c7948c219");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
        request.AddHeader("X-SendData", true);

        // Act
        var response = await _client.GetAsync<GetAttachmentItemResponse>(request);
        response.ThrowIfNull();
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.AttachmentName == "test.png");
        True(response.AttachmentType == "png");
        True(response.AttachmentData is not null);
    }
    
    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAttachmentByIdWith_X_SendDataHeader_With_False_String_Value()
    {
        // Arrange
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", "707b7d6f-dd25-40b2-a2a1-e06c7948c219");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
        request.AddHeader("X-SendData", "false");

        // Act
        var response = await _client.GetAsync<GetAttachmentItemResponse>(request);
        response.ThrowIfNull();
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.AttachmentName == "test.png");
        True(response.AttachmentType == "png");
        True(response.AttachmentData is null);
    }
    
    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAttachmentByIdWith_X_SendDataHeader_With_False_Boolean_Value()
    {
        // Arrange
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", "707b7d6f-dd25-40b2-a2a1-e06c7948c219");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
        request.AddHeader("X-SendData", "false");

        // Act
        var response = await _client.GetAsync<GetAttachmentItemResponse>(request);
        response.ThrowIfNull();
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.AttachmentName == "test.png");
        True(response.AttachmentType == "png");
        True(response.AttachmentData is null);
    }

    [Fact] 
    [Trait(Category, HappyPath)]
    public async Task TestUpdateAttachmentByIdV2()
    {
        // Arrange
        var request = GetRestRequest("attachments-v2/{id}");

        using var outputStream = new MemoryStream();
        var sampleImageFile = GenerateSampleImage(1800, 1800);
        await sampleImageFile.SaveAsPngAsync(outputStream);

        request.AddUrlSegment("id", "644404af-5319-434b-8aae-539cdee691fd");
        request.AddFile("file", outputStream.ToArray(), $"{new Faker().Name.FirstName()}.png", "image/png");
        request.AddOrUpdateHeader(RequestOrigin, "Test-UpdateAttachmentByIdV2-on-" + UtcNow.ToShortDateString());

        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.StatusCode == OK);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAttachmentByIdWith_X_SendDataHeader_As_True_And_Should_Return_All_Fields()
    {
        // Arrange
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", "644404af-5319-434b-8aae-539cdee691fd");
        request.AddOrUpdateHeader(JwtClaimsQId, QId);
        request.AddHeader("X-SendData", true);

        // Act
        var response = await _client.GetAsync<GetAttachmentItemResponse>(request);
        response.ThrowIfNull();
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.AttachmentName is not null);
        True(response.AttachmentType is not null);
        True(response.AttachmentData is not null);
        True(!response.CreatedAt.IsNullOrWhiteSpace());
        True(!response.UpdatedAt.IsNullOrWhiteSpace());
    }
    
    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAttachmentByIdWith_X_SendDataHeader_As_False_And_Should_Return_All_Fields_Except_AttachmentData()
    {
        // Arrange
        var request = GetRestRequest("attachments/{id}");
        request.AddUrlSegment("id", "644404af-5319-434b-8aae-539cdee691fd");
        request.AddOrUpdateHeader(JwtClaimsQId, QId);
        request.AddHeader("X-SendData", false);

        // Act
        var response = await _client.GetAsync<GetAttachmentItemResponse>(request);
        response.ThrowIfNull();
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.AttachmentName is not null);
        True(response.AttachmentType is not null);
        True(response.AttachmentData is null);
        True(!response.CreatedAt.IsNullOrWhiteSpace());
        True(!response.UpdatedAt.IsNullOrWhiteSpace());
    }
    
    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetCommentByReqNumber()
    {
        try
        {
            // Arrange
            var request = GetRestRequest("comments/{reqNumber}");
            request.AddUrlSegment("reqNumber", "59276DCXM");
            request.AddOrUpdateHeader(JwtClaimsQId, 22222222265);
            await ProcessComment(request);
        }
        catch (Exception ex)
        {
            testOutputHelper.LogToConsole(ex.Message);
            // Arrange
            var request = GetRestRequest("comments/{reqNumber}");
            request.AddUrlSegment("reqNumber", "59276DCXM");
            await ProcessComment(request);
        }

        return;

        async Task ProcessComment(RestRequest request)
        {
            // Act
            var response = await _client.GetAsync<List<GetCommentResponse>>(request);
            response.ThrowIfNull();
            testOutputHelper.LogToConsole(response);

            // Assert
            NotNull(response);
            True(response.Count > 0);

            var comment = response.FirstOrDefault();
            testOutputHelper.LogToConsole(comment);
            
            NotNull(comment);
            True(comment.Action == Rework);
            True(comment.CommentText == "ReWork");
        }
    }
}