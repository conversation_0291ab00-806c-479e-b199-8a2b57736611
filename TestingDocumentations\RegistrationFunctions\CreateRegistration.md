# Create Registration Tests

## Test Cases

### 1. TestCreateRegistration

Tests creation of a new registration request.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCreateRegistration()
{
    // Arrange
    var request = CreateUpdateRegistration("CreateRegistrationKey");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.PostAsync<CreateRegistrationResponse>(request);
    response.ThrowIfNull();

    // Assert
    True(response.ReqNumber!.Length > 0);
}
```

## Request Details

### Endpoint
```
POST registrations
```

### Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: "TestCase-CreateRegistrationKey"

## Request Model

### CreateUpdateRegistrationRequest
```csharp
public class CreateUpdateRegistrationRequest
{
    // Personal Information
    public long QId { get; set; }
    public string FNameEn { get; set; }
    public string MNameEn { get; set; }
    public string LNameEn { get; set; }
    public string FNameAr { get; set; }
    public string MNameAr { get; set; }
    public string LNameAr { get; set; }
    public string Nationality { get; set; }
    public DateTime Dob { get; set; }
    public string Gender { get; set; }
    public string MaritalStatus { get; set; }

    // Professional Information
    public string Education { get; set; }
    public string Occupation { get; set; }

    // Contact Information
    public string HomeTel { get; set; }
    public string OfficeTel { get; set; }
    public string MobileNo { get; set; }

    // Emergency Contact
    public string NextOfKinName { get; set; }
    public string NextOfKinLandLine { get; set; }

    // Sponsor Information
    public string SponsorName { get; set; }
    public string SponsorAddress { get; set; }
    public string VisaType { get; set; }

    // Address Information
    public int BNo { get; set; }
    public int ZNo { get; set; }
    public int SNo { get; set; }
    public int UNo { get; set; }

    // Healthcare Information
    public string CatchmentHC { get; set; }
    public string PrefHC { get; set; }
    public string PrefSMSLang { get; set; }
    public string PrefComMode { get; set; }

    // Attachments
    public Guid AttachId1 { get; set; }
    public Guid AttachId2 { get; set; }
    public Guid AttachId3 { get; set; }
    public Guid AttachId4 { get; set; }
    public Guid AttachId5 { get; set; }
    public Guid AttachId6 { get; set; }

    // Submission Information
    public string EmergencyContactID { get; set; }
    public string SubmittedBy { get; set; }
    public DateTime SubmittedAt { get; set; }
    public long SubmitterQId { get; set; }
    public string SubmitterEmail { get; set; }
    public string SubmitterMobile { get; set; }
    public bool IsDraft { get; set; }
    public DateTime GisAddressUpdatedAt { get; set; }
    public bool IsGisAddressManualyEntered { get; set; }
}
```

## Response Model

### CreateRegistrationResponse
```csharp
public class CreateRegistrationResponse
{
    public string ReqNumber { get; set; }
}
```

## Test Data Generation

### Mock Data Sources
1. Occupations
2. Nationalities
3. Visa Types
4. Languages
5. Health Centers

### Data Generation Rules
- QID: 11-digit random number
- Names: English and Arabic versions
- Dates: Past dates within 50 years
- Contact Numbers: Valid phone formats
- Addresses: Random valid numbers
- GUIDs: New unique identifiers
- Boolean flags: Random true/false
- Email: Fixed test email
- Mobile: Fixed test number

## Validation Rules

### Success Case Validation
1. Response not null
2. Request number generated
3. Request number not empty

## Error Cases

1. **Invalid Data**
   - Missing required fields
   - Invalid field formats
   - Data validation failures

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID
   - Unauthorized access

3. **System Errors**
   - Database errors
   - Service unavailable
   - Attachment processing errors

## Notes

1. **Test Data**
   - Uses Faker for realistic data
   - Consistent test patterns
   - Valid field formats

2. **Request Format**
   - Complex object structure
   - Multiple data categories
   - File attachments

3. **Performance**
   - Data generation overhead
   - Attachment handling
   - Response validation
