﻿using Newtonsoft.Json.Serialization;

namespace EServiceFunctions.Helpers;

public static class HttpResponseHelpers
{
    public static async Task<HttpResponseData> WriteErrorResponseAsync(this HttpRequestData request,
        HttpStatusCode statusCode = BadRequest, string message = "Bad Request!!!")
    {
        var error = new ErrorResponse(statusCode, message);
        var response = request.CreateResponse(statusCode);
        response.Headers.Add(ContentType, ApplicationJson);
        var responseJson = SerializeObject(error);
        await response.WriteStringAsync(responseJson);
        return response;
    }

    public static async Task<HttpResponseData> WriteUnauthorizedResponseAsync(this HttpRequestData request,
        string message = "Unauthorized Access!!!")
    {
        var error = new ErrorResponse(Unauthorized, message);
        var response = request.CreateResponse(Unauthorized);
        response.Headers.Add(ContentType, ApplicationJson);
        var responseJson = SerializeObject(error);
        await response.WriteStringAsync(responseJson);
        return response;
    }

    public static Task<HttpResponseData> WriteNoContentResponseAsync(this HttpRequestData request)
    {
        var response = request.CreateResponse(NoContent);
        response.Headers.Add(ContentType, ApplicationJson);
        return Task.FromResult(response);
    }

    public static async Task<HttpResponseData> WriteOkResponseAsync(this HttpRequestData request, object responseData,
        HttpStatusCode statusCode = OK)
    {
        try
        {
            var response = request.CreateResponse(statusCode);
            var responseJson = SerializeObject(responseData, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            });
            await response.WriteStringAsync(responseJson);
            response.Headers.Add(ContentType, ApplicationJson);
            return response;
        }
        catch (Exception ex)
        {
            return await request.WriteErrorResponseAsync(InternalServerError, ex.Message);
        }
    }
}