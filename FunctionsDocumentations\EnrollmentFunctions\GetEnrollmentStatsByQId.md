# GetEnrollmentStatsByQId

## Overview
Retrieves enrollment statistics for a given submitter's QId, optionally filtered by status.

## Endpoint
- **Route**: `enrollments/submitter-qid/{qId}/stats`
- **Method**: GET
- **Authorization**: Function level with QId validation

## Parameters
- **qId** (path, required): Submitter's QId
- **status** (query, optional): Filter by status category
  - InProcess: Submitted, Rework, Reworked
  - Archived: Approved, Cancelled, CancelledByEServ, Rejected

## Response
- **200 OK**: Returns enrollment count
  ```json
  {
    "Count": "integer"
  }
  ```
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Invalid QId access

## Business Logic
1. Validates submitter QId authorization
2. Queries Enrollment table with Status join
3. Filters by submitter QId and optional status category
4. Returns total count of matching enrollments

## Query Optimization
- Uses AsNoTracking() for read-only data
- Direct count operation without loading entities
- Efficient join with Status table
