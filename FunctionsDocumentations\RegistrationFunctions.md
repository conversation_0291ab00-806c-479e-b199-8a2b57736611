# Registration Functions API Documentation

## Overview
The Registration Functions module manages user registration requests in the eServices platform. It provides comprehensive endpoints for creating, retrieving, updating, and managing registration requests with multilingual support (English/Arabic) and status tracking.

## Core Features
1. **Registration Management**
   - Create new registration requests
   - Update existing registrations
   - View registration details
   - Delete draft registrations
   - Status tracking

2. **Request Status Management**
   - InProcess status tracking
   - Status statistics
   - Multilingual status descriptions
   - Category-based status filtering

3. **User-Specific Operations**
   - QID-based access control
   - Submitter-specific listings
   - Request validation
   - Authorization checks

## API Endpoints

### 1. Get Registration List By QID
- [Detailed Documentation](RegistrationFunctions/GetRegistrationListByQID.md)
- **Endpoint**: `GET /registrations/submitter-qid/{qId}`
- **Description**: Retrieves registration list for a specific submitter
- **Key Features**:
  * Pagination support
  * Status filtering
  * Multilingual responses
  * QID validation

### 2. Get Registration Stats By QID
- [Detailed Documentation](RegistrationFunctions/GetRegistrationStatsByQID.md)
- **Endpoint**: `GET /registrations/submitter-qid/{qId}/stats`
- **Description**: Retrieves registration statistics for a submitter
- **Key Features**:
  * Status-based counting
  * Category filtering
  * Quick statistics
  * Efficient counting

### 3. Check InProcess Registration By QID
- [Detailed Documentation](RegistrationFunctions/CheckInProcessRegistrationByQID.md)
- **Endpoint**: `GET /registrations/{qId}/inprocess-validation`
- **Description**: Validates if user has any in-process registrations
- **Key Features**:
  * Quick validation
  * Status checking
  * Boolean response
  * Efficient query

### 4. Get Registration Item By Request Number
- [Detailed Documentation](RegistrationFunctions/GetRegistrationItemByReqNumber.md)
- **Endpoint**: `GET /registrations/{reqNumber}`
- **Description**: Retrieves detailed registration information
- **Key Features**:
  * Complete details
  * Status information
  * Multilingual data
  * Authorization check

### 5. Create Registration
- [Detailed Documentation](RegistrationFunctions/CreateRegistration.md)
- **Endpoint**: `POST /registrations`
- **Description**: Creates a new registration request
- **Key Features**:
  * Draft support
  * Request tracking
  * Origin tracking
  * Status management

### 6. Update Registration By Request Number
- [Detailed Documentation](RegistrationFunctions/UpdateRegistrationByReqNumber.md)
- **Endpoint**: `PUT /registrations/{reqNumber}`
- **Description**: Updates an existing registration
- **Key Features**:
  * History tracking
  * Status management
  * Origin tracking
  * Field preservation

### 7. Delete Registration By Request Number
- [Detailed Documentation](RegistrationFunctions/DeleteRegistrationByReqNumber.md)
- **Endpoint**: `DELETE /registrations/{reqNumber}`
- **Description**: Deletes a draft registration
- **Key Features**:
  * Draft-only deletion
  * Status validation
  * Submitter verification
  * Safe deletion

## Technical Implementation

### Database Architecture
1. **Context Management**
   - RegistrationContext
   - Entity Framework Core
   - Connection pooling
   - Transaction handling

2. **Data Models**
   - Registration entity
   - Status entity
   - Navigation properties
   - Audit fields

### Performance Optimization
1. **Query Efficiency**
   - AsNoTracking queries
   - Efficient projections
   - Pagination support
   - Status filtering

2. **Resource Management**
   - Connection pooling
   - Memory optimization
   - Response caching
   - Efficient disposal

### Security Implementation
1. **Authorization**
   - Function-level security
   - QID validation
   - Submitter verification
   - Origin validation

2. **Data Protection**
   - Input validation
   - Status control
   - Error masking
   - Secure logging

### Error Management
1. **Exception Handling**
   - Standard responses
   - Detailed logging
   - Error categorization
   - Recovery strategies

2. **Validation**
   - Input validation
   - Business rules
   - Status validation
   - Data consistency

## Status Workflow
1. **Draft Mode**
   - Status: Saved
   - Editable
   - Deletable
   - No processing

2. **Submitted Mode**
   - Status: Ready
   - Processing begins
   - Updates restricted
   - Deletion restricted

3. **InProcess Category**
   - Submitted
   - Rework
   - Reworked
   - ConditionallyApproved
   - ResubmitOriginalsRequested

4. **Archived Category**
   - Approved
   - Cancelled
   - CancelledByEServ

## Dependencies
- Entity Framework Core
- Azure Functions
- System.Text.Json
- Microsoft.Azure.WebJobs
- Custom Extensions:
  * Request/Response Handlers
  * Authorization Extensions
  * Validation Extensions