# Update MHDS Tests

## Test Cases

### 1. TestUpdateMhdsByReqNumber

Tests successful update of MHDS request.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateMhdsByReqNumber()
{
    // Arrange
    var request = GetRestRequest("mhds/{reqNumber}");
    request.AddUrlSegment("reqNumber", "GCGHP71DQGF");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateMHDSByReqNumber");

    var updateMhdsRequest = new Faker<UpdateMHDSRequest>()
        .RuleFor(x => x.QId, RandomNumber(11))
        .RuleFor(x => x.SubmitterQId, 71215041525)
        .RuleFor(x => x.IsGisAddressManualyEntered, true)
        .RuleFor(x => x.UNo, f => f.Random.Int(10000, 20000))
        .RuleFor(x => x.BNo, f => f.Random.Int(1000, 2000))
        .RuleFor(x => x.SNo, f => f.Random.Int(1, 9))
        .RuleFor(x => x.ZNo, f => f.Random.Int(10000, 99999))
        .Generate();

    request.AddJsonBody(updateMhdsRequest);
    request.AddOrUpdateHeader(JwtClaimsQId, 71215041525);

    // Act
    var response = await _client.PutAsync(request);

    // Assert
    True(response is not null);
    True(response.IsSuccessStatusCode);
    True(response.StatusCode == OK);
}
```

### 2. TestUpdateMhdsByReqNumberWithInvalidReqNumber

Tests update with invalid request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestUpdateMhdsByReqNumberWithInvalidReqNumber()
{
    // Arrange
    var request = GetRestRequest("mhds/{reqNumber}");
    request.AddUrlSegment("reqNumber", "InvalidReqNumber");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateMHDSByReqNumber");
    request.AddOrUpdateHeader(JwtClaimsQId, 71215041525);
    request.Method = Method.Put;
    
    var updateMhdsRequest = new Faker<UpdateMHDSRequest>()
        .RuleFor(x => x.QId, RandomNumber(11))
        .RuleFor(x => x.SubmitterQId, 71215041525)
        .RuleFor(x => x.IsGisAddressManualyEntered, true)
        .RuleFor(x => x.UNo, f => f.Random.Int(10000, 20000))
        .RuleFor(x => x.BNo, f => f.Random.Int(1000, 2000))
        .RuleFor(x => x.SNo, f => f.Random.Int(1, 9))
        .RuleFor(x => x.ZNo, f => f.Random.Int(10000, 99999))
        .Generate();

    request.AddJsonBody(updateMhdsRequest);

    // Act
    var response = await _client.ExecuteAsync(request);

    // Assert
    Equal(BadRequest, response.StatusCode);
}
```

## Request Details

### Endpoint
```
PUT mhds/{reqNumber}
```

### Headers
- `JwtClaimsQId`: "71215041525"
- `RequestOrigin`: "UnitTest-UpdateMHDSByReqNumber"

### URL Parameters
- `reqNumber`: MHDS request number

## Request Model

### UpdateMHDSRequest
```csharp
public class UpdateMHDSRequest
{
    public long QId { get; set; }
    public long SubmitterQId { get; set; }
    public bool IsGisAddressManualyEntered { get; set; }
    public int UNo { get; set; }
    public int BNo { get; set; }
    public int SNo { get; set; }
    public int ZNo { get; set; }
}
```

## Test Data

### Success Case
- Request Number: "GCGHP71DQGF"
- Generated test data:
  * QId: 11-digit random number
  * SubmitterQId: 71215041525
  * Address: Random valid numbers
  * GIS: Manual entry enabled

### Error Case
- Request Number: "InvalidReqNumber"
- Same generated data structure
- Expected: BadRequest response

## Validation Rules

### Request Validation
1. Valid request number
2. Valid QID formats
3. Valid address format
4. Matching submitter QID

### Response Validation
1. Success case:
   - Response not null
   - Success status code
   - OK status code

2. Error case:
   - BadRequest status code

## Error Cases

1. **Invalid Request Number**
   - Non-existent request
   - Malformed number
   - Invalid format

2. **Invalid Data**
   - Invalid QID
   - Invalid address
   - Missing fields

3. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - QID mismatch

## Notes

1. **Test Data Generation**
   - Uses Faker for realistic data
   - Consistent formats
   - Valid ranges

2. **Address Handling**
   - GIS integration
   - Manual entry option
   - Format validation

3. **Performance**
   - Single update operation
   - Validation checks
   - Error handling
