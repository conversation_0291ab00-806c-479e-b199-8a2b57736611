using EServiceFunctions.Models.Registration;
using EServiceFunctions.RequestResponseModels.Registration;
using Microsoft.Azure.Functions.Worker.Http;
using static System.Guid;
using static MockDataLibrary.GenerateMoqData;

namespace UnitTestEServiceFunctions;

public class TestRegistrationDbContext : IDbContextFactory<RegistrationContext>
{
    public RegistrationContext CreateDbContext()
    {
        // Use a unique database name for each test run to avoid conflicts
        var options = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: $"RegistrationDb_{Guid.NewGuid()}")
            .Options;
        var context = new RegistrationContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        // Get the registration data
        var registrations = MockDataForRegistrationFunctions.GetRegistrations();
        
        // Find the specific registration with REQ0001
        var req0001Registration = registrations.FirstOrDefault(r => r.ReqNumber == "REQ0001");
        
        // Ensure no duplicate IDs by grouping and selecting the first of each group
        var uniqueRegistrations = registrations
            .GroupBy(r => r.Id)
            .Select(g => g.First())
            .ToList();
        
        // If REQ0001 was lost in deduplication, add it back
        if (req0001Registration != null && uniqueRegistrations.All(r => r.ReqNumber != "REQ0001"))
        {
            // Ensure the ID is unique
            var maxId = uniqueRegistrations.Max(r => r.Id);
            req0001Registration.Id = maxId + 1;
            uniqueRegistrations.Add(req0001Registration);
        }
            
        // Get status data and ensure no duplicates
        var statuses = MockDataForRegistrationFunctions.GetRegistrationStatus().ToList();
        
        context.Registration.AddRange(uniqueRegistrations);
        context.Status.AddRange(statuses);
        context.SaveChanges();
        return context;
    }
}

public static class MockDataForRegistrationFunctions
{
    public static List<Registration> GetRegistrations()
    {
        var occupationCodeList = MockOccupations().Select(o => o.Code).ToList();
        var nationalityCodeList = MockNationality().Select(o => o.NatCode).ToList();
        var visaTypeCodeList = MockVisaTypes().Select(o => o.VisaTypeCode).ToList();
        var languageCodeList = MockLanguages().Select(o => o.NatCode).ToList();
        var healthCenterCodeList = MockHealthCenters().Select(o => o.Name).ToList();
        var registrationListFaker = new Faker<Registration>()
            .RuleFor(r => r.Id, f => f.Random.Int(10, 10000000))
            .RuleFor(r => r.QId, RandomNumber(11))
            .RuleFor(r => r.FNameEn, FirstNameEnglish)
            .RuleFor(r => r.MNameEn, MiddleNameEnglish)
            .RuleFor(r => r.LNameEn, LastNameEnglish)
            .RuleFor(r => r.FNameAr, FirstNameArabic)
            .RuleFor(r => r.MNameAr, MiddleNameArabic)
            .RuleFor(r => r.LNameAr, LastNameArabic)
            .RuleFor(r => r.Nationality, f => f.PickRandom(nationalityCodeList))
            .RuleFor(r => r.Dob, f => f.Date.Past(50))
            .RuleFor(r => r.Gender, f => f.PickRandom("1", "2"))
            .RuleFor(r => r.MaritalStatus, f => f.PickRandom("1", "0"))
            .RuleFor(r => r.Education, f => f.Internet.UserName())
            .RuleFor(r => r.Occupation, f => f.PickRandom(occupationCodeList))
            .RuleFor(r => r.HomeTel, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.OfficeTel, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.MobileNo, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.NextOfKinName, f => f.Name.FullName())
            .RuleFor(r => r.NextOfKinLandLine, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.SponsorName, f => f.Name.FullName())
            .RuleFor(r => r.SponsorAddress, f => f.Address.FullAddress())
            .RuleFor(r => r.VisaType, f => f.PickRandom(visaTypeCodeList))
            .RuleFor(r => r.BNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.ZNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.SNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.UNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.CatchmentHC, f => f.PickRandom(healthCenterCodeList))
            .RuleFor(r => r.PrefHC, f => f.PickRandom(healthCenterCodeList))
            .RuleFor(r => r.PrefSMSLang, f => f.PickRandom(languageCodeList))
            .RuleFor(r => r.PrefComMode, f => f.PickRandom("1", "2"))
            .RuleFor(r => r.AttachId1, NewGuid())
            .RuleFor(r => r.AttachId2, NewGuid())
            .RuleFor(r => r.AttachId3, NewGuid())
            .RuleFor(r => r.AttachId4, NewGuid())
            .RuleFor(r => r.AttachId5, NewGuid())
            .RuleFor(r => r.AttachId6, NewGuid())
            .RuleFor(r => r.EmergencyContactID, f => f.Person.Phone)
            .RuleFor(r => r.SubmittedBy, f => f.Name.FullName())
            .RuleFor(r => r.SubmittedAt, f => f.Date.Past(50))
            .RuleFor(r => r.SubmitterQId, GetMoqUser().UserQId)
            .RuleFor(r => r.SubmitterEmail, "<EMAIL>")
            .RuleFor(r => r.SubmitterMobile, "+97470559257")
            .RuleFor(r => r.GisAddressUpdatedAt, f => f.Date.Past(50))
            .RuleFor(r => r.IsGisAddressManualyEntered, f => f.Random.Bool())
            .RuleFor(r => r.CreateSource, f => f.PickRandom("Web", "Mobile"))
            .RuleFor(r => r.UpdateSource, f => f.PickRandom("Web", "Mobile"))
            .RuleFor(r => r.Status, InProcess)
            .Generate(100);

        var registrationList = new List<Registration>
        {
            new()
            {
                Id = 1,
                QId = LongQId,
                ReqNumber = "REQ0001",
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Nationality = NationalityEn,
                Dob = new Faker().Date.Past().AddYears(-18),
                Gender = GenderEn,
                MaritalStatus = "Married",
                Education = "Msc",
                Occupation = "Software Engineer",
                HomeTel = new Faker().Phone.PhoneNumber(),
                OfficeTel = new Faker().Phone.PhoneNumber(),
                MobileNo = new Faker().Phone.PhoneNumber(),
                NextOfKinName = new Faker().Name.FullName(),
                NextOfKinLandLine = new Faker().Phone.PhoneNumber(),
                SponsorName = new Faker().Name.FullName(),
                SponsorAddress = new Faker().Address.FullAddress(),
                VisaType = "Residence",
                BNo = new Faker().Random.Number(1, 100),
                ZNo = new Faker().Random.Number(1, 100),
                SNo = new Faker().Random.Number(1, 100),
                UNo = new Faker().Random.Number(1, 100),
                CatchmentHC = HcNumber1,
                PrefHC = "Omar Bin Khattab",
                PrefSMSLang = "English",
                PrefComMode = "Phone",
                AttachId1 = NewGuid(),
                AttachId2 = NewGuid(),
                AttachId3 = NewGuid(),
                AttachId4 = NewGuid(),
                AttachId5 = NewGuid(),
                AttachId6 = NewGuid(),
                EmergencyContactID = new Faker().Random.Number(1, 100).ToString(),
                SubmittedBy = FirstNameEn + " " + LastNameEn,
                SubmittedAt = new Faker().Date.Past().AddYears(-18),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new Faker().Date.Past(),
                UpdatedAt = GetCurrentTime(),
                Status = InProcess,
                StatusInternal = InProcess,
                SN = "SN0001",
                GisAddressUpdatedAt = GetCurrentTime(),
                IsGisAddressManualyEntered = true,
                CreateSource = "Web",
                UpdateSource = "Web",
                StatusNavigation = new Status
                {
                    Code = InProcess,
                    Category = InProcess,
                    DescriptionEn = "Request is in process",
                    DescriptionAr = "الطلب قيد المعالجة"
                }
            },
            new()
            {
                Id = 2,
                QId = LongQId,
                ReqNumber = "REQ0002",
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Nationality = NationalityEn,
                Dob = new Faker().Date.Past().AddYears(-18),
                Gender = GenderEn,
                MaritalStatus = "Married",
                Education = "Msc",
                Occupation = "Software Engineer",
                HomeTel = new Faker().Phone.PhoneNumber(),
                OfficeTel = new Faker().Phone.PhoneNumber(),
                MobileNo = new Faker().Phone.PhoneNumber(),
                NextOfKinName = new Faker().Name.FullName(),
                NextOfKinLandLine = new Faker().Phone.PhoneNumber(),
                SponsorName = new Faker().Name.FullName(),
                SponsorAddress = new Faker().Address.FullAddress(),
                VisaType = "Residence",
                BNo = new Faker().Random.Number(1, 100),
                ZNo = new Faker().Random.Number(1, 100),
                SNo = new Faker().Random.Number(1, 100),
                UNo = new Faker().Random.Number(1, 100),
                CatchmentHC = HcNumber1,
                PrefHC = "Omar Bin Khattab",
                PrefSMSLang = "English",
                PrefComMode = "Phone",
                AttachId1 = NewGuid(),
                AttachId2 = NewGuid(),
                AttachId3 = NewGuid(),
                AttachId4 = NewGuid(),
                AttachId5 = NewGuid(),
                AttachId6 = NewGuid(),
                EmergencyContactID = new Faker().Random.Number(1, 100).ToString(),
                SubmittedBy = FirstNameEn + " " + LastNameEn,
                SubmittedAt = new Faker().Date.Past().AddYears(-18),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new Faker().Date.Past(),
                UpdatedAt = GetCurrentTime(),
                Status = InProcess,
                StatusInternal = InProcess,
                SN = "SN0001",
                GisAddressUpdatedAt = GetCurrentTime(),
                IsGisAddressManualyEntered = true,
                CreateSource = "Web",
                UpdateSource = "Web",
                StatusNavigation = new Status
                {
                    Code = "Saved",
                    Category = "Saved"
                }
            }
        };

        registrationList.AddRange(registrationListFaker);
        return registrationList;
    }

    public static IEnumerable<Status> GetRegistrationStatus()
    {
        var status = new List<Status>
        {
            new()
            {
                Code = "New",
                DescriptionEn = "New",
                DescriptionAr = "جديد",
                Category = "New"
            },
            new()
            {
                Code = Approved,
                DescriptionEn = "Request Completed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = Cancelled,
                DescriptionEn = "Request Cancelled",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = CancelledByEServ,
                DescriptionEn = "Cancelled by EServices",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = "CancelledByRegistration",
                DescriptionEn = "Cancelled by Registration",
                DescriptionAr = "ألغيت بالتسجيل",
                Category = Archived
            },
            new()
            {
                Code = CancelledFrom107,
                DescriptionEn = "Cancelled from 107",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = ConditionallyApproved,
                DescriptionEn = "Request Tentatively Approved",
                DescriptionAr = "تمت الموافقة المبدئية للطلب",
                Category = InProcess
            },
            new()
            {
                Code = "Confirmed",
                DescriptionEn = "Confirmed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = InProgress,
                DescriptionEn = "Request In Progress",
                DescriptionAr = "الطلب قيد الاجراء",
                Category = InProcess
            },
            new()
            {
                Code = "PaymentReceived",
                DescriptionEn = "Payment Received",
                DescriptionAr = "تمت عملية الدفع",
                Category = InProcess
            },
            new()
            {
                Code = PendingOrder,
                DescriptionEn = "Request Pending",
                DescriptionAr = "الطلب قيد الإنتظار",
                Category = InProcess
            },
            new()
            {
                Code = "ProceedToRegistration",
                DescriptionEn = "Proceed to Registration",
                DescriptionAr = "الشروع في التسجيل",
                Category = InProcess
            }
        };
        return status;
    }
}

public class UnitTestRegistrationFunctions(ITestOutputHelper output)
{
    private readonly ILogger<RegistrationFunctions> _logger = Mock.Of<ILogger<RegistrationFunctions>>();

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetRegistrationListByQId_Should_Return_OkObjectResult_For_Valid_QId()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();

        string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, 1);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.Unauthorized, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_NoContentResult_For_Invalid_Status()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = $"?status=InProcess1&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_NoContentResult_For_Invalid_Skip()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = $"?status=InProcess&skip=1000&take={Take}";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_OkObjectResult_For_Invalid_Take()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=1000";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_OkObjectResult_For_Empty_Status()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = $"?status=&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_OkObjectResult_For_Empty_Skip()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = "?status=&skip=&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_OkObjectResult_For_Empty_Take()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = $"?status=&skip={Skip}&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_OkObjectResult_For_Empty_QueryParameters()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string queryParams = "?status=&skip=&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Return_OkObjectResult_For_No_QueryParameters()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationStatsByQId_Should_Return_OkObjectResult_And_One_Count_For_Valid_QId()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Assert.NotNull(response);
        Assert.IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationStatsByQId_Should_Return_Zero_Count_For_Invalid_QId()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationStatsByQId(mockHttpRequestData, 1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Assert.NotNull(response);
        Assert.IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessRegistrationByQId_Should_Return_True_For_Valid_QId()
    {
        //Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        //Act
        var response = await registrationFunctions.CheckInProcessRegistrationByQId(mockHttpRequestData, LongQId);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessRegistrationResponse>(result);
        True(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessRegistrationByQId_Should_Return_False_For_InvalidQId()
    {
        //Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        //Act
        var response = await registrationFunctions.CheckInProcessRegistrationByQId(mockHttpRequestData, 123);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessRegistrationResponse>(result);
        False(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);
        const string reqNumber = "REQ0001";

        // Act
        var response = await registrationFunctions.GetRegistrationItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        Assert.NotNull(response);
        Assert.IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetRegistrationItemResponse>(result);
        Assert.Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetRegistrationItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber_And_Valid_Status()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);
        const string reqNumber = "REQ0001";
        mockHttpRequestData.Query.Add(InProcess, InProcess);

        // Act
        var response = await registrationFunctions.GetRegistrationItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetRegistrationItemResponse>(result);
        NotNull(okResponseObject); // Ensure the response object is not null
        Assert.Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationItemByReqNumber_Should_Return_NoContentResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ123456789";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Return_CreatedResult_For_Valid_Request()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = NationalityEn,
            Dob = new Faker().Date.Past().AddYears(-18),
            Gender = GenderEn,
            MaritalStatus = "Married",
            Education = "Msc",
            Occupation = "Software Engineer",
            HomeTel = new Faker().Phone.PhoneNumber(),
            OfficeTel = new Faker().Phone.PhoneNumber(),
            MobileNo = new Faker().Phone.PhoneNumber(),
            NextOfKinName = new Faker().Name.FullName(),
            NextOfKinLandLine = new Faker().Phone.PhoneNumber(),
            SponsorName = new Faker().Name.FullName(),
            SponsorAddress = new Faker().Address.FullAddress(),
            VisaType = "Residence",
            BNo = new Faker().Random.Number(1, 100),
            ZNo = new Faker().Random.Number(1, 100),
            SNo = new Faker().Random.Number(1, 100),
            UNo = new Faker().Random.Number(1, 100),
            CatchmentHC = HcNumber1,
            PrefHC = "Omar Bin Khattab",
            PrefSMSLang = "English",
            PrefComMode = "Phone",
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            EmergencyContactID = new Faker().Random.Number(1, 100).ToString(),
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            GisAddressUpdatedAt = GetCurrentTime(),
            IsGisAddressManualyEntered = true,
            IsDraft = false
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Return_Unauthorized_For_Invalid_Request()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var request = new CreateUpdateRegistrationRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Return_BadRequestResult_For_Missing_Header()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = NationalityEn,
            Dob = new Faker().Date.Past().AddYears(-18),
            Gender = GenderEn,
            MaritalStatus = "Married",
            Education = "Msc",
            Occupation = "Software Engineer",
            HomeTel = new Faker().Phone.PhoneNumber(),
            OfficeTel = new Faker().Phone.PhoneNumber(),
            MobileNo = new Faker().Phone.PhoneNumber(),
            NextOfKinName = new Faker().Name.FullName(),
            NextOfKinLandLine = new Faker().Phone.PhoneNumber(),
            SponsorName = new Faker().Name.FullName(),
            SponsorAddress = new Faker().Address.FullAddress(),
            VisaType = "Residence",
            BNo = new Faker().Random.Number(1, 100),
            ZNo = new Faker().Random.Number(1, 100),
            SNo = new Faker().Random.Number(1, 100),
            UNo = new Faker().Random.Number(1, 100),
            CatchmentHC = HcNumber1,
            PrefHC = "Omar Bin Khattab",
            PrefSMSLang = "English",
            PrefComMode = "Phone",
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            EmergencyContactID = new Faker().Random.Number(1, 100).ToString(),
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            GisAddressUpdatedAt = GetCurrentTime(),
            IsGisAddressManualyEntered = true,
            IsDraft = false
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Return_OK_For_Valid_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0001";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = NationalityEn,
            Dob = new Faker().Date.Past().AddYears(-18),
            Gender = GenderEn,
            MaritalStatus = "Married",
            Education = "Msc",
            Occupation = "Software Engineer",
            HomeTel = new Faker().Phone.PhoneNumber(),
            OfficeTel = new Faker().Phone.PhoneNumber(),
            MobileNo = new Faker().Phone.PhoneNumber(),
            NextOfKinName = new Faker().Name.FullName(),
            NextOfKinLandLine = new Faker().Phone.PhoneNumber(),
            SponsorName = new Faker().Name.FullName(),
            SponsorAddress = new Faker().Address.FullAddress(),
            VisaType = "Residence",
            BNo = new Faker().Random.Number(1, 100),
            ZNo = new Faker().Random.Number(1, 100),
            SNo = new Faker().Random.Number(1, 100),
            UNo = new Faker().Random.Number(1, 100),
            CatchmentHC = HcNumber1,
            PrefHC = "Omar Bin Khattab",
            PrefSMSLang = "English",
            PrefComMode = "Phone",
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            EmergencyContactID = new Faker().Random.Number(1, 100).ToString(),
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            GisAddressUpdatedAt = GetCurrentTime(),
            IsGisAddressManualyEntered = true,
            IsDraft = false
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Return_Unauthorized_For_Empty_RequestObject()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0001";
        var request = new CreateUpdateRegistrationRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Return_BadRequestResult_For_Missing_Headers()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0002";
        var request = new CreateUpdateRegistrationRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0002";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Return_BadRequestObjectResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    // Additional test methods for RegistrationFunctions to achieve 100% code coverage

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationStatsByQId_Should_Return_BadRequest_For_Null_Request()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        // Create HttpRequestData without a query string to simulate null request
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: "");
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationStatsByQId_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: "?status=InProcess");
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationStatsByQId(mockHttpRequestData, 1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var unauthorizedResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, unauthorizedResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationStatsByQId_Should_Return_OkObjectResult_For_Specific_Status()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        // Use CreateAuthorizedHttpRequestData to ensure the request is properly authorized
        var mockHttpRequestData = CreateAuthorizedHttpRequestData(query: "?status=InProcess");
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);

        var responseContent = DeserializeObject<GetRegistrationStatsResponse>(okResponse.ReadBodyToEnd());
        NotNull(responseContent);
        True(responseContent.Count > 0);
    }

    private static HttpRequestData CreateAuthorizedHttpRequestData(string? payload = null, string method = "GET", string? query = null)
    {
        var req = MockHelpers.CreateHttpRequestData(payload: payload, method: method, query: query);
        // Ensure the request is authorized by setting the QId to match the submitter QId
        req.Headers.Remove(JwtClaimsQId);
        req.Headers.Add(JwtClaimsQId, LongQId.ToString());
        // Set IsAuthValReq to false to bypass authorization check
        req.Headers.Remove(IsAuthValReq);
        req.Headers.Add(IsAuthValReq, "false");
        return req;
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessRegistrationByQId_Should_Return_BadRequest_For_Invalid_QId_Format()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CheckInProcessRegistrationByQId(mockHttpRequestData, 0);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationItemByReqNumber_Should_Return_BadRequest_For_Empty_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);
        string reqNumber = string.Empty;

        // Act
        var response = await registrationFunctions.GetRegistrationItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationItemByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        // Set a different QId in claims than the one in the registration
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, "12345678902");
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);
        const string reqNumber = "REQ0001";

        // Act
        var response = await registrationFunctions.GetRegistrationItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var unauthorizedResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, unauthorizedResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Return_BadRequest_For_Null_Request()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: "", method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Return_Unauthorized_For_Empty_Request()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var request = new CreateUpdateRegistrationRequest
        {
            // Missing required fields
            SubmitterQId = null,
            SubmitterEmail = null,
            SubmitterMobile = null
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var unauthorizedResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, unauthorizedResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Handle_Draft_Registration()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            IsDraft = true // Testing draft mode
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var createdResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, createdResponse.StatusCode);

        // Verify the registration was saved with "Saved" status
        var createdRegistration = DeserializeObject<Registration>(createdResponse.ReadBodyToEnd());
        Equal("Saved", createdRegistration.Status);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Return_NoContent_For_NonExistent_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ9999";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var noContentResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, noContentResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Return_BadRequest_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = ""; // Invalid request number
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Handle_Draft_Registration()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0001";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            IsDraft = true // Testing draft mode
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);

        // Verify the registration was updated with "Saved" status
        var updatedRegistration = DeserializeObject<Registration>(okResponse.ReadBodyToEnd());
        Equal("InProcess", updatedRegistration.Status);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Return_BadRequest_For_Empty_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        string reqNumber = string.Empty;
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Return_NoContent_For_NonExistent_ReqNumber()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ9999"; // Non-existent request number
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData; // Rename variable for clarity
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode); // <-- Change expected status code
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0002";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, "12345678902");
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var unauthorizedResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, unauthorizedResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Handle_DbUpdateException()
    {
        // Arrange
        // Create a real in-memory database with a unique name
        var options = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: $"ThrowingDb_{NewGuid()}")
            .Options;

        // First, create and populate a real context
        await using (var context = new RegistrationContext(options))
        {
            // Add a test registration that will be found and deleted
            var registration = new Registration
            {
                Id = 100,
                ReqNumber = "REQ0003",
                Status = "Saved", // Status that allows deletion
                SubmitterQId = LongQId // Match the QId in the request
            };

            context.Registration.Add(registration);
            await context.SaveChangesAsync();
        }

        // Now create a mock context that will throw on SaveChanges
        var mockContext = new Mock<RegistrationContext>(options);

        // Use a real context to get the DbSet, but we'll mock SaveChangesAsync
        await using (var realContext = new RegistrationContext(options))
        {
            // Setup the mock to use the real DbSet (which has our test data)
            mockContext.Setup(c => c.Registration).Returns(realContext.Registration);

            // Setup SaveChangesAsync to throw the exception
            mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
                .ThrowsAsync(new DbUpdateException("Test exception", new Exception("Inner exception")));
        }

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        const string reqNumber = "REQ0003";
        var mockHttpRequestData = CreateHttpRequestData(method: Delete);
        // Add required headers
        mockHttpRequestData.Headers.Add("X-User-QId", LongQId.ToString());
        mockHttpRequestData.Headers.Add(RequestOrigin, "Test");

        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, errorResponse.StatusCode);
    }

// Helper method from your file (ensure it exists or adapt)
    private static HttpRequestData CreateHttpRequestData(string method = "GET", string payload = "", string query = "")
    {
        return MockHelpers.CreateHttpRequestData(payload: payload, method: method, query: query);
    }
}

public class ExtendedRegistrationTests(ITestOutputHelper output)
{
    private readonly ILogger<RegistrationFunctions> _logger = Mock.Of<ILogger<RegistrationFunctions>>();

    // Import static methods
    private static HttpRequestData CreateHttpRequestData(string method = "GET", string payload = "", string query = "")
    {
        return MockHelpers.CreateHttpRequestData(payload: payload, method: method, query: query);
    }

    private static Mock<DbSet<T>> CreateMockDbSet<T>(List<T> data) where T : class
    {
        var queryable = data.AsQueryable();
        var mockSet = new Mock<DbSet<T>>();
        mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(queryable.Provider);
        mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryable.Expression);
        mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
        using var enumerator = queryable.GetEnumerator();
        mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(enumerator);
        return mockSet;
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Handle_DbUpdateException_Extended()
    {
        // Arrange
        // Create a real in-memory database with a unique name
        var options = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: $"ThrowingDbDelete_{NewGuid()}")
            .Options;

        // First, create and populate a real context
        await using (var context = new RegistrationContext(options))
        {
            // Get a test registration from mock data
            var existingRegistration = MockDataForRegistrationFunctions.GetRegistrations().First(r => r.ReqNumber == "REQ0001");
            existingRegistration.Status = "Saved"; // Ensure it's in a deletable state

            context.Registration.Add(existingRegistration);
            await context.SaveChangesAsync();
        }

        // Now create a mock context that will throw on SaveChanges
        var mockContext = new Mock<RegistrationContext>(options);

        // Use a real context to get the DbSet, but we'll mock SaveChangesAsync
        await using (var realContext = new RegistrationContext(options))
        {
            // Setup the mock to use the real DbSet (which has our test data)
            mockContext.Setup(c => c.Registration).Returns(realContext.Registration);

            // Setup SaveChangesAsync to throw the exception
            mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
                .ThrowsAsync(new DbUpdateException("Test delete exception", new Exception("Inner delete exception")));
        }

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        const string reqNumber = "REQ0001";
        var mockHttpRequestData = CreateHttpRequestData(method: "DELETE");
        // Add required headers
        mockHttpRequestData.Headers.Add("X-User-QId", LongQId.ToString());
        mockHttpRequestData.Headers.Add(RequestOrigin, "Test");

        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, errorResponse.StatusCode);
    }

// --- Added Tests for 100% Coverage ---

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Return_BadRequest_For_Missing_RequestOrigin_Header()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        // Create request without X-RequestOrigin header
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Handle_DbUpdateException()
    {
        // Arrange
        // Create a mock context that will throw an exception when SaveChanges is called
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbCreate")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);
        mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Test exception", new Exception("Inner exception")));

        var mockDbSet = new Mock<DbSet<Registration>>();
        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateRegistration_Should_Handle_SaveChangesAsync_Returning_Zero()
    {
        // Arrange
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ZeroResultDb")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);
        mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(0); // Return 0 to indicate no rows affected

        var mockDbSet = new Mock<DbSet<Registration>>();
        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId, // Ensure QId matches the existing registration's SubmitterQId
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = "UpdatedFirstName" // Add some change
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.CreateRegistration(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Return_BadRequest_For_Missing_RequestOrigin_Header()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0001";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone
        };
        var json = SerializeObject(request);
        // Create request without the X-RequestOrigin header
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Put);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Handle_DbUpdateException()
    {
        // Arrange
        // Create a mock context that will throw an exception when SaveChanges is called
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ThrowingDbUpdate")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);
        // Need to mock FindAsync first as it's called before SaveChangesAsync in the update method
        var existingRegistration =
            MockDataForRegistrationFunctions.GetRegistrations().First(r => r.ReqNumber == "REQ0001");
        var mockDbSet = CreateMockDbSet([existingRegistration]);
        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Test update exception", new Exception("Inner update exception")));

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        const string reqNumber = "REQ0001";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId, // Ensure QId matches the existing registration's SubmitterQId
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = "UpdatedFirstNameAgain" // Add some change
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Put);
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateRegistrationByReqNumber_Should_Handle_SaveChangesAsync_Returning_Zero()
    {
        // Arrange
        var mockOptions = new DbContextOptionsBuilder<RegistrationContext>()
            .UseInMemoryDatabase(databaseName: "ZeroResultUpdateDb")
            .Options;

        var mockContext = new Mock<RegistrationContext>(mockOptions);
        // Need to mock FindAsync first
        var existingRegistration =
            MockDataForRegistrationFunctions.GetRegistrations().First(r => r.ReqNumber == "REQ0001");
        var mockDbSet = CreateMockDbSet([existingRegistration]);
        mockContext.Setup(c => c.Registration).Returns(mockDbSet.Object);

        mockContext.Setup(m => m.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(0); // Return 0 to indicate no rows affected

        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockContext.Object);

        const string reqNumber = "REQ0001";
        var request = new CreateUpdateRegistrationRequest
        {
            QId = GetMoqUser().UserQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = "UpdatedFirstNameAgain" // Add some change
        };
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Put);
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.UpdateRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationListByQId_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database connection failed"));

        var mockHttpRequestData = CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRegistrationStatsByQId_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database connection failed"));

        var mockHttpRequestData = CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.GetRegistrationStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessRegistrationByQId_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<RegistrationContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database connection failed"));

        var mockHttpRequestData = CreateHttpRequestData();
        var registrationFunctions = new RegistrationFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await registrationFunctions.CheckInProcessRegistrationByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var errorResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, errorResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteRegistrationByReqNumber_Should_Return_BadRequest_For_Missing_RequestOrigin_Header()
    {
        // Arrange
        var mockRegistrationContext = new TestRegistrationDbContext();
        const string reqNumber = "REQ0001";
        // Create request without X-RequestOrigin header
        var mockHttpRequestData = CreateHttpRequestData(method: Delete);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var registrationFunctions = new RegistrationFunctions(mockRegistrationContext, _logger);

        // Act
        var response = await registrationFunctions.DeleteRegistrationByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var badRequestResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, badRequestResponse.StatusCode);
    }
}