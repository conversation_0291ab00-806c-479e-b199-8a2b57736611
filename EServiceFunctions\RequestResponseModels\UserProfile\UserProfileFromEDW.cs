﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.RequestResponseModels.UserProfile;

[ExcludeFromCodeCoverage]
public class UserProfileFromEDW
{
    [Key]
    [Column("QID")]
    public long QId { get; set; }
    [Column("QID_EXPIRY_DT")]
    public DateTime? QIdExpiryDt { get; set; }
    [Column("FIRST_NAME_EN")]
    public string? FNameEn { get; set; }
    [Column("MIDDLE_NAME_EN")]
    public string? MNameEn { get; set; }
    [Column("LAST_NAME_EN")]
    public string? LNameEn { get; set; }
    [Column("FIRST_NAME_AR")]
    public string? FNameAr { get; set; }
    [Column("MIDDLE_NAME_AR")]
    public string? MNameAr { get; set; }
    [Column("LAST_NAME_AR")]
    public string? LNameAr { get; set; }
    [Column("DOB")]
    public DateTime? Dob { get; set; }
    [Column("NATIONALITY_CODE")]
    public string? NationalityCode { get; set; }
    [Column("NATIONALITY_EN")]
    public string? NationalityEn { get; set; }
    [Column("NATIONALITY_AR")]
    public string? NationalityAr { get; set; }
    [Column("GENDER_CODE")]
    public string? GenderCode { get; set; }
    [Column("GENDER_EN")]
    public string? GenderEn { get; set; }
    [Column("GENDER_AR")]
    public string? GenderAr { get; set; }
    [Column("VISA_CODE")]
    public string? VisaCode { get; set; }
    [Column("VISA_DESC_ENG")]
    public string? VisaDescriptionEn { get; set; }
    [Column("VISA_DESC_ARABIC")]
    public string? VisaDescriptionAr { get; set; }
    [Column("OCCUP_CODE")]
    public string? OccupationCode { get; set; }
    [Column("OCCUP_DESC_ENG")]
    public string? OccupationDescriptionEn { get; set; }
    [Column("OCCUP_DESC_ARABIC")]
    public string? OccupationDescriptionAr { get; set; }
    [Column("PHONE_MOBILE")]
    public string? PhoneMobile { get; set; }
    [NotMapped]
    public string? SecondaryPhoneMobile { get; set; }
    [Column("HC_CODE")]
    public string? AssignedHealthCenter { get; set; }
    [Column("ASSIGNED_HC_EN")]
    public string? AssignedHealthCenterEn { get; set; }
    [Column("ASSIGNED_HC_ARB")]
    public string? AssignedHealthCenterAr { get; set; }
    [Column("HC_NUMBER")]
    public string? HcNumber { get; set; }
    [Column("HC_EXPIRY_DATE")]
    public DateTime? HcExpiryDate { get; set; }
    [Column("GIS_ADDRESS_STREET")]
    public string? GisAddressStreet { get; set; }
    [Column("GIS_ADDRESS_BUILDING")]
    public string? GisAddressBuilding { get; set; }
    [Column("GIS_ADDRESS_ZONE")]
    public string? GisAddressZone { get; set; }
    [Column("GIS_ADDRESS_UNIT")]
    public string? GisAddressUnit { get; set; }
    [Column("GIS_UPD_DTTM")]
    public DateTime? GisAddressUpdatedAt { get; set; }
    [Column("PHYSICIAN_CODE")]
    public string? PhysicianId { get; set; }
    [Column("PHYSICIAN_FULL_NAME_EN")]
    public string? PhysicianFullNameEn { get; set; }
    [Column("PHYSICIAN_FULL_NAME_AR")]
    public string? PhysicianFullNameAr { get; set; }
    [Column("IS_STAFF_FLAG")]
    public int? IsStaff { get; set; }
}
