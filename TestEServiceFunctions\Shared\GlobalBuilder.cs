﻿using MockDataLibrary;

namespace TestEServiceFunctions.Shared;

public static class GlobalBuilder
{
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(GetCurrentDirectory())
            .AddJsonFile(AppEnvironment.Development, optional: true, reloadOnChange: true)
            .AddEnvironmentVariables()
            .Build();
    }

    public static async Task WaitAsync(int wait = 1) => await Task.Delay(FromSeconds(wait));

    public static string? ReadFromConfiguration(string? keyName)
    {
        var configuration = BuildConfiguration();
        if (keyName is null) return null;
        var config = configuration[keyName];
        return !string.IsNullOrEmpty(config) ? config : null;
    }

    private static Dictionary<string, string> RequiredHeaders()
    {
        var headers = new Dictionary<string, string>
        {
            { RequestOrigin, "Testing-request-origin" },
            { PhccEnvironment, "DEV" },
            { JwtClaimsQId, QId },
            { EligibleClinicCodes, "12, 13, 14, 15, 16, 17, 18, 19, 20" },
            { IsAuthValReq, "true" },
            { GlobalConstants.ContentType , ApplicationJson}
        };
        return headers;
    }

    public static RestRequest GetRestRequest(string route)
    {
        var request = new RestRequest(route);
        request.AddHeaders(RequiredHeaders());
        request.AddHeader("x-functions-key", ReadFromConfiguration("MasterKey"));
        return request;
    }
}