# Update Enrollment Tests

## Test Case: TestUpdateEnrollmentByReqNumber_Should_Return_OK

Tests the update of an existing enrollment by request number.

### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateEnrollmentByReqNumber_Should_Return_OK()
{
    // Arrange
    var request = GetRestRequest("enrollments/{reqNumber}");
    request.AddUrlSegment("reqNumber", "18708IJIB");
    var enrollment = GenerateEnrollment();
    enrollment.SubmitterQId = 22222222270;
    request.AddJsonBody(enrollment);
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateEnrollmentByReqNumber");
    request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
    
    // Act
    var response = await _client.PutAsync(request);
    testOutputHelper.LogToConsole(response);
    
    // Assert
    NotNull(response);
    Equal(OK, response.StatusCode);
}
```

## Request Details

### Endpoint
```
PUT enrollments/{reqNumber}
```

### Headers
- `RequestOrigin`: "UnitTest-UpdateEnrollmentByReqNumber"
- `JwtClaimsQId`: "22222222270"

### URL Parameters
- `reqNumber`: "18708IJIB"

### Request Body
Generated using `GenerateEnrollment()` with modified submitter QID:
```csharp
var enrollment = GenerateEnrollment();
enrollment.SubmitterQId = 22222222270;
```

## Test Data

### Fixed Values
- Request Number: "18708IJIB"
- Submitter QID: 22222222270

### Generated Data
Same structure as Create Enrollment:
- Personal Information
- Attachments
- Submitter Information
- Consent

## Validation Rules

### Request Validation
1. Valid request number
2. Matching submitter QID
3. Valid enrollment data

### Response Validation
1. Response not null
2. Status code = OK

## Error Cases

1. **Invalid Request Number**
   - Non-existent request number
   - Malformed request number
   - Invalid format

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - QID mismatch

3. **Data Validation**
   - Invalid enrollment data
   - Missing required fields
   - Invalid field formats

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims
   - QID must match submitter

2. **Request Number**
   - Must exist in system
   - Case sensitive
   - Format validation

3. **Performance**
   - Database update operation
   - Attachment validation
   - Multiple service calls
