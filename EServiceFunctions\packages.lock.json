{"version": 1, "dependencies": {"net8.0": {"Azure.Extensions.AspNetCore.Configuration.Secrets": {"type": "Direct", "requested": "[1.4.0, )", "resolved": "1.4.0", "contentHash": "cy8kazCYnG/IUHQ9048UUmObgbPsqkQbcdrCAPxl1JA6ylLBV+v+8586bOWpuSF6GZaxpulLGFDBBm3PR5t1gA==", "dependencies": {"Azure.Core": "1.44.1", "Azure.Security.KeyVault.Secrets": "4.6.0", "Microsoft.Extensions.Configuration": "2.1.0"}}, "Azure.Identity": {"type": "Direct", "requested": "[1.14.1, )", "resolved": "1.14.1", "contentHash": "qqZL9kBS/vi9dGqs3w4jfrHzg+Nk0Ko0lZh/rhYRrb0rMcc5A2VprLFNI24g/bT2A6bO1Og93Llc+pMhplxNLA==", "dependencies": {"Azure.Core": "1.46.1", "Microsoft.Identity.Client": "4.71.1", "Microsoft.Identity.Client.Extensions.Msal": "4.71.1", "System.Memory": "4.5.5"}}, "Azure.Security.KeyVault.Secrets": {"type": "Direct", "requested": "[4.8.0, )", "resolved": "4.8.0", "contentHash": "tmcIgo+de2K5+PTBRNlnFLQFbmSoyuT9RpDr5MwKS6mIfNxLPQpARkRAP91r3tmeiJ9j/UCO0F+hTlk1Bk7HNQ==", "dependencies": {"Azure.Core": "1.46.2"}}, "Bogus": {"type": "Direct", "requested": "[35.6.3, )", "resolved": "35.6.3", "contentHash": "+5omfZWy8gOcbpc48vKfMI4h/ecbdf3NQm6xCl3zMbaP4J3rMMm5h3kPFeIWBgt4mbz6YY0eeZDLR/6yv1khKw=="}, "Microsoft.ApplicationInsights.WorkerService": {"type": "Direct", "requested": "[2.23.0, )", "resolved": "2.23.0", "contentHash": "3+EJOAIkgbU5rQMFowiB1s04/gf56kuwwGjcFyoeL2iyweHirjJpGu7XoigjvtWGkxue6CAAjUPwuuGMHE97Yg==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.23.0", "Microsoft.ApplicationInsights.EventCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.23.0", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.Logging.ApplicationInsights": "2.23.0"}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"type": "Direct", "requested": "[8.0.17, )", "resolved": "8.0.17", "contentHash": "eg8TB2jqlMbSywcCDSl46VMT8H/DJyLl/88C2wd5u/KuHvCOivVV/lJQRs6l6auVt8IRcMduqX8V4lWGI3c0Yg==", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.17", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}}, "Microsoft.Azure.AppConfiguration.AspNetCore": {"type": "Direct", "requested": "[8.2.0, )", "resolved": "8.2.0", "contentHash": "67pMWly3oQkvAApppiIJRh6l7fXTFao3ctbmuIDqKRvITdWgDVidjrpQozRrTEVDAjdWMCwY2AFuCG1kDsmTTA==", "dependencies": {"Microsoft.Extensions.Configuration.AzureAppConfiguration": "8.2.0"}}, "Microsoft.Azure.Functions.Worker": {"type": "Direct", "requested": "[2.0.0, )", "resolved": "2.0.0", "contentHash": "vK0+XApQq2vDSbJxqrflliGLVILRl66gMfZrlv8yemGKdoh2KI7l4zQPW8ibCThznC2PEMp60+a1D3wp09ZXEg==", "dependencies": {"Microsoft.Azure.Functions.Worker.Core": "2.0.0", "Microsoft.Azure.Functions.Worker.Grpc": "2.0.0"}}, "Microsoft.Azure.Functions.Worker.ApplicationInsights": {"type": "Direct", "requested": "[2.0.0, )", "resolved": "2.0.0", "contentHash": "sfHpA5/oVmxbFAsZRs52KDLu6NpVmAOqDlYWC/FbT9N1K/Hhf7I0Moo+r4v0wk0T+XxvJI2oBMNpq/kHexEpmg==", "dependencies": {"Azure.Identity": "1.12.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.Azure.Functions.Worker.Core": "2.0.0", "Microsoft.Bcl.AsyncInterfaces": "8.0.0"}}, "Microsoft.Azure.Functions.Worker.Extensions.Http": {"type": "Direct", "requested": "[3.3.0, )", "resolved": "3.3.0", "contentHash": "a1W5bpOQCrYP0kbSJUFrK6PYeQAfg3UXQR+kPdt1YLvWrc/JTaNWyOA1DRKGrvRRo87xSM9NdFZuDULEMAxl4Q==", "dependencies": {"Microsoft.Azure.Functions.Worker.Core": "1.19.0", "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": "1.3.0"}}, "Microsoft.Azure.Functions.Worker.Extensions.OpenApi": {"type": "Direct", "requested": "[1.5.1, )", "resolved": "1.5.1", "contentHash": "14W3/70jZCpVv91WYPKFpnrNs7hx75sKU7EJRK8mV2R8KciEBsVMZ5MOlo8jv8bM2MSViq3zs/Z3Xo0fW6hgBg==", "dependencies": {"Microsoft.Azure.Core.NewtonsoftJson": "1.0.0", "Microsoft.Azure.Functions.Worker.Core": "1.8.0", "Microsoft.Azure.Functions.Worker.Extensions.Http": "3.0.13", "Microsoft.Azure.WebJobs.Extensions.OpenApi.Core": "1.5.1", "YamlDotNet": "12.0.1"}}, "Microsoft.Azure.Functions.Worker.Sdk": {"type": "Direct", "requested": "[2.0.5, )", "resolved": "2.0.5", "contentHash": "6Dyo1+tw8+ZQ+yJOGvAFc+fEkBlJ0+hCqUegoWrMm3VKLSNU2B/Wu238l4w4L6CI7iSEpJsJ2rShK0SgVsAZdg==", "dependencies": {"Microsoft.Azure.Functions.Worker.Sdk.Analyzers": "1.2.2", "Microsoft.Azure.Functions.Worker.Sdk.Generators": "1.3.5"}}, "Microsoft.Data.SqlClient": {"type": "Direct", "requested": "[6.0.2, )", "resolved": "6.0.2", "contentHash": "RDqwzNu5slSqGy0eSgnN4fuLdGI1w9ZHBRNALrbUsykOIbXtGCpyotG0r5zz+HHtzxbe6LtcAyWcOiu0a+Fx/A==", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Bcl.Cryptography": "8.0.0", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.1"}}, "Microsoft.EntityFrameworkCore.Design": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "6xabdZH2hOqSocjDIOd0FZLslH7kDX8ODY4lBR298GwkAkxuItjNgZHuRbTi9hmfDS2Hh02r+d17Fa8XT4lKLQ==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.6"}}, "Microsoft.EntityFrameworkCore.SqlServer": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "hr8vJSL1KXkXdgvNYY5peSygSZkoKQ+r6umXGMLoggBQ9NMbf0jo8p13Hy0biON2IS03ixOl0g4Mgw0hjgTksw==", "dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "System.Formats.Asn1": "9.0.6", "System.Text.Json": "9.0.6"}}, "Microsoft.EntityFrameworkCore.Tools": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "2Qs+OnYPOrfb5wpSXNGdm9v3QattLhou26xamaICIE9jqWAW7xdzDlY/yXRz6zKFLnzRH70IM+XXYVElEVeQ9Q==", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.6"}}, "Microsoft.Extensions.Configuration.UserSecrets": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "0ZZMzdvNwIS0f09S0IcaEbKFm+Xc41vRROsA/soeKEpzRISTDdiVwGlzdldbXEsuPjNVvNHyvIP8YW2hfIig0w==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6"}}, "Microsoft.FeatureManagement": {"type": "Direct", "requested": "[4.1.0, )", "resolved": "4.1.0", "contentHash": "b0IpwPFjAqGj1x9pcr0CG0l2eS1WBkJeV3hgmL1WjrKFxTVL6CCf67qoNaCWw1GNidoqAJVZQPPFiWz6xU5dPg==", "dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.Extensions.Caching.Memory": "2.1.23", "Microsoft.Extensions.Configuration.Binder": "2.1.10", "Microsoft.Extensions.Logging": "2.1.1", "System.Diagnostics.DiagnosticSource": "8.0.1"}}, "Throw": {"type": "Direct", "requested": "[1.4.0, )", "resolved": "1.4.0", "contentHash": "fz2Y+LXepl0obzvZs5PAs2ZnnOF0fIpaErUQMEynMXIxQwnxaKcRgiBiKhCp4Jp2OrSlcKMwgqzAqceWOyl/hg==", "dependencies": {"OneOf": "3.0.216"}}, "Azure.Core": {"type": "Transitive", "resolved": "1.46.2", "contentHash": "HFcvd1besmgBFPIZ7iSFHZOgzGfHTZNTzG8gWYdIP8ZJQySrb+vAdArcmFw7je3kFRMDbbtMoWKNVGj2vvH1sw==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.4.2", "System.Memory.Data": "6.0.1"}}, "Azure.Data.AppConfiguration": {"type": "Transitive", "resolved": "1.6.0", "contentHash": "HAA4fHqlywrVdkLlVhl9wXD9xrddNQp5vS0MZf1zvYl9DQ89M63hrHou41bgx5XEC6KTqzkCbTnLgrrEySbPJg==", "dependencies": {"Azure.Core": "1.44.1"}}, "Azure.Messaging.EventGrid": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "Wm5+RY6hNoIPVLPwmr3T1ijVm5GdLVZBij93c4Brwe9iB3X8nlUYNjlnQVVJqK4QLs85nGwqBGUpB4BfYdGXVQ==", "dependencies": {"Azure.Core": "1.20.0", "System.Memory.Data": "1.0.2", "System.Text.Json": "4.6.0"}}, "DnsClient": {"type": "Transitive", "resolved": "1.7.0", "contentHash": "2hrXR83b5g6/ZMJOA36hXp4t56yb7G1mF3Hg6IkrHxvtyaoXRn2WVdgDPN3V8+GugOlUBbTWXgPaka4dXw1QIg==", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}}, "Google.Protobuf": {"type": "Transitive", "resolved": "3.28.0", "contentHash": "lVsLcLIHpkOABswgydr0sjkIhSN5XE9C6AgLXxvMsa6pSSJIpmAIPvK/NZpuF+HTB6xp1R8T5nhLausWVOrU/w=="}, "Grpc.Core.Api": {"type": "Transitive", "resolved": "2.65.0", "contentHash": "VHElVX8XpJoaoddHkxhSHrXqn+7k3cZRmAxvZFh0NElkZK5oAT0/QOPM2FJtt5/xIxjQPumz3TOkzrUT6b8V6g=="}, "Grpc.Net.Client": {"type": "Transitive", "resolved": "2.65.0", "contentHash": "ys1Rz7pxV0XR2pm4BVMkm/PrJ2BQu8OXFTvnXzzelTvQ+19OiHbUSb1kSZ2X4V7FziaxrtWY52ssXK3MFjQxbg==", "dependencies": {"Grpc.Net.Common": "2.65.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}}, "Grpc.Net.ClientFactory": {"type": "Transitive", "resolved": "2.65.0", "contentHash": "iVJ8/uoaPccipMeyD0uwpbX6lRhFEo4aLJmGhLsdigQWi1tDdf/PHfRNZudX3lqZGuqyxl+wC2CfuApIIKPMhg==", "dependencies": {"Grpc.Net.Client": "2.65.0", "Microsoft.Extensions.Http": "6.0.0"}}, "Grpc.Net.Common": {"type": "Transitive", "resolved": "2.65.0", "contentHash": "XbRegnfDrWoXZJ4GGdJat+KXnmCCkR6XQmWnIZxxIjphyZkWZgI0FN1PtGXU4RZ5a5C85qg1cscz1ju2biVXGg==", "dependencies": {"Grpc.Core.Api": "2.65.0"}}, "Humanizer.Core": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw=="}, "Microsoft.ApplicationInsights": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "nWArUZTdU7iqZLycLKWe0TDms48KKGE6pONH2terYNa8REXiqixrMOkf1sk5DHGMaUTqONU2YkS4SAXBhLStgw==", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.DependencyCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "9YRdl9SNbTxd4AafJckyoJLr5gJdnvqFivjo+PY0lQTPEncPB+z3ZABG4iDfxN9HI1aLqyRINr1/7de9Wg8ZuQ==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.EventCounterCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "gGt0JPw2dcSeIAIefyORJBdeMz8KgAFIktu8HV/NwkiGmLyw+YtifLm6B5gvGxO15AeMsGPbmvWEIvLfq88XPw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0"}}, "Microsoft.ApplicationInsights.PerfCounterCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "q9ApjZfBS9O8m3aQM2oVjsGBmlE8BCFywT7UR+8aqdNuz7HpoIxw4jHy0XOBergiFX/olrJF4OyPkGxc3H5JHg==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "2B8CGfnB/tribkQAqRBhMvJYJK5TkEPMG/BB0QrlxdwVGEufayNLMveXjkQCqld9arXd6wKR1ve2XmkA0+xXKQ==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.23.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.23.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "798Dudr4tkujslk1w+XcXOcCErmVsk+nhp+QCHLa3lcgi25vkAxBmzPUeQlRJVCNL/1f4x/YF+vQZ8RSuTXWCw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "System.IO.FileSystem.AccessControl": "4.7.0"}}, "Microsoft.AspNet.WebApi.Client": {"type": "Transitive", "resolved": "5.2.8", "contentHash": "dkGLm30CxLieMlUO+oQpJw77rDs0IIx/w3lIHsp+8X94HXCsUfLYuFmlZPsQDItC0O2l1ZlWeKLkZX7ZiNRekw==", "dependencies": {"Newtonsoft.Json": "10.0.1", "Newtonsoft.Json.Bson": "1.0.1"}}, "Microsoft.AspNetCore.Authentication.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Core": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authorization": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Authorization.Policy": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Http": {"type": "Transitive", "resolved": "2.2.2", "contentHash": "BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}}, "Microsoft.AspNetCore.JsonPatch": {"type": "Transitive", "resolved": "8.0.17", "contentHash": "Rs6dBchc36T1bIh750AkV89rOEudlQqjRsegYnY5vrwvfwmtUEKEvasX2Esn62FWRKlUJjM3VW8omwH0X8Ub3A==", "dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}}, "Microsoft.AspNetCore.Mvc.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Core": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ALiY4a6BYsghw8PT5+VU593Kqp911U3w9f/dH9/ZoI3ezDsDAGiObqPu/HP1oXK80Ceu0XdQ3F0bx5AXBeuN/Q==", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.1"}}, "Microsoft.AspNetCore.Mvc.Formatters.Json": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.WebApiCompatShim": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "YKovpp46Fgah0N8H4RGb+7x9vdjj50mS3NON910pYJFQmn20Cd1mYVkTunjy/DrZpvwmJ8o5Es0VnONSYVXEAQ==", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.6", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0"}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}}, "Microsoft.AspNetCore.Routing": {"type": "Transitive", "resolved": "2.2.2", "contentHash": "HcmJmmGYewdNZ6Vcrr5RkQbc/YWU4F79P3uPPBi6fCFOgUewXNM1P4kbPuoem7tN4f7x8mq7gTsm5QGohQ5g/w==", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Routing.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Azure.Core.NewtonsoftJson": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "HdNo6Z5mTe1voaPhFhjegHUtVpNDtXPL7sdJI+xSnFQiCu85bdYzFs6oiL0UhFSO6g17WYVdoC/UW6B2TSjBFQ==", "dependencies": {"Azure.Core": "1.7.0", "Newtonsoft.Json": "10.0.3"}}, "Microsoft.Azure.Functions.Worker.Core": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "nzat+mbFtsNQv2QtqzHpGTCmR6LarjyzreNH7+1JZHjSyqntGVDyjBM6xqyv13zOH0swn5I3vJMKgpPlasdqCA==", "dependencies": {"Azure.Core": "1.44.1", "Microsoft.Extensions.Hosting": "8.0.1"}}, "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "+6+/Yb/ouWUweaSQhesbbiIVSmwYEzkSfjIHrBnNqIiCYnx2iLeoYyWjN/wHP3Fnn5COtyDXRDwHKr5A/tCL9Q=="}, "Microsoft.Azure.Functions.Worker.Grpc": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "NiOm+1EWQy7fz1kl08hCmIym2L36jREXXvQPao75zh7Tx7UCpV2IoH/BxmssWaGJPpup9cyKg3NsXEVFhc7qkQ==", "dependencies": {"Google.Protobuf": "3.28.0", "Grpc.Net.ClientFactory": "2.65.0", "Microsoft.Azure.Functions.Worker.Core": "2.0.0", "Microsoft.Azure.Functions.Worker.Extensions.Abstractions": "1.3.0"}}, "Microsoft.Azure.Functions.Worker.Sdk.Analyzers": {"type": "Transitive", "resolved": "1.2.2", "contentHash": "v+GWIfC2Or3nxriew6JLT0yRGmhqYI84ALLqbQSyj/j9Z6ZQi2dh9iO9i3jmHAqiVFmIC2Aing/0eYQ0pObU4w=="}, "Microsoft.Azure.Functions.Worker.Sdk.Generators": {"type": "Transitive", "resolved": "1.3.5", "contentHash": "lq0D0xjO4rQvIePxtTjgiSiGRpCl7HVvPSBSwsRG62YVRY9kbfDPb4ANnP/zxjtuTvER0QCtZoECbbsIzFF7Ow=="}, "Microsoft.Azure.WebJobs": {"type": "Transitive", "resolved": "3.0.32", "contentHash": "uN8GsFqPFHHcSrwwj/+0tGe6F6cOwugqUiePPw7W3TL9YC594+Hw8GBK5S/fcDWXacqvRRGf9nDX8xP94/Yiyw==", "dependencies": {"Microsoft.Azure.WebJobs.Core": "3.0.32", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.0", "Microsoft.Extensions.Configuration.Json": "2.1.0", "Microsoft.Extensions.Hosting": "2.1.0", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Configuration": "2.1.0", "Newtonsoft.Json": "11.0.2", "System.Memory.Data": "1.0.1", "System.Threading.Tasks.Dataflow": "4.8.0"}}, "Microsoft.Azure.WebJobs.Core": {"type": "Transitive", "resolved": "3.0.32", "contentHash": "pW5lyF0Tno1cC2VkmBLyv7E3o5ObDdbn3pfpUpKdksJo9ysCdQTpgc0Ib99wPHca6BgvoglicGbDYXuatanMfg==", "dependencies": {"System.ComponentModel.Annotations": "4.4.0", "System.Diagnostics.TraceSource": "4.3.0"}}, "Microsoft.Azure.WebJobs.Extensions.Http": {"type": "Transitive", "resolved": "3.2.0", "contentHash": "IXLuo5fOliOYKUZjWO5kQ/j3XblM9TNnk1agjzNYkubpDXq6M436GihaVzwTeQlX279P3G1KquS6I+b7pXaFuQ==", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.8", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.WebApiCompatShim": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.2", "Microsoft.Azure.WebJobs": "3.0.32"}}, "Microsoft.Azure.WebJobs.Extensions.OpenApi.Configuration.AppSettings": {"type": "Transitive", "resolved": "1.5.1", "contentHash": "fxCeaobDiszTKBGcrsKzv7OFPU8gasWlsDp6PECeSQNYt72BGztflR2PluV9LcVHwzlsgmIcIxbozo2d0AuIWw==", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1"}}, "Microsoft.Azure.WebJobs.Extensions.OpenApi.Core": {"type": "Transitive", "resolved": "1.5.1", "contentHash": "JbzBl45/1J+q0H1KTt471KtObzX2a2ObKvZK4yBZRsLw9qyehabZqWxjnEiT8q0Jw1DLat5nTrjul1741DRhng==", "dependencies": {"Microsoft.Azure.WebJobs.Extensions.Http": "3.2.0", "Microsoft.Azure.WebJobs.Extensions.OpenApi.Configuration.AppSettings": "1.5.1", "Microsoft.OpenApi": "1.2.3", "Newtonsoft.Json": "13.0.1", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw=="}, "Microsoft.Bcl.Cryptography": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "Y3t/c7C5XHJGFDnohjf1/9SYF3ZOfEU1fkNQuKg/dGf9hN18yrQj2owHITGfNS3+lKJdW6J4vY98jYu57jCO8A=="}, "Microsoft.Bcl.HashCode": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "GI4jcoi6eC9ZhNOQylIBaWOQjyGaR8T6N3tC1u8p3EXfndLCVNNWa+Zp+ocjvvS3kNBN09Zma2HXL0ezO0dRfw=="}, "Microsoft.Bcl.TimeProvider": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA=="}, "Microsoft.Build.Framework": {"type": "Transitive", "resolved": "17.8.3", "contentHash": "NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g=="}, "Microsoft.Build.Locator": {"type": "Transitive", "resolved": "1.7.8", "contentHash": "sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog=="}, "Microsoft.CodeAnalysis.Analyzers": {"type": "Transitive", "resolved": "3.3.4", "contentHash": "AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g=="}, "Microsoft.CodeAnalysis.Common": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.CodeAnalysis.CSharp": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.8.0]"}}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.8.0]", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}}, "Microsoft.CodeAnalysis.Workspaces.Common": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "dependencies": {"Microsoft.Build.Framework": "16.10.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]", "System.Text.Json": "7.0.3"}}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.Data.SqlClient.SNI.runtime": {"type": "Transitive", "resolved": "6.0.2", "contentHash": "f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w=="}, "Microsoft.EntityFrameworkCore": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}}, "Microsoft.EntityFrameworkCore.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A=="}, "Microsoft.EntityFrameworkCore.Analyzers": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw=="}, "Microsoft.EntityFrameworkCore.Relational": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}}, "Microsoft.Extensions.Azure": {"type": "Transitive", "resolved": "1.7.6", "contentHash": "o2dLnQ8cMw5p7KAtxAPukkk4Mhs4tu96nUyFee4lvfLZEkuyTLhLGT2D5o5bagCwHVxqzt+w4Eb4YOl/pLq6Cw==", "dependencies": {"Azure.Core": "1.44.0", "Azure.Identity": "1.12.0", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0", "Microsoft.Extensions.Configuration.Binder": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Configuration.AzureAppConfiguration": {"type": "Transitive", "resolved": "8.2.0", "contentHash": "jf7TGWVY1WNQUU0XGBKIQsKkLKw6Ve0b23zV04DXerzYySqSdbCo3vBiC3gZhUTD6IxpA13m6NKRKO7Jf1EiZA==", "dependencies": {"Azure.Data.AppConfiguration": "1.6.0", "Azure.Messaging.EventGrid": "4.7.0", "Azure.Security.KeyVault.Secrets": "4.6.0", "DnsClient": "1.7.0", "Microsoft.Bcl.HashCode": "6.0.0", "Microsoft.Extensions.Azure": "1.7.6", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "7IQhGK+wjyGrNsPBjJcZwWAr+Wf6D4+TwOptUt77bWtgNkiV8tDEbhFS+dDamtQFZ2X7kWG9m71iZQRj2x3zgQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "System.Text.Json": "9.0.6"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA=="}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "grVU1ixgMHp+kuhIgvEzhE73jXRY6XmxNBPWrotmbjB9AvJvkwHnIzm1JlOsPpyixFgnzreh/bFBMJAjveX+fQ==", "dependencies": {"System.Text.Encodings.Web": "9.0.6", "System.Text.Json": "9.0.6"}}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "doVPCUUCY7c6LhBsEfiy3W1bvS7Mi6LkfQMS8nlC22jZWNxBv8VO8bdfeyvpYFst6Kxqk7HBC6lytmEoBssvSQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "1HJCAbwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g=="}, "Microsoft.Extensions.Hosting": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "bP9EEkHBEfjgYiG8nUaXqMk/ujwJrffOkNPP7onpRMO8R+OUSESSP4xHkCAXgYZ1COP2Q9lXlU5gkMFh20gRuw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.Configuration.Json": "8.0.1", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.1", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.Logging.Console": "8.0.1", "Microsoft.Extensions.Logging.Debug": "8.0.1", "Microsoft.Extensions.Logging.EventLog": "8.0.1", "Microsoft.Extensions.Logging.EventSource": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6"}}, "Microsoft.Extensions.Logging.ApplicationInsights": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "JLEabPz445i1yRB0hKZVzJJE35QatRIzWlrMOiBQXr9kBJod0jkpkrBf94ln6kXu+jlEGohnXtuXacPPhybJDw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.Extensions.Logging": "2.1.1"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "QWwTrsgOnJMmn+XUslm8D2H1n3PkP/u/v52FODtyBc/k4W9r3i2vcXXeeX/upnzllJYRRbrzVzT0OclfNJtBJA==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "uzcg/5U2eLyn5LIKlERkdSxw6VPC1yydnOSQiRRWGBGN3kphq3iL4emORzrojScDmxRhv49gp5BI8U3Dz7y4iA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Configuration": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Logging.Debug": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "B8hqNuYudC2RB+L/DI33uO4rf5by41fZVdcVL2oZj0UyoAZqnwTwYHp1KafoH4nkl1/23piNeybFFASaV2HkFg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Logging.EventLog": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "ZD1m4GXoxcZeDJIq8qePKj+QAWeQNO/OG8skvrOG8RQfxLp9MAKRoliTc27xanoNUzeqvX5HhS/I7c0BvwAYUg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.EventLog": "8.0.1"}}, "Microsoft.Extensions.Logging.EventSource": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "YMXMAla6B6sEf/SnfZYTty633Ool3AH7KOw2LOaaEqwSo2piK4f7HMtzyc3CNiipDnq1fsUSuG5Oc7ZzpVy8WQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw=="}, "Microsoft.Identity.Client": {"type": "Transitive", "resolved": "4.71.1", "contentHash": "SgvSBcMRvmEEyV10pcvxNVUbwYoShmj/9pxXFVr3AFjE26IUzuwYLtLgt58xkEnT0xJBjfObaXxcol3BMtmEAg==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Identity.Client.Extensions.Msal": {"type": "Transitive", "resolved": "4.71.1", "contentHash": "PGOHaoQhKBKnXy1kfW+Gu9/rxStKsqR+UZKeVv4XAsATdzmfj9y9kkUOftIjVFvxP3oh2Sk7v65ylS0K/qYADA==", "dependencies": {"Microsoft.Identity.Client": "4.71.1", "System.Security.Cryptography.ProtectedData": "4.5.0"}}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "seOFPaBQh2K683eFujAuDsrO2XbOA+SvxRli+wu7kl+ZymuGQzjmmUKfyFHmDazpPOBnmOX1ZnjX7zFDZHyNIA=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "mfyiGptbcH+oYrzAtWWwuV+7MoM0G0si+9owaj6DGWInhq/N/KDj/pWHhq1ShdmBu332gjP+cppjgwBpsOj7Fg==", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "3BInZEajJvnTDP/YRrmJ3Fyw8XAWWR9jG+3FkhhzRJJYItVL+BEH9qlgxSmtrxp7S7N6TOv+Y+X8BG61viiehQ==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.5.0"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "ugyb0Nm+I+UrHGYg28mL8oCV31xZrOEbs8fQkcShUoKvgk22HroD2odCnqEf56CoAFYTwoDExz8deXzrFC+TyA==", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "/U3I/8uutTqZr2n/zt0q08bluYklq+5VWP7ZuOGpTUR1ln5bSbrexAzdSGzrhxTxNNbHMCU8Mn2bNQvcmehAxg==", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.5.0", "System.IdentityModel.Tokens.Jwt": "7.5.0"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "owe33wqe0ZbwBxM3D90I0XotxNyTdl85jud03d+OrUOJNnTiqnYePwBk3WU9yW0Rk5CYX+sfSim7frmu6jeEzQ==", "dependencies": {"Microsoft.IdentityModel.Logging": "7.5.0"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.OpenApi": {"type": "Transitive", "resolved": "1.2.3", "contentHash": "Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw=="}, "Microsoft.SqlServer.Server": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug=="}, "Microsoft.Win32.Registry": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Mono.TextTemplating": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "dependencies": {"System.CodeDom": "6.0.0"}}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "Newtonsoft.Json.Bson": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "dependencies": {"Newtonsoft.Json": "12.0.1"}}, "OneOf": {"type": "Transitive", "resolved": "3.0.216", "contentHash": "hvSxx7O9Z50ZK/HZaqxcEiO1skihnHcsziy0cVRqSq7LrETM9swcRcMZuG+0irai7jq8FYYCHTrB/pSHSah6ug=="}, "runtime.native.System": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "SixLabors.ImageSharp": {"type": "Transitive", "resolved": "3.1.10", "contentHash": "R1HEPcqx3v+kvlOTPouP0g/Nzzud9pHtjlgGbFax3Ivaz8kkaGfS2EPfyDGpmfoTUQ3nQ5wxdhYyYa9fwYA9cw=="}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A=="}, "System.ClientModel": {"type": "Transitive", "resolved": "1.4.2", "contentHash": "goGitN7trB9hoQ01dIpxaSYcruI+lGt/xq471AUv8irFvsIX+4HCqk1pDT/4ZPTLmU6ZUuNzhCb4MJAIwG7+Uw==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "System.Memory.Data": "6.0.1"}}, "System.CodeDom": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA=="}, "System.Collections": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ=="}, "System.ComponentModel.Annotations": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "29K3DQ+IGU7LBaMjTo7SI7T7X/tsMtLvz1p56LJ556Iu0Dw3pKZw5g8yCYCWMRxrOF0Hr0FU0FwW0o42y2sb3A=="}, "System.Composition": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ=="}, "System.Composition.Convention": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "dependencies": {"System.Composition.AttributedModel": "7.0.0"}}, "System.Composition.Hosting": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "dependencies": {"System.Composition.Runtime": "7.0.0"}}, "System.Composition.Runtime": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw=="}, "System.Composition.TypedParts": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}}, "System.Configuration.ConfigurationManager": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg=="}, "System.Diagnostics.PerformanceCounter": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "gbeE5tNp/oB7O8kTTLh3wPPJCxpNOphXPTWVs1BsYuFOYapFijWuh0LYw1qnDo4gwDUYPXOmpTIhvtxisGsYOQ==", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}}, "System.Diagnostics.TraceSource": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Formats.Asn1": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "8LbKs3WVqyDSszFZJA9Uxg9z+C6WbPbFTSPm/HjFEsWx49XWs0ueqaAKPWncvFJ8yl4H4C/RTnUMhCKoXkddkg=="}, "System.Globalization": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt": {"type": "Transitive", "resolved": "7.5.0", "contentHash": "D0TtrWOfoPdyYSlvOGaU9F1QR+qrbgJ/4eiEsQkIz7YQKIKkGXQldXukn6cYG9OahSq5UVMvyAIObECpH6Wglg==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Tokens": "7.5.0"}}, "System.IO": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "vMToiarpU81LR1/KZtnT7VDPvqAZfw9oOS5nY6pPP78nGYz3COLsQH3OfzbR+SjTgltd31R6KmKklz/zDpTmzw==", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.IO.FileSystem.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "0nlr0reXrRmkZNKifKqh2DgGhQgfkT7Qa3gQxIn/JI7/y3WDiTz67M+Sq3vFhUqcG8O5zVrpqHvIHeGPGUBsEw=="}, "System.Memory": {"type": "Transitive", "resolved": "4.5.5", "contentHash": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw=="}, "System.Memory.Data": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "yliDgLh9S9Mcy5hBIdZmX6yphYIW3NH+3HN1kV1m7V1e0s7LNTw/tHNjJP4U9nSMEgl3w1TzYv/KA1Tg9NYy6w=="}, "System.Reflection": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Security.AccessControl": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Pkcs": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA=="}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg=="}, "System.Security.Principal.Windows": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA=="}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "uWRgViw2yJAUyGxrzDLCc6fkzE2dZIoXxs8V6YjCujKsJuP0pnpYSlbm2/7tKd0SjBnMtwfDQhLenk3bXonVOA=="}, "System.Text.Json": {"type": "Transitive", "resolved": "9.0.6", "contentHash": "h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "dependencies": {"System.IO.Pipelines": "9.0.6", "System.Text.Encodings.Web": "9.0.6"}}, "System.Threading": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA=="}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Dataflow": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "PSIdcgbyNv7FZvZ1I9Mqy6XZOwstYYMdZiXuHvIyc0gDyPjEhrrP9OvTGDHp+LAHp1RNSLjPYssyqox9+Kt9Ug=="}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "WSKUTtLhPR8gllzIWO2x6l4lmAIfbyMAiTlyXAis4QBDonXK4b4S6F8zGARX4/P8wH3DH+sLdhamCiHn+fTU1A=="}, "YamlDotNet": {"type": "Transitive", "resolved": "12.0.1", "contentHash": "MltVIOIX817Uz/TFkPSaZImdzB86X/k8QY0kFBQx6KI6GsKOk3rIlrptOhb28O0RD2WnibMoYLnCQnqfYduvaw=="}, "mockdatalibrary": {"type": "Project", "dependencies": {"Bogus": "[35.6.3, )", "SixLabors.ImageSharp": "[3.1.10, )"}}}}}