﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.MHDS;

[ExcludeFromCodeCoverage]
[Table("MHDSRequestDetails")]
public class MHDSRequestDetails
{
    [Key] [Column("Id")] 
    public long Id { get; set; }
    [Column("QId")]
    public long? QId { get; set; }
    [Column("FNameEn")]
    [StringLength(255)]
    public string? FNameEn { get; set; }
    [Column("MNameEn")]
    [StringLength(255)]
    public string? MNameEn { get; set; }
    [Column("LNameEn")]
    [StringLength(255)]
    public string? LNameEn { get; set; }
    [Column("FNameAr")]
    [StringLength(255)]
    public string? FNameAr { get; set; }
    [Column("MNameAr")]
    [StringLength(255)]
    public string? MNameAr { get; set; }
    [Column("LNameAr")]
    [StringLength(255)]
    public string? LNameAr { get; set; }
    [Required]
    [Column("ReqNumber")]
    [StringLength(255)]
    public string? ReqNumber { get; set; }
    [Column("HCNumber")]
    [StringLength(255)]
    public string? HcNumber { get; set; }
    [Column("Nationality")]
    [StringLength(255)]
    public string? Nationality { get; set; }
    [Column("Dob", TypeName = "date")]
    public DateTime? Dob { get; set; }
    [Column("CurrentAssignedHC")]
    [StringLength(255)]
    public string? CurrentAssignedHc { get; set; }
    [StringLength(255)]
    public string? Relationship { get; set; }
    [Column("BNo")]
    public int? BNo { get; set; }
    [Column("ZNo")]
    public int? ZNo { get; set; }
    [Column("SNo")]
    public int? SNo { get; set; }
    [Column("UNo")]
    public int? UNo { get; set; }
    [Column("IsGisAddressManuallyEntered")]
    public bool? IsGisAddressManualyEntered { get; set; }
    [Column("Action")]
    [StringLength(255)]
    public string? Action { get; set; }
    [Column("Consent")]
    [DefaultValue(false)]
    public bool Consent { get; set; }
    [Column("SubmittedBy")]
    [StringLength(255)]
    public string? SubmittedBy { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? SubmittedAt { get; set; }
    [Column("SubmitterQId")]
    public long? SubmitterQId { get; set; }
    [Column("SubmitterEmail")]
    [StringLength(255)]
    public string? SubmitterEmail { get; set; }
    [Column("SubmitterMobile")]
    [StringLength(255)]
    public string? SubmitterMobile { get; set; }
    [Column("SecondaryPhoneMobile")]
    [StringLength(255)]
    public string? SecondaryPhoneMobile { get; set; }
        
    [Column("CreatedAt" , TypeName = "datetime")]
    public DateTime? CreatedAt { get; set; }
    [Column("UpdatedAt", TypeName = "datetime")]
    public DateTime? UpdatedAt { get; set; }
    [StringLength(255)]
    [Column("Status")]
    public string? Status { get; set; }
    [StringLength(255)]
    [Column("StatusInternal")]
    public string? StatusInternal { get; set; }
    [Column("LastActionBy")]
    [StringLength(255)]
    public string? LastActionBy { get; set; }
    [Column("CreateSource")]
    [StringLength(255)]
    public string? CreateSource { get; set; }
    [Column("UpdateSource")]
    [StringLength(255)]
    public string? UpdateSource { get; set; }
    public Status? StatusNavigation { get; set; }
}