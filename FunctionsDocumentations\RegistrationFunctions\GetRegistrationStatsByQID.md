# Get Registration Stats By QID

## Overview
Retrieves registration request statistics for a specific submitter based on their Qatar ID, with optional status filtering.

## Endpoint
- **Route**: `registrations/submitter-qid/{qId}/stats`
- **Method**: GET
- **Authorization**: Function level with QID validation

## Parameters
- **qId** (path, required): Submitter's Qatar ID number
- **status** (query, optional): Filter by status category
  * InProcess: Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested
  * Archived: Approved, Cancelled, CancelledByEServ

## Response
- **200 OK**: Returns registration count
  ```json
  {
    "Count": "integer"
  }
  ```
- **400 Bad Request**: Invalid parameters or null request
- **401 Unauthorized**: Invalid QID authorization

## Business Logic
1. Validates user authorization against QID
2. Applies optional status filtering
3. Joins with Status table for category filtering
4. Counts matching registrations
5. Returns total count

## Query Optimization
- Uses AsNoTracking for read-only operations
- Efficient joins with Status table
- Count-only query
- No unnecessary data loading

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Unauthorized access logging

## Database Operations
- Joins Registration and Status tables
- Read-only transaction
- Count operation
- Status category filtering
