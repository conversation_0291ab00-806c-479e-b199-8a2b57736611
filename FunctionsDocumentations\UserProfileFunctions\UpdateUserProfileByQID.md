# Update User Profile By QID

## Overview
Updates an existing user profile in the EServices system. This endpoint handles modifications to communication preferences and consent settings while maintaining the profile's history.

## Endpoint
```http
PUT /userprofiles/{qId}
```

## Authorization
- Function-level authorization
- QID-based access control
- Claims validation

## Headers
- `X-RequestOrigin` (required): Source of the request

## Parameters

### Path Parameters
- `qId` (long, required): User QID

## Request Body
```json
{
  "qId": "long",
  "prefSMSLang": "string",
  "prefComMode": "string",
  "secondaryPhoneMobile": "string",
  "consent": "boolean"
}
```

### Request Fields
- `qId`: Unique identifier for the user
- `prefSMSLang`: Preferred language for SMS communications
- `prefComMode`: Preferred mode of communication
- `secondaryPhoneMobile`: Alternative contact number
- `consent`: User's consent status

## Response

### Success Response (200 OK)
Returns the updated user profile:
```json
{
  "qId": "long",
  "prefSMSLang": "string",
  "prefComMode": "string",
  "secondaryPhoneMobile": "string",
  "consent": "boolean",
  "createdAt": "string",
  "createSource": "string",
  "updatedAt": "string",
  "updateSource": "string"
}
```

### Error Responses
- 400 Bad Request: Invalid request body, QID not found, or missing required header
- 401 Unauthorized: Invalid or missing authorization
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Validates request origin
- Updates profile fields
- Records update timestamp
- Tracks update source
- Maintains creation metadata
- Implements proper error handling

## Business Logic
- Validates QID authorization
- Validates request origin
- Handles data persistence
- Manages update metadata
- Validates input data
- Maintains audit trail

## Data Validation
- Required fields validation
- QID existence check
- Request origin validation
- Input format validation
- Data consistency
- Consent validation

## Security Considerations
- Validates user authorization
- Implements function-level security
- Ensures data access control
- Validates request origin
- Protects sensitive data
- Proper error handling

## Performance Optimization
- Efficient database operations
- Proper connection handling
- Transaction management
- Resource cleanup
- Early validation
- Error prevention
