# GetMHDSListByHCNumber

## Overview
Retrieves a list of refill medicines that are due for the current cycle for both users and their dependents.

## Endpoint
- **Route**: `mhds/medicinelist`
- **Method**: POST
- **Authorization**: Function level

## Request Body
```json
{
  "HealthCardNumbers": ["string"]
}
```

## Response
- **200 OK**: Returns list of medicine information
  ```json
  [
    {
      "MedicineInformation": [
        {
          "MedicineName": "string",
          "PrescribedDate": "datetime",
          "SupplyDuration": "string",
          "LastDispenseDate": "datetime",
          "LastDispensedLocation": "string",
          "OrderByName": "string",
          "PrescriptionRefillDueDate": "datetime",
          "PrescriptionOrderId": "string"
        }
      ]
    }
  ]
  ```
- **204 No Content**: No medicines found
- **400 Bad Request**: Invalid request

## Business Logic
1. Processes list of health card numbers
2. For each health card number:
   - Retrieves medicine list from EDW (Enterprise Data Warehouse)
   - Retrieves existing MHDS requests from EService database
   - Filters out medicines already in process
3. Combines results for all health card numbers

## Environment Handling
- Supports both PROD and STG environments
- Uses different EDW tables based on environment
- Allows environment override with AssumeProd header

## Data Sources
1. EDW Database:
   - MedicationRefillDetailsProd
   - MedicationRefillDetailsStg
2. MHDS Database:
   - MHDSRequestDetails
   - MHDSRequestMedicineList

## Query Optimization
- Uses AsNoTracking for read-only operations
- Efficient filtering of duplicate medicines
- Batch processing of health card numbers

## Security Considerations
- Function-level authorization
- Input validation
- Logging of operations

## Error Handling
- Null/empty request validation
- Exception handling with default behavior
- Request URL logging
- Operation logging
