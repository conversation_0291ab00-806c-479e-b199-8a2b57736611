# Cancel Appointment Tests

## Overview

These tests verify the functionality of canceling appointments through the API. The test suite covers various cancellation scenarios and validation cases.

## Test Cases

### TestCancelAppointment_ValidRequest_ReturnsOk

Tests the successful cancellation of an existing appointment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCancelAppointment_ValidRequest_ReturnsOk()
{
    // Arrange
    var appointmentId = await CreateTestAppointment();
    var request = GetRestRequest($"appointments/{appointmentId}/cancel");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var cancelRequest = new CancelAppointmentRequest
    {
        Reason = "Schedule conflict"
    };
    request.AddJsonBody(cancelRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(OK, response.StatusCode);
    var cancelled = JsonConvert.DeserializeObject<AppointmentResponse>(response.Content!);
    NotNull(cancelled);
    Equal("Cancelled", cancelled.Status);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid appointment ID
  - Cancellation reason
- **Expected Output**: 
  - 200 OK response
  - Updated appointment status
- **Validation Points**:
  - Response status code
  - Appointment status update
  - Cancellation record

### TestCancelAppointment_PastAppointment_ReturnsBadRequest

Tests the API's handling of cancellation requests for past appointments.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestCancelAppointment_PastAppointment_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest($"appointments/{pastAppointmentId}/cancel");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var cancelRequest = new CancelAppointmentRequest
    {
        Reason = "Cannot attend"
    };
    request.AddJsonBody(cancelRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(BadRequest, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Past appointment ID
- **Expected Output**: 400 BadRequest response
- **Validation Points**:
  - Error status code
  - Error message validation

## Request/Response Models

### CancelAppointmentRequest
```csharp
public class CancelAppointmentRequest
{
    public string Reason { get; set; }
    public string? Notes { get; set; }
}
```

### CancellationResponse
```csharp
public class CancellationResponse
{
    public string AppointmentId { get; set; }
    public string Status { get; set; }
    public DateTime CancellationTime { get; set; }
    public string CancellationReason { get; set; }
}
```

## Cancellation Rules

1. **Timing Rules**
   - Must be future appointment
   - Must be within cancellation window
   - Must not be already cancelled

2. **Status Rules**
   - Cannot cancel completed appointments
   - Cannot cancel already cancelled appointments
   - Must be in cancellable status

3. **Authorization**
   - Must be appointment owner
   - Must have cancellation permission
   - Must be within time limits

## Error Scenarios

1. **Invalid Appointment**
   - Non-existent appointment
   - Already cancelled
   - Completed appointment

2. **Timing Issues**
   - Past appointments
   - Outside cancellation window
   - Too close to appointment time

3. **Authorization Errors**
   - Wrong user
   - Insufficient permissions
   - System restrictions

## Best Practices

### 1. Cancellation Validation
```csharp
// Validate appointment exists and can be cancelled
private bool CanCancelAppointment(Appointment appointment)
{
    if (appointment.Status == "Cancelled")
        return false;

    if (appointment.AppointmentDate <= DateTime.Now)
        return false;

    var hoursUntilAppointment = (appointment.AppointmentDate - DateTime.Now).TotalHours;
    return hoursUntilAppointment >= MinCancellationWindowHours;
}
```

### 2. Cancellation Recording
```csharp
// Record cancellation details
private void RecordCancellation(Appointment appointment, CancelAppointmentRequest request)
{
    appointment.Status = "Cancelled";
    appointment.CancellationTime = DateTime.UtcNow;
    appointment.CancellationReason = request.Reason;
    appointment.LastUpdated = DateTime.UtcNow;
    appointment.CancellationHistory.Add(new CancellationRecord
    {
        Timestamp = DateTime.UtcNow,
        Reason = request.Reason,
        Notes = request.Notes
    });
}
```

### 3. Notification Handling
```csharp
// Send cancellation notifications
private async Task SendCancellationNotifications(Appointment appointment)
{
    await NotifyUser(appointment.QId, "Appointment Cancelled",
        $"Your appointment on {appointment.AppointmentDate} has been cancelled.");
    
    await NotifyProvider(appointment.ProviderId, "Appointment Cancelled",
        $"Patient appointment on {appointment.AppointmentDate} has been cancelled.");
}
```

## Test Helper Methods

### 1. Test Appointment Creation
```csharp
private async Task<string> CreateTestAppointment()
{
    var createRequest = new BookingRequest
    {
        QId = GetMoqUser().UserQId.ToString(),
        PreferredDate = DateTime.Now.AddDays(1)
    };
    var response = await CreateAppointment(createRequest);
    return response.AppointmentId;
}
```

### 2. Validation Helpers
```csharp
private bool IsWithinCancellationWindow(DateTime appointmentDate)
{
    var hoursUntilAppointment = (appointmentDate - DateTime.Now).TotalHours;
    return hoursUntilAppointment >= MinCancellationWindowHours;
}
```

## Notes

- Tests cover immediate and scheduled cancellations
- Proper validation of cancellation rules
- Comprehensive error handling
- Notification system integration
- Detailed cancellation history tracking
- Proper cleanup of test data

## Recommendations

1. **Cancellation Windows**
   - Implement flexible cancellation windows
   - Consider service-specific rules
   - Allow for emergency cancellations

2. **Notification System**
   - Send confirmation emails
   - Update calendar invites
   - Notify relevant staff

3. **Resource Management**
   - Release reserved resources
   - Update availability
   - Handle waiting lists
