# GetFamilyPhysicianByPhysicianCode

## Overview
Retrieves detailed information about a family physician using their physician code.

## Endpoint
- **Route**: `familyphysicians/{phyCode}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **phyCode** (path, required): Physician's unique code identifier

## Response
- **200 OK**: Returns physician details
  ```json
  {
    "PhyCode": "string",
    "PhyNameEn": "string",
    "PhyNameAr": "string",
    "Title": "string",
    "ClinicalTitleEn": "string",
    "ClinicalTitleAr": "string",
    "Language": "string",
    "Qualification": "string",
    "TotalExp": "integer",
    "SpltyCode": "string",
    "Gender": "string",      // 0-Female, 1-Male, 2-Unknown
    "Image": "string",       // Base64 encoded image
    "HCntCode": "string",
    "OtherLanguage": "string",
    "OtherSpecialty": "string"
  }
  ```
- **204 No Content**: Physician not found
- **400 Bad Request**: Invalid physician code

## Business Logic
1. Extracts submitter claims from request
2. Queries MRD_Family_Physicians table
3. Maps physician data to response model
4. Handles gender code mapping (2→0, 0→2, other→1)
5. Converts physician photo to base64 if consent given

## Query Optimization
- Uses AsNoTracking() for read-only data
- Direct selection to response model
- Null coalescing for optional fields

## Data Security
- Photo only included if PHOTO_CONSENT is true
- Proper handling of null fields
