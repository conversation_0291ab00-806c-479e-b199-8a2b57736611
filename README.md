# E-Services Healthcare REST APIs

[![EServices RestAPI CI-CD](https://github.com/public-health-care-center-CORP/EservicesRESTapis/actions/workflows/eservices-ci-cd.yml/badge.svg)](https://github.com/public-health-care-center-CORP/EservicesRESTapis/actions/workflows/eservices-ci-cd.yml)

## Overview
A comprehensive REST API solution for managing various healthcare e-services. Built with .NET and Azure Functions. For a complete overview of our documentation structure, see our [Documentation Map](FunctionsDocumentations/DocumentationMap.md).

## Technical Stack
- **Framework**: .NET 8.0
- **Cloud Platform**: Azure Functions
- **Database**: SQL Server, Azure Storage
- **Authentication**: Azure AD B2C
- **Documentation**: OpenAPI (Swagger)

## Core Services

### Healthcare Management
1. 🏥 [Appointment Services](FunctionsDocumentations/AppointmentFunctions.md)
   - Schedule Management
     * [Create Appointment](FunctionsDocumentations/AppointmentFunctions/CreateAppointment.md)
     * [Update Appointment](FunctionsDocumentations/AppointmentFunctions/UpdateAppointmentByReqNumber.md)
     * [Delete Appointment](FunctionsDocumentations/AppointmentFunctions/DeleteAppointmentByReqNumber.md)
   - Status Tracking
     * [Check Progress](FunctionsDocumentations/AppointmentFunctions/CheckInProgressPMEAppointmentV1.md)
     * [Get Statistics](FunctionsDocumentations/AppointmentFunctions/GetAppointmentStatsByQId.md)
   - Appointment Lists
     * [Get Appointments](FunctionsDocumentations/AppointmentFunctions/GetAppointmentsListByQIdV1.md)
     * [Get Upcoming](FunctionsDocumentations/AppointmentFunctions/GetUpcomingConfirmedAppointmentByAppointmentId.md)

2. ✍️ [Assignment Services](FunctionsDocumentations/AssignmentFunctions.md)
   - Task Management
     * [Create Assignment](FunctionsDocumentations/AssignmentFunctions/CreateAssignment.md)
     * [Update Assignment](FunctionsDocumentations/AssignmentFunctions/UpdateAssignmentByReqNumber.md)
     * [Delete Assignment](FunctionsDocumentations/AssignmentFunctions/DeleteAssignmentByReqNumber.md)
   - Status Tracking
     * [Check Progress](FunctionsDocumentations/AssignmentFunctions/CheckInProcessAssignmentByQId.md)
     * [Get Statistics](FunctionsDocumentations/AssignmentFunctions/GetAssignmentStatsByQId.md)
   - Assignment Lists
     * [Get Assignments](FunctionsDocumentations/AssignmentFunctions/GetAssignmentListByQId.md)
     * [Get Details](FunctionsDocumentations/AssignmentFunctions/GetAssignmentItemByReqNumber.md)

3. 👨‍⚕️ [Family Physician Services](FunctionsDocumentations/FamilyPhysicianFunctions.md)
   - [Get Physician List](FunctionsDocumentations/FamilyPhysicianFunctions/GetFamilyPhysicianList.md)
   - [Get Physician Details](FunctionsDocumentations/FamilyPhysicianFunctions/GetFamilyPhysicianDetails.md)

### User Services
1. 👤 [User Profile Services](FunctionsDocumentations/UserProfileFunctions.md)
   - Profile Management
     * [Create Profile](FunctionsDocumentations/UserProfileFunctions/CreateUserProfile.md)
     * [Update Profile](FunctionsDocumentations/UserProfileFunctions/UpdateUserProfileByQID.md)
     * [Delete Profile](FunctionsDocumentations/UserProfileFunctions/DeleteUserProfileByQID.md)
   - Dependent Management
     * [Get Dependents](FunctionsDocumentations/UserProfileFunctions/GetUserAndDependentListByQID.md)
     * [Get MOI Dependents](FunctionsDocumentations/UserProfileFunctions/GetMOIDependents.md)
     * [Link Dependent](FunctionsDocumentations/UserProfileFunctions/LinkUserDependentByQID.md)
   - Profile Validation
     * [Validate QID](FunctionsDocumentations/UserProfileFunctions/ValidateQID.md)

2. 📎 [Comments And Attachments](FunctionsDocumentations/CommentAndAttachmentFunctions.md)
   - Document Management
     * [Create Attachment](FunctionsDocumentations/CommentAndAttachmentFunctions/CreateAttachment.md)
     * [Delete Attachment](FunctionsDocumentations/CommentAndAttachmentFunctions/DeleteAttachment.md)
   - Comment System
     * [Create Comment](FunctionsDocumentations/CommentAndAttachmentFunctions/CreateComment.md)
     * [Get Comments](FunctionsDocumentations/CommentAndAttachmentFunctions/GetComments.md)

### System Services
1. 📊 [EDW Dropdown Services](FunctionsDocumentations/EdwDropDownListFunctions.md)
   - [Get Dropdown List](FunctionsDocumentations/EdwDropDownListFunctions/GetDropdownList.md)
   - [Get Reference Data](FunctionsDocumentations/EdwDropDownListFunctions/GetReferenceData.md)

2. 🔧 [Miscellaneous Services](FunctionsDocumentations/MiscellaneousFunctions.md)
   - [Get System Health](FunctionsDocumentations/MiscellaneousFunctions/GetSystemHealth.md)
   - [Get Statistics](FunctionsDocumentations/MiscellaneousFunctions/GetStatistics.md)

## Technical Features

### Security
- QID-based authorization
- Claims-based authentication
- Secure data access
- Input validation
[Security Details](FunctionsDocumentations/UserProfileFunctions/ValidateQID.md)

### Performance
- Asynchronous operations
- Efficient query patterns
- Pagination support
- Optimized database access
[Implementation Examples](FunctionsDocumentations/MiscellaneousFunctions.md)

### Multilingual Support
- English/Arabic content
- Bilingual responses
- Language-specific filtering
- Proper text encoding
[Language Implementation](FunctionsDocumentations/EdwDropDownListFunctions.md)

### Data Management
- Multiple database contexts
- Efficient query execution
- Transaction support
- Data validation
[Data Handling Examples](FunctionsDocumentations/UserProfileFunctions.md)

## Getting Started

1. **Prerequisites**
   - .NET Core SDK
   - Azure Functions Tools
   - SQL Server
   - Azure Account (for deployment)

2. **Configuration**
   - Set up connection strings
   - Configure environment variables
   - Set up authentication
   - Configure logging

3. **Deployment**
   - Azure Functions deployment
   - Database setup
   - Environment configuration
   - Security setup

## Support

For technical support or queries:
- Documentation: [API Documentation Map](FunctionsDocumentations/DocumentationMap.md)
- Issue Tracking: GitHub Issues