using EServiceFunctions.Models.ReleaseOfInformation;
using EServiceFunctions.RequestResponseModels.ReleaseOfInformation;

using static EServiceFunctions.MapperModels.ReleaseOfInformationMapper;

namespace EServiceFunctions.Functions;

public class ReleaseOfInformationFunctions(
    IDbContextFactory<ReleaseContext> dbContextFactory,
    ILogger<ReleaseOfInformationFunctions> logger)
{
    private const string UnauthorizedQIdAccessTemplate = "Unauthorized ({QId}) Access!!!";
    private const string ErrorMessageTemplate = "Error: {Message}";

    #region GetReleaseListByQID

    /// <summary>
    /// Get the release list for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetReleaseListByQID")]
    [OpenApiOperation(operationId: "GetReleaseListByQID", tags: ["ReleaseOfInformation"],
        Summary = "Get Release List By Submitter's QID",
        Description = " Get the release list for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, ConditionallyApproved, ResubmitOriginalsRequested} or Archived {Approved, Cancelled, CancelledByEServ,  Rejected}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetReleaseListResponse>),
        Description =
            "Type of requests to return viz. Submitted, Cancelled, CancelledByEServ, ConditionallyApproved, Rejected, Approved, ResubmitOriginalsRequested.")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetReleaseListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "releases/submitter-qid/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Release List By Submitter's QId {QId}", qId);

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string releaseStatus = req.GetCleanedQueryString("status");
            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Release!
                .Join(dbContext.Status!, e => e.Status, s => s.Code, (release, status) => new { release, status })
                .AsQueryable().AsNoTracking();

            query = query.Where(item => item.release.SubmitterQId == qId);

            if (!string.IsNullOrWhiteSpace(releaseStatus))
            {
                query = query.Where(item =>
                    item.status.Category! == releaseStatus || item.release.Status! == releaseStatus);
            }

            var response = await query.Select(item => new GetReleaseListResponse
            {
                QId = item.release.QId,
                ReqNumber = item.release.ReqNumber,
                FNameEn = item.release.FNameEn,
                MNameEn = item.release.MNameEn,
                LNameEn = item.release.LNameEn,
                FNameAr = item.release.FNameAr,
                MNameAr = item.release.MNameAr,
                LNameAr = item.release.LNameAr,
                Status = item.release.StatusNavigation!.Code,
                StatusDescriptionEn = item.release.StatusNavigation.DescriptionEn,
                StatusDescriptionAr = item.release.StatusNavigation.DescriptionAr,
                SubmittedAt = item.release.SubmittedAt.ToUtcString()
            }).OrderBy(p => p.Status).Skip(skip).Take(take).ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for a given QID {QId} and Status {Status}", qId, releaseStatus);
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Release List ({Count}) By Submitter's QID {QId}", response.Count, qId);

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetReleaseStatsByQID

    /// <summary>
    /// Get the release stats for the given submitter's QId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">Submitter's QId</param>
    /// <returns></returns>
    [Function("GetReleaseStatsByQID")]
    [OpenApiOperation(operationId: "GetReleaseStatsByQID", tags: ["ReleaseOfInformation"],
        Summary = "Get Release Stats By Submitter's QID",
        Description = "Get the release stats for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, ConditionallyApproved, ResubmitOriginalsRequested} or Archived {Approved, Cancelled, CancelledByEServ,  Rejected}.",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetReleaseStatsResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetReleaseStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "releases/submitter-qid/{qId}/stats")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Getting Release Stats By Submitter's QID {QId}", qId);

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            string releaseStatus = req.GetCleanedQueryString("status");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Release!
                .Join(dbContext.Status!, e => e.Status, s => s.Code,
                    (release, status) => new
                    {
                        release, status
                    })
                .Where(item => item.release.SubmitterQId == qId)
                .AsQueryable()
                .AsNoTracking();

            if (!string.IsNullOrWhiteSpace(releaseStatus))
            {
                query = query.Where(item =>
                    item.status.Category! == releaseStatus || item.release.Status! == releaseStatus);
            }

            var response = await query.CountAsync();

            logger.LogInformation("Returning Releases count {Count} By Submitter's QID {QId} & Status {Status}", response, qId, releaseStatus);

            return await req.WriteOkResponseAsync(new GetReleaseStatsResponse
            {
                Count = response
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CheckInProcessReleaseByQID

    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("CheckInProcessReleaseByQID")]
    [OpenApiOperation(operationId: "CheckInProcessReleaseByQID", tags: ["ReleaseOfInformation"],
        Summary = "Check InProcess Release(s) By Requester's QID",
        Description = "Check inprocess Release(s) for the given requester's QId")]
    [OpenApiParameter("qId", Description = "Requester's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessReleaseResponse),
        Description = "Returns True Or False")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckInProcessReleaseByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "releases/{qId}/inprocess-validation")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Checking InProcess Release(s) By Requester's QID {QId}", qId);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var query = dbContext.Release
                ?.Join(dbContext.Status!, e => e.Status, s => s.Code, (release, status) => new { release, status })
                .AsQueryable().AsNoTracking();

            var isInProcessRequestExist = await query!.AnyAsync(item =>
                item.release.QId == qId && item.status.Category! == InProcess);

            logger.LogInformation("Returning InProcess Release(s) Validation Result {Result} By Requester's QID {QId}", isInProcessRequestExist, qId);

            return await req.WriteOkResponseAsync(new CheckInProcessReleaseResponse
            {
                IsInprocessExist = isInProcessRequestExist
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetReleaseItemByReqNumber

    /// <summary>
    /// Get release item for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Release Request Number</param>
    /// <returns></returns>
    [Function("GetReleaseItemByReqNumber")]
    [OpenApiOperation(operationId: "GetReleaseItemByReqNumber", tags: ["ReleaseOfInformation"],
        Summary = "Get Release Item By ReqNumber", Description = "Get release item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Release Request Number", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetReleaseItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetReleaseItemByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "releases/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();
            if (string.IsNullOrWhiteSpace(reqNumber))
            {
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            logger.LogInformation("Getting Release Item By Request Number {ReqNumber}", reqNumber);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.Release?.AsNoTracking().Include(s => s.StatusNavigation)
                .FirstOrDefaultAsync(item => item.ReqNumber! == reqNumber)!;

            if (response == null)
            {
                logger.LogInformation("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var getResponse = MapReleaseToGetReleaseItemResponse(response);

            getResponse.Status = response.StatusNavigation?.Code;
            getResponse.StatusDescriptionEn = response.StatusNavigation?.DescriptionEn;
            getResponse.StatusDescriptionAr = response.StatusNavigation?.DescriptionAr;

            logger.LogInformation("Returning Release Item By Request Number {ReqNumber}", getResponse.ReqNumber);

            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CreateRelease

    /// <summary>
    /// Create new release request
    /// </summary>
    /// <param name="req">New Release Create Model</param>
    /// <returns></returns>
    [Function("CreateRelease")]
    [OpenApiOperation(operationId: "CreateRelease", tags: ["ReleaseOfInformation"], Summary = "Create Release",
        Description = "Create new release request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateReleaseRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(Release))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateRelease(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "releases")] HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Creating a Release");

            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateReleaseRequest>(requestBody);

            long submitterQId = request?.SubmitterQId ?? 0;

            if (!req.IsAuthorized(submitterQId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, submitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }

            Release create = MapCreateUpdateReleaseRequestToRelease(request);
            create.ReqNumber = RandomPassword();
            create.SubmittedAt = GetCurrentTime();
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;
            create.Status = Saved;

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            try
            {
                await dbContext.Release!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogCritical(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError, "DB exception thrown while create");
            }

            logger.LogInformation("Created A New Release With Request Number {ReqNumber}", create.ReqNumber);

            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UpdateReleaseByReqNumber

    /// <summary>
    /// Update release for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Release Request Number </param>
    /// <returns></returns>
    [Function("UpdateReleaseByReqNumber")]
    [OpenApiOperation(operationId: "UpdateReleaseByReqNumber", tags: ["ReleaseOfInformation"],
        Summary = "Update Release By ReqNumber", Description = "Update release item for the given request number")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateReleaseRequest))]
    [OpenApiParameter("reqNumber", Description = "Release Request Number", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> UpdateReleaseByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "releases/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            logger.LogInformation("Updating a release with request number {ReqNumber}", reqNumber);

            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (string.IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateReleaseRequest>(requestBody);
            long submitterQId = request?.SubmitterQId ?? 0;

            if (!req.IsAuthorized(submitterQId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, submitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            if (request == null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            Release? response = await dbContext.Release?.SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber)!;

            if (response == null)
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            var mapperResponse = MapCreateUpdateReleaseRequestToRelease(request);

            mapperResponse.Id = response.Id;
            mapperResponse.ReqNumber = response.ReqNumber;
            mapperResponse.Status = response.Status;
            mapperResponse.StatusInternal = response.StatusInternal;
            mapperResponse.SN = response.SN;
            mapperResponse.SubmittedAt = GetCurrentTime();
            mapperResponse.CreatedAt = response.CreatedAt;
            mapperResponse.UpdatedAt = GetCurrentTime();
            mapperResponse.CreateSource = response.CreateSource;
            mapperResponse.UpdateSource = requestOriginSource;
            dbContext.Entry(response).CurrentValues.SetValues(mapperResponse);

            try
            {
                if (dbContext.Entry(response).State == EntityState.Modified)
                {
                    int isUpdated = await dbContext.SaveChangesAsync();
                    if (isUpdated > 0)
                    {
                        logger.LogInformation("Updated a new Release with a request number {ReqNumber}", response.ReqNumber);
                        return await req.WriteOkResponseAsync(mapperResponse);
                    }

                    logger.LogWarning("Something went wrong while updating the release with request number {ReqNumber}", reqNumber);
                }
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, ErrorMessageTemplate, e.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError, "DB exception thrown while update");
            }

            return await req.WriteErrorResponseAsync(BadRequest, "Something went wrong, please try again later.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region DeleteReleaseByReqNumber

    /// <summary>
    /// Delete release for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Release Request Number </param>
    /// <returns></returns>
    [Function("DeleteReleaseByReqNumber")]
    [OpenApiOperation(operationId: "DeleteReleaseByReqNumber", tags: ["ReleaseOfInformation"],
        Summary = "Delete Release By ReqNumber", Description = "Delete release item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Release Request Number", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteReleaseByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "releases/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();

            logger.LogInformation("Deleting a release with request number {ReqNumber}", reqNumber);

            int isDeleted;
            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var response = await dbContext.Release?.SingleOrDefaultAsync(item => item.ReqNumber! == reqNumber)!;

            if (response == null)
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            try
            {
                dbContext.Release.Remove(response);
                isDeleted = await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, ErrorMessageTemplate, e.InnerException?.Message);
                return await req.WriteErrorResponseAsync(BadRequest, "DB exception thrown while deleting");
            }

            logger.LogWarning("Deleted Release By Request Number {ReqNumber}", response.ReqNumber);

            if (isDeleted != 0)
            {
                return await req.WriteOkResponseAsync("Deleted Successfully");
            }

            logger.LogError("Something went wrong while deleting the release with request number {ReqNumber}", reqNumber);
            return await req.WriteErrorResponseAsync(InternalServerError, "DB exception thrown while deleting");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}