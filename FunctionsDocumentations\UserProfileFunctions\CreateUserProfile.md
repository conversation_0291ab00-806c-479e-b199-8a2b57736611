# Create User Profile

## Overview
Creates a new user profile in the EServices system. This endpoint handles the initial profile creation with communication preferences and consent settings.

## Endpoint
```http
POST /userprofiles
```

## Authorization
- Function-level authorization
- QID-based access control
- Claims validation

## Headers
- `X-RequestOrigin` (required): Source of the request

## Request Body
```json
{
  "qId": "long",
  "prefSMSLang": "string",
  "prefComMode": "string",
  "secondaryPhoneMobile": "string",
  "consent": "boolean"
}
```

### Request Fields
- `qId`: Unique identifier for the user
- `prefSMSLang`: Preferred language for SMS communications
- `prefComMode`: Preferred mode of communication
- `secondaryPhoneMobile`: Alternative contact number
- `consent`: User's consent status

## Response

### Success Response (201 Created)
Returns the created user profile:
```json
{
  "qId": "long",
  "prefSMSLang": "string",
  "prefComMode": "string",
  "secondaryPhoneMobile": "string",
  "consent": "boolean",
  "createdAt": "string",
  "createSource": "string"
}
```

### Error Responses
- 400 Bad Request: Invalid request body or missing required header
- 401 Unauthorized: Invalid or missing authorization
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Validates request origin
- Creates user profile record
- Records creation timestamp
- Tracks creation source
- Implements proper error handling
- Uses Entity Framework Core

## Business Logic
- Validates QID authorization
- Validates request origin
- Handles data persistence
- Manages creation metadata
- Validates input data
- Maintains audit trail

## Data Validation
- Required fields validation
- QID existence check
- Request origin validation
- Input format validation
- Duplicate prevention
- Consent validation

## Security Considerations
- Validates user authorization
- Implements function-level security
- Ensures data access control
- Validates request origin
- Protects sensitive data
- Proper error handling

## Performance Optimization
- Efficient database operations
- Proper connection handling
- Transaction management
- Resource cleanup
- Early validation
- Error prevention
