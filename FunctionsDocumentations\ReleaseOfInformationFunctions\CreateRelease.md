# Create Release

## Overview
Creates a new release request in the system.

## Endpoint
```http
POST /releases
```

## Authorization
- Function-level authorization
- QID-based access control
- Requires valid submitter QID

## Headers
- `X-RequestOrigin` (required): Source of the request

## Request Body
```json
{
  "submitterQId": "long",
  "qId": "long",
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string"
  // Additional fields based on release request model
}
```

## Response

### Success Response (201 Created)
Returns the created release request with generated request number:
```json
{
  "reqNumber": "string",
  "submitterQId": "long",
  "qId": "long",
  "status": "string",
  "submittedAt": "string (UTC)",
  "createdAt": "string (UTC)",
  "createSource": "string"
  // Additional fields from request
}
```

### Error Responses
- 400 Bad Request: Invalid request body or missing required fields
- 401 Unauthorized: Invalid QID or unauthorized access
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Generates unique request number
- Sets initial status as "Saved"
- Records creation timestamp and source
- Implements proper database transaction handling
- Supports multilingual content (English/Arabic)

## Business Logic
- Initial Status: Saved (Draft mode)
- Auto-generates request number
- Records submission and creation timestamps
- Tracks request origin source
- Validates submitter authorization

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control
- Validates request origin
- Sanitizes input data

## Performance Optimization
- Efficient database transaction handling
- Proper error handling and logging
- Optimized data insertion
- Secure request number generation
