# CreateAttachment

## Overview
Creates a new attachment with JSON request body.

## Endpoint
- **Route**: `attachments`
- **Method**: POST
- **Authorization**: Function level

## Headers
- `X-RequestOrigin`: Required header indicating request origin

## Request Body
```json
{
  "AttachmentName": "string",
  "AttachmentType": "string",
  "AttachmentData": "base64string",
  "SubmitterQId": "long"
}
```

## Responses
- **201 Created**: Returns created attachment
- **400 Bad Request**: 
  - Invalid request body
  - Invalid file type
  - File size exceeds limit
- **401 Unauthorized**: QID mismatch
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates request origin header
2. Validates request body format
3. Checks QID authorization
4. Validates file extension and size
5. Creates attachment record
6. Returns created attachment

## Validation Rules
- Maximum file size: 5MB
- File extension must be valid
- Request origin must be provided
- QID authorization required

## Dependencies
- CommentAndAttachmentContext
- Authorization middleware
- File validation utilities
