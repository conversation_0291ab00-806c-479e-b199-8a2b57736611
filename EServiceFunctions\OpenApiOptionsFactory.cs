﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Configurations;

using static Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Configurations.DefaultOpenApiConfigurationOptions;
using static Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums.OpenApiVersionType;

namespace EServiceFunctions;

public static class OpenApiOptionsFactory
{
    public static IOpenApiConfigurationOptions CreateOptions()
    {
        var options = new OpenApiConfigurationOptions
        {
            Info = new OpenApiInfo
            {
                Version = nameof(V3)
            },
            Servers = GetHostNames(), 
            OpenApiVersion = V3, 
            IncludeRequestingHostName = true
        };

        return options;
    }
}