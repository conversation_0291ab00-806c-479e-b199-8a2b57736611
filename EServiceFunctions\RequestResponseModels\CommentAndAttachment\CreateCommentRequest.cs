﻿namespace EServiceFunctions.RequestResponseModels.CommentAndAttachment;

[ExcludeFromCodeCoverage]
public class CreateCommentRequest
{
    [JsonRequired]
    public string? ReqNumber { get; set; }
    public string? Action { get; set; }
    public string? CommentText { get; set; }
    public string? CommentType { get; set; }
    public string? CommenterName { get; set; }
    public long? SubmitterQId { get; set; }
    public bool? DisplayToSubmitter { get; set; }
    public DateTime? CreatedAt { get; set; } = GetCurrentTime();
}