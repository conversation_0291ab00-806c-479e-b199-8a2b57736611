﻿namespace TestEServiceFunctions;

[Collection("ReleaseItemFunctions")]
public class TestReleaseItemFunctions(IRestLibrary restLibrary, ITestOutputHelper testOutputHelper)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetReleaseItemList()
    {
        // Arrange
        var request = GetRestRequest("releaseitems");

        // Act
        var response = await _client.GetAsync<List<GetReleaseItemListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count > 0);
        Contains(response, x => x.ItemCode == "RI001");
        Contains(response, x => x.ItemNameEn == "Lab results");
        Contains(response, x => x.ItemNameEn == "Radiology Reports");
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetReleaseItemList_InvalidItemCode()
    {
        // Arrange
        var request = GetRestRequest("releaseitems");
        request.AddQueryParameter("itemCode", "invalidItemCode");

        // Act
        var response = await _client.GetAsync(request);

        // Assert
        NotNull(response);
        True(response.StatusCode == NoContent);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetReleaseItemList_SkipMoreThanTotalItems()
    {
        // Arrange
        var request = GetRestRequest("releaseitems");
        request.AddQueryParameter("skip", 1000);

        // Act
        var response = await _client.GetAsync(request);

        // Assert
        NotNull(response);
        True(response.StatusCode == NoContent);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetReleaseItemList_TakeLessThanTotalItems()
    {
        // Arrange
        var request = GetRestRequest("releaseitems");
        request.AddQueryParameter("take", 5);

        // Act
        var response = await _client.GetAsync<List<GetReleaseItemListResponse>>(request);

        // Assert
        NotNull(response);
        True(response.Count == 5);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetReleaseItemList_NegativeTakeValue()
    {
        // Arrange
        var request = GetRestRequest("releaseitems");
        request.AddQueryParameter("take", -5);

        // Act
        var response = await _client.GetAsync<List<GetReleaseItemListResponse>>(request);

        // Assert
        NotNull(response);
        True(response.Count == 5);
    }
}