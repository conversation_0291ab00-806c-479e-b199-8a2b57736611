﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.HealthCenter;

[Table("MRD_Facility")]
public class HealthCenter
{
    [Key]
    [Column("FACILITY_CODE")]
    public string? HCntCode { get; set; }
    [Column("FACILITY_NAME_EN")]
    public string? HCntNameEn { get; set; }
    [Column("FACILITY_NAME_ARB")]
    public string? HCntNameAr { get; set; }
    public string? ORGANISATION { get; set; }
    public string? FACILITY_STATUS { get; set; }
    public string? FACILITY_TYPE { get; set; }
    public string? REGION_EN { get; set; }
    public string? REGION_ARB { get; set; }
    public string? OPENING_YEAR { get; set; }
    public string? CLOSING_YEAR { get; set; }
    public string? WORKING_HOURS { get; set; }
    public string? WORKING_DAYS { get; set; }
    public string? AMBULATORY_PROVISION { get; set; }
    public string? RSC_HC_STATUS { get; set; }
    public int? COVID_HC { get; set; }
    public string? ATTRIB_2 { get; set; }
    public string? ATTRIB_3 { get; set; }
    public string? ATTRIB_4 { get; set; }
    public DateTime? LAST_UPDATED_DATE { get; set; }
}