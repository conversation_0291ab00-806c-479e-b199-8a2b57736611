# Update User Profile Tests

## Test Cases

### 1. TestUpdateUserProfileByQId_Should_Return_OK

Tests successful update of user profile by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateUserProfileByQId_Should_Return_OK()
{
    // Arrange
    const long qId = 22222222272;
    var request = GetRestRequest("UserProfiles/{qId}");
    request.AddUrlSegment("qId", qId);
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateUserProfileByQID-123");
    request.AddOrUpdateHeader(JwtClaimsQId, qId);

    var requestBody = new Faker<CreateUpdateUserProfileRequest>()
        .RuleFor(x => x.QId, GetMoqUser().UserQId)
        .RuleFor(x => x.PrefSMSLang, f => f.PickRandom("0", "1"))
        .RuleFor(x => x.PrefComMode, f => f.PickRandom("0,1", "0,1", "0,1"))
        .RuleFor(x => x.SecondaryPhoneMobile, f => f.Phone.PhoneNumber())
        .RuleFor(x => x.Consent, true)
        .Generate();

    request.AddJsonBody(requestBody);

    // Act
    var response = await _client.PutAsync(request);
    response.ThrowIfNull();

    // Assert
    True(OK == response.StatusCode);
}
```

### 2. TestUpdateUserProfileByQIdWithInvalidQId

Tests update with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestUpdateUserProfileByQIdWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("UserProfiles/{qId}");
    request.AddUrlSegment("qId", 2323232323211);
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateUserProfileByQID");
    request.Method = Method.Put;

    var requestBody = new Faker<CreateUpdateUserProfileRequest>()
        .RuleFor(x => x.QId, GetMoqUser().UserQId)
        .RuleFor(x => x.PrefSMSLang, f => f.PickRandom("0", "1"))
        .RuleFor(x => x.PrefComMode, f => f.PickRandom("0", "1", "0,1"))
        .RuleFor(x => x.SecondaryPhoneMobile, f => f.Phone.PhoneNumber())
        .RuleFor(x => x.Consent, true)
        .Generate();

    request.AddJsonBody(requestBody);

    // Act
    var response = await _client.ExecuteAsync(request);

    // Assert
    False(response is null);
}
```

## Request Details

### Endpoint
```
PUT UserProfiles/{qId}
```

### Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: "UnitTest-UpdateUserProfileByQID"

### URL Parameters
- `qId`: QID of profile to update

## Request Model

### CreateUpdateUserProfileRequest
```csharp
public class CreateUpdateUserProfileRequest
{
    public long QId { get; set; }
    public string PrefSMSLang { get; set; }
    public string PrefComMode { get; set; }
    public string SecondaryPhoneMobile { get; set; }
    public bool Consent { get; set; }
}
```

## Test Data

### Success Case
- QID: 22222222272
- Generated test data:
  * SMS Language preference
  * Communication mode
  * Secondary phone
  * Consent status

### Error Case
- QID: 2323232323211
- Generated test data
- Expected: Response not null

## Validation Rules

### Success Case Validation
1. Response not null
2. Status code is OK

### Error Case Validation
1. Response not null

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - QID mismatch

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - Unauthorized update

3. **Invalid Data**
   - Missing required fields
   - Invalid field formats
   - Data validation failures

## Notes

1. **Test Data Generation**
   - Uses Faker library
   - Consistent test data
   - Valid field formats

2. **Request Format**
   - JSON body
   - Required headers
   - URL parameters

3. **Performance**
   - Data generation overhead
   - Response validation
   - Error handling
