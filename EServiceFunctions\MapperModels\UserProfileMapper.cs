﻿using EServiceFunctions.RequestResponseModels.UserProfile;

using UserProfileFromModels = EServiceFunctions.Models.UserProfile.UserProfile;
using UserProfileFromRequestResponseModels = EServiceFunctions.RequestResponseModels.UserProfile.UserProfile;

namespace EServiceFunctions.MapperModels;

public static class UserProfileMapper
{
    public static GetUserProfileResponse MapUserProfileToGetUserProfileResponse(UserProfileFromRequestResponseModels userProfile)
    {
        return new GetUserProfileResponse
        {
            QId = userProfile.QId,
            QIdExpiryDt = userProfile.QIdExpiryDt,
            FNameEn = userProfile.FNameEn,
            MNameEn = userProfile.MNameEn,
            LNameEn = userProfile.LNameEn,
            FNameAr = userProfile.FNameAr,
            MNameAr = userProfile.MNameAr,
            LNameAr = userProfile.LNameAr,
            Dob = userProfile.Dob,
            PrefSMSLang = userProfile.PrefSMSLang,
            PrefComMode = userProfile.PrefComMode,
            NationalityCode = userProfile.NationalityCode,
            NationalityEn = userProfile.NationalityEn,
            NationalityAr = userProfile.NationalityAr,
            GenderCode = userProfile.GenderCode,
            GenderEn = userProfile.GenderEn,
            GenderAr = userProfile.GenderAr,
            VisaCode = userProfile.VisaCode,
            VisaDescriptionEn = userProfile.VisaDescriptionEn,
            VisaDescriptionAr = userProfile.VisaDescriptionAr,
            OccupationCode = userProfile.OccupationCode,
            OccupationDescriptionEn = userProfile.OccupationDescriptionEn,
            OccupationDescriptionAr = userProfile.OccupationDescriptionAr,
            PhoneMobile = userProfile.PhoneMobile,
            SecondaryPhoneMobile = userProfile.SecondaryPhoneMobile,
            AssignedHealthCenter = userProfile.AssignedHealthCenter,
            AssignedHealthCenterEn = userProfile.AssignedHealthCenterEn,
            AssignedHealthCenterAr = userProfile.AssignedHealthCenterAr,
            HcNumber = userProfile.HcNumber,
            HcExpiryDate = userProfile.HcExpiryDate,
            GisAddressStreet = userProfile.GisAddressStreet,
            GisAddressBuilding = userProfile.GisAddressBuilding,
            GisAddressZone = userProfile.GisAddressZone,
            GisAddressUnit = userProfile.GisAddressUnit,
            GisAddressUpdatedAt = userProfile.GisAddressUpdatedAt,
            PhysicianId = userProfile.PhysicianId,
            PhysicianFullNameEn = userProfile.PhysicianFullNameEn,
            PhysicianFullNameAr = userProfile.PhysicianFullNameAr,
            Consent = userProfile.Consent,
            IsStaff = userProfile.IsStaff,
            IsCitizen = userProfile.IsCitizen,
            IsOGCC = userProfile.IsOGCC
        };
    }

    public static UserProfileFromModels MapCreateUpdateUserProfileRequestToUserProfile(CreateUpdateUserProfileRequest createUpdateUserProfileRequest)
    {
        return new UserProfileFromModels
        {
            QId = createUpdateUserProfileRequest.QId,
            PrefSMSLang = createUpdateUserProfileRequest.PrefSMSLang,
            PrefComMode = createUpdateUserProfileRequest.PrefComMode,
            SecondaryPhoneMobile = createUpdateUserProfileRequest.SecondaryPhoneMobile,
            Consent = createUpdateUserProfileRequest.Consent
        };
    }
}