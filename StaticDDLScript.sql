﻿--WI#808

-- Changing gender code for female: current code 0, ISO code as per EDW 2
UPDATE Appointment
SET Gender = 'Female'
WHERE Gender = '0' -- Ch
UPDATE Registration
SET Gender = 'Female'
WHERE Gender = '0'

-- Changing gender code for Unknown: current code 2, ISO code as per EDW 0
UPDATE Appointment
SET Gender = 'Unknown'
WHERE Gender = '2'
UPDATE Registration
SET Gender = 'Unknown'
WHERE Gender = '2'

UPDATE Appointment
SET Gender = '2'
WHERE Gender = 'Female'
UPDATE Registration
SET Gender = '2'
WHERE Gender = 'Female'

UPDATE Appointment
SET Gender = '0'
WHERE Gender = 'Unknown'
UPDATE Registration
SET Gender = '0'
WHERE Gender = 'Unknown'


-------------------------------------------------------------------------------------------------------------------------------------

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Gender'
             AND type = 'U')
    DROP TABLE [Gender]; -- MD
GO
CREATE TABLE [Gender]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255)
)
GO

INSERT [dbo].[Gender] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (0, N'Not Known', N'غير معروف')
GO
INSERT [dbo].[Gender] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (1, N'Male', N'الذكر')
GO
INSERT [dbo].[Gender] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (2, N'Female', N'أنثى')
GO
INSERT [dbo].[Gender] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (9, N'Not Applicable', N'غير قابل للتطبيق')
GO

------------------------------------------------------------------------------------------------------------------------------------

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Relationship'
             AND type = 'U')
    DROP TABLE [Relationship]; --MD
GO
CREATE TABLE [Relationship]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255),
)
GO

INSERT [dbo].[Relationship] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (0, N'Father', N'والد')
GO
INSERT [dbo].[Relationship] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (1, N'Mother', N'والدة')
GO
INSERT [dbo].[Relationship] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (2, N'Son', N'ابن')
GO
INSERT [dbo].[Relationship] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (3, N'Daughter', N'ابنة')
GO
INSERT [dbo].[Relationship] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (4, N'Wife', N'زوجة')
GO
INSERT [dbo].[Relationship] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (5, N'Husband', N'زوج')
GO
INSERT [dbo].[Relationship] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (6, N'Other', N'اخرى')
GO

------------------------------------------------------------------------------------------------------------------------------------


IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'MaritalStatus'
             AND type = 'U')
    DROP TABLE [MaritalStatus]; --MD
GO
CREATE TABLE [MaritalStatus]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255),
)
GO

INSERT [dbo].[MaritalStatus] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (0, N'Married', N'متزوج')
GO
INSERT [dbo].[MaritalStatus] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (1, N'Single', N'اعزب')
GO
INSERT [dbo].[MaritalStatus] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (2, N'Widowed', N'ارمل')
GO

------------------------------------------------------------------------------------------------------------------------------------


IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Language'
             AND type = 'U')
    DROP TABLE [Language]; --MD
GO
CREATE TABLE [Language]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255),
)
GO

INSERT [dbo].[Language] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (0, N'Arabic', N'عربي')
GO
INSERT [dbo].[Language] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (1, N'English', N'انجليزي')
GO
INSERT [dbo].[Language] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (2, N'Others', N'اخرى')
GO

------------------------------------------------------------------------------------------------------------------------------------

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Workdays'
             AND type = 'U')
    DROP TABLE [Workdays]; --MD
GO
CREATE TABLE [Workdays]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255),
)
GO

INSERT [dbo].[Workdays] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (1, N'SUN', N'الاحد')
GO
INSERT [dbo].[Workdays] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (2, N'MON', N'الاثنين')
GO
INSERT [dbo].[Workdays] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (3, N'TUE', N'الثلاثاء')
GO
INSERT [dbo].[Workdays] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (4, N'WED', N'الاربعاء')
GO
INSERT [dbo].[Workdays] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (5, N'THU', N'الخميس')
GO
INSERT [dbo].[Workdays] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (6, N'FRI', N'الجمعة')
GO
INSERT [dbo].[Workdays] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (7, N'SAT', N'السبت')
GO

------------------------------------------------------------------------------------------------------------------------------------

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'ModeOfCommunication'
             AND type = 'U')
    DROP TABLE [ModeOfCommunication]; --MD
GO
CREATE TABLE [ModeOfCommunication]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255),
)
GO

INSERT [dbo].[ModeOfCommunication] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (0, N'SMS ', N'رسالة نصية')
GO
INSERT [dbo].[ModeOfCommunication] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (1, N'Email', N'بريد الكتروني')
GO
INSERT [dbo].[ModeOfCommunication] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (2, N'Whatsapp ', N'واتس اب')
GO

------------------------------------------------------------------------------------------------------------------------------------

ALTER TABLE [Appointment]
    ADD FOREIGN KEY ([Gender]) REFERENCES [Gender] (Code);


ALTER TABLE [Registration]
    ADD FOREIGN KEY ([Gender]) REFERENCES [Gender] (Code);


ALTER TABLE [Registration]
    ADD FOREIGN KEY ([MaritalStatus]) REFERENCES [MaritalStatus] (Code);


ALTER TABLE [Registration]
    ADD FOREIGN KEY ([PrefSMSLang]) REFERENCES [Language] (Code);
GO


---Check the below command once with A.Salam

ALTER TABLE [Registration]
    ADD FOREIGN KEY ([PrefComMode]) REFERENCES [ModeOfCommunication] (Code);
GO

ALTER TABLE [Release]
    ADD FOREIGN KEY ([AuthorizedPersonRelation]) REFERENCES [Relationship] (Code);
GO


ALTER TABLE [UserProfile]
    ADD FOREIGN KEY ([PrefSMSLang]) REFERENCES [Language] (Code);
GO



-------------------------------------------------------------------------------------------------------------------------------------
--Notes:
--We're not using FamilyPhysician	table from Azure EService database
--We're not using HealthCenter		table from Azure EService database
--We're not using Clinic			table from Azure EService database
--We're not using ClinicShift		table from Azure EService database

--Shift & Appointment table mapping already exist


-----------------------------------------------------------------------------------------------------------------------------------
/*Added IsActive flag to all the Master Data tables.*/


Alter Table Gender
    Add IsActive BIT

Select *
from Gender

Update Gender
Set IsActive = 1

Alter Table Relationship
    Add IsActive BIT

Select *
from Relationship

Update Relationship
Set IsActive = 1

Alter Table MaritalStatus
    Add IsActive BIT

Select *
from MaritalStatus

Update MaritalStatus
Set IsActive = 1

Alter Table Language
    Add IsActive BIT

Select *
from Language

Update Language
Set IsActive = 1

Alter Table Workdays
    Add IsActive BIT

Select *
from Workdays

Update Workdays
Set IsActive = 1

Alter Table ModeOfCommunication
    Add IsActive BIT

Select *
from ModeOfCommunication

Update ModeOfCommunication
Set IsActive = 1

Update ModeOfCommunication
Set IsActive = 0
where code = 2
  and DescriptionEn = 'Whatsapp'

Alter Table AppointmentRequestType
    Add IsActive BIT

Select *
from AppointmentRequestType

Update AppointmentRequestType
Set IsActive = 1

Alter Table Shift
    Add IsActive BIT

Select *
from Shift

Update Shift
Set IsActive = 1
-----------------------------------------------------------------------------------------------------------------------------------
/****** Object:  StoredProcedure [dbo].[SP_GetDropDownListCodes]    Script Date: 1/27/2021 11:14:14 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[SP_GetAllStaticDropDownList]
AS
BEGIN
    -- declare table variable
    DECLARE @DropDownListCodesTable TABLE
                                    (
                                        [Category]      nvarchar(50)  NOT NULL,
                                        [Code]          nvarchar(255) NOT NULL,
                                        [DescriptionEn] nvarchar(255) NOT NULL,
                                        [DescriptionAr] nvarchar(255) NOT NULL
                                    );


    INSERT INTO @DropDownListCodesTable
    SELECT 'Gender' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [Gender]
    WHERE [IsActive] = 1
    UNION ALL
    SELECT 'Language' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [Language]
    WHERE [IsActive] = 1
    UNION ALL
    SELECT 'MaritalStatus' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [MaritalStatus]
    WHERE [IsActive] = 1
    UNION ALL
    SELECT 'ModeOfCommunication' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [ModeOfCommunication]
    WHERE [IsActive] = 1
    UNION ALL
    SELECT 'Relationship' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [Relationship]
    WHERE [IsActive] = 1
    UNION ALL
    SELECT 'Workdays' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [Workdays]
    WHERE [IsActive] = 1
    UNION ALL
    SELECT 'Shift' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [Shift]
    WHERE [IsActive] = 1
    UNION ALL
    SELECT 'AppointmentRequestType' as [Category], [Code], [DescriptionEn], [DescriptionAr]
    FROM [AppointmentRequestType]
    WHERE [IsActive] = 1

    SELECT [Category], [Code], [DescriptionEn], [DescriptionAr] FROM @DropDownListCodesTable

END
GO


-- This is a SQL stored procedure named "SP_GetAllStaticDropDownList". It creates a table variable named "@DropDownListCodesTable", which has four columns: "Category", "Code", "DescriptionEn", and "DescriptionAr".
-- The procedure then inserts data into this table variable by selecting data from several tables, including "Gender", "Language", "MaritalStatus", "ModeOfCommunication", "Relationship", "Workdays", "Shift", and "AppointmentRequestType". 
-- In each case, the procedure selects the columns "Code", "DescriptionEn", and "DescriptionAr" where the "IsActive" column is equal to 1, and assigns a category value to the "Category" column based on the name of the table.
-- Finally, the procedure selects all the rows from the "@DropDownListCodesTable" variable and returns them as a result set, which includes the columns "Category", "Code", "DescriptionEn", and "DescriptionAr".
-- Overall, this stored procedure is designed to retrieve and return data for several static dropdown lists by consolidating the data from multiple tables into a single result set, the procedure makes it easier for developers to manage and update the dropdown lists as needed.

----------------------------------------------------------------------------------------------------------------------------------------------------