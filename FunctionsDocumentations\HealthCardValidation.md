# Health Card Number Validation

## Overview
This document describes the mandatory Health Card number validation implemented across all eService endpoints (Appointment, Assignment, Transfer, and MHDS) to ensure data integrity and eliminate incomplete submissions.

## Scope
The validation applies to the following endpoints:
- **Appointment**: CreateAppointment, UpdateAppointmentByReqNumber
- **Assignment**: CreateAssignment
- **Transfer**: CreateTransfer, UpdateTransferByReqNumber
- **MHDS**: CreateMhdsRequestAsync

## Validation Rules

### Health Card Number Requirements
1. **Presence**: Health Card number must be present in the request payload
2. **Format**: Must be a valid alphanumeric string
3. **Length**: Between 5-20 characters
4. **Characters**: Only letters and digits are allowed (no special characters or spaces)

### Field Names by Service
- **Appointment**: `HcNumber` (string?)
- **Assignment**: `HCNumber` (string?)
- **Transfer**: `HCNumber` (string?)
- **MHDS**: `HCNumber` (string?)

## Error Responses

### Missing Health Card Number
```json
{
  "code": 400,
  "message": "Health Card No is missing for QID {QID} and request name {RequestType}"
}
```

### Invalid Health Card Format
```json
{
  "code": 400,
  "message": "Health Card No format is invalid for QID {QID} and request name {RequestType}"
}
```

Where:
- `{QID}`: The Qatar ID of the user from the request
- `{RequestType}`: The type of request (Appointment, Assignment, Transfer, MHDS)

## Implementation Details

### Validation Method
The validation is implemented using a shared utility method:
```csharp
public static async Task<HealthCardValidationResult> ValidateHealthCardNumberAsync<T>(
    string? healthCardNumber,
    long qId,
    string requestType,
    HttpRequestData req,
    ILogger<T> logger)
```

### Validation Flow
1. Check if Health Card number is null, empty, or whitespace
2. If missing, log failure and return error response
3. Validate format using `IsValidHealthCardFormat()` method
4. If invalid format, log failure and return error response
5. If valid, return success result

### Logging
All validation failures are logged with structured logging including:
- **Timestamp**: ISO 8601 format (UTC)
- **QID**: Qatar ID of the user
- **Request Type**: Type of eService request
- **Source**: Request origin from `X-RequestOrigin` header
- **URL**: Request URL for context
- **Health Card Number**: (Only for format validation failures)

### Log Format
```
Health Card validation failed - Missing: QID={QId}, RequestType={RequestType}, Source={Source}, Timestamp={Timestamp}, URL={Url}
```

```
Health Card validation failed - Invalid Format: QID={QId}, RequestType={RequestType}, Source={Source}, Timestamp={Timestamp}, URL={Url}, HealthCardNumber={HealthCardNumber}
```

## Integration Points

### Validation Placement
The validation is placed after:
- Request body deserialization
- Basic request validation
- Authorization checks

And before:
- Business logic execution
- Database operations

### Error Handling
- Returns HTTP 400 Bad Request status code
- Uses existing `WriteErrorResponseAsync()` method
- Maintains consistency with existing error response format

## Testing

### Unit Tests
Comprehensive unit tests cover:
- Missing Health Card number scenarios
- Invalid format scenarios (too short, too long, invalid characters)
- Valid format scenarios
- Error message format verification
- Logging verification
- All affected endpoints

### Test Coverage
- 100% code coverage for validation logic
- Tests for all four eService types
- Both positive and negative test cases
- Logging behavior verification

## Security Considerations

### Data Protection
- Health Card numbers are logged only for format validation failures
- Sensitive data is handled according to existing security protocols
- No additional data exposure beyond current implementation

### Access Control
- Validation respects existing authorization mechanisms
- No changes to existing security model
- Maintains QID-based access control

## Performance Impact

### Minimal Overhead
- Validation adds minimal processing time
- Uses efficient string validation methods
- No additional database calls
- Leverages existing logging infrastructure

### Early Validation
- Fails fast for invalid requests
- Prevents unnecessary processing of invalid data
- Reduces load on downstream systems

## Monitoring and Observability

### Metrics
- Validation failure rates can be monitored through existing logging infrastructure
- Request source tracking enables identification of problematic clients
- QID-based tracking allows user-specific analysis

### Alerting
- High validation failure rates may indicate client integration issues
- Monitoring can be set up on validation failure log entries
- Source-based analysis helps identify specific client applications

## Backward Compatibility

### Breaking Change Notice
This is a **breaking change** for client applications that:
- Submit requests without Health Card numbers
- Submit requests with invalid Health Card number formats

### Migration Requirements
Client applications must:
1. Ensure all requests include valid Health Card numbers
2. Update error handling to process new validation error messages
3. Test integration with updated validation rules

### Rollout Strategy
1. Update API documentation
2. Notify client application teams
3. Provide testing environment for validation
4. Deploy with appropriate monitoring
5. Monitor validation failure rates post-deployment

## Support and Troubleshooting

### Common Issues
1. **Missing Health Card**: Ensure `HcNumber`/`HCNumber` field is included in request
2. **Invalid Format**: Verify Health Card number meets format requirements
3. **Field Name Mismatch**: Use correct field name for each service type

### Debugging
- Check application logs for validation failure entries
- Verify request payload includes Health Card number
- Confirm Health Card number format meets requirements
- Review request source and QID for authorization issues
