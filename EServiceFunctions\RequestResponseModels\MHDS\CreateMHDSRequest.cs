namespace EServiceFunctions.RequestResponseModels.MHDS;

[ExcludeFromCodeCoverage]
public class UpdateMHDSRequest
{
    /// <summary>
    /// Qatar Id of the person to whom the request was submitted to 
    /// </summary>
    public long QId { get; set; }
    /// <summary>
    /// Qatar Id of the person who submit the request
    /// </summary>
    public long SubmitterQId { get; set; }
    public bool IsGisAddressManualyEntered { get; set; }
    public int? UNo { get; set; }
    public int? BNo { get; set; }
    public int? SNo { get; set; }
    public int? ZNo { get; set; }
}

[ExcludeFromCodeCoverage]
public class CreateMHDSRequest : UpdateMHDSRequest
{
    public string? FNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameEn { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameEn { get; set; }
    public string? LNameAr { get; set; }
    public string? HCNumber { get; set; }
    public string? CurrentAssignedHC { get; set; }
    public DateTime? Dob { get; set; }
    public string? Nationality { get; set; }
    public string? SubmitterMobile { get; set; }
    public string? SecondaryMobNo { get; set; }
    public string? SubmitterEmail { get; set; }
    public bool Consent { get; set; }
    public string? Action { get; set; }
    public List<CreateMedicineInformation>? MedInfo { get; set; }
}