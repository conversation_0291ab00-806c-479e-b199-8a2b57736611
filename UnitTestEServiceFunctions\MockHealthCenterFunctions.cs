using EServiceFunctions.Helpers;
using Microsoft.Azure.Functions.Worker.Http;

namespace UnitTestEServiceFunctions;

public class MockHealthCenterFunctions
{
    public static async Task<HttpResponseData> ValidateWorkDayAndShift(HttpRequestData req)
    {
        string workDay = req.GetCleanedQueryString("workDay");
        string shift = req.GetCleanedQueryString("shift");

        // Validate workDay
        if (!string.IsNullOrEmpty(workDay) && (workDay.Length > 1 || workDay[0] < '1' || workDay[0] > '7'))
        {
            return await req.WriteErrorResponseAsync(HttpStatusCode.BadRequest, "Invalid workDay value. Must be between 1 and 7.");
        }

        // Validate shift
        if (!string.IsNullOrEmpty(shift) && !new[] { "0", "1", "2" }.Contains(shift))
        {
            return await req.WriteErrorResponseAsync(HttpStatusCode.BadRequest, "Invalid shift value. Must be 0, 1, or 2.");
        }

        return null;
    }
}
