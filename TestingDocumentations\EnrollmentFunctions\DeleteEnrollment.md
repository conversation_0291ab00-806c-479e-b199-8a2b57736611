# Delete Enrollment Tests

## Test Cases

### 1. TestDeleteEnrollmentByReqNumber

Tests successful deletion of an enrollment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteEnrollmentByReqNumber()
{
    // Arrange
    var req = CreateEnrollment();
    var reqNumber = await _client.PostAsync<CreateEnrollmentResponse>(req);
    testOutputHelper.LogToConsole(reqNumber.ReqNumber);
    reqNumber.ReqNumber.ThrowIfNull();
    reqNumber.ThrowIfNull();
    await WaitAsync();

    // Act
    var request = GetRestRequest("enrollments/{reqNumber}");
    request.AddUrlSegment("reqNumber", reqNumber.ReqNumber);

    var response = await _client.DeleteAsync(request);
    testOutputHelper.LogToConsole(response);
    
    // Assert
    True(response is not null);
    True(response.IsSuccessStatusCode);
    Equal(OK, response.StatusCode);
}
```

### 2. TestDeleteEnrollmentByReqNumber_Should_Return_BadRequest

Tests handling of invalid deletion request.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestDeleteEnrollmentByReqNumber_Should_Return_BadRequest()
{
    // Arrange
    var request = GetRestRequest("enrollments/{reqNumber}");
    request.AddUrlSegment("reqNumber", "VIJRANG0581");
    request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
    request.Method = Method.Delete;

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);
    
    // Assert
    NotNull(response);
    Equal(BadRequest, response.StatusCode);
}
```

## Request Details

### Endpoint
```
DELETE enrollments/{reqNumber}
```

### Headers
- `JwtClaimsQId`: "22222222270" (for bad request test)

### URL Parameters
- `reqNumber`: Enrollment request number

## Test Data

### Success Case
- Creates new enrollment
- Uses generated request number
- Waits for processing

### Bad Request Case
- Request Number: "VIJRANG0581"
- Expected: Bad Request response

## Validation Rules

### Success Case Validation
1. Response not null
2. Success status code
3. OK response

### Bad Request Case Validation
1. Response not null
2. Bad Request status code

## Error Cases

1. **Invalid Request Number**
   - Non-existent request number
   - Malformed request number
   - Already deleted

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - Insufficient permissions

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Test Setup**
   - Creates test enrollment
   - Waits for processing
   - Verifies creation

2. **Cleanup**
   - Deletes test data
   - Verifies deletion
   - Handles errors

3. **Performance**
   - Async operations
   - Wait periods
   - Timeout handling
