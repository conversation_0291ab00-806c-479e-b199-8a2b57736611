# Get Transfer Reason List

## Overview
Retrieves a list of available transfer reasons with support for filtering and pagination.

## Endpoint
```http
GET /transfersreason
```

## Authorization
- Function-level authorization
- QID-based access control via claims

## Parameters

### Query Parameters
- `reasonCode` (string, optional): Filter by specific reason code
- `skip` (int, optional): Number of records to skip for pagination
- `take` (int, optional): Number of records to take for pagination

## Response

### Success Response (200 OK)
Returns a list of transfer reasons:
```json
[
  {
    "reasonCode": "string",
    "reasonEn": "string",
    "reasonAr": "string"
  }
]
```

### No Content Response (204)
Returned when no records are found.

### Error Responses
- 400 Bad Request: Invalid parameters
- 401 Unauthorized: Invalid QID or unauthorized access

## Implementation Details
- Uses Entity Framework Core with AsNoTracking for optimal performance
- Implements pagination for large result sets
- Supports filtering by reason code
- Supports multilingual content (English/Arabic)
- Validates data existence
- Proper null handling

## Business Logic
- Retrieves transfer reasons from master data
- Supports specific reason code filtering
- Implements pagination
- Maintains bilingual descriptions
- Validates data consistency

## Security Considerations
- Claims-based authorization
- Input validation
- Data access control
- Proper error handling
- Secure query execution

## Performance Optimization
- Uses AsNoTracking for read-only operations
- Implements efficient pagination
- Optimizes database queries
- Early validation checks
- Efficient null checking
