# Comment and Attachments Functions Test Documentation

## Overview

The Comment and Attachments Functions test suite verifies the functionality of managing comments and file attachments in the EservicesRESTapis project. This includes adding, retrieving, updating, and deleting comments and attachments.

## Test Categories

### 1. Comment Management
- [Add Comments](./AddComments.md) - Tests for creating new comments with and without attachments
- [Get Comments](./GetComments.md) - Tests for retrieving comments with pagination and filtering
- [Update Comments](./UpdateComments.md) - Tests for modifying existing comments and their attachments
- [Delete Comments](./DeleteComments.md) - Tests for removing comments and cleaning up attachments

### 2. Attachment Handling
- [Upload Attachments](./UploadAttachments.md)
- [Download Attachments](./DownloadAttachments.md)
- [Delete Attachments](./DeleteAttachments.md)

### 3. Validation and Security
- [Comment Validation](./CommentValidation.md)
- [Attachment Validation](./AttachmentValidation.md)

## Test Structure

Each test follows a consistent pattern:

```csharp
[Fact]
[Trait(Category, HappyPath/UnHappyPath)]
public async Task TestMethodName()
{
    // Arrange
    var request = GetRestRequest("comments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var commentRequest = new AddCommentRequest
    {
        EntityId = "TEST-123",
        Content = "Test comment content",
        Type = "General"
    };
    request.AddJsonBody(commentRequest);

    // Act
    var response = await _client.ExecuteAsync<CommentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(expectedStatusCode, response.StatusCode);
    NotNull(response.Data);
    Equal(commentRequest.Content, response.Data.Content);
}
```

Key Components:
1. **Test Attributes**
   - `[Fact]` for individual test cases
   - `[Theory]` for parameterized tests
   - `[Trait]` for test categorization

2. **Test Structure**
   - Arrange: Setup test data and requests
   - Act: Execute API calls
   - Assert: Validate responses

3. **Validation Patterns**
   - Status code checks
   - Response data validation
   - Error handling verification

## Common Components

### 1. Request Building
```csharp
// Basic request
var request = GetRestRequest("comments");
request.AddOrUpdateHeader(JwtClaimsQId, userQId);

// JSON body
var commentData = new AddCommentRequest 
{
    EntityId = entityId,
    Content = content,
    Type = type
};
request.AddJsonBody(commentData);

// Query parameters
request.AddQueryParameter("page", page.ToString());
request.AddQueryParameter("pageSize", pageSize.ToString());
request.AddQueryParameter("type", commentType);
```

### 2. File Handling
```csharp
// Single file upload
request.AddFile("file", filePath, "application/octet-stream");

// Multiple files
foreach (var file in files)
{
    request.AddFile("files", file.Path, file.ContentType);
}

// File validation
private async Task ValidateFile(IFormFile file)
{
    if (file.Length > MaxFileSize)
        throw new ValidationException($"File size exceeds {MaxFileSize} bytes");

    if (!AllowedFileTypes.Contains(file.ContentType))
        throw new ValidationException($"File type {file.ContentType} not allowed");

    await ScanFileForViruses(file);
}
```

### 3. Response Validation
```csharp
// Basic validation
NotNull(response);
True(response.IsSuccessful);
Equal(expectedStatusCode, response.StatusCode);

// Data validation
NotNull(response.Data);
Equal(expectedContent, response.Data.Content);
NotEmpty(response.Data.CommentId);

// Error validation
False(response.IsSuccessful);
Equal(BadRequest, response.StatusCode);
Contains(expectedError, response.ErrorMessage);

// Attachment validation
NotEmpty(response.Data.Attachments);
All(response.Data.Attachments, a => a.Size > 0);
All(response.Data.Attachments, a => !string.IsNullOrEmpty(a.FileName));
```

## Response Models

### CommentResponse
```csharp
public class CommentResponse
{
    public string CommentId { get; set; }
    public string Content { get; set; }
    public string Author { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<AttachmentInfo> Attachments { get; set; }
}
```

### AttachmentInfo
```csharp
public class AttachmentInfo
{
    public string AttachmentId { get; set; }
    public string FileName { get; set; }
    public string ContentType { get; set; }
    public long Size { get; set; }
    public DateTime UploadedAt { get; set; }
}
```

## Test Environment Setup

### Prerequisites
- Valid test user credentials
- Test files for attachment upload
- Temporary storage for test files

### Configuration
```json
{
    "MaxFileSize": 10485760,
    "AllowedFileTypes": [
        "application/pdf",
        "image/jpeg",
        "image/png"
    ],
    "MaxCommentsPerRequest": 100
}
```

## Best Practices

1. **File Handling**
   - Clean up test files
   - Verify file integrity
   - Check size limits

2. **Comment Management**
   - Validate content
   - Check permissions
   - Handle special characters

3. **Security**
   - Verify authentication
   - Check authorization
   - Validate file types

## Common Test Scenarios

1. **Comment Flow**
   - Create comment
   - Add attachments
   - Update content
   - Delete comment

2. **Attachment Flow**
   - Upload file
   - Verify content
   - Download file
   - Delete file

3. **Validation Flow**
   - Check size limits
   - Verify file types
   - Validate content

## Error Handling

1. **Comment Errors**
   ```csharp
   // Invalid content
   [Fact]
   [Trait(Category, UnhappyPath)]
   public async Task TestAddComment_EmptyContent_ReturnsBadRequest()
   {
       var request = new AddCommentRequest
       {
           EntityId = "TEST-123",
           Content = "",
           Type = "General"
       };
       var response = await ExecuteRequest(request);
       Equal(BadRequest, response.StatusCode);
       Contains("Content cannot be empty", response.ErrorMessage);
   }

   // Missing permissions
   [Fact]
   [Trait(Category, UnhappyPath)]
   public async Task TestUpdateComment_UnauthorizedUser_ReturnsUnauthorized()
   {
       var request = new UpdateCommentRequest
       {
           CommentId = "COMMENT-123",
           Content = "Updated content"
       };
       var response = await ExecuteRequest(request, unauthorizedUser);
       Equal(Unauthorized, response.StatusCode);
       Contains("Not authorized", response.ErrorMessage);
   }
   ```

2. **File Errors**
   ```csharp
   // Size exceeded
   [Fact]
   [Trait(Category, UnhappyPath)]
   public async Task TestUploadAttachment_SizeExceeded_ReturnsBadRequest()
   {
       var largefile = CreateTestFile(MaxFileSize + 1024);
       var request = GetUploadRequest(largefile);
       var response = await ExecuteRequest(request);
       Equal(BadRequest, response.StatusCode);
       Contains("exceeds maximum size", response.ErrorMessage);
   }

   // Invalid type
   [Fact]
   [Trait(Category, UnhappyPath)]
   public async Task TestUploadAttachment_InvalidType_ReturnsBadRequest()
   {
       var request = GetUploadRequest("test.exe");
       var response = await ExecuteRequest(request);
       Equal(BadRequest, response.StatusCode);
       Contains("file type not allowed", response.ErrorMessage);
   }
   ```

3. **System Errors**
   ```csharp
   // Storage errors
   public async Task HandleStorageError(Exception ex)
   {
       if (ex is StorageFullException)
       {
           // Log error
           _logger.LogError("Storage full: {Message}", ex.Message);
           // Notify admin
           await _notificationService.NotifyAdmin("Storage Full");
           throw new ServiceException("Unable to store file", ex);
       }
   }

   // Network issues
   public async Task<T> ExecuteWithRetry<T>(
       Func<Task<T>> action,
       int maxRetries = 3)
   {
       for (int i = 0; i < maxRetries; i++)
       {
           try
           {
               return await action();
           }
           catch (NetworkException ex)
           {
               if (i == maxRetries - 1) throw;
               await Task.Delay((i + 1) * 1000);
               _logger.LogWarning("Retry {Attempt} after network error", i + 1);
           }
       }
       throw new ServiceException("Max retries exceeded");
   }
   ```

## Performance Considerations

1. **File Processing**
   - Chunk large files
   - Compress content
   - Stream downloads

2. **Comment Loading**
   - Paginate results
   - Cache common queries
   - Optimize sorting

## Security Guidelines

1. **File Security**
   - Scan for malware
   - Validate content type
   - Check permissions

2. **Comment Security**
   - Filter content
   - Prevent injection
   - Rate limit requests

## Test Execution Guidelines

1. **Setup**
   - Prepare test files
   - Configure environment
   - Set up cleanup

2. **Execution**
   - Run tests in order
   - Handle dependencies
   - Clean up resources

3. **Validation**
   - Check responses
   - Verify files
   - Confirm changes

## Troubleshooting

Common issues and solutions:

1. **Upload Issues**
   - Check file size
   - Verify content type
   - Check permissions

2. **Download Problems**
   - Verify file exists
   - Check access rights
   - Handle timeouts

3. **Comment Issues**
   - Validate format
   - Check constraints
   - Handle duplicates

## Notes

- Tests use xUnit framework
- RestSharp for API calls
- File handling utilities
- Comprehensive logging
- Proper cleanup routines

## Recommendations

1. **Testing Strategy**
   - Cover all file types
   - Test size limits
   - Verify error cases

2. **Performance**
   - Optimize file handling
   - Cache responses
   - Handle concurrency

3. **Security**
   - Implement virus scanning
   - Validate content
   - Check permissions
