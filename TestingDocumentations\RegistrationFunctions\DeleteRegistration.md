# Delete Registration Tests

## Test Cases

### 1. TestDeleteRegistrationByReqNumber

Tests deletion of registration by request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteRegistrationByReqNumber()
{
    // Arrange
    if (GetCurrentTime() > new DateTime(2022, 01, 01))
    {
        True(GetCurrentTime() > new DateTime(2022, 01, 01));
        return;
    }

    var request = CreateUpdateRegistration("CreateRegistrationKey");
    var requestNumber = await _client.PostAsync<CreateRegistrationResponse>(request);
    requestNumber.ReqNumber.ThrowIfNull();
    await WaitAsync(6);
    
    var deleteRequest = GetRestRequest("registrations/{reqNumber}");
    deleteRequest.AddUrlSegment("reqNumber", requestNumber.ReqNumber.Trim());
    
    // Act
    var response = await _client.DeleteAsync(deleteRequest);
    response.ThrowIfNull();

    // Assert
    Equal(NoContent, response.StatusCode);
}
```

## Request Details

### Endpoint
```
DELETE registrations/{reqNumber}
```

### URL Parameters
- `reqNumber`: Registration request number to delete

## Test Flow

1. **Time Check**
   - Validates current time against cutoff date
   - Skips test if after January 1, 2022

2. **Create Registration**
   - Creates new registration
   - Gets request number
   - Waits for 6 seconds

3. **Delete Registration**
   - Sends delete request
   - Validates response

## Response Validation

### Success Case
- Status Code: NoContent (204)
- Response not null

## Test Data

### Success Case
1. **Create Request**
   - Generated test data
   - Valid registration details

2. **Delete Request**
   - Valid request number
   - Trimmed request number

## Validation Rules

### Pre-deletion Checks
1. Time validation
2. Request number exists
3. Request number not null

### Success Case Validation
1. Response not null
2. Status code is NoContent

## Error Cases

1. **Invalid Request Number**
   - Non-existent request
   - Malformed number
   - Invalid format

2. **Timing Issues**
   - Test cutoff date passed
   - Insufficient wait time
   - Request timing conflicts

3. **Authorization Errors**
   - Missing permissions
   - Invalid credentials
   - Unauthorized deletion

4. **System Errors**
   - Database errors
   - Service unavailable
   - Deletion conflicts

## Notes

1. **Time Dependency**
   - Test has cutoff date
   - Time-sensitive operation
   - Wait period required

2. **Request Flow**
   - Create then delete pattern
   - Asynchronous operations
   - Status verification

3. **Performance**
   - Wait time impact
   - Deletion verification
   - Resource cleanup

4. **Special Considerations**
   - No permission after 2022
   - Request number trimming
   - Null checking important
