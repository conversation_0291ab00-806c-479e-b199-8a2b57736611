﻿namespace EServiceFunctions.Models.Enrollment;

public class EnrollmentContext(DbContextOptions<EnrollmentContext> options) : DbContext(options)
{
    public virtual DbSet<Enrollment?>? Enrollment { get; set; }
    public virtual DbSet<Status>? Status { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<Enrollment>(entity =>
        {
            entity.HasIndex(e => e.QId)
                .HasDatabaseName("IDX_Enrollment_QId");

            entity.HasIndex(e => e.ReqNumber)
                .HasDatabaseName("UQ__Enrollme__237F342300F1649F")
                .IsUnique();

            entity.HasIndex(e => e.SubmitterQId)
                .HasDatabaseName("IDX_Enrollment_SubmitterQId");

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.Property(e => e.FNameAr).HasMaxLength(255);

            entity.Property(e => e.FNameEn).HasMaxLength(255);

            entity.Property(e => e.HCNumber).HasMaxLength(255);

            entity.Property(e => e.LNameAr).HasMaxLength(255);

            entity.Property(e => e.LNameEn).HasMaxLength(255);

            entity.Property(e => e.MNameAr).HasMaxLength(255);

            entity.Property(e => e.MNameEn).HasMaxLength(255);

            entity.Property(e => e.Nationality).HasMaxLength(255);

            entity.Property(e => e.ReqNumber).HasMaxLength(255);

            entity.Property(e => e.SN).HasMaxLength(255);

            entity.Property(e => e.Status).HasMaxLength(255);

            entity.Property(e => e.StatusInternal).HasMaxLength(255);

            entity.Property(e => e.SubmittedAt).HasColumnType("datetime");

            entity.Property(e => e.SubmittedBy).HasMaxLength(255);

            entity.Property(e => e.SubmitterEmail).HasMaxLength(255);

            entity.Property(e => e.SubmitterHCNumber).HasMaxLength(255);

            entity.Property(e => e.SubmitterHCntCode).HasMaxLength(255);

            entity.Property(e => e.SubmitterMobile).HasMaxLength(255);

            entity.Property(e => e.SubmitterNationality).HasMaxLength(255);

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);

            entity.HasOne(d => d.StatusNavigation)
                .WithMany(p => p.Enrollment)
                .HasForeignKey(d => d.Status)
                .HasConstraintName("FK__Enrollmen__Statu__6225902D");
        });

        modelBuilder.Entity<Status>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Status__A25C5AA68EAE98DF");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);

            entity.Property(e => e.Category).HasMaxLength(255);
        });
    }
}