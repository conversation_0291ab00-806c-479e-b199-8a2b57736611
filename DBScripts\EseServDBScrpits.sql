IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'AppointmentRequestType'
             AND type = 'U')
    DROP TABLE [AppointmentRequestType]; -- MD
GO
CREATE TABLE [AppointmentRequestType]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255)
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Status'
             AND type = 'U')
    DROP TABLE [Status]; -- MD
GO
CREATE TABLE [Status]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255)
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'SMS'
             AND type = 'U')
    DROP TABLE [SMS]; -- MD
GO
CREATE TABLE [SMS]
(
    [Id]           int PRIMARY KEY IDENTITY (1, 1),
    [ReqNumber]    nvarchar(255),
    [TranId]       [decimal](30, 0) NULL,
    [Sender]       [varchar](30)    NULL,
    [Receiver]     [varchar](30)    NULL,
    [Msg]          [nvarchar](1024) NULL,
    [SentTime]     [varchar](100)   NULL,
    [ReceivedTime] [varchar](100)   NULL,
    [Reference]    [varchar](100)   NULL,
    [Status]       nvarchar(255) FOREIGN KEY REFERENCES Status ([Code]),
    [MsgType]      [varchar](160)   NULL,
    [Operator]     [varchar](100)   NULL,
    [ErrorMsg]     [varchar](600)   NULL,
    [ScheduleTime] datetime,
    [CreatedAt]    datetime DEFAULT (GETDATE()),
    INDEX IDX_SMS_ReqNumber ([ReqNumber])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'UserProfile'
             AND type = 'U')
    DROP TABLE [UserProfile]; -- MD
GO
CREATE TABLE [UserProfile]
(
    [QId]                  bigint PRIMARY KEY,
    [PrefSMSLang]          nvarchar(255),
    [PrefComMode]          nvarchar(255),
    [SecondaryPhoneMobile] nvarchar(255),
    [IsActive]             BIT      DEFAULT (1), -- To support future for soft delete feature
    [CreatedAt]            datetime DEFAULT (GETDATE()),
    [UpdatedAt]            datetime
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'UserDependent'
             AND type = 'U')
    DROP TABLE [UserDependent]; -- MD
GO
CREATE TABLE [UserDependent]
(
    [QId]                  bigint,
    [Dob]                  date,                 -- As per the call on 2 june 2020 it's decided that this value wont be coming from UI but it needs to be updated through some backend job.
    [ParentQId]            bigint,
    [IsMinor]              BIT      DEFAULT (1), -- If false don't return the record.
    [IsRelationshipActive] BIT      DEFAULT (1), -- To support future for soft delete feature
    [CreatedAt]            datetime DEFAULT (GETDATE()),
    [UpdatedAt]            datetime,
    CONSTRAINT PK_QId_ParentQId PRIMARY KEY (QId, ParentQId)
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Attachment'
             AND type = 'U')
    DROP TABLE [Attachment]; -- TR
GO
CREATE TABLE [Attachment]
(
    [Id]             uniqueidentifier PRIMARY KEY NONCLUSTERED DEFAULT (NEWID()),
    [AttachmentName] nvarchar(255),
    [AttachmentType] nvarchar(255),
    [AttachmentData] nvarchar(max),
    [SubmitterQId]   bigint,
    [CreatedAt]      datetime                                  DEFAULT (GETDATE()),
    [UpdatedAt]      datetime,
    INDEX IDX_Attachment_SubmitterQId ([SubmitterQId])
)
GO

-- Get call should return only Action, comment text, created at & DisplayToSubmitter is true "1"
IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Comment'
             AND type = 'U')
    DROP TABLE [Comment]; -- TR
GO
CREATE TABLE [Comment]
(
    [Id]                 int PRIMARY KEY IDENTITY (1, 1),
    [ReqNumber]          nvarchar(255),
    [Action]             nvarchar(255),
    [CommentText]        nvarchar(1000),
    [CommentType]        nvarchar(255),
    [CommenterName]      nvarchar(1000),
    [SubmitterQId]       bigint,
    [DisplayToSubmitter] BIT,
    [CreatedAt]          datetime DEFAULT (GETDATE()),
    INDEX IDX_Comment_ReqNumber ([ReqNumber]),
    INDEX IDX_Comment_SubmitterQId ([SubmitterQId])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'ReleaseItem'
             AND type = 'U')
    DROP TABLE [ReleaseItem]; -- MD
GO
CREATE TABLE [ReleaseItem]
(
    [Id]             int PRIMARY KEY IDENTITY (1, 1),
    [ItemCode]       nvarchar(255),
    [ItemNameEn]     nvarchar(255),
    [ItemNameAr]     nvarchar(255),
    --[Category] nvarchar(255) DEFAULT 'base',
    [CategoryCode]   nvarchar(255),
    [CategoryNameEn] nvarchar(255),
    [CategoryNameAr] nvarchar(255),
    [CreatedAt]      datetime DEFAULT (GETDATE()),
    UNIQUE ([ItemCode])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'HealthCenter'
             AND type = 'U')
    DROP TABLE [HealthCenter]; -- MD
GO
CREATE TABLE [HealthCenter]
(
    [HCntCode]    nvarchar(255) PRIMARY KEY,
    [HCntNameEn]  nvarchar(255),
    [HCntNameAr]  nvarchar(255),
    [HCntPurpose] nvarchar(255),
    [CreatedAt]   datetime DEFAULT (GETDATE())
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Clinic'
             AND type = 'U')
    DROP TABLE [Clinic]; -- MD
GO
CREATE TABLE [Clinic]
(
    [ClinicCode]       nvarchar(255) PRIMARY KEY,
    [ClinicNameEn]     nvarchar(255),
    [ClinicNameAr]     nvarchar(255),
    [IsNewAppointment] bit,
    [IsEditable]       bit,
    [CreatedAt]        datetime DEFAULT (GETDATE())
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'ClinicShift'
             AND type = 'U')
    DROP TABLE [ClinicShift]; -- MD
GO
CREATE TABLE [ClinicShift]
(
    [Id]         int PRIMARY KEY IDENTITY (1, 1),
    [HCntCode]   nvarchar(255) FOREIGN KEY REFERENCES HealthCenter (HCntCode),
    [ClinicCode] nvarchar(255) FOREIGN KEY REFERENCES Clinic (ClinicCode),
    [WorkDays]   nvarchar(255), -- 1-SUN, 2-MON, 3-TUE, 4-WED, 5-THU, 6-FRI, 7-SAT "comma separated string ex: 1,3,5 for SUN, TUE & THU"
    [ShiftTime]  nvarchar(255), -- 0-AM or 1-PM or 2-BOTH
    [CreatedAt]  datetime DEFAULT (GETDATE()),
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Speciality'
             AND type = 'U')
    DROP TABLE [Speciality]; -- MD
GO
CREATE TABLE [Speciality]
(
    [SpltyCode]   nvarchar(255) PRIMARY KEY,
    [SpltyNameEn] nvarchar(255),
    [SpltyNameAr] nvarchar(255)
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'FamilyPhysician'
             AND type = 'U')
    DROP TABLE [FamilyPhysician]; -- MD
GO
CREATE TABLE [FamilyPhysician]
(
    [Id]            int PRIMARY KEY IDENTITY (1, 1),
    [PhyCode]       nvarchar(255),
    [PhyNameEn]     nvarchar(255),
    [PhyNameAr]     nvarchar(255),
    [Title]         nvarchar(255),
    [Qualification] nvarchar(255),
    [Language]      nvarchar(255), -- code coming from a dropdown-list 0-Arabic, 1-English. source: Frontend. "comma separated string ex: 0,1 for Arabic & English"
    [TotalExp]      int,
    [SpltyCode]     nvarchar(255), -- comma separated SpltyCode string
    [Gender]        nvarchar(255), -- text (not code) coming from a dropdown-list (en or ar) 0-Female, 1-Male, 2-Unknown. source: Frontend
    [Image]         nvarchar(max),
    [HCntCode]      nvarchar(255),
    UNIQUE ([PhyCode])
)
GO

-- No self Enrollment. It's only for dependents. Saved status to be used when the user is created.
IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Enrollment'
             AND type = 'U')
    DROP TABLE [Enrollment]; -- TR
GO
CREATE TABLE [Enrollment]
(
    [Id]                   int PRIMARY KEY IDENTITY (1, 1),
    [QId]                  bigint,
    [ReqNumber]            nvarchar(255),
    [HCNumber]             nvarchar(255),
    [FNameEn]              nvarchar(255),
    [MNameEn]              nvarchar(255),
    [LNameEn]              nvarchar(255),
    [FNameAr]              nvarchar(255),
    [MNameAr]              nvarchar(255),
    [LNameAr]              nvarchar(255),
    [Nationality]          nvarchar(255),                                        -- text (not code) coming from a dropdown-list (en or ar)
    --[ParentName] nvarchar(255), -- 
    --[ParentHCNumber] nvarchar(255), -- Renamed to SubmitterHCNumber
    --[PrefSMSLang] nvarchar(255), -- SBy Shazia on 13 April, Not req hence removed
    --[PrefComMode] nvarchar(255), -- SBy Shazia on 13 April, Not req hence removed
    --[attach_passport] uniqueidentifier,
    [AttachId1]            uniqueidentifier,
    [AttachId2]            uniqueidentifier,
    [AttachId3]            uniqueidentifier,
    [AttachId4]            uniqueidentifier,
    [AttachId5]            uniqueidentifier,
    [AttachId6]            uniqueidentifier,
    --[attach_birth] uniqueidentifier,
    --[AttachOther] uniqueidentifier,
    [SubmittedBy]          nvarchar(255),
    [SubmittedAt]          datetime,
    [SubmitterQId]         bigint,
    [SubmitterEmail]       nvarchar(255),
    [SubmitterMobile]      nvarchar(255),
    [SubmitterNationality] nvarchar(255),                                        -- Nationality code coming from a dropdown-list. source: MOI
    [SubmitterHCNumber]    nvarchar(255),
    [CreatedAt]            datetime DEFAULT (GETDATE()),
    [UpdatedAt]            datetime,
    [Status]               nvarchar(255) FOREIGN KEY REFERENCES Status ([Code]), -- saved (before submit), submitted (after submit), rework, reworked, approved, cancelled & rejected.
    [StatusInternal]       nvarchar(255),                                        -- Not for APIs\external users. Only for K2 WF. HIM Operations, HIM Operations in HC   
    [SN]                   nvarchar(255),
    [Consent]              bit,                                                  -- Task#756 :: Value coming as Trure/False from the front-end.
    UNIQUE ([ReqNumber]),
    INDEX IDX_Enrollment_QId ([QId]),
    INDEX IDX_Enrollment_SubmitterQId ([SubmitterQId])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Registration'
             AND type = 'U')
    DROP TABLE [Registration];
GO
CREATE TABLE [Registration]
(
    [Id]                 int PRIMARY KEY IDENTITY (1, 1),
    [QId]                bigint,
    [ReqNumber]          nvarchar(255),
    [FNameEn]            nvarchar(255),
    [MNameEn]            nvarchar(255),
    [LNameEn]            nvarchar(255),
    [FNameAr]            nvarchar(255),
    [MNameAr]            nvarchar(255),
    [LNameAr]            nvarchar(255),
    [Nationality]        nvarchar(255),                                        -- text (not code) coming from a dropdown-list (en or ar). source: MOI
    [Dob]                date,
    [Gender]             nvarchar(255),                                        -- text (not code) coming from a dropdown-list (en or ar) 0-Female, 1-Male, 2-Unknown. source: Frontend
    [MaritalStatus]      nvarchar(255),                                        -- code coming from a dropdown-list   0-Married, 1-single, 2- widowed. source: Frontend
    [Education]          nvarchar(255),
    [Occupation]         nvarchar(255),                                        -- code coming from a dropdown-list. source: MOI
    [HomeTel]            nvarchar(255),
    [OfficeTel]          nvarchar(255),
    [MobileNo]           nvarchar(255),
    [NextOfKinName]      nvarchar(255),
    [NextOfKinLandLine]  nvarchar(255),
    [SponsorName]        nvarchar(255),
    [SponsorAddress]     nvarchar(255),
    [VisaType]           nvarchar(255),                                        -- code coming from a dropdown-list. source: Frontend
    [BNo]                int,
    [ZNo]                int,
    [SNo]                int,
    [UNo]                int,
    [CatchmentHC]        nvarchar(255),                                        -- code coming from a dropdown-list. source: eServ Azure SQL
    [PrefHC]             nvarchar(255),                                        -- code coming from a dropdown-list. source: eServ Azure SQL
    [PrefSMSLang]        nvarchar(255),                                        -- code coming from a dropdown-list 0-Arabic, 1-English. source: Frontend
    [PrefComMode]        nvarchar(255),
    --[AttachQId] uniqueidentifier,
    --[AttachRental] uniqueidentifier,
    --[AttachVaccination] uniqueidentifier,
    --[AttachOther] uniqueidentifier,
    [AttachId1]          uniqueidentifier,
    [AttachId2]          uniqueidentifier,
    [AttachId3]          uniqueidentifier,
    [AttachId4]          uniqueidentifier,
    [AttachId5]          uniqueidentifier,
    [AttachId6]          uniqueidentifier,
    [EmergencyContactID] nvarchar(255),                                        --Task#786
    [SubmittedBy]        nvarchar(255),
    [SubmittedAt]        datetime,
    [SubmitterQId]       bigint,
    [SubmitterEmail]     nvarchar(255),
    [SubmitterMobile]    nvarchar(255),
    [CreatedAt]          datetime DEFAULT (GETDATE()),
    [UpdatedAt]          datetime,
    [Status]             nvarchar(255) FOREIGN KEY REFERENCES Status ([Code]), -- 
    [StatusInternal]     nvarchar(255),                                        -- Not for APIs\external users. Only for K2 WF. ex:HIM Operations, HIM Operations in HC   
    [SN]                 nvarchar(255),
    UNIQUE ([ReqNumber]),
    INDEX IDX_Registration_QId ([QId]),
    INDEX IDX_Registration_SubmitterQId ([SubmitterQId])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Appointment'
             AND type = 'U')
    DROP TABLE [Appointment];
GO
CREATE TABLE [Appointment]
(
    [Id]                   int PRIMARY KEY IDENTITY (1, 1),
    [QId]                  bigint,                                               -- V
    [ReqNumber]            nvarchar(255),                                        --L
    [FNameEn]              nvarchar(255),
    [MNameEn]              nvarchar(255),
    [LNameEn]              nvarchar(255),
    [FNameAr]              nvarchar(255),
    [MNameAr]              nvarchar(255),
    [LNameAr]              nvarchar(255),
    [Gender]               nvarchar(255),                                        -- Gender code coming from a dropdown-list: 0-Female, 1-Male, 2-Unknown. source: Frontend
    [SecondaryPhoneMobile] nvarchar(255),
    [CancellationCall]     bit,                                                  -- Confirmation call for cancellation
    [Consent]              bit,                                                  -- If reschedule is requested for clinic "Ultra Sound" use needs to read and provide his consent.
    [HCNumber]             nvarchar(255),
    [Clinic]               nvarchar(255),                                        -- code coming from a dropdown-list. source: eServ Azure SQL
    [ClinicTime]           nvarchar(255),                                        -- Used only for request cancellation. ex: 12:00, 13:00 etc.
    [PrefDate]             date,                                                 --L
    [AmPm]                 nvarchar(255),                                        -- code coming from a dropdown-list 0-am, 1-pm. source: Frontend
    [SelectedHC]           nvarchar(255),                                        -- code coming from a dropdown-list. source: eServ Azure SQL
    [RequestType]          nvarchar(255),                                        -- New/Reschedule/Cancel appointment :: new entry on each request
    [PrefContactTime]      nvarchar(255),                                        -- code coming from a dropdown-list 0-am, 1-pm, 2-both. source: Frontend
    [SubmittedBy]          nvarchar(255),
    [SubmittedAt]          datetime,
    [SubmitterQId]         bigint,
    [SubmitterEmail]       nvarchar(255),
    [SubmitterMobile]      nvarchar(255),
    [CreatedAt]            datetime DEFAULT (GETDATE()),
    [UpdatedAt]            datetime,
    [Status]               nvarchar(255) FOREIGN KEY REFERENCES Status ([Code]), --
    [StatusInternal]       nvarchar(255),                                        -- Not for APIs\external users. Only for K2 WF. ex:HIM Operations, HIM Operations in HC   
    [SN]                   nvarchar(255),
    [AppointmentId]        bigint,                                               -- It'll be populated for RESCHEDULE & CANCEL appointments and will be NULL for NEW appointments.
    UNIQUE ([ReqNumber]),
    INDEX IDX_Appointment_QId ([QId]),
    INDEX IDX_Appointment_SubmitterQId ([SubmitterQId])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'TransferReason'
             AND type = 'U')
    DROP TABLE [TransferReason]; -- MD
GO
CREATE TABLE [TransferReason]
(
    [ReasonCode] nvarchar(255) PRIMARY KEY,
    [ReasonEn]   nvarchar(1000),
    [ReasonAr]   nvarchar(1000),
    [CreatedAt]  datetime DEFAULT (GETDATE())
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Transfer'
             AND type = 'U')
    DROP TABLE [Transfer];
GO
CREATE TABLE [Transfer]
(
    [Id]              int PRIMARY KEY IDENTITY (1, 1),
    [QId]             bigint,
    [FNameEn]         nvarchar(255),
    [MNameEn]         nvarchar(255),
    [LNameEn]         nvarchar(255),
    [FNameAr]         nvarchar(255),
    [MNameAr]         nvarchar(255),
    [LNameAr]         nvarchar(255),
    [ReqNumber]       nvarchar(255),
    [Nationality]     nvarchar(255),                                        -- Nationality code coming from a dropdown-list. source: MOI
    [Dob]             date,
    [Consent]         bit,
    [HCNumber]        nvarchar(255),
    [BNo]             int,
    [ZNo]             int,
    [SNo]             int,
    [UNo]             int,
    [CurrentHC]       nvarchar(255),                                        -- code coming from a dropdown-list. source: eServ Azure SQL
    [CatchmentHC]     nvarchar(255),                                        -- code coming from a dropdown-list. source: eServ Azure SQL
    [PrefHC]          nvarchar(255),                                        -- code coming from a dropdown-list. source: eServ Azure SQL
    --[AttachAddressProof1] uniqueidentifier,
    --[AttachAddressProof2] uniqueidentifier,
    [AttachId1]       uniqueidentifier,
    [AttachId2]       uniqueidentifier,
    [AttachId3]       uniqueidentifier,
    [AttachId4]       uniqueidentifier,
    [AttachId5]       uniqueidentifier,
    [AttachId6]       uniqueidentifier,
    [TransferReason]  nvarchar(255) FOREIGN KEY REFERENCES TransferReason ([ReasonCode]),
    [SubmittedBy]     nvarchar(255),
    [SubmittedAt]     datetime,                                             -- L
    [SubmitterQId]    bigint,
    [SubmitterEmail]  nvarchar(255),
    [SubmitterMobile] nvarchar(255),
    [CreatedAt]       datetime DEFAULT (GETDATE()),
    [UpdatedAt]       datetime,
    [Status]          nvarchar(255) FOREIGN KEY REFERENCES Status ([Code]), --
    [StatusInternal]  nvarchar(255),                                        -- Not for APIs\external users. Only for K2 WF. ex:HIM Operations, HIM Operations in HC  
    [SN]              nvarchar(255),
    UNIQUE ([ReqNumber]),
    INDEX IDX_Transfer_QId ([QId]),
    INDEX IDX_Transfer_SubmitterQId ([SubmitterQId])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Assignment'
             AND type = 'U')
    DROP TABLE [Assignment];
GO
CREATE TABLE [Assignment]
(
    [Id]                int PRIMARY KEY IDENTITY (1, 1),
    [QId]               bigint,
    [FNameEn]           nvarchar(255),
    [MNameEn]           nvarchar(255),
    [LNameEn]           nvarchar(255),
    [FNameAr]           nvarchar(255),
    [MNameAr]           nvarchar(255),
    [LNameAr]           nvarchar(255),
    [ReqNumber]         nvarchar(255),
    [HCNumber]          nvarchar(255),                                        -- Health Card Number
    [Nationality]       nvarchar(255),                                        -- Nationality code coming from a dropdown-list. source: MOI
    [Dob]               date,
    [CurrentAssignedHC] nvarchar(255),                                        -- HC code coming from a dropdown-list. source: eServ Azure SQL
    [CurrentPhysician]  nvarchar(255),                                        -- Current Physician code coming from a dropdown-list. source: eServ Azure SQL
    [SelectedPhysician] nvarchar(255),                                        -- Selected Physician code coming from a dropdown-list. source: eServ Azure SQL
    [AttachId1]         uniqueidentifier,
    [AttachId2]         uniqueidentifier,
    [AttachId3]         uniqueidentifier,
    [AttachId4]         uniqueidentifier,
    [AttachId5]         uniqueidentifier,
    [AttachId6]         uniqueidentifier,
    [ChangeReason]      nvarchar(1000),
    [SubmittedBy]       nvarchar(255),
    [SubmittedAt]       datetime,
    [SubmitterQId]      bigint,
    [SubmitterEmail]    nvarchar(255),
    [SubmitterMobile]   nvarchar(255),
    [CreatedAt]         datetime DEFAULT (GETDATE()),
    [UpdatedAt]         datetime,
    [Status]            nvarchar(255) FOREIGN KEY REFERENCES Status ([Code]), --
    [StatusInternal]    nvarchar(255),                                        -- Not for APIs\external users. Only for K2 WF. ex:HIM Operations, HIM Operations in HC  
    [SN]                nvarchar(255),
    UNIQUE ([ReqNumber]),
    INDEX IDX_Assignment_QId ([QId]),
    INDEX IDX_Assignment_SubmitterQId ([SubmitterQId])
)
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Release'
             AND type = 'U')
    DROP TABLE [Release];
GO
CREATE TABLE [Release]
(
    [Id]                       int PRIMARY KEY IDENTITY (1, 1),
    [QId]                      bigint,
    [FNameEn]                  nvarchar(255),
    [MNameEn]                  nvarchar(255),
    [LNameEn]                  nvarchar(255),
    [FNameAr]                  nvarchar(255),
    [MNameAr]                  nvarchar(255),
    [LNameAr]                  nvarchar(255),
    [ReqNumber]                nvarchar(255),
    [HCNumber]                 nvarchar(255),
    [Nationality]              nvarchar(255),                                        -- Nationality code coming from a dropdown-list. source: MOI
    [Dob]                      date,
    [CurrentAssignedHC]        nvarchar(255),                                        -- HC code coming from a dropdown-list. source: eServ Azure SQL
    [AttendanceRptStartDate]   date,                                                 -- Attendance Report Start Date
    [AttendanceRptEndDate]     date,                                                 -- Attendance Report End Date
    [ReleaseItems]             nvarchar(1000),                                       -- comma separated Release Item Code(s)
    [OtherReleaseItems]        nvarchar(255),                                        -- comma separated other release item(s) text
    [EncounterStartDate]       date,
    [EncounterEndDate]         date,
    [PickupDelegated]          bit,
    [AuthorizedQId]            bigint,
    [AuthorizedPersonName]     nvarchar(255),
    [AuthorizedPersonRelation] nvarchar(255),
    [DelegationOthers]         nvarchar(255),                                        -- Delegated person's other relation ship
    [AttachId1]                uniqueidentifier,
    [AttachId2]                uniqueidentifier,
    [AttachId3]                uniqueidentifier,
    [AttachId4]                uniqueidentifier,
    [AttachId5]                uniqueidentifier,
    [AttachId6]                uniqueidentifier,
    [SubmittedBy]              nvarchar(255),
    [SubmittedAt]              datetime,                                             --L
    [SubmitterQId]             bigint,
    [SubmitterEmail]           nvarchar(255),
    [SubmitterMobile]          nvarchar(255),
    [CreatedAt]                datetime DEFAULT (GETDATE()),
    [UpdatedAt]                datetime,
    [Status]                   nvarchar(255) FOREIGN KEY REFERENCES Status ([Code]), --
    [StatusInternal]           nvarchar(255),                                        -- Not for APIs\external users. Only for K2 WF. ex:HIM Operations, HIM Operations in HC  
    [SN]                       nvarchar(255),
    UNIQUE ([ReqNumber]),
    INDEX IDX_Release_QId ([QId]),
    INDEX IDX_Release_SubmitterQId ([SubmitterQId])
)
GO

-- WI#2370
--Commented the below 2 tables. we are not using this scripts in creating tables. 
--IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MHDS' AND type = 'U') DROP TABLE [MHDS];
--GO
--CREATE TABLE [MHDS] (
--	[Id]  int PRIMARY KEY IDENTITY(1, 1),
--	[QId] bigint,
--	[FNameEn] nvarchar(255),
--	[MNameEn] nvarchar(255),
--	[LNameEn] nvarchar(255),
--	[FNameAr] nvarchar(255),
--	[MNameAr] nvarchar(255),
--	[LNameAr] nvarchar(255),
--	[ReqNumber] nvarchar(255) ,
--	[HCNumber] nvarchar(255) ,
--	[Nationality] nvarchar(255) ,
--	[Dob] date,
--	[CurrentAssignedHC] nvarchar(255) ,
--	[Relationship]  nvarchar(255) , -- Not to be used for Phase 1
--	[BNo] int,
--	[ZNo] int,
--	[SNo] int,
--	[UNo] int,
--	[MedInfo] NVARCHAR(max), -- Not to be used for Phase 1				 
--	[IsGisAddressManualyEntered] bit,
--	[Action] nvarchar(255) ,
--	[Consent] nvarchar(255) ,
--	[SubmittedBy] nvarchar(255) ,
--	[SubmittedAt] datetime,
--	[SubmitterQId] bigint,
--	[SubmitterEmail] nvarchar(255) ,
--	[SubmitterMobile] nvarchar(255) ,
--	[SecondaryPhoneMobile] nvarchar(255),
--	[CreatedAt] datetime,
--	[UpdatedAt] datetime,
--	[Status] nvarchar(255) FOREIGN KEY REFERENCES Status([Code]),
--	[StatusInternal] nvarchar(255) ,	
--	[LastActionBy] nvarchar(255) ,
--	[CreateSource] nvarchar(255) ,
--	[UpdateSource] nvarchar(255) 
--	UNIQUE([ReqNumber]),
--	INDEX IDX_MHDS_QId ([QId]),
--	INDEX IDX_MHDS_SubmitterQId ([SubmitterQId])
--)
--GO

--IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MHDSReqMedList' AND type = 'U') DROP TABLE [MHDSReqMedList];
--GO

--CREATE TABLE [dbo].[MHDSReqMedList](
--	[Id] [int] IDENTITY(1,1),
--	[QId] bigint,
--	[SubmitterQId] bigint,
--	[ReqNumber] [nvarchar](255) FOREIGN KEY REFERENCES [MHDS-Deprecated] ([ReqNumber]),
--	[MedName] [nvarchar](255),
--	[PrscOrderId] [nvarchar](255),
--	[PrscDate] [datetime],
--	[PrscDueDate] [datetime],
--	[LastDispenseDate] [datetime],
--	[SupplyDuration] [nvarchar](255),
--	[LastdispensedLocation] [nvarchar](255),
--	[OrderByName] [nvarchar](255),
--	[AttachId] [uniqueidentifier],
--	[CreatedAt] [datetime],
--	[UpdatedAt] [datetime],
--	[CreateSource] [nvarchar](255),
--	[UpdateSource] [nvarchar](255),	
--  	INDEX IDX_MHDSReqMedList_RqNm ([ReqNumber]),
--	INDEX IDX_MHDSReqMedList_QId ([QId]),
--  	INDEX IDX_MHDSReqMedList_SubQId ([SubmitterQId])
--) 
--GO


IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'MHDSRequestDetails'
             AND type = 'U')
    DROP TABLE [MHDSRequestDetails];
GO

CREATE TABLE [dbo].[MHDSRequestDetails]
(
    [Id]                          [bigint] IDENTITY (1,1) NOT NULL,
    [QId]                         [bigint]                NULL,
    [FNameEn]                     [nvarchar](255)         NULL,
    [MNameEn]                     [nvarchar](255)         NULL,
    [LNameEn]                     [nvarchar](255)         NULL,
    [FNameAr]                     [nvarchar](255)         NULL,
    [MNameAr]                     [nvarchar](255)         NULL,
    [LNameAr]                     [nvarchar](255)         NULL,
    [ReqNumber]                   [nvarchar](255)         NULL,
    [HCNumber]                    [nvarchar](255)         NULL,
    [Nationality]                 [nvarchar](255)         NULL,
    [Dob]                         [date]                  NULL,
    [CurrentAssignedHC]           [nvarchar](255)         NULL,
    [Relationship]                [nvarchar](255)         NULL,
    [BNo]                         [int]                   NULL,
    [ZNo]                         [int]                   NULL,
    [SNo]                         [int]                   NULL,
    [UNo]                         [int]                   NULL,
    [IsGisAddressManuallyEntered] [bit]                   NULL,
    [Action]                      [nvarchar](255)         NULL,
    [Consent]                     [bit]                   NULL,
    [SubmittedBy]                 [nvarchar](255)         NULL,
    [SubmittedAt]                 [datetime]              NULL,
    [SubmitterQId]                [bigint]                NULL,
    [SubmitterEmail]              [nvarchar](255)         NULL,
    [SubmitterMobile]             [nvarchar](255)         NULL,
    [SecondaryPhoneMobile]        [nvarchar](255)         NULL,
    [CreatedAt]                   [datetime]              NULL,
    [UpdatedAt]                   [datetime]              NULL,
    [Status]                      [nvarchar](255)         NULL,
    [StatusInternal]              [nvarchar](255)         NULL,
    [LastActionBy]                [nvarchar](255)         NULL,
    [CreateSource]                [nvarchar](255)         NULL,
    [UpdateSource]                [nvarchar](255)         NULL,
    PRIMARY KEY CLUSTERED
        (
         [Id] ASC
            ) WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
    UNIQUE NONCLUSTERED
        (
         [ReqNumber] ASC
            ) WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[MHDSRequestDetails]
    WITH CHECK ADD CONSTRAINT [MHDSRequestDetails_Status_Code_fk] FOREIGN KEY ([Status])
        REFERENCES [dbo].[Status] ([Code])
GO

ALTER TABLE [dbo].[MHDSRequestDetails]
    CHECK CONSTRAINT [MHDSRequestDetails_Status_Code_fk]
GO

--------------------------------------------------------------------------------------------------------------------------


IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'MHDSRequestMedicineList'
             AND type = 'U')
    DROP TABLE [MHDSRequestMedicineList];
GO

CREATE TABLE [dbo].[MHDSRequestMedicineList]
(
    [Id]                    [bigint] IDENTITY (1,1) NOT NULL,
    [RequestNumber]         [nvarchar](255)         NULL,
    [MedicineName]          [nvarchar](255)         NULL,
    [PrescriptionOrderId]   [nvarchar](255)         NULL,
    [PrescribedDate]        [datetime]              NULL,
    [PrescriptionDueDate]   [datetime]              NULL,
    [LastDispenseDate]      [datetime]              NULL,
    [SupplyDuration]        [nvarchar](255)         NULL,
    [LastDispensedLocation] [nvarchar](255)         NULL,
    [OrderByName]           [nvarchar](255)         NULL,
    [AttachId]              [uniqueidentifier]      NULL,
    CONSTRAINT [MHDSRequestMedicineList_pk] PRIMARY KEY CLUSTERED
        (
         [Id] ASC
            ) WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[MHDSRequestMedicineList]
    WITH CHECK ADD FOREIGN KEY ([RequestNumber])
        REFERENCES [dbo].[MHDSRequestDetails] ([ReqNumber])
GO

-- WI#2370