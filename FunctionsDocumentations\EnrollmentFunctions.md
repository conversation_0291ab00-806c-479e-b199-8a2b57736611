# Enrollment Functions Documentation

## Overview
The Enrollment Functions module provides REST API endpoints for managing enrollment requests in the eServices platform. This module handles the complete lifecycle of enrollment requests, including creation, retrieval, updates, and deletion, with comprehensive support for bilingual data (Arabic/English), status management, and integration with the Enterprise Data Warehouse (EDW).

## API Endpoints

### 1. Get Enrollment List By QID
- **Endpoint**: `GET /enrollments/submitter-qid/{qId}`
- **Description**: Retrieves a paginated list of enrollments for a given submitter's QID
- **Authorization**: Function level, QID-based
- **Parameters**:
  - `qId` (path, required): Submitter's QID (long)
  - `status` (query, optional): Filter by status category
    - InProcess: Submitted, Rework, Reworked
    - Archived: Approved, Cancelled, CancelledByEServ, Rejected
  - `skip` (query, optional): Number of records to skip (int)
  - `take` (query, optional): Number of records to take (int)
- **Response**:
  ```json
  [
    {
      "qId": "long",
      "reqNumber": "string",
      "fNameEn": "string",
      "mNameEn": "string",
      "lNameEn": "string",
      "fNameAr": "string",
      "mNameAr": "string",
      "lNameAr": "string",
      "status": "string",
      "statusDescriptionEn": "string",
      "statusDescriptionAr": "string",
      "submittedAt": "string (UTC)"
    }
  ]
  ```
- **Error Responses**:
  - 204: No enrollments found
  - 400: Invalid request parameters
  - 401: Unauthorized access
  - 500: Internal server error

### 2. Get Enrollment Stats By QID
- **Endpoint**: `GET /enrollments/submitter-qid/{qId}/stats`
- **Description**: Retrieves enrollment count statistics for a given submitter's QID
- **Authorization**: Function level, QID-based
- **Parameters**:
  - `qId` (path, required): Submitter's QID (long)
  - `status` (query, optional): Filter by status category
- **Response**:
  ```json
  {
    "count": "integer"
  }
  ```
- **Error Responses**:
  - 400: Invalid request parameters
  - 401: Unauthorized access
  - 500: Internal server error

### 3. Check InProcess Enrollment
- **Endpoint**: `GET /enrollments/inprocess-validation/{applicantQId}/{submitterQId}`
- **Description**: Validates if there are any in-process enrollments for the given applicant and submitter combination
- **Authorization**: Function level, QID-based
- **Parameters**:
  - `applicantQId` (path, required): Applicant's QID (long)
  - `submitterQId` (path, required): Submitter's QID (long)
- **Response**:
  ```json
  {
    "isInprocessExist": "boolean"
  }
  ```
- **Error Responses**:
  - 400: Invalid request parameters
  - 401: Unauthorized access
  - 500: Internal server error

### 4. Get Enrollment Item By Request Number
- **Endpoint**: `GET /enrollments/{reqNumber}`
- **Description**: Retrieves detailed enrollment information by request number
- **Authorization**: Function level, QID-based
- **Parameters**:
  - `reqNumber` (path, required): Enrollment Request Number (string)
- **Response**:
  ```json
  {
    "id": "integer",
    "reqNumber": "string",
    "qId": "string",
    "submitterQId": "long",
    "fNameEn": "string",
    "mNameEn": "string",
    "lNameEn": "string",
    "fNameAr": "string",
    "mNameAr": "string",
    "lNameAr": "string",
    "nationality": "string",
    "hcNumber": "string",
    "status": "string",
    "statusDescriptionEn": "string",
    "statusDescriptionAr": "string",
    "submittedAt": "string (UTC)",
    "createdAt": "string (UTC)",
    "updatedAt": "string (UTC)",
    "createSource": "string",
    "updateSource": "string"
  }
  ```
- **Error Responses**:
  - 204: Enrollment not found
  - 400: Invalid request number
  - 401: Unauthorized access
  - 500: Internal server error

### 5. Create Enrollment
- **Endpoint**: `POST /enrollments`
- **Description**: Creates a new enrollment request with automatic user details lookup
- **Authorization**: Function level, QID-based
- **Headers**:
  - `X-RequestOrigin` (required): Request origin source identifier
  - `X-PHCC-Environment`: PHCC environment for user details lookup
- **Request Body**:
  ```json
  {
    "qId": "string",
    "submitterQId": "long",
    "fNameEn": "string",
    "mNameEn": "string",
    "lNameEn": "string",
    "fNameAr": "string",
    "mNameAr": "string",
    "lNameAr": "string",
    "nationality": "string",
    "hcNumber": "string"
  }
  ```
- **Response**: Created enrollment object
- **Error Responses**:
  - 400: Invalid request body or missing headers
  - 401: Unauthorized access
  - 500: Database operation error

### 6. Update Enrollment By Request Number
- **Endpoint**: `PUT /enrollments/{reqNumber}`
- **Description**: Updates an existing enrollment while maintaining history
- **Authorization**: Function level, QID-based
- **Parameters**:
  - `reqNumber` (path, required): Enrollment Request Number
- **Headers**:
  - `X-RequestOrigin` (required): Request origin source
- **Request Body**: Same as Create Enrollment
- **Response**: Updated enrollment object
- **Error Responses**:
  - 400: Invalid request parameters or body
  - 401: Unauthorized access
  - 500: Database operation error

### 7. Delete Enrollment By Request Number
- **Endpoint**: `DELETE /enrollments/{reqNumber}`
- **Description**: Deletes an enrollment request (soft delete)
- **Authorization**: Function level, QID-based
- **Parameters**:
  - `reqNumber` (path, required): Enrollment Request Number
- **Response**: No content
- **Error Responses**:
  - 400: Invalid request number
  - 401: Unauthorized access
  - 500: Database operation error

## Technical Implementation

### Database Integration
1. **Context Management**
   - `EnrollmentContext`: Primary enrollment data
   - `EDWDbContext`: Enterprise Data Warehouse integration
   - DbContextFactory pattern for efficient connection management

2. **Query Optimization**
   - Asynchronous operations with async/await
   - No-tracking queries for read operations
   - Efficient projection using Select statements
   - Join operations with proper indexing

3. **Data Access Patterns**
   - Repository pattern implementation
   - Unit of Work for transaction management
   - Proper connection disposal
   - Error handling and logging

### Security Implementation
1. **Authorization**
   - Function-level security
   - QID-based access control
   - Request origin validation
   - Proper error handling for unauthorized access

2. **Data Protection**
   - Input validation and sanitization
   - Secure error messages
   - Audit logging
   - Proper exception handling

### Performance Features
1. **Query Optimization**
   - Pagination implementation
   - Efficient joins and projections
   - Proper indexing strategy
   - Cache considerations

2. **Resource Management**
   - Connection pooling
   - Async operations
   - Proper resource disposal
   - Memory optimization

### Integration Features
1. **EDW Integration**
   - User details lookup via stored procedure
   - XML parameter handling
   - Error handling and fallback
   - Environment-specific routing

2. **Status Management**
   - Comprehensive status tracking
   - Status category management
   - Status change logging
   - Status validation rules

### Data Handling
1. **Multilingual Support**
   - Arabic/English field pairs
   - Proper character encoding
   - RTL/LTR handling
   - Language-specific validation

2. **Audit Trail**
   - Creation/modification tracking
   - Source tracking
   - Timestamp management
   - User action logging

## Response Models

### GetEnrollmentListResponse
```json
{
  "qId": "long",
  "reqNumber": "string",
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string",
  "status": "string",
  "statusDescriptionEn": "string",
  "statusDescriptionAr": "string",
  "submittedAt": "string (UTC)"
}
```

### GetEnrollmentStatsResponse
```json
{
  "count": "integer"
}
```

### CheckInProcessEnrollmentResponse
```json
{
  "isInprocessExist": "boolean"
}
```

### GetEnrollmentItemResponse
```json
{
  "id": "integer",
  "reqNumber": "string",
  "qId": "string",
  "submitterQId": "long",
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string",
  "nationality": "string",
  "hcNumber": "string",
  "status": "string",
  "statusDescriptionEn": "string",
  "statusDescriptionAr": "string",
  "submittedAt": "string (UTC)",
  "createdAt": "string (UTC)",
  "updatedAt": "string (UTC)",
  "createSource": "string",
  "updateSource": "string"
}
```

## Error Handling

### Error Response Format
```json
{
  "message": "string",
  "details": "string",
  "stackTrace": "string (development only)"
}
```

### Common Error Scenarios
1. **Authorization Errors**
   - Invalid QID
   - Unauthorized access
   - Missing headers

2. **Validation Errors**
   - Invalid request parameters
   - Missing required fields
   - Invalid data formats

3. **Database Errors**
   - Connection issues
   - Constraint violations
   - Deadlocks

4. **Integration Errors**
   - EDW service unavailable
   - Invalid response format
   - Timeout issues

## Best Practices
1. **Error Handling**
   - Use proper HTTP status codes
   - Provide meaningful error messages
   - Implement proper logging
   - Handle all exceptions

2. **Performance**
   - Implement pagination
   - Use async/await
   - Optimize database queries
   - Consider caching

3. **Security**
   - Validate all inputs
   - Implement proper authorization
   - Use secure communication
   - Follow least privilege principle

4. **Maintenance**
   - Follow coding standards
   - Document all changes
   - Monitor performance
   - Regular security reviews