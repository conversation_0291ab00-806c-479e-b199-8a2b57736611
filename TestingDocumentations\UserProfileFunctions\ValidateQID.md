# Validate QID Tests

## Test Cases

### 1. TestValidateQId

Tests QID validation with various flags.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestValidateQId()
{
    // Arrange
    var request = GetRestRequest("validate-qid/{qId}");
    request.AddUrlSegment("qId", GetMoqUser().UserQId);
    request.AddOrUpdateHeader(PhccEnvironment, "STG");

    // Act
    var response = await _client.GetAsync<ValidateQIDFlags>(request);
    response.ThrowIfNull();

    // Assert
    True(response.isCitizen == false);
    True(response.isMinor == false);
    True(response.isHCardPresent == true);
    True(response.isHCardValid == true);
    True(response.isHCardinGrace == false);
    True(response.isQIDValid == true);
    True(response.isQIDinGrace == false);
    True(response.isFPhysicianAssigned == true);
    True(response.isHCenterAssigned == true);
    True(response.isGISAddressNotRecent == false);
    True(response.isOGCC == false);
}
```

### 2. TestValidateWithInvalidQId

Tests QID validation with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestValidateWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("validate-qid/{qId}");
    request.AddUrlSegment("qId", "22222222211");
    request.AddHeader(PhccEnvironment, "STG");

    // Act
    var response = await _client.GetAsync<ValidateQIDFlags>(request);

    // Assert
    True(response is null);
}
```

## Request Details

### Endpoint
```
GET validate-qid/{qId}
```

### Headers
- `PhccEnvironment`: "STG"

### URL Parameters
- `qId`: QID to validate

## Response Model

### ValidateQIDFlags
```csharp
public class ValidateQIDFlags
{
    public bool isCitizen { get; set; }
    public bool isMinor { get; set; }
    public bool isHCardPresent { get; set; }
    public bool isHCardValid { get; set; }
    public bool isHCardinGrace { get; set; }
    public bool isQIDValid { get; set; }
    public bool isQIDinGrace { get; set; }
    public bool isFPhysicianAssigned { get; set; }
    public bool isHCenterAssigned { get; set; }
    public bool isGISAddressNotRecent { get; set; }
    public bool isOGCC { get; set; }
}
```

## Test Data

### Success Case
- QID: From GetMoqUser().UserQId
- Expected: All validation flags set correctly

### Error Case
- QID: "22222222211"
- Expected: Null response

## Validation Rules

### Success Case Validation
1. Response not null
2. All flags validated:
   - Citizenship status
   - Age status (minor)
   - Health card status
   - QID validity
   - Physician assignment
   - Health center assignment
   - GIS address status
   - OGCC status

### Error Case Validation
1. Response is null

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Invalid format

2. **System Errors**
   - Database errors
   - Service unavailable
   - Validation service issues

## Notes

1. **Validation Flags**
   - Multiple status checks
   - Health card validation
   - QID validation
   - Assignment checks

2. **Response Format**
   - Boolean flags
   - Comprehensive status
   - Multiple validations

3. **Performance**
   - Multiple system checks
   - External service calls
   - Status verification
