﻿using EServiceFunctions.Models.Transfer;
using EServiceFunctions.RequestResponseModels.Transfer;

namespace EServiceFunctions.MapperModels;

public static class TransferMapper
{
    public static GetTransferItemResponse MapTransferToGetTransferItemResponse(Transfer transfer)
    {
       
            var response = new GetTransferItemResponse()
            {
                QId = transfer.QId,
                FNameEn = transfer.FNameEn,
                MNameEn = transfer.MNameEn,
                LNameEn = transfer.LNameEn,
                FNameAr = transfer.FNameAr,
                MNameAr = transfer.MNameAr,
                LNameAr = transfer.LNameAr,
                ReqNumber = transfer.ReqNumber,
                Nationality = transfer.Nationality,
                Dob = transfer.Dob.ToUtcString(),
                Consent = transfer.Consent,
                HCNumber = transfer.HCNumber,
                BNo = transfer.BNo,
                ZNo = transfer.ZNo,
                SNo = transfer.SNo,
                UNo = transfer.UNo,
                CurrentHC = transfer.CurrentHC,
                CatchmentHC = transfer.CatchmentHC,
                PrefHC = transfer.PrefHC,
                AttachId1 = transfer.AttachId1,
                AttachId2 = transfer.AttachId2,
                AttachId3 = transfer.AttachId3,
                AttachId4 = transfer.AttachId4,
                AttachId5 = transfer.AttachId5,
                AttachId6 = transfer.AttachId6,
                TransferReason = transfer.TransferReasonNavigation?.ReasonCode,
                TransferReasonDescriptionEn = transfer.TransferReasonNavigation!.ReasonEn,
                TransferReasonDescriptionAr = transfer.TransferReasonNavigation!.ReasonAr,
                SubmittedBy = transfer.SubmittedBy,
                SubmittedAt = transfer.SubmittedAt.ToUtcString(),
                SubmitterQId = transfer.SubmitterQId,
                SubmitterEmail = transfer.SubmitterEmail,
                SubmitterMobile = transfer.SubmitterMobile,
                CreatedAt = transfer.CreatedAt.ToUtcString(),
                UpdatedAt = transfer.UpdatedAt.ToUtcString(),
                Status = transfer.StatusNavigation?.Code,
                StatusDescriptionEn = transfer.StatusNavigation!.DescriptionEn,
                StatusDescriptionAr = transfer.StatusNavigation!.DescriptionAr,
                SN = transfer.SN,
                GisAddressUpdatedAt = transfer.GisAddressUpdatedAt.ToUtcString(),
                IsGisAddressManualyEntered = transfer.IsGisAddressManualyEntered
            };

            return response;
        }


    public static Transfer MapCreateUpdateTransferRequestToTransfer(CreateUpdateTransferRequest createUpdateTransferRequest)
    {
        return new Transfer
        {
            QId = createUpdateTransferRequest.QId,
            FNameEn = createUpdateTransferRequest.FNameEn,
            MNameEn = createUpdateTransferRequest.MNameEn,
            LNameEn = createUpdateTransferRequest.LNameEn,
            FNameAr = createUpdateTransferRequest.FNameAr,
            MNameAr = createUpdateTransferRequest.MNameAr,
            LNameAr = createUpdateTransferRequest.LNameAr,
            Nationality = createUpdateTransferRequest.Nationality,
            Dob = createUpdateTransferRequest.Dob,
            Consent = createUpdateTransferRequest.Consent,
            HCNumber = createUpdateTransferRequest.HCNumber,
            BNo = createUpdateTransferRequest.BNo,
            ZNo = createUpdateTransferRequest.ZNo,
            SNo = createUpdateTransferRequest.SNo,
            UNo = createUpdateTransferRequest.UNo,
            CurrentHC = createUpdateTransferRequest.CurrentHC,
            CatchmentHC = createUpdateTransferRequest.CatchmentHC,
            PrefHC = createUpdateTransferRequest.PrefHC,
            AttachId1 = createUpdateTransferRequest.AttachId1,
            AttachId2 = createUpdateTransferRequest.AttachId2,
            AttachId3 = createUpdateTransferRequest.AttachId3,
            AttachId4 = createUpdateTransferRequest.AttachId4,
            AttachId5 = createUpdateTransferRequest.AttachId5,
            AttachId6 = createUpdateTransferRequest.AttachId6,
            TransferReason = createUpdateTransferRequest.TransferReason,
            SubmittedBy = createUpdateTransferRequest.SubmittedBy,
            SubmitterQId = createUpdateTransferRequest.SubmitterQId,
            SubmitterEmail = createUpdateTransferRequest.SubmitterEmail,
            SubmitterMobile = createUpdateTransferRequest.SubmitterMobile,
            GisAddressUpdatedAt = createUpdateTransferRequest.GisAddressUpdatedAt,
            IsGisAddressManualyEntered = createUpdateTransferRequest.IsGisAddressManualyEntered
        };
    }
}