﻿namespace TestEServiceFunctions;

[Collection("EDWDropDownListFunctions")]
public class TestEdwDropDownListFunctions(ITestOutputHelper output, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetOccupationList()
    {
        // Arrange
        var request = GetRestRequest("occupations");
        
        // Act
        var response = await _client.GetAsync<List<GetOccupationListResponse>>(request);
        output.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count > 0);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetNationalityList()
    {
        // Arrange
        var request = GetRestRequest("nationalities");
        
        // Act
        var response = await _client.GetAsync<List<GetNationalityListResponse>>(request);
        output.LogToConsole(response);
        response.Should().NotBeNull();
        response.Should().HaveCountGreaterThan(0);
        
        // Assert
        True(response?.Count > 0);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetVisaTypeList()
    {
        // Arrange
        var request = GetRestRequest("visatypes");
        
        // Act
        var response = await _client.GetAsync<List<GetVisaTypeListResponse>>(request);
        output.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count > 0);
        True(response.Count > 10);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetLanguageList()
    {
        // Arrange
        var request = GetRestRequest("languages");
        
        // Act
        var response = await _client.GetAsync<List<GetLanguageListResponse>>(request);
        output.LogToConsole(response);

        // Assert
        True(response?.Count > 0);
        True(response is not null);
    }
}