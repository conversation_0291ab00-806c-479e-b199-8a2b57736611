﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.FamilyPhysician;

//[Keyless]
[Table("MRD_Nationality")]
public class MRD_Nationality
{
    [StringLength(3)]
    [Unicode(false)]
    [Key]
    public string? COUNTRY_CODE { get; set; }
    [StringLength(2)]
    [Unicode(false)]
    public string? ALPHA2_CODE { get; set; }
    [StringLength(3)]
    [Unicode(false)]
    public string? ALPHA3_CODE { get; set; }
    [StringLength(200)]
    [Unicode(false)]
    public string? COUNTRY_NAME_EN { get; set; }
    [StringLength(500)]
    public string? COUNTRY_NAME_ARB { get; set; }
    [StringLength(10)]
    [Unicode(false)]
    public string? STANDARD_SYSTEM { get; set; }
    [StringLength(20)]
    [Unicode(false)]
    public string? IS_QATARI { get; set; }
    [StringLength(10)]
    [Unicode(false)]
    public string? IS_GCC { get; set; }
    [StringLength(10)]
    [Unicode(false)]
    public string? IS_MENA { get; set; }
    [StringLength(100)]
    [Unicode(false)]
    public string? REGION { get; set; }
    public int? ACTIVE_FLG { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LAST_UPDATE_DATE { get; set; }
    [StringLength(50)]
    [Unicode(false)]
    public string? NATIONALITY { get; set; }
}