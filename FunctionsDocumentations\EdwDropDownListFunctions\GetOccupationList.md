# GetOccupationList

## Overview
Retrieves a list of occupations from the Medical Records Database.

## Endpoint
- **Route**: `occupations`
- **Method**: GET
- **Authorization**: Function level

## Response
- **200 OK**: Returns list of occupations
  ```json
  [
    {
      "PRF_CODE": "string",      // Occupation code
      "PRF_ENDSC": "string",     // English description
      "PRF_ARDSCM": "string"     // Arabic description (male)
    }
  ]
  ```
- **204 No Content**: No occupations found
- **400 Bad Request**: Error processing request

## Business Logic
1. Logs request URL and operation start
2. Queries MAP_MOI_OccupationTypes table
3. Projects to GetOccupationListResponse model
4. Checks for empty result set
5. Returns formatted response

## Data Source
- Table: MAP_MOI_OccupationTypes
- Context: MRDDbContext (Medical Records Database)

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient projection in Select statement
- No unnecessary joins or filters
