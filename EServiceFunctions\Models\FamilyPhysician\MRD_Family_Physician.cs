﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.FamilyPhysician;

//[Keyless]
public class MRD_Family_Physician
{
    [StringLength(30)]
    [Unicode(false)]
    [Key]
    public string? CORP_ID { get; set; }
    [StringLength(240)]
    [Unicode(false)]
    public string? NAME_EN { get; set; }
    [StringLength(250)]
    public string? NAME_ARB { get; set; }
    public int? PHCC_EXP_YRS { get; set; }
    [StringLength(150)]
    [Unicode(false)]
    public string? POSITION_EN { get; set; }
    [StringLength(150)]
    public string? POSITION_ARB { get; set; }
    [StringLength(50)]
    [Unicode(false)]
    public string? CLINICAL_TITLE_EN { get; set; }
    [StringLength(8)]
    public string? CLINICAL_TITLE_ARB { get; set; }
    [StringLength(3)]
    [Unicode(false)]
    public string? FAC_CODE { get; set; }
    [StringLength(114)]
    [Unicode(false)]
    public string HEALTH_CENTER { get; set; } = null!;
    public string? QUALIFICATIONS { get; set; }
    public string? GENDER_CODE { get; set; }
    [StringLength(50)]
    [Unicode(false)]
    public string? LANGUAGE_CODE { get; set; }
    public int? TOTAL_EXPERIENCE { get; set; }
    [Unicode(false)]
    public string? PHYSICIAN_PHOTO { get; set; }
    [StringLength(50)]
    [Unicode(false)]
    public string? SPECIALTY_CODE { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LAST_UPDATE_DATE { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LAST_LOAD_DATE { get; set; }
    [StringLength(250)]
    [Unicode(false)]
    public string? OTHER_LANGUAGES { get; set; }
    [StringLength(250)]
    [Unicode(false)]
    public string? OTHER_SPECIALITY { get; set; }
    public bool? STATUS { get; set; }
    public bool? PHOTO_CONSENT { get; set; }
    public byte[]? PHYSICIAN_PHOTO_BINARY { get; set; }
}