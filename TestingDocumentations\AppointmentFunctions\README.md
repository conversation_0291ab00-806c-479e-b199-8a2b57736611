# Appointment Functions Test Documentation

## Overview

The Appointment Functions test suite verifies the functionality of appointment-related operations in the EservicesRESTapis project. This comprehensive test suite covers appointment booking, retrieval, updating, and cancellation operations.

## Test Categories

### 1. Appointment Retrieval
- [Get Appointments List](./GetAppointmentsList.md)
- [Get Appointment Details](./GetAppointmentDetails.md)
- [Get Available Slots](./GetAvailableSlots.md)

### 2. Appointment Management
- [Book Appointment](./BookAppointment.md)
- [Update Appointment](./UpdateAppointment.md)
- [Cancel Appointment](./CancelAppointment.md)

### 3. Appointment Validation
- [Slot Availability Check](./SlotAvailabilityCheck.md)
- [Booking Validation](./BookingValidation.md)

## Test Structure

Each test follows a consistent pattern:

```csharp
[Fact]
[Trait(Category, HappyPath/UnHappyPath)]
public async Task TestMethodName()
{
    // Arrange
    // Test setup and data preparation

    // Act
    // Execute the API call

    // Assert
    // Validate the response
}
```

## Common Test Components

### 1. Request Building
```csharp
var request = GetRestRequest("endpoint");
request.AddOrUpdateHeader(JwtClaimsQId, userQId);
request.AddJsonBody(requestBody);
```

### 2. Response Validation
```csharp
NotNull(response);
Equal(expectedStatusCode, response.StatusCode);
// Additional validations specific to the test case
```

### 3. Test Data Generation
```csharp
private User GetMoqUser() => new()
{
    UserQId = ***********,
    // Additional user properties
};
```

## Test Categories Overview

### Happy Path Tests
- Verify successful appointment operations
- Test with valid input data
- Ensure proper response formats
- Validate business logic

### Unhappy Path Tests
- Test error handling
- Validate input validation
- Check security constraints
- Verify edge cases

## Response Models

### AppointmentResponse
```csharp
public class AppointmentResponse
{
    public string QId { get; set; }
    public string AppointmentId { get; set; }
    public DateTime AppointmentDate { get; set; }
    public string Status { get; set; }
    // Additional properties
}
```

### BookingRequest
```csharp
public class BookingRequest
{
    public string QId { get; set; }
    public DateTime PreferredDate { get; set; }
    public string ServiceType { get; set; }
    // Additional properties
}
```

## Test Environment Setup

### Prerequisites
- Valid test user credentials
- Test environment configuration
- Required test data

### Configuration
```json
{
    "BaseUrl": "https://api-endpoint",
    "TestUser": {
        "QId": "***********",
        "Role": "User"
    }
}
```

## Best Practices

1. **Test Independence**
   - Each test should be self-contained
   - Clean up test data after execution
   - Avoid test interdependencies

2. **Error Handling**
   - Validate error responses
   - Check error messages
   - Verify error codes

3. **Data Management**
   - Use consistent test data
   - Clean up test data
   - Handle test data conflicts

4. **Logging**
   - Log request/response details
   - Track test execution
   - Facilitate debugging

## Common Test Scenarios

1. **Booking Flow**
   - Check available slots
   - Book appointment
   - Verify booking
   - Cancel if needed

2. **Update Flow**
   - Book initial appointment
   - Update details
   - Verify changes
   - Clean up

3. **Cancellation Flow**
   - Book appointment
   - Cancel appointment
   - Verify cancellation
   - Check slot availability

## Test Execution Guidelines

1. **Prerequisites Check**
   - Verify environment
   - Check configurations
   - Ensure test data

2. **Test Execution**
   - Run tests in isolation
   - Monitor execution
   - Check logs

3. **Results Validation**
   - Verify assertions
   - Check coverage
   - Review logs

## Troubleshooting

Common issues and solutions:

1. **Authentication Failures**
   - Check QId validity
   - Verify JWT token
   - Validate headers

2. **Data Conflicts**
   - Clean test data
   - Use unique identifiers
   - Handle concurrent tests

3. **Environment Issues**
   - Check API availability
   - Verify configurations
   - Review network status

## Notes

- Tests use xUnit framework
- RestSharp for API calls
- JSON.NET for serialization
- Comprehensive logging implemented
