# Comment Validation Tests

## Overview

These tests verify the validation rules and error handling for comment-related operations. The test suite covers input validation, permission checks, and business rule enforcement.

## Test Cases

### TestAddComment_EmptyContent_ReturnsBadRequest

Tests validation of empty comment content.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnhappyPath)]
public async Task TestAddComment_EmptyContent_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest("comments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var commentRequest = new AddCommentRequest
    {
        EntityId = "TEST-123",
        Content = "",
        Type = "General"
    };
    request.AddJsonBody(commentRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    False(response.IsSuccessful);
    Equal(BadRequest, response.StatusCode);
    Contains("Content cannot be empty", response.Content);
}
```

### TestAddComment_ContentTooLong_ReturnsBadRequest

Tests validation of comment content length.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnhappyPath)]
public async Task TestAddComment_ContentTooLong_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest("comments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var commentRequest = new AddCommentRequest
    {
        EntityId = "TEST-123",
        Content = new string('x', MaxContentLength + 1),
        Type = "General"
    };
    request.AddJsonBody(commentRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    False(response.IsSuccessful);
    Equal(BadRequest, response.StatusCode);
    Contains($"Content exceeds maximum length of {MaxContentLength}", response.Content);
}
```

## Validation Rules

### 1. Content Validation
```csharp
public class CommentValidator : AbstractValidator<Comment>
{
    public CommentValidator()
    {
        RuleFor(x => x.Content)
            .NotEmpty()
            .WithMessage("Content cannot be empty")
            .MaximumLength(MaxContentLength)
            .WithMessage($"Content cannot exceed {MaxContentLength} characters")
            .Must(BeValidContent)
            .WithMessage("Content contains invalid characters");

        RuleFor(x => x.Type)
            .NotEmpty()
            .WithMessage("Type is required")
            .Must(BeValidType)
            .WithMessage("Invalid comment type");

        RuleFor(x => x.EntityId)
            .NotEmpty()
            .WithMessage("EntityId is required")
            .Must(BeValidEntityId)
            .WithMessage("Invalid entity ID format");
    }

    private bool BeValidContent(string content)
    {
        return !content.Contains("<script>") &&
               !content.Contains("javascript:") &&
               Regex.IsMatch(content, @"^[\w\s\.,!?@#$%^&*()-=+\[\]{}|;:'""<>\/\\]*$");
    }

    private bool BeValidType(string type)
    {
        return AllowedTypes.Contains(type);
    }

    private bool BeValidEntityId(string entityId)
    {
        return Regex.IsMatch(entityId, @"^[A-Z]+-\d+$");
    }
}
```

### 2. Permission Validation
```csharp
public class CommentPermissionValidator
{
    private readonly IUserContext _userContext;
    private readonly IEntityRepository _entityRepository;

    public async Task ValidatePermissions(Comment comment)
    {
        var user = _userContext.GetCurrentUser();
        var entity = await _entityRepository.GetByIdAsync(comment.EntityId);

        if (entity == null)
            throw new ValidationException("Entity not found");

        if (!await HasAccessToEntity(user, entity))
            throw new UnauthorizedException("Not authorized to comment on this entity");

        if (IsEntityLocked(entity))
            throw new ValidationException("Entity is locked for comments");

        if (await HasReachedCommentLimit(user, entity))
            throw new ValidationException("Comment limit reached for this entity");
    }

    private async Task<bool> HasAccessToEntity(User user, Entity entity)
    {
        return entity.IsPublic ||
               entity.OwnerId == user.Id ||
               await _entityRepository.HasSharedAccess(entity.Id, user.Id);
    }

    private bool IsEntityLocked(Entity entity)
    {
        return entity.Status == EntityStatus.Locked ||
               entity.Status == EntityStatus.Archived;
    }

    private async Task<bool> HasReachedCommentLimit(User user, Entity entity)
    {
        var count = await _commentRepository.GetUserCommentCount(user.Id, entity.Id);
        return count >= MaxCommentsPerUserPerEntity;
    }
}
```

### 3. Business Rule Validation
```csharp
public class CommentBusinessRuleValidator
{
    private readonly ICommentRepository _commentRepository;
    private readonly IUserContext _userContext;

    public async Task ValidateBusinessRules(Comment comment)
    {
        await ValidateCommentFrequency(comment);
        await ValidateContentDuplication(comment);
        await ValidateUserStatus(comment);
        await ValidateWorkingHours(comment);
    }

    private async Task ValidateCommentFrequency(Comment comment)
    {
        var user = _userContext.GetCurrentUser();
        var lastComment = await _commentRepository.GetLastUserComment(user.Id);

        if (lastComment != null)
        {
            var timeSinceLastComment = DateTime.UtcNow - lastComment.CreatedAt;
            if (timeSinceLastComment < MinTimeBetweenComments)
                throw new ValidationException(
                    $"Please wait {MinTimeBetweenComments.TotalSeconds} seconds between comments"
                );
        }
    }

    private async Task ValidateContentDuplication(Comment comment)
    {
        var user = _userContext.GetCurrentUser();
        var recentComments = await _commentRepository.GetRecentUserComments(
            user.Id,
            TimeSpan.FromMinutes(5)
        );

        if (recentComments.Any(c => 
            StringSimilarity.Calculate(c.Content, comment.Content) > 0.9))
        {
            throw new ValidationException("Similar comment recently posted");
        }
    }

    private async Task ValidateUserStatus(Comment comment)
    {
        var user = _userContext.GetCurrentUser();
        var status = await _userRepository.GetUserStatus(user.Id);

        if (status.IsSuspended)
            throw new ValidationException("User is suspended");

        if (status.RequiresVerification)
            throw new ValidationException("Please verify your account");

        if (status.SpamScore > MaxSpamScore)
            throw new ValidationException("Action blocked due to suspicious activity");
    }

    private async Task ValidateWorkingHours(Comment comment)
    {
        var entity = await _entityRepository.GetByIdAsync(comment.EntityId);
        if (!entity.AllowAfterHoursComments)
        {
            var localTime = TimeZoneInfo.ConvertTimeFromUtc(
                DateTime.UtcNow,
                TimeZoneInfo.FindSystemTimeZoneById(entity.TimeZone)
            );

            if (localTime.Hour < WorkingHoursStart || localTime.Hour >= WorkingHoursEnd)
                throw new ValidationException("Comments only allowed during working hours");
        }
    }
}
```

## Error Handling

### 1. Validation Error Handling
```csharp
private async Task<IActionResult> HandleValidationError(
    ValidationException ex)
{
    _logger.LogWarning(
        "Validation failed: {Message}",
        ex.Message
    );

    return BadRequest(new ErrorResponse
    {
        Code = "VALIDATION_ERROR",
        Message = ex.Message,
        Details = ex.Errors
            .Select(e => new ValidationError
            {
                Field = e.PropertyName,
                Message = e.ErrorMessage
            })
            .ToList()
    });
}
```

### 2. Permission Error Handling
```csharp
private async Task<IActionResult> HandlePermissionError(
    UnauthorizedException ex)
{
    _logger.LogWarning(
        "Permission denied: {Message}",
        ex.Message
    );

    await _securityService.LogSecurityEvent(
        SecurityEventType.PermissionDenied,
        GetCurrentUser().Id,
        ex.Message
    );

    return Unauthorized(new ErrorResponse
    {
        Code = "PERMISSION_DENIED",
        Message = ex.Message
    });
}
```

## Test Helper Methods

### 1. Validation Testing
```csharp
private async Task<IActionResult> ValidateComment(Comment comment)
{
    try
    {
        var validator = new CommentValidator();
        var result = await validator.ValidateAsync(comment);

        if (!result.IsValid)
        {
            return BadRequest(new ValidationProblemDetails(
                result.Errors.ToDictionary(
                    x => x.PropertyName,
                    x => new[] { x.ErrorMessage }
                )
            ));
        }

        await _permissionValidator.ValidatePermissions(comment);
        await _businessRuleValidator.ValidateBusinessRules(comment);

        return Ok();
    }
    catch (ValidationException ex)
    {
        return await HandleValidationError(ex);
    }
    catch (UnauthorizedException ex)
    {
        return await HandlePermissionError(ex);
    }
}
```

### 2. Test Data Generation
```csharp
private Comment GenerateValidComment()
{
    return new Comment
    {
        EntityId = "TEST-123",
        Content = "Valid test comment content",
        Type = "General",
        CreatedBy = GetMoqUser().UserQId,
        CreatedAt = DateTime.UtcNow
    };
}

private Comment GenerateInvalidComment()
{
    return new Comment
    {
        EntityId = "",
        Content = "<script>alert('xss')</script>",
        Type = "InvalidType",
        CreatedBy = GetMoqUser().UserQId,
        CreatedAt = DateTime.UtcNow
    };
}
```

## Notes

1. **Validation Strategy**
   - Implement comprehensive validation
   - Use proper error messages
   - Handle all edge cases

2. **Security**
   - Prevent XSS attacks
   - Validate permissions
   - Rate limit requests

3. **Performance**
   - Cache validation results
   - Optimize regex patterns
   - Handle concurrent validation

## Recommendations

1. **Validation Approach**
   - Use fluent validation
   - Implement custom rules
   - Add localization

2. **Security Measures**
   - Add content filtering
   - Implement rate limiting
   - Track validation failures

3. **Performance**
   - Cache common validations
   - Optimize rule checks
   - Handle bulk validation
