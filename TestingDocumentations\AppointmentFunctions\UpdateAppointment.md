# Update Appointment Tests

## Overview

These tests verify the functionality of updating existing appointments through the API. The test suite covers appointment rescheduling, modification of details, and various validation scenarios.

## Test Cases

### TestUpdateAppointment_ValidRequest_ReturnsOk

Tests the successful update of an existing appointment with valid request data.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateAppointment_ValidRequest_ReturnsOk()
{
    // Arrange
    var appointmentId = await CreateTestAppointment();
    var request = GetRestRequest($"appointments/{appointmentId}");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var updateRequest = new UpdateAppointmentRequest
    {
        AppointmentDate = DateTime.Now.AddDays(2),
        Notes = "Rescheduled appointment"
    };
    request.AddJsonBody(updateRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(OK, response.StatusCode);
    var updated = JsonConvert.DeserializeObject<AppointmentResponse>(response.Content!);
    NotNull(updated);
    Equal(updateRequest.AppointmentDate, updated.AppointmentDate);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid appointment ID
  - Updated appointment details
- **Expected Output**: 
  - 200 OK response
  - Updated appointment details
- **Validation Points**:
  - Response status code
  - Updated data verification
  - Appointment status

### TestUpdateAppointment_InvalidAppointment_ReturnsNotFound

Tests the API's handling of updates to non-existent appointments.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestUpdateAppointment_InvalidAppointment_ReturnsNotFound()
{
    // Arrange
    var request = GetRestRequest("appointments/invalid-id");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var updateRequest = new UpdateAppointmentRequest
    {
        AppointmentDate = DateTime.Now.AddDays(2)
    };
    request.AddJsonBody(updateRequest);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(NotFound, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Invalid appointment ID
- **Expected Output**: 404 NotFound response
- **Validation Points**:
  - Error status code
  - Error message validation

## Request/Response Models

### UpdateAppointmentRequest
```csharp
public class UpdateAppointmentRequest
{
    public DateTime? AppointmentDate { get; set; }
    public string? ServiceType { get; set; }
    public string? Location { get; set; }
    public string? Notes { get; set; }
}
```

### AppointmentResponse
```csharp
public class AppointmentResponse
{
    public string AppointmentId { get; set; }
    public string Status { get; set; }
    public DateTime AppointmentDate { get; set; }
    public string Location { get; set; }
    public string Notes { get; set; }
}
```

## Update Validation Rules

1. **Date Validation**
   - Must be a future date
   - Must be within update window
   - Must be a valid working day

2. **Status Validation**
   - Appointment must be in updateable status
   - Cannot update cancelled appointments
   - Cannot update completed appointments

3. **Authorization**
   - Must be appointment owner
   - Must have update permission
   - Must be within time limits

## Error Scenarios

1. **Invalid Appointment**
   - Non-existent appointment
   - Cancelled appointment
   - Completed appointment

2. **Invalid Updates**
   - Past dates
   - Invalid service types
   - Restricted locations

3. **Authorization Errors**
   - Wrong user
   - Insufficient permissions
   - Expired update window

## Best Practices

### 1. Update Validation
```csharp
// Validate appointment exists
var appointment = await GetAppointment(appointmentId);
if (appointment == null)
{
    return NotFound("Appointment not found");
}

// Validate update permission
if (!CanUpdateAppointment(appointment))
{
    return Forbidden("Cannot update this appointment");
}
```

### 2. Partial Updates
```csharp
// Apply only provided updates
if (updateRequest.AppointmentDate.HasValue)
{
    appointment.AppointmentDate = updateRequest.AppointmentDate.Value;
}

if (!string.IsNullOrEmpty(updateRequest.Notes))
{
    appointment.Notes = updateRequest.Notes;
}
```

### 3. Status Tracking
```csharp
// Track update history
appointment.LastUpdated = DateTime.UtcNow;
appointment.UpdatedBy = userQId;
appointment.UpdateHistory.Add(new UpdateRecord
{
    Timestamp = DateTime.UtcNow,
    UpdatedBy = userQId,
    Changes = changes
});
```

## Test Helper Methods

### 1. Appointment Creation
```csharp
private async Task<string> CreateTestAppointment()
{
    var createRequest = new BookingRequest
    {
        QId = GetMoqUser().UserQId.ToString(),
        PreferredDate = DateTime.Now.AddDays(1)
    };
    var response = await CreateAppointment(createRequest);
    return response.AppointmentId;
}
```

### 2. Validation Helpers
```csharp
private bool IsValidUpdateWindow(DateTime appointmentDate)
{
    var hoursUntilAppointment = (appointmentDate - DateTime.Now).TotalHours;
    return hoursUntilAppointment > MinUpdateWindowHours;
}
```

## Notes

- Tests cover both full and partial updates
- Proper validation of all updated fields
- Comprehensive error scenario coverage
- Detailed update history tracking
- Proper cleanup of test appointments
