<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.6" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="MockQueryable.Moq" Version="7.0.3" />
        <PackageReference Include="Moq.EntityFrameworkCore" Version="9.0.0.5" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\EServiceFunctions\EServiceFunctions.csproj" />
      <ProjectReference Include="..\MockDataLibrary\MockDataLibrary.csproj" />
      <ProjectReference Include="..\TestDoublesForUnitTest\TestDoublesForUnitTest\TestDoublesForUnitTest.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="UnitTestRegistrationFunctions_UpdateFix.cs" />
      <Compile Remove="UnitTestRegistrationFunctions_Fixed.cs" />
      <Compile Remove="UnitTestRegistrationFunctions_DeleteFix.cs" />
      <Compile Remove="AdditionalRegistrationFunctionsTests.cs" />
    </ItemGroup>

</Project>
