﻿namespace TestEServiceFunctions;

[Collection("TransferFunctions")]
public class TestTransferFunctions(IRestLibrary restLibrary, ITestOutputHelper testOutputHelper)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetTransferListByQId()
    {
        // Arrange
        var request = GetRestRequest("transfers/submitter-qid/{qId}");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

        // Act
        var response = await _client.GetAsync<List<GetTransferListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.Count > 0);
        Contains(response, x => x.QId == 94733427819);
        Contains(response, x => x.ReqNumber == "SV1ZCMN9RX1");
        Contains(response, x => x.FNameEn == "Jett");
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetTransferListByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("transfers/submitter-qid/{qId}");
        request.Method = Method.Get;
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222273);
        request.AddUrlSegment("qId", 22222222273);
        
        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response.StatusCode == NoContent);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetTransferStatsByQId()
    {
        // Arrange
        var request = GetRestRequest("transfers/submitter-qid/{qId}/stats");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

        // Act
        var response = await _client.GetAsync<GetTransferStatsResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.Count > 0);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetTransferStatsByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("transfers/submitter-qid/{qId}/stats");
        request.Method = Method.Get;
        request.AddUrlSegment("qId", 22222222273);
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222273);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response.StatusCode == NoContent);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessTransferByQId()
    {
        // Arrange
        var request = GetRestRequest("transfers/{qId}/inprocess-validation");
        request.AddUrlSegment("qId", 94733427819);

        // Act
        var response = await _client.GetAsync<CheckInProcessTransferResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.IsInprocessExist);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProcessTransferByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("transfers/{qId}/inprocess-validation");
        request.AddUrlSegment("qId", 94733427818);

        // Act
        var response = await _client.GetAsync<CheckInProcessTransferResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        False(response.IsInprocessExist);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetTransferItemByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("transfers/{reqNumber}");
        request.AddUrlSegment("reqNumber", "XBM423S5S9A");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);

        // Act
        var response = await _client.GetAsync<GetTransferItemResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.QId == 36210462220);
        True(response.ReqNumber == "XBM423S5S9A");
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetTransferItemByInvalidReqNumber()
    {
        // Arrange 
        var request = GetRestRequest("transfers/{reqNumber}");
        request.Method = Method.Get;
        request.AddUrlSegment("reqNumber", "SV1ZCMN9RX2");
        
        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        True(response.StatusCode == NoContent);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task<string> TestCreateTransfer()
    {
        // Arrange
        var request = GetRestRequest("transfers");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateTransfer");
        request = CreateUpdateTransfer(request);
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

        // Act
        var response = await _client.PostAsync<CreateTransferResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();
        response.ReqNumber.ThrowIfNull();

        // Assert
        NotNull(response);
        True(!response.ReqNumber.IsNullOrEmpty());
        return response.ReqNumber;
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCreateTransferWithInvalidObject()
    {
        // Arrange
        var request = GetRestRequest("transfers");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateTransfer");
        request.Method = Method.Post;
        
        var reqBody = new CreateUpdateTransferRequest();
        request.AddJsonBody(reqBody);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        True(response.StatusCode == BadRequest);
    }

    private static RestRequest CreateUpdateTransfer(RestRequest request)
    {
        var nationalityCodeList = MockNationality().Select(o => o.NatCode).ToList();
        var healthCentersList = MockHealthCenters().Select(o => o.Name).ToList();

        var createRequest = new Faker<CreateUpdateTransferRequest>()
            .RuleFor(x => x.QId, RandomNumber(11))
            .RuleFor(x => x.FNameEn, FirstNameEnglish)
            .RuleFor(x => x.MNameEn, MiddleNameEnglish)
            .RuleFor(x => x.LNameEn, LastNameEnglish)
            .RuleFor(x => x.FNameAr, FirstNameArabic)
            .RuleFor(x => x.MNameAr, MiddleNameArabic)
            .RuleFor(x => x.LNameAr, LastNameArabic)
            .RuleFor(x => x.Nationality, f => f.PickRandom(nationalityCodeList))
            .RuleFor(x => x.Dob, f => f.Date.Past(50))
            .RuleFor(x => x.Consent, true)
            .RuleFor(x => x.HCNumber, "HC" + RandomNumber(8))
            .RuleFor(x => x.BNo, f => f.Random.Int(1, 100))
            .RuleFor(x => x.ZNo, f => f.Random.Int(1, 100))
            .RuleFor(x => x.SNo, f => f.Random.Int(1, 100))
            .RuleFor(x => x.UNo, f => f.Random.Int(1, 100))
            .RuleFor(x => x.CurrentHC, f => f.PickRandom(healthCentersList))
            .RuleFor(x => x.CatchmentHC, f => f.PickRandom(healthCentersList))
            .RuleFor(x => x.PrefHC, f => f.PickRandom(healthCentersList))
            .RuleFor(x => x.AttachId1, NewGuid())
            .RuleFor(x => x.AttachId2, NewGuid())
            .RuleFor(x => x.AttachId3, NewGuid())
            .RuleFor(x => x.AttachId4, NewGuid())
            .RuleFor(x => x.AttachId5, NewGuid())
            .RuleFor(x => x.AttachId6, NewGuid())
            .RuleFor(x => x.TransferReason, f => f.PickRandom("TR1", "TR2", "TR3", "TR4"))
            .RuleFor(x => x.SubmittedBy, "Unit Test User")
            .RuleFor(x => x.SubmitterQId, GetMoqUser().UserQId)
            .RuleFor(x => x.SubmitterEmail, GetMoqUser().UserEmail)
            .RuleFor(x => x.SubmitterMobile, GetMoqUser().UserMobileNumber)
            .RuleFor(x => x.GisAddressUpdatedAt, f => f.Date.Past(2))
            .RuleFor(x => x.IsGisAddressManualyEntered, true)
            .Generate();

        request.AddJsonBody(createRequest);
        return request;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateTransferByReqNumber_Should_Return_OK()
    {
        // Arrange
        var request = GetRestRequest("transfers/{reqNumber}");
        request = CreateUpdateTransfer(request);
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("reqNumber", "NELRRPFMSHF");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateTransfer");
 
        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(OK == response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestUpdateTransferByInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("transfers/{reqNumber}");
        request = CreateUpdateTransfer(request);
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.Method = Method.Put;
        request.AddUrlSegment("reqNumber", "InvalidReqNumber");
        request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateTransfer");
        
        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        True(response.StatusCode == BadRequest);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteTransferByReqNumber()
    {
        // Arrange
        var reqNumber = await TestCreateTransfer();
        reqNumber.ThrowIfNull();
        await WaitAsync();
        var request = GetRestRequest("transfers/{reqNumber}");
        request.AddUrlSegment("reqNumber", reqNumber);
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

        // Act
        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.StatusCode == OK);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestDeleteTransferByInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("transfers/{reqNumber}");
        request.AddUrlSegment("reqNumber", "InvalidReqNumber");
        request.Method = Method.Delete;
        
        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.StatusCode == BadRequest);
    }
}