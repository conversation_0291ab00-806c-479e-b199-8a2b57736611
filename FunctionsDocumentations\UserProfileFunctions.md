# User Profile Functions API Documentation

This document provides detailed information about the User Profile Functions API endpoints, which handle user profile management, dependent relationships, and profile validation with support for bilingual (English/Arabic) descriptions.

## Overview
The User Profile module provides a comprehensive set of Azure Functions for managing user profiles and their dependents in the EServices system. This module handles user profile management, dependent relationships, and integration with MOI (Ministry of Interior) data.

## API Endpoints

### Get User And Dependent List By QID
- **Endpoint**: `GET /userprofiles/user-dependents/{qId}`
- **Description**: Retrieves user and dependent profile information based on a QID
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): User QID (long)
  - `isSubmitterDetailsOnly` (query, optional): Filter to get only submitter QID details (boolean)
- **Response Format**:
  ```json
  {
    "QId": "number",
    "QIdExpiryDt": "string",
    "PersonalDetails": {
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string"
    },
    "Dob": "string",
    "NationalityCode": "string",
    "NationalityEn": "string",
    "NationalityAr": "string",
    "GenderCode": "string",
    "GenderEn": "string",
    "GenderAr": "string",
    "PhoneMobile": "string",
    "SecondaryPhoneMobile": "string",
    "PrefComMode": "string",
    "PrefSMSLang": "string",
    "Consent": "boolean",
    "HealthcareInfo": {
      "HcNumber": "string",
      "HcExpiryDate": "string",
      "AssignedHealthCenter": "string",
      "AssignedHealthCenterEn": "string",
      "AssignedHealthCenterAr": "string",
      "PhysicianId": "string",
      "PhysicianFullNameEn": "string",
      "PhysicianFullNameAr": "string"
    },
    "Address": {
      "GisAddressStreet": "string",
      "GisAddressBuilding": "string",
      "GisAddressZone": "string",
      "GisAddressUnit": "string",
      "GisAddressUpdatedAt": "string"
    },
    "Dependents": [
      {
        // Same structure as parent profile
      }
    ]
  }
  ```
- **Responses**:
  - 200: Success (GetUserProfileResponse)
  - 204: No Content
  - 401: Unauthorized
- **Features**:
  - Live feeds enrichment (configurable)
  - Healthcare information integration
  - Dependent relationship management

### Validate QID
- **Endpoint**: `GET /validate-qid/{qId}`
- **Description**: Validates a QID and returns various status flags
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): User QID (long)
- **Response Format**:
  ```json
  {
    "isCitizen": "boolean",
    "isMinor": "boolean",
    "isQIDValid": "boolean",
    "isQIDinGrace": "boolean",
    "isHCardPresent": "boolean",
    "isHCardValid": "boolean",
    "isHCardinGrace": "boolean",
    "isGISAddressNotRecent": "boolean",
    "isFPhysicianAssigned": "boolean",
    "isHCenterAssigned": "boolean",
    "isOGCC": "boolean"
  }
  ```
- **Responses**:
  - 200: Success (ValidateQIDFlags)
  - 204: No Content

### Unenroll User Dependent By QID
- **Endpoint**: `DELETE /user-dependents/{submitterQId}/{dependentQId}`
- **Description**: Removes the relationship between a user and dependent
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `submitterQId` (path, required): User QID (long)
  - `dependentQId` (path, required): Dependent QID (long)
- **Responses**:
  - 200: Success
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error

### Create User Profile
- **Endpoint**: `POST /userprofiles`
- **Description**: Creates a new user profile
- **Authorization**: Function-level authorization required
- **Headers**:
  - `X-RequestOrigin` (required): Source of the request
- **Request Body**:
  ```json
  {
    "QId": "number",
    "PrefSMSLang": "string",
    "PrefComMode": "string",
    "SecondaryPhoneMobile": "string",
    "Consent": "boolean"
  }
  ```
- **Responses**:
  - 201: Created (UserProfile)
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error

### Update User Profile By QID
- **Endpoint**: `PUT /UserProfiles/{qId}`
- **Description**: Updates an existing user profile
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): User QID (long)
- **Headers**:
  - `X-RequestOrigin` (required): Source of the request
- **Request Body**: Same as Create User Profile
- **Responses**:
  - 200: Success
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error

### Delete User Profile By QID
- **Endpoint**: `DELETE /UserProfiles/{qId}`
- **Description**: Deletes a user profile (Internal use only)
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): User QID (long)
- **Responses**:
  - 200: Success with confirmation message
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error
- **Note**: This endpoint is marked with `[OpenApiIgnore]` and is for internal use only

### Get MOI Dependents
- **Endpoint**: `GET /userprofiles/moi-dependents/{qId}`
- **Description**: Retrieves dependent information from MOI (Ministry of Interior)
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `qId` (path, required): User QID (long)
  - `isLinked` (query, optional): Filter for linked/unlinked dependents (boolean)
- **Response Format**:
  ```json
  [
    {
      "QId": "number",
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "Linked": "boolean"
    }
  ]
  ```
- **Responses**:
  - 200: Success (List<GetMoiDependentsResponse>)
  - 204: No Content

### Link User Dependent By QID
- **Endpoint**: `POST /userprofiles/link-dependents/{submitterQId}/{dependentQId}`
- **Description**: Creates a relationship between a user and dependent
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `submitterQId` (path, required): User QID (long)
  - `dependentQId` (path, required): Dependent QID (long)
- **Headers**:
  - `X-RequestOrigin` (required): Source of the request
- **Responses**:
  - 201: Created
  - 400: Bad Request
  - 401: Unauthorized
  - 500: Internal Server Error

## Code Organization

The UserProfileFunctions class is organized into the following logical regions:

### GetUserAndDependentListByQID Function
- Main function for retrieving user and dependent profiles
- Handles data enrichment from live feeds
- Manages authorization and validation

### ValidateQID
- QID validation functionality
- Status flag generation
- Multiple validation checks

### UnenrollUserDependentByQID
- Dependent relationship management
- Relationship termination
- Authorization checks
- **Note**: Function is tagged under "Enrollment" in OpenAPI specification

### CreateUserProfile
- New user profile creation
- Request origin validation
- Data persistence

### UpdateUserProfile
- Profile update functionality
- Data validation
- Change tracking

### DeleteUserProfileByQID
- Profile deletion (internal use)
- Data cleanup
- Security checks

### GetMOIDependents
- MOI data retrieval
- Dependent information fetching
- XML data handling

### LinkUserDependent
- Dependent relationship creation
- Relationship validation
- Status tracking

### Private Methods
- BuildQIDsXml: XML generation for QID lists
- GetUserProfileDetailsFromEdw: EDW data retrieval
- ConvertDataRowToUserProfile: Data mapping
- GetMoiDependentsFromEdw: MOI data processing

## Technical Implementation Details

### Data Sources
- User Profile Context
  - UserProfile table
  - UserDependent table
- EDW Database Context
  - Stored procedures for user and dependent details
- HMC Live Feed Context
  - Patient information

### Security
- QID-based authorization
- Request origin validation
- Submitter validation
- Internal-only endpoints protection

### Error Handling
- Comprehensive exception logging
- Standardized error responses
- Database transaction handling
- Input validation

### Performance Considerations
- Asynchronous operations
- No-tracking queries for read operations
- Efficient XML handling for MOI data
- Feature flags for live feed enrichment
- Database transaction optimization
- Reduced logging with combined log statements
- Optimized live feeds data retrieval with ordering by event date

## Response Models

### GetUserProfileResponse
- Complete user profile information
- Healthcare details
- Address information
- Dependent relationships
- Communication preferences

### ValidateQIDFlags
- QID validity status
- Healthcare card status
- Address verification
- Physician assignment
- Nationality-based flags

### GetMoiDependentsResponse
- Basic dependent information
- Relationship status
- Bilingual name fields

### CreateUpdateUserProfileRequest
- User identification
- Communication preferences
- Consent settings
- Contact information

## Feature Flags
- LiveFeedsEnrichment: Controls integration with healthcare live feeds

## Dependencies
- Entity Framework Core
- Azure Functions
- OpenAPI/Swagger
- Feature Management
- SQL Server
- Custom extensions for request handling