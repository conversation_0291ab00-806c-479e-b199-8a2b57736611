﻿namespace TestEServiceFunctions;

[Collection("RegistrationFunctions")]
public class TestRegistrationFunctions(ITestOutputHelper testOutputHelper, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetRegistrationListByQId()
    {
        // Arrange
        var request = GetRestRequest("registrations/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);
        request.AddUrlSegment("qId", 22222222270);

        // Act
        var response = await _client.GetAsync<List<GetRegistrationListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count > 2);
        Contains(response, item => item.QId == 33322222237);
        Contains(response, item => item.Status == Cancelled);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetRegistrationListByQIdWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("registrations/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, 33322222230);
        request.AddUrlSegment("qId", 33322222230);

        // Act
        var response = await _client.GetAsync<List<GetRegistrationListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetRegistrationStatsByQId()
    {
        // Arrange
        var request = GetRestRequest("registrations/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);
        request.AddUrlSegment("qId", 22222222270);
        request.AddQueryParameter("status", InProcess);

        // Act
        var response = await _client.GetAsync<GetRegistrationStatsResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        response.ThrowIfNull();
        True(response.Count > 0);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetRegistrationStatsByQIdWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("registrations/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, 33322222230);
        request.AddUrlSegment("qId", 33322222230);
        request.AddQueryParameter("status", InProcess);

        // Act
        var response = await _client.GetAsync<GetRegistrationStatsResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal(0, response.Count);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessRegistrationByQId()
    {
        // Arrange
        var request = GetRestRequest("registrations/{qId}/inprocess-validation");
        request.AddUrlSegment("qId", 22222222270);

        // Act
        var response = await _client.GetAsync<CheckInProcessRegistrationResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.IsInprocessExist);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProcessRegistrationByQIdWithInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("registrations/{qId}/inprocess-validation");
        request.AddUrlSegment("qId", 33322222230);

        // Act
        var response = await _client.GetAsync<CheckInProcessRegistrationResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        False(response.IsInprocessExist);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetRegistrationItemByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("registrations/{reqNumber}");
        request.AddUrlSegment("reqNumber", "396OAJU24");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

        // Act
        var response = await _client.GetAsync<GetRegistrationItemResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal("396OAJU24", response.ReqNumber);
        True(response.QId > 0);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetRegistrationItemByReqNumberWithInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("registrations/{reqNumber}");
        request.AddUrlSegment("reqNumber", "REG123UMAR1");

        // Act
        var response = await _client.GetAsync<GetRegistrationItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    private static RestRequest CreateUpdateRegistration(string keyName)
    {
        var occupationCodeList = MockOccupations().Select(o => o.Code).ToList();
        var nationalityCodeList = MockNationality().Select(o => o.NatCode).ToList();
        var visaTypeCodeList = MockVisaTypes().Select(o => o.VisaTypeCode).ToList();
        var languageCodeList = MockLanguages().Select(o => o.NatCode).ToList();
        var healthCenterCodeList = MockHealthCenters().Select(o => o.Name).ToList();

        RestRequest request;
        if (keyName.Equals("CreateRegistrationKey", OrdinalIgnoreCase))
        {
            request = GetRestRequest("registrations");
        }
        else
        {
            request = GetRestRequest("registrations/{reqNumber}");
            request.AddUrlSegment("reqNumber", "0ITH1815BPT");
        }

        request.AddOrUpdateHeader(RequestOrigin, $"TestCase-{keyName}");

        var requestBody = new Faker<CreateUpdateRegistrationRequest>()
            .RuleFor(r => r.QId, RandomNumber(11))
            .RuleFor(r => r.FNameEn, FirstNameEnglish)
            .RuleFor(r => r.MNameEn, MiddleNameEnglish)
            .RuleFor(r => r.LNameEn, LastNameEnglish)
            .RuleFor(r => r.FNameAr, FirstNameArabic)
            .RuleFor(r => r.MNameAr, MiddleNameArabic)
            .RuleFor(r => r.LNameAr, LastNameArabic)
            .RuleFor(r => r.Nationality, f => f.PickRandom(nationalityCodeList))
            .RuleFor(r => r.Dob, f => f.Date.Past(50))
            .RuleFor(r => r.Gender, f => f.PickRandom("1", "2"))
            .RuleFor(r => r.MaritalStatus, f => f.PickRandom("1", "0"))
            .RuleFor(r => r.Education, f => f.Internet.UserName())
            .RuleFor(r => r.Occupation, f => f.PickRandom(occupationCodeList))
            .RuleFor(r => r.HomeTel, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.OfficeTel, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.MobileNo, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.NextOfKinName, f => f.Name.FullName())
            .RuleFor(r => r.NextOfKinLandLine, f => f.Phone.PhoneNumber())
            .RuleFor(r => r.SponsorName, f => f.Name.FullName())
            .RuleFor(r => r.SponsorAddress, f => f.Address.FullAddress())
            .RuleFor(r => r.VisaType, f => f.PickRandom(visaTypeCodeList))
            .RuleFor(r => r.BNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.ZNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.SNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.UNo, f => f.Random.Int(1, 100))
            .RuleFor(r => r.CatchmentHC, f => f.PickRandom(healthCenterCodeList))
            .RuleFor(r => r.PrefHC, f => f.PickRandom(healthCenterCodeList))
            .RuleFor(r => r.PrefSMSLang, f => f.PickRandom(languageCodeList))
            .RuleFor(r => r.PrefComMode, f => f.PickRandom("1", "2"))
            .RuleFor(r => r.AttachId1, NewGuid())
            .RuleFor(r => r.AttachId2, NewGuid())
            .RuleFor(r => r.AttachId3, NewGuid())
            .RuleFor(r => r.AttachId4, NewGuid())
            .RuleFor(r => r.AttachId5, NewGuid())
            .RuleFor(r => r.AttachId6, NewGuid())
            .RuleFor(r => r.EmergencyContactID, f => f.Person.Phone)
            .RuleFor(r => r.SubmittedBy, f => f.Name.FullName())
            .RuleFor(r => r.SubmittedAt, f => f.Date.Past(50))
            .RuleFor(r => r.SubmitterQId, GetMoqUser().UserQId)
            .RuleFor(r => r.SubmitterEmail, "<EMAIL>")
            .RuleFor(r => r.SubmitterMobile, "+97470559257")
            .RuleFor(r => r.IsDraft, true)
            .RuleFor(r => r.GisAddressUpdatedAt, f => f.Date.Past(50))
            .RuleFor(r => r.IsGisAddressManualyEntered, f => f.Random.Bool())
            .Generate();

        request.AddJsonBody(requestBody);
        return request;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateRegistrationByReqNumber_Should_Return_OK()
    {
        // Arrange
        var request = CreateUpdateRegistration("UpdateRegistrationByReqNumber");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal(OK, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCreateRegistration()
    {
        // Arrange
        var request = CreateUpdateRegistration("CreateRegistrationKey");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

        // Act
        var response = await _client.PostAsync<CreateRegistrationResponse>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.ReqNumber!.Length > 0);
    }

    [Fact] // No permission to delete registration
    [Trait(Category, HappyPath)]
    public async Task TestDeleteRegistrationByReqNumber()
    {
        // Arrange
        if (GetCurrentTime() > new DateTime(2022, 01, 01))
        {
            True(GetCurrentTime() > new DateTime(2022, 01, 01));
            return;
        }

        var request = CreateUpdateRegistration("CreateRegistrationKey");
        var requestNumber = await _client.PostAsync<CreateRegistrationResponse>(request);
        testOutputHelper.LogToConsole(requestNumber);
        requestNumber.ReqNumber.ThrowIfNull();
        await WaitAsync(6);
        
        var deleteRequest = GetRestRequest("registrations/{reqNumber}");
        deleteRequest.AddUrlSegment("reqNumber", requestNumber.ReqNumber.Trim());
        
        // Act
        var response = await _client.DeleteAsync(deleteRequest);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal(NoContent, response.StatusCode);
    }
}