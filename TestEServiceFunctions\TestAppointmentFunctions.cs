using System.Text.Json;
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace TestEServiceFunctions;

[Collection("AppointmentFunctions")]
public class TestAppointmentFunctions(ITestOutputHelper testOutputHelper, IRestLibrary restLibrary)
    : IClassFixture<RestLibrary>
{
    private readonly RestClient _client = restLibrary.Client;
    
    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAppointmentsListByQIdv1_EmptyQIdList_ReturnsBadRequest()
    {
        // Arrange: No QIDs provided should trigger validation
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        var qIdList = new QIdListRequest { QIds = [] };
        request.AddJsonBody(qIdList);
        request.AddOrUpdateHeader("EligibleClinicCodes", "CLINIC1");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(NoContent, response.StatusCode);
    }

    [Fact]
    public async Task TestGetAppointmentsListByQIdv1_MultipleClinicCodesHeaders_CaseInsensitive()
    {
        // Arrange: Test multi-header and case-insensitivity
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddOrUpdateHeader("EligibleClinicCodes", "CLINIC1");
        request.AddOrUpdateHeader("eligiblecliniccodes", "CLINIC2");
        var qIdList = new QIdListRequest { QIds = [GetMoqUser().UserQId.ToString()] };
        request.AddJsonBody(qIdList);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert: Should aggregate all clinic codes
        Equal(NoContent, response.StatusCode);
    }

    [Fact]
    public async Task TestGetAppointmentsListByQIdv1_MissingClinicCodesHeader_ReturnsBadRequest()
    {
        // Arrange: No EligibleClinicCodes header
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        var qIdList = new QIdListRequest { QIds = [GetMoqUser().UserQId.ToString()] };
        request.AddJsonBody(qIdList);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAppointmentsListByQIdv1_InvalidQId_ReturnsNoContent()
    {
        // Arrange
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        var qIdList = new QIdListRequest { QIds = ["12345678901"] };
        request.AddJsonBody(qIdList);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAppointmentsListByQIdv1_NoAppointments_ReturnsNoContent()
    {
        // Arrange
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        var qIdList = new QIdListRequest { QIds = [GetMoqUser().UserQId.ToString()] };
        request.AddJsonBody(qIdList);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAppointmentsListByQIdv1_MultipleQIds_ReturnsOk()
    {
        // Arrange
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

        // Create a QIdListRequest object - This is the key change
        var qIds = Enumerable.Range(0, 11)
            .Select(i => (22222222272 - i).ToString())
            .ToList();
        
        var qIdList = new QIdListRequest { QIds = qIds };
        var json = SerializeObject(qIdList, Formatting.Indented);
        
        request.AddStringBody(json, DataFormat.Json);
        
        request.Method = Method.Post;
        testOutputHelper.LogToConsole(request);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        
        // Assert
        response.IsSuccessful.Should().BeTrue("The request to the function should be successful.");
        response.StatusCode.Should().Be(NoContent, "The HTTP status code should be OK.");
    }    
    
    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByQId()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);

        // Act
        var response = await _client.GetAsync<List<GetAppointmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count > 0);
        response.ForEach(item => NotEmpty(item.ReqNumber!));
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetEServRequestedAppointmentListByInvalidOrDifferentQId()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", 22222222273);
        request.AddQueryParameter("status", InProcess);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(Unauthorized, response.StatusCode);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByQIdAndClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("clinicCode", "13");

        // Act
        var response = await _client.GetAsync<List<GetAppointmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count > 0);
        response.ForEach(item => NotEmpty(item.ReqNumber!));
        response.ForEach(item => Equal("13", item.Clinic));
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByQIdAndInvalidEligibleClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("clinicCode", "14, 15, 16, 17, 18, 19, 20");
        request.AddQueryParameter("requestType", New);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetEServRequestedAppointmentListByInvalidQIdAndValidClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", LongQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("clinicCode", "111");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(Unauthorized == response.StatusCode);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByDifferentQIdAndValidClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", LongQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("clinicCode", "13");
        request.Method = Method.Get;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(Unauthorized == response.StatusCode);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByQIdAndClinicCodeAndTakeOneObject()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("clinicCode", "13");
        request.AddQueryParameter("take", 1);

        // Act
        var response = await _client.GetAsync<List<GetAppointmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count == 1);
        var clinicCode = response.Select(item => item.Clinic).FirstOrDefault();
        Equal("13", clinicCode);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByQIdAndEmptyClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("clinicCode", EmptyString);
        request.AddQueryParameter("requestType", New);

        // Act
        var response = await _client.GetAsync<List<GetAppointmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count > 0);
        response.ForEach(item => NotEmpty(item.ReqNumber!));
    }


    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByQIDTakeOneObject()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("take", 1);
        request.AddQueryParameter("requestType", New);

        // Act
        var response = await _client.GetAsync<List<GetAppointmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        True(response.Count == 1);
        response.ForEach(item => NotEmpty(item.ReqNumber!));
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentListByQIdSkipThreeObjects()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", InProcess);
        request.AddQueryParameter("requestType", New);
        request.AddQueryParameter("take", 1);
        request.AddQueryParameter("skip", 3);

        // Act
        testOutputHelper.LogToConsole(request);
        var response = await _client.GetAsync<List<GetAppointmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetEServRequestedAppointmentListByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}");
        request.AddUrlSegment("qId", 22222222273);
        request.AddUrlSegment("status", InProcess);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(Unauthorized, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetAppointmentStatsByQId()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", Submitted);
        request.AddQueryParameter("requestType", New);
        request.AddQueryParameter("skip", Skip);
        request.AddQueryParameter("take", 10);

        testOutputHelper.LogToConsole(request);

        // Act
        var response = await _client.GetAsync<GetAppointmentStatsResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count > 0);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAppointmentStatsByInvalidQId()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}/stats");
        request.AddUrlSegment("qId", 22222222273);
        request.Method = Method.Get;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);
        response.ThrowIfNull();

        // Assert
        Equal(Unauthorized, response.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProcessConfirmedAppointmentByHcNumberAndClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/inprocess-confirmed-check");
        request.AddJsonBody(new CheckInProcessConfirmedAppointmentRequest
        {
            HcNumber = "HC04054004",
            ClinicCode = "13"
        });

        // Act
        var response = await _client.PostAsync<CheckInProcessConfirmedAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.IsInProcessConfirmedRequestExist);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProcessConfirmedAppointmentWithInvalidHcNumber()
    {
        // Arrange
        var request = GetRestRequest("appointments/inprocess-confirmed-check");
        request.AddJsonBody(new CheckInProcessConfirmedAppointmentRequest
        {
            HcNumber = "HC14054004",
            ClinicCode = "13"
        });

        // Act
        var response = await _client.PostAsync<CheckInProcessConfirmedAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        //  Assert
        False(response.IsInProcessConfirmedRequestExist);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProcessConfirmedAppointmentWithInvalidClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/inprocess-confirmed-check");
        request.AddJsonBody(new CheckInProcessConfirmedAppointmentRequest
        {
            HcNumber = "HC14054005",
            ClinicCode = "17"
        });

        // Act
        var response = await _client.PostAsync<CheckInProcessConfirmedAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        False(response.IsInProcessConfirmedRequestExist);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetEServRequestedAppointmentByReqNumber()
    {
        // Arrange
        var request = GetRestRequest("appointments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "52656WSSM");
        request.AddOrUpdateHeader(EligibleClinicCodes, "16, 17, 18");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
        testOutputHelper.LogToConsole(request);

        // Act
        var response = await _client.GetAsync<GetAppointmentItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response is not null);
        Equal("52656WSSM", response.ReqNumber);
        Equal("HC04054004", response.HcNumber);
        Equal("16", response.Clinic);
        Equal("826", response.Nationality);
        Equal("WAJ", response.SelectedHc);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetEServRequestedAppointmentByReqNumberWithInvalidClinicCode()
    {
        // Arrange
        var request = GetRestRequest("appointments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "52656WSSM");
        request.AddOrUpdateHeader(EligibleClinicCodes, "17, 18, 19, 20");
        request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
        testOutputHelper.LogToConsole(request);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(BadRequest, response.StatusCode);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestGetEServRequestedAppointmentByInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("appointments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "52656WSSR");

        // Act
        var response = await _client.GetAsync<GetAppointmentItemResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCheckAppointmentChangeRequestExist()
    {
        // Arrange
        var request = GetRestRequest("appointment-change/{appointmentId}");
        request.AddUrlSegment("appointmentId", 8736436996);

        // Act
        var response = await _client.GetAsync<CheckAppointmentChangeEligibilityResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        False(response.IsCancelRequestExist);
        False(response.IsRescheduleInProcessExist);
        Equal(8736436996, response.AppointmentId);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckAppointmentChangeRequestExistWithInvalidAppointmentId()
    {
        // Arrange
        var request = GetRestRequest("appointment-change/{appointmentId}");
        request.AddUrlSegment("appointmentId", 8736436997);

        // Act
        var response = await _client.GetAsync<CheckAppointmentChangeEligibilityResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        False(response.IsCancelRequestExist);
        False(response.IsRescheduleInProcessExist);
        Equal(8736436997, response.AppointmentId);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCreateAppointment()
    {
        // Arrange
        var request = CreateAppointment();
        request.AddOrUpdateHeader(EligibleClinicCodes, "14, 15, 16, 13");

        // Act
        var response = await _client.PostAsync<CreateAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.ReqNumber is not null);
        True(!response.ReqNumber.IsNullOrWhiteSpace());

        await WaitAsync(3);
        await DeleteAppointment(response.ReqNumber);
    }


    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestCreateAppointment_Without_Eligible_Clinic_Code_Header()
    {
        // Arrange
        var request = CreateAppointment();
        request.AddOrUpdateHeader(EligibleClinicCodes, "14, 15, 16");
        request.Method = Method.Post;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(BadRequest, response.StatusCode);
    }

    private async Task DeleteAppointment(string reqNumber)
    {
        // Arrange
        var request = GetRestRequest("appointments/{reqNumber}");
        request.AddUrlSegment("reqNumber", reqNumber);

        // Act
        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.StatusCode == OK);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestCreateAppointmentWithInvalidObject()
    {
        // Arrange
        var request = GetRestRequest("appointments");
        request.AddOrUpdateHeader(RequestOrigin, UnitTestCreate);
        request.AddJsonBody(new CreateUpdateAppointmentRequest());
        request.Method = Method.Post;

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(response.StatusCode == Unauthorized);
        IsType<HttpRequestException>(response.ErrorException);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpdateAppointmentByReqNumber_Should_Return_OkObjectResult()
    {
        // Arrange
        var request = GetRestRequest("appointments/{reqNumber}");
        request.AddUrlSegment("reqNumber", "TIESRL9R2ZC");
        request.AddOrUpdateHeader(RequestOrigin, UnitTestUpdate);
        request.AddOrUpdateHeader(EligibleClinicCodes, "70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80");
        var updateAppointment = GenerateAppointmentObject();
        request.AddJsonBody(updateAppointment);

        // Act
        var response = await _client.PutAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.StatusCode == OK);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestUpdateAppointmentByInvalidReqNumber()
    {
        // Arrange
        var request = GetRestRequest("appointments/{reqNumber}");
        request.Method = Method.Put;
        request.AddUrlSegment("reqNumber", "QUYUYAJS12");
        request.AddOrUpdateHeader(RequestOrigin, UnitTestUpdate);
        var updateAppointment = GenerateAppointmentObject();
        request.AddJsonBody(updateAppointment);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(BadRequest, response.StatusCode);
    }


    private static CreateUpdateAppointmentRequest GenerateAppointmentObject()
    {
        var updateAppointment = new Faker<CreateUpdateAppointmentRequest>()
            .RuleFor(a => a.QId, RandomNumber(11))
            .RuleFor(a => a.FNameEn, FirstNameEnglish())
            .RuleFor(a => a.MNameEn, MiddleNameEnglish())
            .RuleFor(a => a.LNameEn, FirstNameEnglish())
            .RuleFor(a => a.FNameAr, FirstNameArabic())
            .RuleFor(a => a.MNameAr, MiddleNameArabic())
            .RuleFor(a => a.LNameAr, LastNameArabic())
            .RuleFor(a => a.Gender, f => f.PickRandom("1", "2"))
            .RuleFor(a => a.HcNumber, f => "HC" + f.Random.AlphaNumeric(8).ToUpper())
            .RuleFor(a => a.Clinic, "13")
            .RuleFor(a => a.ClinicTime, GetCurrentTime().AddDays(3).ToLongDateString())
            .RuleFor(a => a.PrefDate, GetCurrentTime().AddDays(3))
            .RuleFor(a => a.AmPm, f => f.PickRandom("1", "0"))
            .RuleFor(a => a.SelectedHc, f => f.Company.CompanySuffix())
            .RuleFor(a => a.RequestType, f => f.PickRandom(New, Reschedule, Cancel))
            .RuleFor(a => a.PrefContactTime, f => f.PickRandom("0", "1"))
            .RuleFor(a => a.SubmittedBy, UnitTestUpdate)
            .RuleFor(a => a.SubmittedAt, GetCurrentTime())
            .RuleFor(a => a.SubmitterQId, LongQId)
            .RuleFor(a => a.SubmitterEmail, f => f.Internet.Email())
            .RuleFor(a => a.SubmitterMobile, f => f.Phone.PhoneNumber())
            .RuleFor(a => a.AppointmentId, RandomNumber(15))
            .Generate();

        return updateAppointment;
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestDeleteAppointmentByReqNumber()
    {
        // Arrange
        var requestNumberToDelete = CreateAppointment();
        requestNumberToDelete.AddOrUpdateHeader(EligibleClinicCodes, "13");

        var reqNumber = await _client.PostAsync<CreateAppointmentResponse>(requestNumberToDelete);
        reqNumber.ReqNumber.ThrowIfNull().Throw().IfEmpty();
        await WaitAsync();

        var request = GetRestRequest("appointments/{reqNumber}");
        request.AddUrlSegment("reqNumber", reqNumber.ReqNumber);

        // Act
        var response = await _client.DeleteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.StatusCode == OK);
    }

    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestUpcomingConfirmedAppointmentsByHcNumberListV2()
    {
        // Arrange
        var request = GetRestRequest("appointments/upcoming-confirmed-v2");
        var hcNumbers = new List<string> { "HC07893887" };
        var hcNumberList = new HcNumberList { HcNumbers = hcNumbers };
        request.AddJsonBody(hcNumberList);

        // Act
        var response = await _client.PostAsync<List<UpcomingConfirmedAppointmentListResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        True(true);
    }

    [Fact] //unhappy path
    [Trait(Category, UnHappyPath)]
    public async Task TestUpcomingConfirmedAppointmentsByInvalidHcNumberListV2()
    {
        // Arrange
        var request = GetRestRequest("appointments/upcoming-confirmed-v2");
        request.Method = Method.Post;
        var hcNumbers = new List<string>
        {
            "HC17893800",
            "HC17893801"
        };
        var hcNumberList = new HcNumberList { HcNumbers = hcNumbers };
        request.AddJsonBody(hcNumberList);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(NoContent, response.StatusCode);
    }


    [Fact] //happy path
    [Trait(Category, HappyPath)]
    public async Task TestGetUpcomingConfirmedAppointmentByAppointmentId()
    {
        // Arrange
        var request = GetRestRequest("appointments/upcoming-confirmed-v2/{appointmentId}");
        request.AddUrlSegment("appointmentId", 403614625);
        request.AddOrUpdateHeader(PhccEnvironment, "PROD");
        request.AddOrUpdateHeader(EligibleClinicCodes, "26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36");

        // Act
        var response = await _client.GetAsync<GetUpcomingConfirmedAppointmentItemResponse>(request);
        testOutputHelper.LogToConsole(response);
        request.ThrowIfNull();

        // Assert
        NotNull(response);
        True(response.AppointmentId == 403614625);
        Equal("HC06058802", response.HcNumber?.Trim());
        Equal(31935605225, response.QId);
        Equal("AYESHA ABEER", response.FNameEn?.Trim());
        Equal("356", response.NationalityCode?.Trim());
    }

    private static RestRequest CreateAppointment()
    {
        var request = GetRestRequest("appointments");

        var appointment = new Faker<CreateUpdateAppointmentRequest>()
            .RuleFor(a => a.QId, RandomNumber(11))
            .RuleFor(a => a.FNameEn, FirstNameEnglish())
            .RuleFor(a => a.MNameEn, MiddleNameEnglish())
            .RuleFor(a => a.LNameEn, FirstNameEnglish())
            .RuleFor(a => a.FNameAr, FirstNameArabic())
            .RuleFor(a => a.MNameAr, MiddleNameArabic())
            .RuleFor(a => a.LNameAr, LastNameArabic())
            .RuleFor(a => a.HcNumber, f => f.Random.AlphaNumeric(8).ToUpper())
            .RuleFor(a => a.Clinic, "13")
            .RuleFor(a => a.ClinicTime, GetCurrentTime().AddDays(3).ToString("R"))
            .RuleFor(a => a.PrefDate, GetCurrentTime().AddDays(3))
            .RuleFor(a => a.AmPm, f => f.PickRandom("1", "0"))
            .RuleFor(a => a.SelectedHc, f => f.Company.CompanySuffix())
            .RuleFor(a => a.RequestType, "New")
            .RuleFor(a => a.PrefContactTime, f => f.PickRandom("0", "1"))
            .RuleFor(a => a.SubmittedBy, "Unit-Test-CreateAppointment")
            .RuleFor(a => a.SubmittedAt, GetCurrentTime())
            .RuleFor(a => a.SubmitterQId, LongQId)
            .RuleFor(a => a.SubmitterEmail, f => f.Internet.Email())
            .RuleFor(a => a.SubmitterMobile, f => f.Phone.PhoneNumber())
            .RuleFor(a => a.AppointmentId, RandomNumber(15))
            .RuleFor(a => a.Gender, f => f.PickRandom("1", "2"))
            .RuleFor(a => a.SecondaryPhoneMobile, f => f.Phone.PhoneNumber())
            .RuleFor(a => a.CancellationCall, f => f.Random.Bool())
            .RuleFor(a => a.Consent, f => f.Random.Bool())
            .RuleFor(a => a.Nationality, f => f.PickRandom("608", "826", "818", "586", "634", "356"))
            .RuleFor(a => a.Dob, f => f.Date.Past(50, GetCurrentTime().AddYears(-18)))
            .Generate();
        return request.AddJsonBody(appointment);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProgressPmeAppointmentV1_ValidRequest_ReturnsTrue()
    {
        // Arrange
        var request = GetRestRequest("appointments/exists/{qId}");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddOrUpdateHeader(EligibleClinicCodes, "13,14,15");

        // Act
        var response = await _client.GetAsync<CheckInProcessConfirmedAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.IsInProcessConfirmedRequestExist);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProgressPmeAppointmentV1_InvalidQId_ReturnsInternalServerError()
    {
        // Arrange
        var request = GetRestRequest("appointments/exists/{qId}");
        request.AddUrlSegment("qId", "invalidQId");
        request.AddOrUpdateHeader(EligibleClinicCodes, "13,14,15");

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(InternalServerError, response.StatusCode);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAppointmentsListByQIdV1_ValidRequest_ReturnsNull()
    {
        // Arrange
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddOrUpdateHeader(EligibleClinicCodes, "13,14,15");
        var qIdList = new QIdListRequest { QIds = [GetMoqUser().UserQId.ToString()] };
        request.AddJsonBody(qIdList);

        // Act
        var response = await _client.PostAsync<List<AppointmentResponse>>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Null(response);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAppointmentsListByQIdV1_EmptyQIdList_ReturnsNoContent()
    {
        // Arrange
        var request = GetRestRequest("appointments/bookings");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        var qIdList = new QIdListRequest { QIds = [] };
        request.AddJsonBody(qIdList);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAppointmentsListByQIdV1_MissingQIdHeader_ReturnsBadRequest()
    {
        // Arrange
        var request = GetRestRequest("appointments/bookings");
        request.Parameters.RemoveParameter(JwtClaimsQId);
        
        List<string> qIds = [GetMoqUser().UserQId.ToString()];
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            WriteIndented = true
        };

        var qIdList = new QIdListRequest { QIds = qIds };
        var json = System.Text.Json.JsonSerializer.Serialize(qIdList, options);
        
        var jsonObject = DeserializeObject(json);
        request.AddJsonBody(jsonObject);
        
        request.Method = Method.Post;
        testOutputHelper.LogToConsole(request);

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestCheckInProgressPmeAppointmentV1_NoEligibleClinicCodes_ReturnsTrue()
    {
        // Arrange
        var request = GetRestRequest("appointments/exists/{qId}");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        // Deliberately not adding EligibleClinicCodes header

        // Act
        var response = await _client.GetAsync<CheckInProcessConfirmedAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.IsInProcessConfirmedRequestExist);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProgressPmeAppointmentV1_EmptyEligibleClinicCodes_ReturnsFalse()
    {
        // Arrange
        var request = GetRestRequest("appointments/exists/{qId}");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddOrUpdateHeader(EligibleClinicCodes, "");
        
        // Act
        var response = await _client.GetAsync<CheckInProcessConfirmedAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        False(response.IsInProcessConfirmedRequestExist);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestCheckInProgressPmeAppointmentV1_NoFutureAppointments_ReturnsFalse()
    {
        // Arrange
        var request = GetRestRequest("appointments/exists/{qId}");
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddOrUpdateHeader(EligibleClinicCodes, "999"); // Using a clinic code that shouldn't have future appointments
        
        // Act
        var response = await _client.GetAsync<CheckInProcessConfirmedAppointmentResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        False(response.IsInProcessConfirmedRequestExist);
    }

    [Fact]
    [Trait(Category, UnHappyPath)]
    public async Task TestGetAppointmentStatsByQId_InvalidStatus_ReturnsZeroCount()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", "InvalidStatus");
        request.AddQueryParameter("requestType", New);

        // Act
        var response = await _client.GetAsync<GetAppointmentStatsResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        Equal(0, response.Count);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAppointmentStatsByQId_InvalidRequestType_ReturnsCount()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        request.AddQueryParameter("status", Submitted);
        request.AddQueryParameter("requestType", "InvalidType");

        // Act
        var response = await _client.GetAsync<GetAppointmentStatsResponse>(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        NotNull(response);
        True(response.Count >= 0);
    }

    [Fact]
    [Trait(Category, HappyPath)]
    public async Task TestGetAppointmentStatsByQId_WithoutOptionalParameters_ReturnsCount()
    {
        // Arrange
        var request = GetRestRequest("appointments/submitter-qid/{qId}/stats");
        request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
        request.AddUrlSegment("qId", GetMoqUser().UserQId);
        // Deliberately not adding optional parameters

        // Act
        var response = await _client.ExecuteAsync(request);
        testOutputHelper.LogToConsole(response);

        // Assert
        Equal(BadRequest, response.StatusCode);
    }
}