# MHDS (Medicine Home Delivery Service) Functions Test Documentation

## Overview
This documentation covers the test suite for Medicine Home Delivery Service (MHDS) API functions in the EServices REST API project. The test suite validates various MHDS operations including creation, retrieval, updates, and deletion of medicine delivery requests.

## Test Categories

### 1. CRUD Operations
- [Create MHDS Request](./CreateMHDS.md)
- [Get MHDS Item](./GetMHDSItem.md)
- [Update MHDS](./UpdateMHDS.md)
- [Delete MHDS](./DeleteMHDS.md)

### 2. List and Statistics
- [Get MHDS List](./GetMHDSList.md)
- [Get MHDS Stats](./GetMHDSStats.md)
- [Get Medicine List](./GetMedicineList.md)

### 3. Validation
- [Check In-Process MHDS](./CheckInProcessMHDS.md)

## Common Components

### Authentication
- JWT-based authentication
- QID claims validation
- Request origin tracking

### Test Data Generation
- Uses Faker/Bogus library
- Generates realistic test data including:
  * QID (11 digits)
  * Names (English/Arabic)
  * Health Card Numbers
  * Addresses (UNo, BNo, SNo, ZNo)
  * Medicine Information

### Request Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: Request source identifier
- `IsApp`: Application identifier

### Response Models
1. `CreateMHDSResponse`
   - ReqNumber (string)

2. `GetMHDSItemResponse`
   - ReqNumber (string)
   - Other request details

3. `MHDSRequestList`
   - List of MHDS requests
   - Request details

4. `GetMHDSStatsResponse`
   - Count (int)

5. `CheckInProcessMHDSResponse`
   - IsInProcessExist (bool)

6. `GetMedicineList`
   - Medicine information
   - Prescription details

### Test Categories
- Happy Path: Valid test scenarios
- Unhappy Path: Error handling and validation scenarios

## Best Practices
1. **Data Generation**
   - Use consistent test data patterns
   - Generate realistic values
   - Handle both English and Arabic content

2. **Validation**
   - Verify response status codes
   - Check null responses
   - Validate data integrity
   - Test boundary conditions

3. **Error Handling**
   - Test invalid inputs
   - Verify error responses
   - Check authorization failures

4. **Performance**
   - Efficient test execution
   - Proper resource cleanup
   - Async operation handling

## Test Environment
- .NET Test Framework: xUnit
- HTTP Client: RestSharp
- Test Data Generation: Bogus
- Authentication: JWT Tokens
