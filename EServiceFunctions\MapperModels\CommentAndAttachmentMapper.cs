﻿using EServiceFunctions.Models.CommentAndAttachment;
using EServiceFunctions.RequestResponseModels.CommentAndAttachment;

namespace EServiceFunctions.MapperModels;

public static class CommentAndAttachmentMapper
{
    public static GetAttachmentItemResponse MapAttachmentToGetAttachmentItemResponse(Attachment attachment)
    {
        var response = new GetAttachmentItemResponse
        {
            AttachmentName = attachment.AttachmentName,
            AttachmentType = attachment.AttachmentType,
            AttachmentData = attachment.AttachmentData,
            CreatedAt = attachment.CreatedAt.ToUtcString(),
            UpdatedAt = attachment.UpdatedAt.ToUtcString()
        };

        return response;
    }

    public static Attachment MapCreateUpdateAttachmentRequestToAttachment(CreateUpdateAttachmentRequest createUpdateAttachmentRequest)
    {
        var response = new Attachment
        {                
            AttachmentName = createUpdateAttachmentRequest.AttachmentName,
            AttachmentType = createUpdateAttachmentRequest.AttachmentType,
            AttachmentData = createUpdateAttachmentRequest.AttachmentData,
            SubmitterQId = createUpdateAttachmentRequest.SubmitterQId
        };
        return response;
    }

    public static Comment MapCreateCommentRequestToComment(CreateCommentRequest createCommentRequest)
    {
        var response = new Comment
        {
            ReqNumber = createCommentRequest.ReqNumber,
            Action = createCommentRequest.Action,
            CommentText = createCommentRequest.CommentText,
            CommentType = createCommentRequest.CommentType,
            CommenterName = createCommentRequest.CommenterName,
            SubmitterQId = createCommentRequest.SubmitterQId,
            DisplayToSubmitter = createCommentRequest.DisplayToSubmitter
        };
        return response;
    }
}