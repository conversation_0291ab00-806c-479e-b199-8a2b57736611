using EServiceFunctions.Models.Appointment;
using EServiceFunctions.Models.HmcLiveFeeds;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.Appointment;
using static System.Data.SqlDbType;
using static EServiceFunctions.MapperModels.AppointmentMapper;
using static Microsoft.OpenApi.Models.ParameterLocation;
using DateTime = System.DateTime;
using Exception = System.Exception;
using System.Globalization;

namespace EServiceFunctions.Functions;

public class AppointmentFunctions(
    IDbContextFactory<AppointmentContext> dbContextFactory,
    IDbContextFactory<EDWDbContext> edwDbContextFactory,
    IDbContextFactory<HmcLiveFeedContext> hmcLiveFeedsContextFactory,
    ILogger<AppointmentFunctions> logger)
{
    private const string NoClinicCodesInHeaderMessage = "No Clinic Codes found in the request header";
    private const string ClinicCodesRequiredMessage = "Clinic Codes are required...";
    private const string ValidRequestTypeMessage = "Please provide a valid request type: New, Reschedule, or Cancel";

    #region CheckInProgressPMEAppointmentV1

    [Function("CheckInProgressPMEAppointmentV1")]
    [OpenApiOperation(operationId: "CheckInProcessConfirmedAppointment", tags: ["Appointment"],
        Summary = "Check In-Progress PME Appointment",
        Description = "Check if a PME Appointment request or a 'New' Cerner PME Appointment exists for the given QID")]
    [OpenApiParameter("qId", Description = "QID", In = ParameterLocation.Path, Required = true, Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessConfirmedAppointmentResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NotFound, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(ErrorResponse))]
    public async Task<HttpResponseData> CheckInProgressPmeAppointmentV1(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "appointments/exists/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation("Checking In-Progress PME Appointment for QID: {QId}", qId);

            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            await using var hmcLiveFeedsDbContext = await hmcLiveFeedsContextFactory.CreateDbContextAsync();

            var inProgressPmeRequest = await dbContext.Appointment
                // Check if the appointment belongs to the specified QId
                .AnyAsync(a => a.QId == qId &&
                               // Verify if the clinic is in the allowed list of clinic codes
                               clinicCodes.Contains(a.Clinic) &&
                               //at-least 1 record of type new or reschedule if it has status != completed return true.
                               (a.RequestType == New || a.RequestType == Reschedule) &&
                               a.Status != Completed);

            if (inProgressPmeRequest)
            {
                return await req.WriteOkResponseAsync(new CheckInProcessConfirmedAppointmentResponse
                {
                    IsInProcessConfirmedRequestExist = true
                });
            }

            var eligibleAppointmentTypes = await dbContext.ESEligibleAppointmentTypes
                .Where(at => clinicCodes.Contains(at.ClinicCode.ToString()))
                .Select(at => at.AppointmentType)
                .ToListAsync();

            var confirmedFuturePmeAppointment = await hmcLiveFeedsDbContext.AppointmentBookings
                .AnyAsync(ab =>
                    // Check if the appointment belongs to the specified QId
                    ab.QId == qId.ToString() &&
                    // Ensure the appointment is today or in the future
                    ab.AppointmentDateTime >= DateTime.UtcNow.Date &&
                    // Exclude confirmed appointments
                    (ab.AppointmentStatus == Confirmed || ab.AppointmentStatus == Reschedule) &&
                    // Check if the appointment type is eligible
                    eligibleAppointmentTypes.Contains(ab.AppointmentTypeCode)
                );

            return await req.WriteOkResponseAsync(new CheckInProcessConfirmedAppointmentResponse
            {
                IsInProcessConfirmedRequestExist = confirmedFuturePmeAppointment
            });
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Get PME Appointments By QID List V1

    [Function("GetAppointmentsListByQIdv1")]
    [OpenApiOperation(operationId: "GetAppointmentsListByQIdv1", tags: ["Appointment"],
        Summary = "Retrieve Appointments by QID List (V1)",
        Description = "Retrieve appointments for the given QID list.")]
    [OpenApiRequestBody("application/json", typeof(QIdListRequest), Description = "Request body with list of QIDs.")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<AppointmentResponse>),
        Description = "List of Appointments")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(Forbidden, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NotFound, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(ErrorResponse))]
    public async Task<HttpResponseData> GetAppointmentsListByQIdV1(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "appointments/bookings")]
        HttpRequestData req)
    {
        try
        {
            var submitterQId = req.GetClaims().QId;

            if (!submitterQId.HasValue)
            {
                logger.LogWarning("QID not found in the header");
                return await req.WriteUnauthorizedResponseAsync();
            }

            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var qIdListRequest = DeserializeObject<QIdListRequest>(requestBody);

            if (qIdListRequest?.QIds == null || qIdListRequest.QIds.Count == 0)
            {
                return await req.WriteErrorResponseAsync(BadRequest,
                    "Invalid request body. Please provide a valid QID list.");
            }

            var authorizedQIds = new List<string> { submitterQId.Value.ToString() };

            await using var appointmentDbContext = await dbContextFactory.CreateDbContextAsync();
            await using var hmcLiveFeedsDbContext = await hmcLiveFeedsContextFactory.CreateDbContextAsync();

            var relatedQIds = await appointmentDbContext.Appointment
                .Where(a => a.SubmitterQId == submitterQId
                            && qIdListRequest.QIds.Contains(a.QId.ToString())
                            && clinicCodes.Contains(a.Clinic))
                .Select(a => a.QId.HasValue ? a.QId.Value.ToString() : string.Empty)
                .Where(qid => !string.IsNullOrEmpty(qid))
                .Distinct()
                .ToListAsync();

            authorizedQIds.AddRange(relatedQIds);

            var eligibleAppointmentTypes = await appointmentDbContext.ESEligibleAppointmentTypes
                .Where(at => clinicCodes.Contains(at.ClinicCode.ToString()))
                .Select(at => at.AppointmentType)
                .ToListAsync();

            var createdOn = DateTime.Now.Date.AddDays(-7);

            var appointments = await hmcLiveFeedsDbContext.AppointmentBookings
                .Where(ab => authorizedQIds.Contains(ab.QId))
                .Where(ab => ab.AppointmentDateTime > createdOn)
                .Where(ab => eligibleAppointmentTypes.Contains(ab.AppointmentTypeCode))
                .ToListAsync();

            if (appointments.Count == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            var response = appointments.Select(ab => new AppointmentResponse
            {
                QId = ab.QId ?? string.Empty,
                HcNumber = ab.HcNumber ?? string.Empty,
                AppointmentId = ab.AppointmentId,
                AppointmentDateTime = ab.AppointmentDateTime,
                AppointmentType = ab.AppointmentTypeCode ?? string.Empty,
                AppointmentStatus = ab.AppointmentStatus ?? string.Empty,
                AppointmentHCnt = ab.AppointmentHcCode ?? string.Empty
            }).ToList();

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetEServRequestedAppointmentListByQID

    [Function("GetEServRequestedAppointmentListByQID")]
    [OpenApiOperation(operationId: "GetEServRequestedAppointmentListByQID", tags: ["Appointment"],
        Summary = "Get EServ Requested Appointment List By Submitter's QID",
        Description = " Get the eServ requested appointment list for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status",
        Description =
            "Please provide a valid status: InProcess {Submitted, Rework, Reworked} or Archived {Approved, Cancelled, CancelledByEServ, Rejected}.",
        In = Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("requestType", Description = "Please provide a valid request type: New, Reschedule Or Cancel.",
        In = Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("clinicCode", Description = "Clinic Code", In = Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetAppointmentListResponse>),
        Description =
            "Type of requests to return viz. Submitted, Cancelled, CancelledByEServ, Rework, Rejected, Approved, Reworked.")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetEServRequestedAppointmentListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get",
            Route = "appointments/submitter-qid/{qId}")]
        HttpRequestData req,
        long qId)
    {
        try
        {
            logger.LogInformation("Getting Appointments By QID {QId}", qId);

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning("Unauthorized ({QId}) Access!!!", qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);
            if (clinicCodes.Count == 0)
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, ClinicCodesRequiredMessage);
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var appointmentStatus = req.GetCleanedQueryString("status");
            var requestType = req.GetCleanedQueryString("requestType");

            var query = dbContext.Appointment?.Join(dbContext.Status!, e => e.Status, s => s.Code,
                    (appointment, status) => new { appointment, status })
                .Where(item => item.appointment.SubmitterQId == qId)
                .Where(item => clinicCodes.Contains(item.appointment.Clinic));

            if (requestType.IsNotNullOrWhiteSpace())
            {
                if (New.Equals(requestType, OrdinalIgnoreCase) || Reschedule.Equals(requestType, OrdinalIgnoreCase) ||
                    Cancel.Equals(requestType, OrdinalIgnoreCase))
                {
                    query = query.Where(item => item.appointment.RequestType == requestType);
                }
                else
                {
                    logger.LogError(ValidRequestTypeMessage);
                    return await req.WriteErrorResponseAsync(BadRequest, "Invalid Request Type");
                }
            }

            if (appointmentStatus.IsNotNullOrWhiteSpace())
            {
                query = query.Where(item =>
                    item.status.Category == appointmentStatus || item.appointment.Status == appointmentStatus);
            }

            var clinicCode = req.GetCleanedQueryString("clinicCode");
            if (clinicCode.IsNotNullOrWhiteSpace())
            {
                query = query.Where(item => item.appointment.Clinic == clinicCode);
            }

            var queryResult = await query.OrderBy(q => q.appointment.PrefDate)
                .Skip(req.GetIntQueryParameter("skip"))
                .Take(req.GetIntQueryParameter("take"))
                .ToListAsync();

            if (queryResult.Count == 0) return await req.WriteNoContentResponseAsync();

            var response = queryResult.Select(item => new GetAppointmentListResponse
            {
                QId = item.appointment.QId,
                ReqNumber = item.appointment.ReqNumber,
                FNameEn = item.appointment.FNameEn,
                MNameEn = item.appointment.MNameEn,
                LNameEn = item.appointment.LNameEn,
                FNameAr = item.appointment.FNameAr,
                MNameAr = item.appointment.MNameAr,
                LNameAr = item.appointment.LNameAr,
                HcNumber = item.appointment.HCNumber,
                Clinic = item.appointment.Clinic,
                PrefDate = item.appointment.PrefDate.ToUtcString(),
                AmPm = item.appointment.AmPmShiftNavigation?.Code,
                AmPmDescriptionEn = item.appointment.AmPmShiftNavigation?.DescriptionEn,
                AmPmDescriptionAr = item.appointment.AmPmShiftNavigation?.DescriptionAr,
                RequestType = item.appointment.AppointmentRequestTypeNavigation?.Code,
                RequestTypeDescriptionEn = item.appointment.AppointmentRequestTypeNavigation?.DescriptionEn,
                RequestTypeDescriptionAr = item.appointment.AppointmentRequestTypeNavigation?.DescriptionAr,
                Status = item.appointment.StatusNavigation?.Code,
                StatusDescriptionEn = item.appointment.StatusNavigation?.DescriptionEn,
                StatusDescriptionAr = item.appointment.StatusNavigation?.DescriptionAr,
                SubmittedAt = item.appointment.SubmittedAt.ToUtcString()
            }).ToList();

            logger.LogInformation(
                "Returning Appointments ({Count}) for the given QID {QId}, RequestType {RequestType} and Status {Status}",
                response.Count, qId, requestType, appointmentStatus);

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetAppointmentStatsByQID

    [ExcludeFromCodeCoverage]
    [Function("GetAppointmentStatsByQID")]
    [OpenApiOperation(operationId: "GetAppointmentStatsByQID", tags: ["Appointment"],
        Summary = "Get Appointment Stats By Submitter's QID",
        Description = "Get the Appointment stats for the given submitter's QId")]
    [OpenApiParameter("qId", Description = "Submitter's QId", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("status", Description =
            "Please provide a valid status: InProcess {Submitted, PendingOrder} or Archived {Confirmed, CancelledFrom107}.",
        In = Query, Required = false, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetAppointmentStatsResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    public async Task<HttpResponseData> GetAppointmentStatsByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "appointments/submitter-qid/{qId}/stats")]
        HttpRequestData req, long qId)
    {
        try
        {
            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning("Unauthorized ({QId}) Access!!!", qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);

            if (clinicCodes.Count == 0)
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, ClinicCodesRequiredMessage);
            }

            string appointmentStatus = req.GetCleanedQueryString("status");
            string requestType = req.GetCleanedQueryString("requestType");

            logger.LogInformation("Getting Appointments Stats By QID {QId}", qId);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var query = dbContext.Appointment?.Join(dbContext.Status!, e => e.Status, s => s.Code,
                (appointment, status) => new { appointment, status });

            if (query is null)
            {
                logger.LogError("Error while getting the appointments");
                return await req.WriteErrorResponseAsync(BadRequest, "Error while getting the appointments");
            }

            query = query.Where(item => item.appointment.SubmitterQId == qId);

            if (appointmentStatus.IsNotNullOrWhiteSpace())
            {
                query = query.Where(item =>
                    item.status.Category == appointmentStatus || item.appointment.Status == appointmentStatus);
            }

            if (requestType.IsNullOrWhiteSpace())
            {
                logger.LogError(ValidRequestTypeMessage);
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid Request Type");
            }

            if (requestType.IsNotNullOrWhiteSpace() &&
                (New.Equals(requestType, OrdinalIgnoreCase) || Reschedule.Equals(requestType, OrdinalIgnoreCase) ||
                 Cancel.Equals(requestType, OrdinalIgnoreCase)))
            {
                query = query.Where(item => item.appointment.RequestType == requestType);
            }

            query = query.Where(item => clinicCodes.Contains(item.appointment.Clinic));

            var response = await query.CountAsync();

            logger.LogInformation(
                "Returning Appointments count {Count} By QID {QId}, RequestType {RequestType} and Status {Status}",
                response, qId, requestType, appointmentStatus);

            return await req.WriteOkResponseAsync(new GetAppointmentStatsResponse { Count = response });
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CheckInProcessConfirmedAppointment By HCNumber & ClinicCode

    [ExcludeFromCodeCoverage]
    [Function("CheckInProcessConfirmedAppointment")]
    [OpenApiOperation(operationId: "CheckInProcessConfirmedAppointment", tags: ["Appointment"],
        Summary = "Check InProcess Or Confirmed Appointments",
        Description = "Check inprocess or confirmed Appointments for the given hcNumber & clinicCode")]
    [OpenApiRequestBody("application/json", typeof(CheckInProcessConfirmedAppointmentRequest),
        Description = "Request containing Health Card Number and Clinic Code")]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckInProcessConfirmedAppointmentResponse),
        Description = "Returns True (InProcess or Confirmed ) Appointments Exist Or False")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckInProcessConfirmedAppointment(
        [HttpTrigger(AuthorizationLevel.Function, "post",
            Route = "appointments/inprocess-confirmed-check")]
        HttpRequestData req)
    {
        try
        {
            var submitter = req.GetClaims();

            logger.LogInformation("Checking InProcess Appointment(s) - {SubmitterQId}", submitter.QId);

            // Read request body
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CheckInProcessConfirmedAppointmentRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again");
            }

            if (string.IsNullOrWhiteSpace(request.HcNumber))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Health Card Number is required");
            }

            if (string.IsNullOrWhiteSpace(request.ClinicCode))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Clinic Code is required");
            }

            var hcNumber = request.HcNumber.Trim();
            var clinicCode = request.ClinicCode.Trim();

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var inProcessConfirmedRequestExist = await dbContext.Appointment
                .Join(dbContext.Status, appointment => appointment.Status,
                    status => status.Code,
                    (appointment, status) => new { appointment, status })
                .AnyAsync(a => a.appointment.HCNumber == hcNumber
                          && a.appointment.Clinic == clinicCode
                          && a.status.Category == InProcess);

            if (!inProcessConfirmedRequestExist)
            {
                var hcNumbersXml = ConvertListOfHcNumbersToXml(new HcNumberList
                    { HcNumbers = [hcNumber] });
                var env = req.TryGetHeader(PhccEnvironment);
                var response = await UpcomingConfirmedAppointmentsByHcNumbersOrQIdsXml(hcNumbersXml, clinicCode, env);
                inProcessConfirmedRequestExist = response.Count != 0;
                if (inProcessConfirmedRequestExist)
                {
                    logger.LogInformation("Confirmed Appointment(s) Exist, Count : {ResponseCount}", response.Count);
                }
                else
                {
                    logger.LogInformation("No confirmed appointments exist");
                }
            }

            logger.LogInformation(
                "InProcess Appointment(s): {Status}", inProcessConfirmedRequestExist ? "Exist" : "Not Exist");

            return await req.WriteOkResponseAsync(new CheckInProcessConfirmedAppointmentResponse
            {
                IsInProcessConfirmedRequestExist = inProcessConfirmedRequestExist
            });
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetEServRequestedAppointmentByReqNumber

    [Function("GetEServRequestedAppointmentByReqNumber")]
    [OpenApiOperation(operationId: "GetEServRequestedAppointmentByReqNumber", tags: ["Appointment"],
        Summary = "Get Appointment Item By ReqNumber",
        Description = "Get Appointment item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Appointment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetAppointmentItemResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetEServRequestedAppointmentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "appointments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();
            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);

            if (clinicCodes.Count == 0)
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, ClinicCodesRequiredMessage);
            }

            if (reqNumber.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Request Number is required...");
            }

            reqNumber = reqNumber.Trim();

            logger.LogInformation("Getting Appointment Item By Request Number {ReqNumber}", reqNumber);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            Appointment? response = await dbContext.Appointment!
                .Include(s => s.StatusNavigation)
                .Include(a => a.AppointmentRequestTypeNavigation)
                .Include(sh => sh.AmPmShiftNavigation)
                .Include(ps => ps.PrefContactTimeShiftNavigation)
                .FirstOrDefaultAsync(item => item.ReqNumber == reqNumber);

            if (IsEmptyObject(response) || response is null)
            {
                logger.LogInformation("Item not found");
                return await req.WriteNoContentResponseAsync();
            }

            var matchingClinicCodes = clinicCodes.Where(clinicCode => response.Clinic == clinicCode);

            if (!matchingClinicCodes.Any())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Request not for eligible clinic");
            }

            if (submitter.QId is not null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning("Unauthorized ({SubmitterQId}) Access!!!", response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var getResponse = MapAppointmentToGetAppointmentItemResponse(response);
            logger.LogInformation("Returning Appointment Item By Request Number {ReqNumber}", getResponse.ReqNumber);
            return await req.WriteOkResponseAsync(getResponse);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CheckAppointmentChangeRequestExist

    /// <summary>
    ///  CheckAppointmentChangeRequestExist
    /// </summary>
    /// <param name="req"></param>
    /// <param name="appointmentId"></param>
    /// <returns></returns>
    [Function("CheckAppointmentChangeRequestExist")]
    [OpenApiOperation(operationId: "CheckAppointmentChangeRequestExist", tags: ["Appointment"],
        Summary = "Check Appointment Change Request Exist",
        Description = "Check appointment change eligibility for the given appointment id")]
    [OpenApiParameter("appointmentId", Description = "Appointment Id", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(CheckAppointmentChangeEligibilityResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> CheckAppointmentChangeRequestExist(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "appointment-change/{appointmentId}")]
        HttpRequestData req, long appointmentId)
    {
        try
        {
            logger.LogInformation(
                "Checking appointment change eligibility for the given appointmentId {AppointmentId}",
                appointmentId);

            var clinicCode = req.TryGetHeaders(EligibleClinicCodes);

            if (clinicCode.Count == 0)
            {
                logger.LogWarning("No Clinic Code found in the request header");
            }

            var inProcessCancellation = new[]
            {
                Submitted,
                PendingOrder,
                Confirmed
            };

            var inProcessReSchedule = new[]
            {
                Submitted,
                PendingOrder
            };

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var resultCancellation = await dbContext.Appointment!
                .Where(item =>
                    item.AppointmentId == appointmentId &&
                    item.RequestType == Cancel)
                .Select(item => new Appointment
                {
                    AppointmentId = item.AppointmentId,
                    RequestType = item.RequestType,
                    Status = item.Status,
                    CreatedAt = item.CreatedAt
                }).ToListAsync();

            var resultReschedule = await dbContext.Appointment!
                .Where(item =>
                    item.AppointmentId == appointmentId &&
                    item.RequestType == Reschedule)
                .Select(item => new Appointment
                {
                    AppointmentId = item.AppointmentId,
                    RequestType = item.RequestType,
                    Status = item.Status,
                    CreatedAt = item.CreatedAt
                }).ToListAsync();

            var latestCancellation = resultCancellation.MaxBy(item => item.CreatedAt);
            var latestReschedule = resultReschedule.MaxBy(item => item.CreatedAt);

            CheckAppointmentChangeEligibilityResponse response = new()
            {
                AppointmentId = appointmentId,
                IsCancelRequestExist = inProcessCancellation.Contains(latestCancellation?.Status),
                IsRescheduleInProcessExist = inProcessReSchedule.Contains(latestReschedule?.Status)
            };

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Create new Appointment request

    /// <summary>
    /// Create new Appointment request
    /// </summary>
    /// <param name="req">New Appointment Create Model</param>
    /// <returns></returns>
    [Function("CreateAppointment")]
    [OpenApiOperation(operationId: "CreateAppointment", tags: ["Appointment"], Summary = "Create Appointment",
        Description = "Create new Appointment request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateAppointmentRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(Appointment))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateAppointment(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "Appointments")]
        HttpRequestData req)
    {
        try
        {
            var requestOriginSource = req.TryGetHeader(RequestOrigin);
            if (requestOriginSource.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);

            if (clinicCodes.Count == 0)
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, ClinicCodesRequiredMessage);
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateAppointmentRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again");
            }

            long qId = request.SubmitterQId ?? 0;

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning("Unauthorized ({QId}) Access!!!", qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            // Validate Health Card number
            var healthCardValidation = await ValidateHealthCardNumberAsync(
                request.HcNumber, request.QId, "Appointment", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            if (clinicCodes.All(clinicCode => clinicCode != request.Clinic))
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, "Request not for eligible clinic");
            }

            logger.LogInformation("Creating An Appointment");

            if (!New.Equals(request.RequestType, OrdinalIgnoreCase)
                && !Reschedule.Equals(request.RequestType, OrdinalIgnoreCase)
                && !Cancel.Equals(request.RequestType, OrdinalIgnoreCase))
            {
                return await req.WriteErrorResponseAsync(BadRequest,
                    ValidRequestTypeMessage);
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            // Check for existing in-process appointment requests
            var existingInProcessQuery = dbContext.Appointment!
                .Join(dbContext.Status!, a => a.Status, s => s.Code, (appointment, status) => new { appointment, status })
                .AsQueryable();

            var isInProcessRequestExist = await existingInProcessQuery.AnyAsync(item =>
                item.appointment.HCNumber == request.HcNumber
                && item.appointment.Clinic == request.Clinic
                && item.status.Category! == InProcess);

            if (isInProcessRequestExist)
            {
                logger.LogWarning("Duplicate appointment request detected for HCNumber {HCNumber} and Clinic {Clinic}", request.HcNumber, request.Clinic);
                return await req.WriteErrorResponseAsync(BadRequest, "An in-process appointment request already exists for this health card number at the same clinic.");
            }

            Appointment create = MapCreateUpdateAppointmentRequestToAppointment(request);
            create.ReqNumber = RandomPassword();
            create.SubmittedAt = GetCurrentTime();
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;
            create.Status = Saved;

            try
            {
                await dbContext.Appointment!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                logger.LogInformation("Error: {InnerExceptionMessage}", ex.InnerException?.Message);
                return await req.WriteErrorResponseAsync(InternalServerError, ex.Message);
            }

            logger.LogInformation("Created A New Appointment With Request Number {ReqNumber}", create.ReqNumber);
            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Update Appointment for the given request number

    /// <summary>
    /// Update Appointment for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Appointment Request Number </param>
    /// <exception cref="ArgumentNullException"></exception>
    /// <returns></returns>
    [Function("UpdateAppointmentByReqNumber")]
    [OpenApiOperation(
        operationId: "UpdateAppointmentByReqNumber",
        tags: ["Appointment"],
        Summary = "Update Appointment By ReqNumber",
        Description = "Update an appointment for the given request number")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateAppointmentRequest))]
    [OpenApiParameter("reqNumber", Description = "Appointment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(Ambiguous, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateAppointmentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "Appointments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            logger.LogInformation("Updating an appointment");

            var requestOriginSource = req.TryGetHeader(RequestOrigin);
            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);

            if (clinicCodes.Count == 0)
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, "Clinic Code is required...");
            }

            if (reqNumber.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Request Number is required...");
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var response = await dbContext.Appointment!.FirstOrDefaultAsync(item => item.ReqNumber == reqNumber);

            if (response == null || IsEmptyObject(response))
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid/Non existing Req Number {reqNumber}");
            }

            if (clinicCodes.All(clinicCode => clinicCode != response.Clinic))
            {
                logger.LogError("No Eligible Clinic Codes found in the request header");
                return await req.WriteErrorResponseAsync(BadRequest, "Request not for eligible clinic");
            }

            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateAppointmentRequest>(requestBody);

            long qId = request.SubmitterQId ?? 0;

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning("Unauthorized ({QId}) Access!!!", qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            // Validate Health Card number
            var healthCardValidation = await ValidateHealthCardNumberAsync(
                request.HcNumber, request.QId, "Appointment", req, logger);

            if (!healthCardValidation.IsValid)
            {
                return healthCardValidation.ErrorResponse!;
            }

            if (!New.Equals(request.RequestType, OrdinalIgnoreCase)
                && !Reschedule.Equals(request.RequestType, OrdinalIgnoreCase)
                && !Cancel.Equals(request.RequestType, OrdinalIgnoreCase))
            {
                return req.WriteErrorResponseAsync(BadRequest,
                    ValidRequestTypeMessage).Result;
            }

            Appointment mapperResponse = MapCreateUpdateAppointmentRequestToAppointment(request);
            mapperResponse.Id = response.Id;
            mapperResponse.ReqNumber = response.ReqNumber;
            mapperResponse.Status = response.Status;
            mapperResponse.Clinic = response.Clinic;
            mapperResponse.StatusInternal = response.StatusInternal;
            mapperResponse.SN = response.SN;
            mapperResponse.SubmittedAt = response.SubmittedAt;
            mapperResponse.CreatedAt = response.CreatedAt;
            mapperResponse.UpdatedAt = GetCurrentTime();
            mapperResponse.CreateSource = response.CreateSource;
            mapperResponse.UpdateSource = requestOriginSource;
            dbContext.Entry(response).CurrentValues.SetValues(mapperResponse);

            await dbContext.SaveChangesAsync();

            logger.LogInformation("Updated an appointment with request number {ReqNumber}", response.ReqNumber);

            return await req.WriteOkResponseAsync(mapperResponse);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Delete Appointment for the given request number

    /// <summary>
    /// Delete Appointment for the given request number
    /// </summary>
    /// <param name="req"></param>
    /// <param name="reqNumber">Appointment Request Number </param>
    /// <returns></returns>
    [ExcludeFromCodeCoverage]
    [Function("DeleteAppointmentByReqNumber")]
    [OpenApiOperation(operationId: "DeleteAppointmentByReqNumber", tags: ["Appointment"],
        Summary = "Delete Appointment By ReqNumber",
        Description = "Delete Appointment item for the given request number")]
    [OpenApiParameter("reqNumber", Description = "Appointment Request Number", In = ParameterLocation.Path,
        Required = true, Type = typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiResponseWithBody(Unauthorized, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteAppointmentByReqNumber(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "Appointments/{reqNumber}")]
        HttpRequestData req, string reqNumber)
    {
        try
        {
            var submitter = req.GetClaims();

            logger.LogInformation("Deleting an appointment with request number: {ReqNumber}", reqNumber);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.Appointment!.FirstOrDefaultAsync(item => item.ReqNumber! == reqNumber);

            if (response == null)
            {
                logger.LogWarning("Invalid Req Number {ReqNumber}", reqNumber);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Req Number {reqNumber}");
            }

            if (submitter.QId != null && response.SubmitterQId != submitter.QId)
            {
                logger.LogWarning("Unauthorized ({SubmitterQId}) Access!!!", response.SubmitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            try
            {
                dbContext.Appointment.Remove(response);
                await dbContext.SaveChangesAsync();
                logger.LogInformation("Deleted Appointment By Request Number {ReqNumber}", response.ReqNumber);
                return await req.WriteOkResponseAsync($"Appointment Deleted By Request Number {reqNumber}");
            }
            catch (DbUpdateException ex)
            {
                logger.LogInformation("Error: {InnerExceptionMessage}", ex.InnerException?.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unexpected error deleting appointment {ResponseReqNumber}: {ExMessage}", response.ReqNumber, ex.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Retrieve Upcoming Confirmed Appointments By HC Number List

    /// <summary>
    /// Retrieve Upcoming Confirmed Appointments By HC Number List
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    [ExcludeFromCodeCoverage]
    [Function("UpcomingConfirmedAppointmentsByHCNumberListV2")]
    [OpenApiOperation(operationId: "UpcomingConfirmedAppointmentsByHCNumberListV2", tags: ["Appointment"],
        Summary = "Retrieve Upcoming Confirmed Appointments By Health Card Number List (V2)",
        Description = " Retrieve upcoming confirmed appointments for the Health Card Number List")]
    [OpenApiRequestBody("application/json", typeof(HcNumberList))]
    [OpenApiParameter("clinicCode", Description = "Clinic Code", In = Query, Required = false,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json",
        typeof(List<UpcomingConfirmedAppointmentListResponse>),
        Description = "List of Upcoming Confirmed Appointment")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpcomingConfirmedAppointmentsByHcNumberListV2(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "appointments/upcoming-confirmed-v2")]
        HttpRequestData req)
    {
        try
        {
            var submitter = req.GetClaims();
            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);

            if (clinicCodes.Count == 0)
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, ClinicCodesRequiredMessage);
            }

            logger.LogInformation("Getting Upcoming Confirmed Appointments By HCNumber List by {SubmitterQId}",
                submitter.QId);

            var request = DeserializeObject<HcNumberList>(await new StreamReader(req.Body).ReadToEndAsync());

            var hcNumbersXml = ConvertListOfHcNumbersToXml(request).Trim().ThrowIfNull().IfEmpty();
            string clinicCode = req.Query["clinicCode"];

            var env = req.TryGetHeader(PhccEnvironment);

            var userConfirmedAppts =
                await UpcomingConfirmedAppointmentsByHcNumbersOrQIdsXml(hcNumbersXml, clinicCode, env);

            if (userConfirmedAppts.Count == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Total Confirmed Appointments: {Count}", userConfirmedAppts.Count);

            var response = userConfirmedAppts
                .Where(item => clinicCodes.Contains(item.ClinicCode))
                .Select(item =>
                    MapGetUpcomingConfirmedAppointmentListResponseToUpcomingConfirmedAppointmentListResponse(
                        ConvertDataRowToUpcomingConfirmedAppointmentItem(item.ThrowIfNull())))
                .OrderBy(x => x.AppointmentDateTime)
                .ToList();

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Get Upcoming Confirmed Appointment Item By AppointmentId

    /// <summary>
    /// Get Upcoming Confirmed Appointment Item By AppointmentId
    /// </summary>
    /// <param name="req"></param>
    /// <param name="appointmentId"></param>
    /// <returns></returns>
    [ExcludeFromCodeCoverage]
    [Function("GetUpcomingConfirmedAppointmentByAppointmentId")]
    [OpenApiOperation(operationId: "GetUpcomingConfirmedAppointmentByAppointmentId", tags: ["Appointment"],
        Summary = "Retrieve Upcoming Confirmed Appointment Item By AppointmentId",
        Description = " Retrieve upcoming confirmed appointment for the given AppointmentId")]
    [OpenApiParameter("appointmentId", Description = "Appointment Id", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetUpcomingConfirmedAppointmentItemResponse),
        Description = "List of Upcoming Confirmed Appointment")]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetUpcomingConfirmedAppointmentByAppointmentId(
        [HttpTrigger(AuthorizationLevel.Function, "get",
            Route = "appointments/upcoming-confirmed-v2/{appointmentId}")]
        HttpRequestData req, long appointmentId)
    {
        try
        {
            var submitter = req.GetClaims();
            var clinicCodes = req.TryGetHeaders(EligibleClinicCodes);

            if (clinicCodes.Count == 0)
            {
                logger.LogError(NoClinicCodesInHeaderMessage);
                return await req.WriteErrorResponseAsync(BadRequest, ClinicCodesRequiredMessage);
            }

            logger.LogInformation("Requested by {SubmitterQId}", submitter.QId);
            logger.LogInformation("Getting Upcoming Confirmed Appointment Item By AppointmentId {AppointmentId}",
                appointmentId);

            var env = req.TryGetHeader(PhccEnvironment);

            var userConfirmedAppointments =
                await UpcomingConfirmedAppointmentItemByAppointmentId(appointmentId, env);

            if (userConfirmedAppointments.Count == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            var confirmedAppointment = userConfirmedAppointments
                .Select(row => ConvertDataRowToUpcomingConfirmedAppointmentItem(row.ThrowIfNull()))
                .FirstOrDefault(item => clinicCodes.Contains(item.ClinicCode));

            if (confirmedAppointment == null || IsEmptyObject(confirmedAppointment))
            {
                return await req.WriteNoContentResponseAsync();
            }

            var response = MapGetUpcomingConfirmedAppointmentListResponseToGetUpcomingConfirmedAppointmentItemResponse(
                confirmedAppointment);

            logger.LogInformation("Returning Appointment Item By Request Number {AppointmentId}", response.AppointmentId);
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Private Methods

    #region ConvertDataRowToUpcomingConfirmedAppointmentItem

    [ExcludeFromCodeCoverage]
    private static GetUpcomingConfirmedAppointmentListResponse ConvertDataRowToUpcomingConfirmedAppointmentItem(
        GetUpcomingConfirmedAppointmentListResponseFromEdw itemFromEdw)
    {
        try
        {
            var item = new GetUpcomingConfirmedAppointmentListResponse();

            if (!itemFromEdw.AppointmentId.IsNullOrWhiteSpace())
                item.AppointmentId = ToInt64(itemFromEdw.AppointmentId);

            if (!itemFromEdw.QId.IsNullOrDefault() && long.TryParse(itemFromEdw.QId.ToString(), out long qId))
                item.QId = qId;

            if (!itemFromEdw.QIdExpiryDt.IsNullOrDefault() && DateTime.TryParse(itemFromEdw.QIdExpiryDt.ToUtcString(), out DateTime qIdExpiryDt))
            {
                item.QIdExpiryDt = qIdExpiryDt;
            }

            item.FNameEn = itemFromEdw.FNameEn;
            item.MNameEn = itemFromEdw.MNameEn;
            item.LNameEn = itemFromEdw.LNameEn;
            item.FNameAr = itemFromEdw.FNameAr;
            item.MNameAr = itemFromEdw.MNameAr;
            item.LNameAr = itemFromEdw.LNameAr;
            item.NationalityCode = itemFromEdw.NationalityCode;
            item.NationalityEn = itemFromEdw.NationalityEn;
            item.NationalityAr = itemFromEdw.NationalityAr;

            if (!itemFromEdw.Dob.IsNullOrDefault() && DateTime.TryParse(itemFromEdw.Dob.ToString(), CultureInfo.InvariantCulture, out DateTime dob))
                item.Dob = dob;

            item.HCNumber = itemFromEdw.HCNumber;

            if (!itemFromEdw.HCExpiryDate.IsNullOrDefault() &&
                DateTime.TryParse(itemFromEdw.HCExpiryDate.ToUtcString(), CultureInfo.InvariantCulture, out DateTime hcExpiryDate))
                item.HCExpiryDate = hcExpiryDate;

            item.GenderCode = itemFromEdw.GenderCode;
            item.GenderEn = itemFromEdw.GenderEn;
            item.GenderAr = itemFromEdw.GenderAr;
            item.PhoneMobile = itemFromEdw.PhoneMobile;

            item.AggignedHCntCode = itemFromEdw.AggignedHCntCode;
            item.AggignedHCntNameEn = itemFromEdw.AggignedHCntNameEn;
            item.AggignedHCntNameAr = itemFromEdw.AggignedHCntNameAr;

            item.AppointmentHCntCode = itemFromEdw.AppointmentHCntCode;
            item.AppointmentHCntNameEn = itemFromEdw.AppointmentHCntNameEn;
            item.AppointmentHCntNameAr = itemFromEdw.AppointmentHCntNameAr;

            item.ClinicCode = itemFromEdw.ClinicCode;
            item.ClinicNameEn = itemFromEdw.ClinicNameEn;
            item.ClinicNameAr = itemFromEdw.ClinicNameAr;

            item.GisAddressStreet = itemFromEdw.GisAddressStreet;
            item.GisAddressBuilding = itemFromEdw.GisAddressBuilding;
            item.GisAddressZone = itemFromEdw.GisAddressZone;
            item.GisAddressUnit = itemFromEdw.GisAddressUnit;

            item.PhysicianId = itemFromEdw.PhysicianId;
            item.PhysicianFullNameEn = itemFromEdw.PhysicianFullNameEn;
            item.PhysicianFullNameAr = itemFromEdw.PhysicianFullNameAr;

            if (!itemFromEdw.BookedDateTime.IsNullOrDefault() &&
                DateTime.TryParse(itemFromEdw.BookedDateTime.ToString(), CultureInfo.InvariantCulture, out DateTime bookedDateTime))
                item.BookedDateTime = bookedDateTime;

            if (!itemFromEdw.AppointmentDateTime.IsNullOrDefault() &&
                DateTime.TryParse(itemFromEdw.AppointmentDateTime.ToString(), CultureInfo.InvariantCulture, out DateTime appointmentDateTime))
                item.AppointmentDateTime = appointmentDateTime;

            item.AppointmentType = itemFromEdw.AppointmentType;
            item.AppointmentStatus = itemFromEdw.AppointmentStatus;
            item.AppointmentLocation = itemFromEdw.AppointmentLocation;
            return item;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    #endregion

    #region UpcomingConfirmedAppointmentsByHCNumbersOrQIDsXml

    [ExcludeFromCodeCoverage]
    private async Task<List<GetUpcomingConfirmedAppointmentListResponseFromEdw>>
        UpcomingConfirmedAppointmentsByHcNumbersOrQIdsXml(string xmlString, string clinicCode, string env,
            bool isQIdInput = false)
    {
        try
        {
            logger.LogInformation("InputXML {XmlString}", xmlString);

            var inputXmlString = new SqlParameter("@InputXML", VarChar);
            var clinicCodeParam = new SqlParameter("@CLINIC_CODE", VarChar, 20);
            var envParam = new SqlParameter("@ENV", VarChar, 10);

            string spName = isQIdInput
                ? "dbo.SP_GET_MULTI_USER_CONFIRMED_APPTS_QID "
                : "dbo.SP_GET_MULTI_USER_CONFIRMED_APPTS_V2 ";

            if (!string.IsNullOrEmpty(xmlString))
            {
                inputXmlString.Value = xmlString;
            }

            envParam.Value = !string.IsNullOrEmpty(env) ? env : "STG";

            await using var edwDbContext = await edwDbContextFactory.CreateDbContextAsync();

            if (!string.IsNullOrEmpty(clinicCode))
            {
                clinicCodeParam.Value = clinicCode;

                var response = await edwDbContext.UpcomingConfirmedAppointmentListResponseFromEDW
                    .FromSqlInterpolated($"EXEC {spName} {inputXmlString}, {clinicCodeParam}, {envParam}")
                    .ToListAsync();
                return response;
            }
            else
            {
                var response = await edwDbContext.UpcomingConfirmedAppointmentListResponseFromEDW
                    .FromSqlInterpolated($"EXEC {spName} {inputXmlString}, {null}, {envParam}").ToListAsync();
                return response;
            }
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    #endregion

    #region UpcomingConfirmedAppointmentItemByAppointmentId

    [ExcludeFromCodeCoverage]
    private async Task<List<GetUpcomingConfirmedAppointmentListResponseFromEdw>>
        UpcomingConfirmedAppointmentItemByAppointmentId(long appointmentId, string env)
    {
        try
        {
            var inputAppointmentId = new SqlParameter("@APPT_ID", BigInt);
            var envParam = new SqlParameter("@ENV", VarChar);

            const string spName = "dbo.SP_GET_CONFIRMED_APPT_DETAILS ";

            if (appointmentId > 0) inputAppointmentId.Value = appointmentId;

            await using var edwDbContext = await edwDbContextFactory.CreateDbContextAsync();

            if (!string.IsNullOrEmpty(env))
            {
                envParam.Value = env;
                var response = await edwDbContext.UpcomingConfirmedAppointmentListResponseFromEDW
                    .FromSqlInterpolated($"EXEC {spName} {inputAppointmentId}, {envParam}").ToListAsync();
                return response;
            }
            else
            {
                logger.LogWarning("Environment variable is null or empty");
                var response = await edwDbContext.UpcomingConfirmedAppointmentListResponseFromEDW
                    .FromSqlInterpolated($"EXEC {spName} {inputAppointmentId}").ToListAsync();
                return response;
            }
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    #endregion

    #region Convert List Of HCNUmbers to XML

    [ExcludeFromCodeCoverage]
    private static string ConvertListOfHcNumbersToXml(HcNumberList hcList)
    {
        if (hcList.HcNumbers == null || hcList.HcNumbers.Count == 0) return string.Empty;
        var xml = new XElement("HC_NUMBERList", hcList.HcNumbers?.Select(x => new XElement("HC_NUMBER", x))).ToString();
        return Replace(xml, @"\s+", EmptyString);
    }

    #endregion

    #endregion
}