--- Script to Create Database tables ---

﻿
IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Enrollment'
             AND type = 'U')
ALTER TABLE [dbo].[Enrollment]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Appointment'
             AND type = 'U')
ALTER TABLE [dbo].[Appointment]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Assignment'
             AND type = 'U')
ALTER TABLE [dbo].[Assignment]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Registration'
             AND type = 'U')
ALTER TABLE [dbo].[Registration]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Release'
             AND type = 'U')
ALTER TABLE [dbo].[Release]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Transfer'
             AND type = 'U')
ALTER TABLE [dbo].[Transfer]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Attachment'
             AND type = 'U')
ALTER TABLE [dbo].[Attachment]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'Comment'
             AND type = 'U')
ALTER TABLE [dbo].[Comment]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'UserDependent'
             AND type = 'U')
ALTER TABLE [dbo].[UserDependent]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'UserProfile'
             AND type = 'U')
ALTER TABLE [dbo].[UserProfile]
    ADD [CreateSource] NVARCHAR(255) NULL,
        [UpdateSource] NVARCHAR(255) NULL
GO
