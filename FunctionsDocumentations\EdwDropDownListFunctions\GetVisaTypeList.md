# GetVisaTypeList

## Overview
Retrieves a list of visa types from the Medical Records Database.

## Endpoint
- **Route**: `visatypes`
- **Method**: GET
- **Authorization**: Function level

## Response
- **200 OK**: Returns list of visa types
  ```json
  [
    {
      "VisaTypeCode": "string",        // Visa type code
      "VisaDescriptionEn": "string",   // English description
      "VisaDescriptionAr": "string"    // Arabic description
    }
  ]
  ```
- **204 No Content**: No visa types found
- **400 Bad Request**: Error processing request

## Business Logic
1. Logs request URL and operation start
2. Queries MAP_MOI_VisaTypes table
3. Projects to GetVisaTypeListResponse model
4. Checks for empty result set
5. Returns formatted response

## Data Source
- Table: MAP_MOI_VisaTypes
- Context: MRDDbContext (Medical Records Database)

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient projection in Select statement
- No unnecessary joins or filters
