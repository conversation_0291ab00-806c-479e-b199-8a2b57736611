# UpdateMHDSByReqNumber

## Overview
Updates an existing Medicine Home Delivery Service (MHDS) request, allowing request cancellation and GIS information updates.

## Endpoint
- **Route**: `mhds/{reqNumber}`
- **Method**: PUT
- **Authorization**: Function level

## Request Headers
- **X-RequestOrigin**: Required. Source of the request

## Parameters
- **reqNumber** (path, required): MHDS Request Number

## Request Body
```json
{
  "QId": "long",
  "SubmitterQId": "long",
  "IsGisAddressManualyEntered": "boolean",
  "UNo": "string",
  "BNo": "string",
  "SNo": "string",
  "ZNo": "string"
}
```

## Response
- **200 OK**: Returns updated MHDS request details
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Invalid QID authorization
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates request origin header
2. Validates user authorization against submitter <PERSON><PERSON>
3. Retrieves existing MHDS request
4. Updates allowed fields:
   - QID information
   - GIS address details (if manually entered)
5. Records update metadata:
   - Update timestamp
   - Update source

## Data Operations
1. Retrieves MHDSRequestDetails entry
2. Updates specified fields
3. Tracks changes before saving
4. Updates only if changes detected

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Request origin validation
- Logging of all operations

## Error Handling
- Invalid request number validation
- Empty request body validation
- Change tracking validation
- Exception handling with default behavior
