using EServiceFunctions.Models.GeneralWorks;
using EServiceFunctions.RequestResponseModels.GeneralWorks;
using System.Diagnostics;
using static EServiceFunctions.Helpers.KeyVaultHelper;
using static EServiceFunctions.MapperModels.CovidVaccinationMapper;

namespace EServiceFunctions.Functions;

[ExcludeFromCodeCoverage]
public class CovidVaccinationFunctions(
    IDbContextFactory<GeneralWorksContext> dbContextFactory,
    ILogger<CovidVaccinationFunctions> logger)
{
    private readonly IDbContextFactory<GeneralWorksContext> _dbContextFactory =
        dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));

    private readonly ILogger<CovidVaccinationFunctions> _logger =
        logger ?? throw new ArgumentNullException(nameof(logger));

    private const string ServiceName = "CovidVaccinationService";

    #region CreateCovidVaccinationRequest

    /// <summary>
    /// Creates a new Covid Vaccination Request
    /// </summary>
    /// <param name="req">HTTP request containing the Covid Vaccination Request details</param>
    /// <returns>HTTP response with the created vaccination URL or error details</returns>
    /// <remarks>
    /// Success Response:
    /// - 201 Created with vaccination URL
    /// Error Responses:
    /// - 400 Bad Request: Invalid input data
    /// - 500 Internal Server Error: Database or system errors
    /// </remarks>
    [Function("CreateCovidVaccinationRequest")]
    [OpenApiOperation(operationId: "CreateCovidVaccinationRequest", tags: ["CovidVaccination"],
        Summary = "Create Covid Vaccination Request",
        Description = "Creates a new Covid vaccination request with validation and error handling")]
    [OpenApiRequestBody("application/json", typeof(CovidVaccinationRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(CreateCovidVaccinationResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(ErrorResponse))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateCovidVaccinationRequest(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "covidvaccinationrequest")]
        HttpRequestData req)
    {
        using var operation = new ActivitySource(ServiceName).StartActivity();
        var correlationId = operation?.TraceId.ToString() ?? Guid.NewGuid().ToString();

        try
        {
            _logger.LogInformation(
                "Starting Covid Vaccination Request creation. {CorrelationId}",
                correlationId);

            // Validate and parse request
            var request = await ValidateAndParseRequest(req, correlationId);
            if (!request.IsSuccess)
                return request.Error!;

            // Create vaccination record
            var createResult = await CreateVaccinationRecord(req, request.Value!, correlationId);
            if (!createResult.IsSuccess)
                return createResult.Error!;

            // Generate response
            var response = await GenerateSuccessResponse(req, createResult.Value!, correlationId);

            _logger.LogInformation(
                "Successfully created Covid Vaccination Request. {CorrelationId}, RequestId: {RequestId}",
                correlationId, createResult.Value!.Id);

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Unhandled exception in CreateCovidVaccinationRequest. {CorrelationId}, Error: {Error}",
                correlationId, ex.Message);
            return await req.WriteErrorResponseAsync(
                InternalServerError,
                "An unexpected error occurred. Please try again later.");
        }
    }

    private async Task<Result<CovidVaccinationRequest>> ValidateAndParseRequest(
        HttpRequestData req, string correlationId)
    {
        try
        {
            _logger.LogDebug("Parsing request body. {CorrelationId}", correlationId);

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CovidVaccinationRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                _logger.LogWarning(
                    "Invalid request body received. {CorrelationId}", correlationId);
                return Result<CovidVaccinationRequest>.Failure(
                    await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again."));
            }

            var validationResult = ValidateCovidVaccinationRequest(request);
            if (validationResult.IsValid)
            {
                return Result<CovidVaccinationRequest>.Success(request);
            }

            _logger.LogWarning(
                "Validation failed for request. {CorrelationId}, Errors: {Errors}",
                correlationId, string.Join(", ", validationResult.Errors));
            
            return Result<CovidVaccinationRequest>.Failure(
                await req.WriteErrorResponseAsync(BadRequest, validationResult.Errors[0]));

        }
        catch (JsonException ex)
        {
            _logger.LogError(ex,
                "JSON parsing error. {CorrelationId}, Error: {Error}",
                correlationId, ex.Message);
            return Result<CovidVaccinationRequest>.Failure(
                await req.WriteErrorResponseAsync(BadRequest, "Invalid request format."));
        }
    }

    private async Task<Result<CovidVaccination>> CreateVaccinationRecord(
        HttpRequestData req,
        CovidVaccinationRequest request,
        string correlationId)
    {
        try
        {
            var vaccination = MapCovidVaccinationRequestToCovidVaccination(request);
            vaccination.Id = Guid.NewGuid();
            vaccination.Status = Ready;
            vaccination.CreatedAt = GetCurrentTime();

            await using var dbContext = await _dbContextFactory.CreateDbContextAsync();

            _logger.LogDebug(
                "Saving vaccination record to database. {CorrelationId}, RequestId: {RequestId}",
                correlationId, vaccination.Id);

            await dbContext.CovidVaccination!.AddAsync(vaccination);
            await dbContext.SaveChangesAsync();

            return Result<CovidVaccination>.Success(vaccination);
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex,
                "Database error while creating vaccination record. {CorrelationId}, Error: {Error}",
                correlationId, ex.InnerException?.Message);
            return Result<CovidVaccination>.Failure(
                await req.WriteErrorResponseAsync(InternalServerError,
                    "Unable to create vaccination record. Please try again later."));
        }
    }

    private async Task<HttpResponseData> GenerateSuccessResponse(
        HttpRequestData req,
        CovidVaccination vaccination,
        string correlationId)
    {
        try
        {
            var covidVaccinationUrl = $"{GetSecret("K2CovidVaccinationURL")}{vaccination.Id}";
            var response = new CreateCovidVaccinationResponse
            {
                CovidVaccinationURL = covidVaccinationUrl
            };

            return await req.WriteOkResponseAsync(response, Created);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error generating success response. {CorrelationId}, VaccinationId: {VaccinationId}, Error: {Error}",
                correlationId, vaccination.Id, ex.Message);
            return await req.WriteErrorResponseAsync(InternalServerError,
                "Request processed but unable to generate response URL.");
        }
    }

    private static ValidationResult ValidateCovidVaccinationRequest(CovidVaccinationRequest request)
    {
        var result = new ValidationResult();

        if (!request.IDNumber.IsNotNullOrWhiteSpace())
            result.Errors.Add("ID Number is required.");

        if (!request.IDType.IsNotNullOrWhiteSpace())
            result.Errors.Add("ID Type is required.");

        if (!request.MobileNumber.IsNotNullOrWhiteSpace())
            result.Errors.Add("Mobile Number is required.");
        
        return result;
    }

    #endregion
}

public class Result<T>
{
    public bool IsSuccess { get; }
    public T? Value { get; }
    public HttpResponseData? Error { get; }

    private Result(bool isSuccess, T? value, HttpResponseData? error)
    {
        IsSuccess = isSuccess;
        Value = value;
        Error = error;
    }

    public static Result<T> Success(T value) => new(true, value, null);
    public static Result<T> Failure(HttpResponseData error) => new(false, default, error);
}

public class ValidationResult
{
    public List<string> Errors { get; } = [];
    public bool IsValid => Errors.Count == 0;
}