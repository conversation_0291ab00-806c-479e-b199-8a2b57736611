# CreateAttachmentV2

## Overview
Creates a new attachment using multipart/form-data format.

## Endpoint
- **Route**: `attachments-v2`
- **Method**: POST
- **Authorization**: Function level

## Headers
- `X-RequestOrigin`: Required header indicating request origin
- `Content-Type`: Must be multipart/form-data

## Request Body
- Form data containing file
- File must be included in the form data

## Responses
- **201 Created**: Returns created attachment
- **400 Bad Request**: 
  - Invalid form data
  - Invalid file type
  - File size exceeds limit
  - Missing file in form data
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates request origin header
2. Processes multipart form data
3. Extracts file content and metadata
4. Validates file extension and size
5. Creates attachment record
6. Returns created attachment

## Validation Rules
- Maximum file size: 5MB
- File extension must be valid
- Request origin must be provided
- File must be present in form data

## Dependencies
- CommentAndAttachmentContext
- MultipartReader
- File validation utilities
