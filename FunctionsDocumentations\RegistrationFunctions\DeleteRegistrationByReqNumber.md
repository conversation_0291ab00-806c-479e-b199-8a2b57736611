# Delete Registration By Request Number

## Overview
Deletes a draft registration request, only allowing deletion of registrations in "Saved" status.

## Endpoint
- **Route**: `registrations/{reqNumber}`
- **Method**: DELETE
- **Authorization**: Function level with submitter validation
- **OpenAPI Ignore**: True (endpoint hidden from OpenAPI documentation)

## Parameters
- **reqNumber** (path, required): Registration request number

## Response
- **200 OK**: Returns success message
  ```json
  "Successfully Deleted"
  ```
- **400 Bad Request**: Invalid request number or non-draft registration
- **401 Unauthorized**: Invalid submitter authorization
- **500 Internal Server Error**: Database operation error

## Business Logic
1. Validates request number
2. Verifies "Saved" status
3. Validates submitter authorization
4. Performs soft delete
5. Records deletion

## Status Requirements
- Only registrations with status "Saved" can be deleted
- Prevents deletion of submitted or processed registrations
- Maintains data integrity

## Security Considerations
- Function-level authorization
- Submitter validation
- Status validation
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Database error handling

## Database Operations
- Single record deletion
- Status validation
- Submitter verification
- Transaction management

## Validation Checks
1. Request number validation
2. Status must be "Saved"
3. Submitter must match
4. Empty object check
