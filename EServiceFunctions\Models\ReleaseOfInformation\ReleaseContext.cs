﻿namespace EServiceFunctions.Models.ReleaseOfInformation;

public class ReleaseContext(DbContextOptions<ReleaseContext> options) : DbContext(options)
{
    public virtual DbSet<Release>? Release { get; set; }
    public virtual DbSet<Status>? Status { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<Release>(entity =>
        {
            entity.HasIndex(e => e.QId)
                .HasDatabaseName("IDX_Release_QId");

            entity.HasIndex(e => e.ReqNumber)
                .HasDatabaseName("UQ__Release__237F3423F15414DE")
                .IsUnique();

            entity.HasIndex(e => e.SubmitterQId)
                .HasDatabaseName("IDX_Release_SubmitterQId");

            entity.Property(e => e.AttendanceRptEndDate).HasColumnType("date");

            entity.Property(e => e.AttendanceRptStartDate).HasColumnType("date");

            entity.Property(e => e.AuthorizedPersonName).HasMaxLength(255);

            entity.Property(e => e.AuthorizedPersonRelation).HasMaxLength(255);

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.Property(e => e.CurrentAssignedHC).HasMaxLength(255);

            entity.Property(e => e.DelegationOthers).HasMaxLength(255);

            entity.Property(e => e.Dob).HasColumnType("date");

            entity.Property(e => e.EncounterEndDate).HasColumnType("date");

            entity.Property(e => e.EncounterStartDate).HasColumnType("date");

            entity.Property(e => e.FNameAr).HasMaxLength(255);

            entity.Property(e => e.FNameEn).HasMaxLength(255);

            entity.Property(e => e.HCNumber).HasMaxLength(255);

            entity.Property(e => e.LNameAr).HasMaxLength(255);

            entity.Property(e => e.LNameEn).HasMaxLength(255);

            entity.Property(e => e.MNameAr).HasMaxLength(255);

            entity.Property(e => e.MNameEn).HasMaxLength(255);

            entity.Property(e => e.Nationality).HasMaxLength(255);

            entity.Property(e => e.OtherReleaseItems).HasMaxLength(255);

            entity.Property(e => e.ReleaseItems).HasMaxLength(1000);

            entity.Property(e => e.ReqNumber).HasMaxLength(255);

            entity.Property(e => e.SN).HasMaxLength(255);

            entity.Property(e => e.Status).HasMaxLength(255);

            entity.Property(e => e.StatusInternal).HasMaxLength(255);

            entity.Property(e => e.SubmittedAt).HasColumnType("datetime");

            entity.Property(e => e.SubmittedBy).HasMaxLength(255);

            entity.Property(e => e.SubmitterEmail).HasMaxLength(255);

            entity.Property(e => e.SubmitterMobile).HasMaxLength(255);

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);

            entity.HasOne(d => d.StatusNavigation)
                .WithMany(p => p.Release)
                .HasForeignKey(d => d.Status)
                .HasConstraintName("FK__Release__Status__66EA454A");
        });

        modelBuilder.Entity<Status>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Status__A25C5AA68EAE98DF");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);

            entity.Property(e => e.Category).HasMaxLength(255);
        });
    }
}