# GetRequestStatusStatsByQID

## Overview
Retrieves task status statistics for a specific user based on their Qatar ID, providing counts of requests across different services and statuses.

## Endpoint
- **Route**: `requeststatus-stats/{qId}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **qId** (path, required): Submitter's Qatar ID number

## Response
- **200 OK**: Returns list of task status statistics
  ```json
  [
    {
      "EService": "string",      // Service name or "All"
      "ReqCount": "integer",     // Total requests for service
      "Stats": {                 // Status-wise counts
        "status1": "integer",
        "status2": "integer"
      }
    }
  ]
  ```
- **204 No Content**: No tasks found
- **400 Bad Request**: Invalid parameters
- **401 Unauthorized**: Invalid QID authorization

## Business Logic
1. Validates user authorization against QID
2. Executes stored procedure `SP_GetMyTaskStatusStats`
3. Groups results by:
   - Individual services
   - All services combined
4. Calculates counts for each status
5. Provides total request count per service

## Query Optimization
- Uses stored procedure for efficient data retrieval
- AsNoTracking for read-only operations
- Efficient grouping and aggregation
- Optimized data transformation

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Unauthorized access logging

## Database Operations
- Uses stored procedure
- No direct table access
- Efficient data aggregation
- Read-only transaction
