using EServiceFunctions.Models.HmcLiveFeeds;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.Models.UserProfile;
using EServiceFunctions.RequestResponseModels.UserProfile;
using Microsoft.FeatureManagement;
using static System.DateTime;
using static System.String;
using static EServiceFunctions.MapperModels.UserProfileMapper;
using UserProfile = EServiceFunctions.RequestResponseModels.UserProfile.UserProfile;

namespace EServiceFunctions.Functions;

[ExcludeFromCodeCoverage]
public class UserProfileFunctions(
    IDbContextFactory<UserProfileContext> dbContextFactory,
    IDbContextFactory<EDWDbContext> edwDbContextFactory,
    IDbContextFactory<HmcLiveFeedContext> hmcLiveFeedsContextFactory,
    IFeatureManager featureManager,
    ILogger<UserProfileFunctions> logger)
{
    // Logging message templates
    private const string UnauthorizedQIdAccessTemplate = "Unauthorized ({QId}) Access!!!";
    private const string ErrorMessageTemplate = "Error: {Message}";
    private const string RequestedUrlTemplate = "Requested URL: {Url}";
    private const string ValidatingQIdTemplate = "Validating QId: {SubmitterQId}";
    private const string UnenrolledUserDependentTemplate = "Unenrolled a user dependent with QId: {DependentQId} for the given submitter QId: {SubmitterQId}";
    private const string NoRecordsFoundTemplate = "No Records Found For The Given Submitter QID {SubmitterQId} & Dependent QID {DependentQId}";
    private const string InvalidQIdTemplate = "Invalid QID {QId}";
    private const string DeletingUserProfileTemplate = "Deleting a User Profile";
    private const string GettingDependentDetailsTemplate = "Getting Dependent Details By Submitter QID {QId}";
    private const string ReturningAllDependentsTemplate = "Returning all dependents list ({Count}) By Submitter's QID {QId}";
    private const string ReturningLinkedDependentsTemplate = "Returning linked dependents List ({Count}) By Submitter's QID {QId}";
    private const string ReturningUnlinkedDependentsTemplate = "Returning unlinked dependents List ({Count}) By Submitter's QID {QId}";
    private const string SettingHcExpiryMainTemplate = "Setting HcExpiry From live Feeds for Main Profile";
    private const string SettingHcExpiryDependentTemplate = "Setting HcExpiry From live Feeds for Dependent Profile";
    private const string LinkingUserDependentTemplate = "Linking An User Dependent {DependentQId} For The Given Submitter QID {SubmitterQId}";
    private const string InputXmlTemplate = "Input XML: {InputXml}";
    private const string CreatingUserProfileTemplate = "Creating a UserProfile";
    private const string UpdatingUserProfileTemplate = "Updating a UserProfile";
    private const string DependentQIdAlreadyLinkedTemplate = "Dependent QID {DependentQId} has been already linked";
    private const string InvalidDependentQIdTemplate = "Invalid Dependent QID {DependentQId}";
    private const string UserProfileCreatedTemplate = "Created UserProfile with QID {QId}";
    private const string UserProfileUpdatedTemplate = "Updated UserProfile with QID {QId}";
    private const string UserProfileDeletedTemplate = "Deleted UserProfile with QID {QId}";
    private const string UserDependentLinkedTemplate = "Linked User Dependent with QID {QId}";
    private const string GetMoiDependentsTemplate = "Get dependents from MOI for the given QID {QId}";

    #region GetUserAndDependentListByQID Function

    /// <summary>
    /// Retrieves user and dependent profile information based on a QID.
    /// It fetches data from a database, optionally filtering for only the submitter,
    /// then enriches the results with live feed data if available,
    /// handling potential authorization and data inconsistencies.
    /// The response includes user and dependent details,
    /// potentially updated with healthcare information from live feeds.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("GetUserAndDependentListByQID")]
    [OpenApiOperation(operationId: "GetUserAndDependentListByQID", tags: ["UserProfile"],
        Summary = "Get User And Dependent List By QID",
        Description = "Get user and dependent details for the given qid")]
    [OpenApiParameter("qId", Description = "User QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("isSubmitterDetailsOnly", Description = "To Get Only Submitter QID Details",
        In = ParameterLocation.Query, Required = false, Type = typeof(bool))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(GetUserProfileResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetUserAndDependentListByQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "userprofiles/user-dependents/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation(RequestedUrlTemplate, req.LogRequestedUrl());
            logger.LogInformation("Live Feeds Enrichment FeatureEnabled: {LiveFeedsEnrichment}", await featureManager.IsEnabledAsync("LiveFeedsEnrichment"));

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            bool.TryParse(req.Query["isSubmitterDetailsOnly"], out bool isSubmitterDetailsOnly);
            var qIdList = new List<long>();

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            await using var liveFeedsDbContext = await hmcLiveFeedsContextFactory.CreateDbContextAsync();

            if (!isSubmitterDetailsOnly)
            {
                qIdList = await dbContext.UserDependent!.AsNoTracking()
                    .Where(item => item.ParentQId == qId
                                   && item.IsMinor == true
                                   && item.IsRelationshipActive == true)
                    .Select(item => item.QId).ToListAsync();
            }

            qIdList.Add(qId);

            StringBuilder builder = BuildQIDsXml(qIdList);
            var env = req.TryGetHeader(PhccEnvironment);
            GetUserProfileResponse response = await GetUserProfileDetailsFromEdw(logger, builder, qId, env);

            if (response.QId == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            var liveFeedsResponses = await liveFeedsDbContext.Patients
                    .Where(p => p.QId == qId.ToString())
                    .ToListAsync();

            if (liveFeedsResponses.Count == 0)
            {
                return await req.WriteOkResponseAsync(response);
            }

            // Get the most recent live feeds response if multiple records exist, otherwise take the only record
            var liveFeedsResponse = liveFeedsResponses.Count > 1
                ? liveFeedsResponses.OrderByDescending(p => p.EvnDate).First()
                : liveFeedsResponses[0];

            // Update main profile
            if (response.QId.ToString() == liveFeedsResponse.QId && liveFeedsResponse.HcExpiryDate is not null)
            {
                logger.LogInformation(SettingHcExpiryMainTemplate);
                response.HcExpiryDate = liveFeedsResponse.HcExpiryDate.ToUtcString();
            }

            // Update dependents if matching
            if (response.Dependents.Count == 0)
            {
                return await req.WriteOkResponseAsync(response);
            }

            var matchingDependent = response.Dependents.FirstOrDefault(d =>
                d.QId.ToString().Trim().Equals(liveFeedsResponse.QId, OrdinalIgnoreCase));

            if (matchingDependent is null || liveFeedsResponse.HcExpiryDate is null)
            {
                return await req.WriteOkResponseAsync(response);
            }

            logger.LogInformation(SettingHcExpiryDependentTemplate);
            matchingDependent.HcExpiryDate = liveFeedsResponse.HcExpiryDate.ToUtcString();

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
    
    #region ValidateQID

    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("ValidateQID")]
    [OpenApiOperation(operationId: "ValidateQID", tags: ["UserProfile"], Summary = "Validate QID",
        Description = "validate the details for the given qid")]
    [OpenApiParameter("qId", Description = "User QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(ValidateQIDFlags))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> ValidateQId(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "validate-qid/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            // Reduced logging: combining URL and validation info into one log
            logger.LogInformation($"{RequestedUrlTemplate} - {ValidatingQIdTemplate}", 
                req.LogRequestedUrl(), req.GetClaims().QId);

            List<long> qIdList = [qId];
            StringBuilder builder = BuildQIDsXml(qIdList);
            var env = req.TryGetHeader(PhccEnvironment);
            var response = await GetUserProfileDetailsFromEdw(logger, builder, qId, env);

            if (response.QId == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            var validateQIdFlagsResponse = new ValidateQIDFlags
            {
                isCitizen = response.NationalityCode == "634"
            };

            DateTime dob = ToDateTime(response.Dob);
            validateQIdFlagsResponse.isMinor = AgeHelper.IsMinor(dob);

            if (!IsNullOrWhiteSpace(response.QIdExpiryDt))
            {
                var qIdExpiryDateTimeSpan = Today - ToDateTime(response.QIdExpiryDt);
                validateQIdFlagsResponse.isQIDValid = qIdExpiryDateTimeSpan.Days < 0;
                validateQIdFlagsResponse.isQIDinGrace = qIdExpiryDateTimeSpan.Days is > 0 and <= 365;
            }
            else
            {
                validateQIdFlagsResponse.isQIDValid = false;
                validateQIdFlagsResponse.isQIDinGrace = false;
            }

            if (!IsNullOrWhiteSpace(response.HcExpiryDate))
            {
                validateQIdFlagsResponse.isHCardPresent = true;
                var hcExpiryDateTimeSpan = Today - ToDateTime(response.HcExpiryDate);
                validateQIdFlagsResponse.isHCardValid = hcExpiryDateTimeSpan.Days < 0;
                validateQIdFlagsResponse.isHCardinGrace = hcExpiryDateTimeSpan.Days is > 0 and <= 365;
            }
            else
            {
                validateQIdFlagsResponse.isHCardPresent = false;
                validateQIdFlagsResponse.isHCardValid = false;
                validateQIdFlagsResponse.isHCardinGrace = false;
            }

            if (!IsNullOrWhiteSpace(response.GisAddressUpdatedAt))
            {
                var gisAddressLastUpdateDateTimeSpan = Today - ToDateTime(response.GisAddressUpdatedAt);
                int.TryParse(GetEnvironmentVariable("MinNumberOfDaysFromGISAddressLastUpdate"),
                    out var minNumberOfDaysFromGisAddressLastUpdate);
                validateQIdFlagsResponse.isGISAddressNotRecent = gisAddressLastUpdateDateTimeSpan.Days
                                                                 <= minNumberOfDaysFromGisAddressLastUpdate;
            }

            validateQIdFlagsResponse.isFPhysicianAssigned = !IsNullOrEmpty(response.PhysicianId);
            validateQIdFlagsResponse.isHCenterAssigned = !IsNullOrEmpty(response.AssignedHealthCenter);
            var oGccList = new[] { "414", "512", "682", "048", "784" };
            validateQIdFlagsResponse.isOGCC = oGccList.Contains(response.NationalityCode);
            logger.LogInformation("Returning ValidateQIDFlags response for QID {QId}", qId);
            return await req.WriteOkResponseAsync(validateQIdFlagsResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UnenrollUserDependentByQID

    /// <param name="req"></param>
    /// <param name="submitterQId"></param>
    /// <param name="dependentQId"></param>
    /// <returns></returns>
    [Function("UnenrollUserDependentByQID")]
    [OpenApiOperation(operationId: "UnenrollUserDependentByQID", tags: ["Enrollment"],
        Summary = "Unenroll User Dependent By QID",
        Description =
            "Removes the User and Dependent relationship from EServices system for the given submitter & dependent qid")]
    [OpenApiParameter("submitterQId", Description = "User QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("dependentQId", Description = "User Dependent QID", In = ParameterLocation.Path,
        Required = true, Type = typeof(long))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UnEnrollUserDependentByQId(
        [HttpTrigger(AuthorizationLevel.Function, "delete",
            Route = "user-dependents/{submitterQId}/{dependentQId}")]
        HttpRequestData req, long submitterQId, long dependentQId)
    {
        try
        {
            logger.LogInformation(RequestedUrlTemplate, req.LogRequestedUrl());

            if (!req.IsAuthorized(submitterQId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, submitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.UserDependent!.SingleOrDefaultAsync(item =>
                item.QId == dependentQId && item.ParentQId == submitterQId);

            if (response == null)
            {
                logger.LogWarning(NoRecordsFoundTemplate, submitterQId, dependentQId);

                return await req.WriteErrorResponseAsync(BadRequest,
                    $"No Record Found For The Given Submitter QID {submitterQId} & Dependent QID {dependentQId}");
            }

            if (response.ParentQId != submitterQId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.ParentQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            try
            {
                dbContext.UserDependent?.Remove(response);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation(UnenrolledUserDependentTemplate, dependentQId, submitterQId);

            return await req.WriteNoContentResponseAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region CreateUserProfile

    /// <summary>
    /// Create new UserProfile request
    /// </summary>
    /// <param name="req">New UserProfile Create Model</param>
    /// <returns></returns>
    [Function("CreateUserProfile")]
    [OpenApiOperation(operationId: "CreateUserProfile", tags: ["UserProfile"],
        Summary = "Create UserProfile", Description = "Create new UserProfile request")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateUserProfileRequest))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(UserProfile))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> CreateUserProfile(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "userprofiles")]
        HttpRequestData req)
    {
        try
        {
            // Reduced logging: combining URL and create info into one log
            logger.LogInformation($"{RequestedUrlTemplate} - {CreatingUserProfileTemplate}", req.LogRequestedUrl());

            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var request = DeserializeObject<CreateUpdateUserProfileRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }

            long qId = request.QId;

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            Models.UserProfile.UserProfile create = MapCreateUpdateUserProfileRequestToUserProfile(request);
            create.CreatedAt = GetCurrentTime();
            create.CreateSource = requestOriginSource;

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            try
            {
                await dbContext.UserProfile!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException e)
            {
                logger.LogError(e, ErrorMessageTemplate, e.Message);
                return await req.WriteErrorResponseAsync(InternalServerError,
                    "Internal Server Error. Please try again.");
            }

            logger.LogInformation(UserProfileCreatedTemplate, create.QId);

            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region UpdateUserProfile

    /// <summary>
    /// Update UserProfile for the given qid
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">UserProfile QID </param>
    /// <returns></returns>       
    [Function("UpdateUserProfileByQID")]
    [OpenApiOperation(operationId: "UpdateUserProfileByQID", tags: ["UserProfile"],
        Summary = "Update UserProfile By QID", Description = "Update UserProfile item for the given QID")]
    [OpenApiRequestBody("application/json", typeof(CreateUpdateUserProfileRequest))]
    [OpenApiParameter("qId", Description = "UserProfile QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> UpdateUserProfileByQId(
        [HttpTrigger(AuthorizationLevel.Function, "put", Route = "UserProfiles/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            // Reduced logging: combining URL and update info into one log
            logger.LogInformation($"{RequestedUrlTemplate} - {UpdatingUserProfileTemplate}", req.LogRequestedUrl());

            if (!req.IsAuthorized(qId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, qId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();

            var request = DeserializeObject<CreateUpdateUserProfileRequest>(requestBody);

            if (request == null || IsEmptyObject(request))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid request body. Please try again.");
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.UserProfile!
                .SingleOrDefaultAsync(item => item.QId == qId) ?? null;

            if (response is null)
            {
                logger.LogWarning(InvalidQIdTemplate, qId);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid QID {qId}");
            }

            try
            {
                response.PrefSMSLang = request.PrefSMSLang;
                response.PrefComMode = request.PrefComMode;
                response.SecondaryPhoneMobile = request.SecondaryPhoneMobile;
                response.Consent = request.Consent;
                response.UpdatedAt = GetCurrentTime();
                response.UpdateSource = requestOriginSource;
                dbContext.UserProfile!.Update(response);

                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation(UserProfileUpdatedTemplate, response.QId);
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }
    
    #endregion
    
    #region DeleteUserProfileByQID

    /// <summary>
    /// Delete UserProfile for the given qid
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId">UserProfile QID </param>
    /// <returns></returns>
    [Function("DeleteUserProfileByQID")]
    [OpenApiOperation(operationId: "DeleteUserProfileByQID", tags: ["UserProfile"],
        Summary = "Delete UserProfile By QID", Description = "Delete UserProfile item for the given qid")]
    [OpenApiParameter("qId", Description = "UserProfile QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    [OpenApiIgnore]
    public async Task<HttpResponseData> DeleteUserProfileByQId(
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "UserProfiles/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            // Reduced logging: combining URL and delete info into one log
            logger.LogInformation($"{RequestedUrlTemplate} - {DeletingUserProfileTemplate}", req.LogRequestedUrl());

            var submitter = req.GetClaims();

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var response = await dbContext.UserProfile!.SingleOrDefaultAsync(item => item.QId == qId);

            if (response == null)
            {
                logger.LogWarning(InvalidQIdTemplate, qId);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid QID {qId}");
            }

            if (submitter.QId != null && response.QId != submitter.QId)
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, response.QId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            try
            {
                dbContext.UserProfile!.Remove(response);
                await dbContext.SaveChangesAsync();
            }
            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation(UserProfileDeletedTemplate, response.QId);

            return await req.WriteOkResponseAsync($"Deleted UserProfile with the QId: {response.QId}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetMOIDependents

    /// <summary>
    /// Get dependents from MOI for the given QID
    /// </summary>
    /// <param name="req"></param>
    /// <param name="qId"></param>
    /// <returns></returns>
    [Function("GetMOIDependents")]
    [OpenApiOperation(operationId: "GetMOIDependents", tags: ["UserProfile"],
        Summary = "Get dependents from MOI", Description = "get the dependent details from MOI")]
    [OpenApiParameter("qId", Description = "User QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("isLinked", Description = "To Get Only linked dependent Details",
        In = ParameterLocation.Query, Required = false, Type = typeof(bool))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetMoiDependentsResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetMoiDependents(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "userprofiles/moi-dependents/{qId}")]
        HttpRequestData req, long qId)
    {
        try
        {
            logger.LogInformation(GetMoiDependentsTemplate, qId);
            var submitter = req.GetClaims();
            logger.LogInformation(GettingDependentDetailsTemplate, submitter.QId);

            string strIsLinked = req.GetCleanedQueryString("isLinked");
            var natXml = GetEnvironmentVariable("NationalitiesXML");
            StringBuilder nationalitiesXml = new(natXml);
            var env = req.TryGetHeader(PhccEnvironment);
            var response = await GetMoiDependentsFromEdw(logger, nationalitiesXml, qId, env);

            if (response.Count == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            switch (strIsLinked)
            {
                case null:
                {
                    var allDependentsList = response.ToList();
                    logger.LogInformation(ReturningAllDependentsTemplate, allDependentsList.Count, qId);
                    return await req.WriteOkResponseAsync(allDependentsList);
                }
                case "true":
                {
                    var linkedDependentsList = response.Where(i => i.Linked == true).ToList();
                    if (linkedDependentsList.Count <= 0)
                    {
                        return await req.WriteNoContentResponseAsync();
                    }

                    logger.LogInformation(ReturningLinkedDependentsTemplate, linkedDependentsList.Count, qId);
                    return await req.WriteOkResponseAsync(linkedDependentsList);
                }
                default:
                {
                    var unLinkedDependentsList = response.Where(i => i.Linked == false).ToList();
                    if (unLinkedDependentsList.Count <= 0)
                    {
                        return await req.WriteNoContentResponseAsync();
                    }

                    logger.LogInformation(ReturningUnlinkedDependentsTemplate, unLinkedDependentsList.Count, qId);
                    return await req.WriteOkResponseAsync(unLinkedDependentsList);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region LinkUserDependent

    /// <summary>
    /// link User Dependent By QID
    /// </summary>
    /// <param name="req"></param>
    /// <param name="submitterQId"></param>
    /// <param name="dependentQId"></param>
    /// <returns></returns>
    [Function("LinkUserDependentByQID")]
    [OpenApiOperation(operationId: "LinkUserDependentByQID", tags: ["UserProfile"],
        Summary = "link User Dependent By QID",
        Description =
            "link the User and Dependent relationship from EServices system for the given submitter & dependent qid")]
    [OpenApiParameter("submitterQId", Description = "User QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiParameter("dependentQId", Description = "User Dependent QID", In = ParameterLocation.Path,
        Required = true, Type = typeof(long))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    [OpenApiResponseWithBody(Created, "application/json", typeof(object))]
    [OpenApiResponseWithBody(InternalServerError, "application/json", typeof(object))]
    public async Task<HttpResponseData> LinkUserDependentByQId(
        [HttpTrigger(AuthorizationLevel.Function, "post",
            Route = "userprofiles/link-dependents/{submitterQId}/{dependentQId}")]
        HttpRequestData req, long submitterQId, long dependentQId)
    {
        try
        {
            // Reduced logging: combining URL and linking info into one log
            logger.LogInformation($"{RequestedUrlTemplate} - {LinkingUserDependentTemplate}", 
                req.LogRequestedUrl(), dependentQId, submitterQId);

            if (!req.IsAuthorized(submitterQId))
            {
                logger.LogWarning(UnauthorizedQIdAccessTemplate, submitterQId);
                return await req.WriteUnauthorizedResponseAsync();
            }

            var requestOriginSource = req.TryGetHeader(RequestOrigin);

            if (IsNullOrWhiteSpace(requestOriginSource))
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Required 'X-RequestOrigin' header value");
            }

            var natXml = GetEnvironmentVariable("NationalitiesXML");

            StringBuilder nationalitiesXml = new(natXml);

            var env = req.TryGetHeader(PhccEnvironment);

            var response = await GetMoiDependentsFromEdw(logger, nationalitiesXml, submitterQId, env);

            if (response.Count == 0)
            {
                return await req.WriteNoContentResponseAsync();
            }

            var unLinkedDependentsList = response.Where(x => x.QId == dependentQId && x.Linked == false).ToList();
            var linkedDependentsList = response.Where(x => x.QId == dependentQId && x.Linked == true).ToList();

            UserDependent create = new();

            if (unLinkedDependentsList.Count > 0)
            {
                create.QId = dependentQId;
                create.ParentQId = submitterQId;
                create.CreatedAt = GetCurrentTime();
                create.CreateSource = requestOriginSource;
            }
            else if (linkedDependentsList.Count > 0)
            {
                logger.LogWarning(DependentQIdAlreadyLinkedTemplate, dependentQId);
                return await req.WriteErrorResponseAsync(BadRequest, $"Dependent QID {dependentQId} has been already linked");
            }
            else
            {
                logger.LogWarning(InvalidDependentQIdTemplate, dependentQId);
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Dependent QID {dependentQId}");
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            try
            {
                await dbContext.UserDependent!.AddAsync(create);
                await dbContext.SaveChangesAsync();
            }

            catch (DbUpdateException ex)
            {
                logger.LogError(ex, ErrorMessageTemplate, ex.InnerException?.Message);
                return ex.DefaultExceptionBehaviour(req, logger);
            }

            logger.LogInformation(UserDependentLinkedTemplate, create.QId);

            return await req.WriteOkResponseAsync(create, Created);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ErrorMessageTemplate, ex.Message);
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region Private Methods (BuildQIDsXml, GetUserProfileDetailsFromEdw, ConvertDataRowToUserProfile, GetMoiDependentsFromEdw)

    private static StringBuilder BuildQIDsXml(List<long> qIdList)
    {
        StringBuilder builder = new();

        XmlWriterSettings xmlWriterSettings = new()
        {
            OmitXmlDeclaration = true
        };

        using StringWriter stringWriter = new(builder);
        using XmlWriter writer = XmlWriter.Create(stringWriter, xmlWriterSettings);
        writer.WriteStartDocument();
        writer.WriteStartElement("QIDList");
        foreach (var item in qIdList)
        {
            writer.WriteElementString("QID", item.ToString());
        }

        writer.WriteEndElement();
        writer.WriteEndDocument();

        return builder;
    }

    private async Task<GetUserProfileResponse> GetUserProfileDetailsFromEdw(ILogger log, StringBuilder builder,
        long qId,
        string env)
    {
        var inputXml = new SqlParameter("@InputXML", SqlDbType.Xml)
        {
            Value = builder.ToString()
        };
        var envParam = new SqlParameter("@ENV", SqlDbType.VarChar)
        {
            Value = env
        };

        log.LogInformation(InputXmlTemplate, inputXml.Value);

        await using var edwDbContext = await edwDbContextFactory.CreateDbContextAsync();

        var response = await edwDbContext.UserProfileResponseFromEDW?
            .FromSqlInterpolated($"EXEC dbo.SP_GET_USER_AND_DEPENDENTS_DETAILS_V2 {inputXml}, {envParam}")
            .ToListAsync()!;

        if (response.Count == 0)
        {
            log.LogInformation("No data found for the given QID/QIDs");
            return new GetUserProfileResponse();
        }

        log.LogInformation("Stored procedure executed successfully");
        var userProfileResponse = new GetUserProfileResponse();
        var parentProfile = response.FirstOrDefault(c => c.QId == qId);

        if (parentProfile is not null || IsEmptyObject(parentProfile))
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var userProfileFromEServices = await dbContext.UserProfile?.FirstOrDefaultAsync(item => item.QId == qId);
            var userProfile = ConvertDataRowToUserProfile(parentProfile);
            userProfileResponse = MapUserProfileToGetUserProfileResponse(userProfile);

            if (userProfileFromEServices is not null)
            {
                userProfileResponse.PrefComMode = userProfileFromEServices.PrefComMode;
                userProfileResponse.PrefSMSLang = userProfileFromEServices.PrefSMSLang;
                userProfileResponse.SecondaryPhoneMobile = userProfileFromEServices.SecondaryPhoneMobile;
                userProfileResponse.Consent = userProfileFromEServices.Consent;
            }

            userProfileResponse.Dependents = [];
        }

        var dependents = response.Where(c => c.QId != qId).ToList();
        if (dependents.Count == 0)
        {
            return userProfileResponse;
        }

        foreach (UserProfile? dependentsItem in dependents.Select(ConvertDataRowToUserProfile))
        {
            userProfileResponse.Dependents?.Add(dependentsItem);
        }

        return userProfileResponse;
    }

    private static UserProfile ConvertDataRowToUserProfile(UserProfileFromEDW? userProfileFromEdw)
    {
        ArgumentNullException.ThrowIfNull(userProfileFromEdw);

        var item = new UserProfile
        {
            QId = userProfileFromEdw.QId
        };
        if (!userProfileFromEdw.QIdExpiryDt.IsNullOrDefault())
            item.QIdExpiryDt = userProfileFromEdw.QIdExpiryDt.ToUtcString();

        item.FNameEn = userProfileFromEdw.FNameEn;
        item.MNameEn = userProfileFromEdw.MNameEn;
        item.LNameEn = userProfileFromEdw.LNameEn;
        item.FNameAr = userProfileFromEdw.FNameAr;
        item.MNameAr = userProfileFromEdw.MNameAr;
        item.LNameAr = userProfileFromEdw.LNameAr;

        if (!userProfileFromEdw.Dob.IsNullOrDefault())
            item.Dob = userProfileFromEdw.Dob.ToUtcString();

        item.NationalityCode = userProfileFromEdw.NationalityCode;
        item.NationalityEn = userProfileFromEdw.NationalityEn;
        item.NationalityAr = userProfileFromEdw.NationalityAr;
        item.GenderCode = userProfileFromEdw.GenderCode;
        item.GenderEn = userProfileFromEdw.GenderEn;
        item.GenderAr = userProfileFromEdw.GenderAr;
        item.VisaCode = userProfileFromEdw.VisaCode;
        item.VisaDescriptionEn = userProfileFromEdw.VisaDescriptionEn;
        item.VisaDescriptionAr = userProfileFromEdw.VisaDescriptionAr;
        item.OccupationCode = userProfileFromEdw.OccupationCode;
        item.OccupationDescriptionEn = userProfileFromEdw.OccupationDescriptionEn;
        item.OccupationDescriptionAr = userProfileFromEdw.OccupationDescriptionAr;
        item.PhoneMobile = userProfileFromEdw.PhoneMobile;
        item.AssignedHealthCenter = userProfileFromEdw.AssignedHealthCenter;
        item.AssignedHealthCenterEn = userProfileFromEdw.AssignedHealthCenterEn;
        item.AssignedHealthCenterAr = userProfileFromEdw.AssignedHealthCenterAr;
        item.HcNumber = userProfileFromEdw.HcNumber;

        if (!userProfileFromEdw.HcExpiryDate.IsNullOrDefault())
            item.HcExpiryDate = userProfileFromEdw.HcExpiryDate.ToUtcString();

        item.GisAddressStreet = userProfileFromEdw.GisAddressStreet;
        item.GisAddressBuilding = userProfileFromEdw.GisAddressBuilding;
        item.GisAddressZone = userProfileFromEdw.GisAddressZone;
        item.GisAddressUnit = userProfileFromEdw.GisAddressUnit;
        item.GisAddressUpdatedAt = userProfileFromEdw.GisAddressUpdatedAt.ToUtcString();
        item.PhysicianId = userProfileFromEdw.PhysicianId;
        item.PhysicianFullNameEn = userProfileFromEdw.PhysicianFullNameEn;
        item.PhysicianFullNameAr = userProfileFromEdw.PhysicianFullNameAr;
        item.IsStaff = userProfileFromEdw.IsStaff is 1;
        item.IsCitizen = userProfileFromEdw.NationalityCode == "634";

        var oGccList = new[] { "414", "512", "682", "048", "784" };
        item.IsOGCC = oGccList.Contains(userProfileFromEdw.NationalityCode ?? Empty);
        return item;
    }

    private async Task<List<GetMoiDependentsResponse>> GetMoiDependentsFromEdw(ILogger log,
        StringBuilder nationalitiesXml,
        long qId, string env)
    {
        var response = new List<GetMoiDependentsResponse>();

        var inputXml = new SqlParameter("@InputXML", SqlDbType.Xml)
        {
            Value = nationalitiesXml.ToString()
        };
        var envParam = new SqlParameter("@ENV", SqlDbType.VarChar)
        {
            Value = env
        };
        var submitterQId = new SqlParameter("@QID", SqlDbType.VarChar)
        {
            Value = qId.ToString()
        };

        await using var edwDbContext = await edwDbContextFactory.CreateDbContextAsync();

        var moiDependents = await edwDbContext.GetMoiDependentsResponse
            .FromSqlInterpolated($"EXEC dbo.SP_GET_ELIGIBLE_DEPENDENTS_LIST {submitterQId}, {inputXml}, {envParam}")
            .ToListAsync();

        if (moiDependents.Count == 0)
        {
            return [];
        }

        log.LogInformation("Row count {MoiDependentsCount}", moiDependents.Count);

        foreach (var moiDependent in moiDependents)
        {
            var moiDependentItemResponse = new GetMoiDependentsResponse
            {
                QId = ToInt64(moiDependent.QId),
                FNameEn = moiDependent.FNameEn,
                MNameEn = moiDependent.MNameEn,
                LNameEn = moiDependent.LNameEn,
                FNameAr = moiDependent.FNameAr,
                MNameAr = moiDependent.MNameAr,
                LNameAr = moiDependent.LNameAr,
                Linked = false
            };

            response.Add(moiDependentItemResponse);
        }

        await using var dbContext = await dbContextFactory.CreateDbContextAsync();

        var linkedQIDs = await dbContext.UserDependent!
            .Where(item => response.Select(x => x.QId).Contains(item.QId) && item.ParentQId == qId)
            .Select(y => y.QId).Distinct().ToListAsync();

        foreach (var item in response.Where(item => linkedQIDs.Contains(item.QId)))
        {
            item.Linked = true;
        }

        return response;
    }

    #endregion
}