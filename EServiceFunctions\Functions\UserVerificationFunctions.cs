using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.UserVerification;

using static EServiceFunctions.Helpers.AgeHelper;

namespace EServiceFunctions.Functions;

public class UserVerificationFunctions(
    IDbContextFactory<EDWDbContext> context,
    ILogger<UserVerificationFunctions>? logger)
{
    private readonly IDbContextFactory<EDWDbContext>? _dbContextFactory = context;

    #region UserVerificationFunctions

    [Function("GetUserVerificationById")]
    [OpenApiOperation(operationId: "GetUserVerificationByQID", tags: ["UseVerification"],
        Summary = "Get User's age verification and vital status by QId",
        Description =
            "Get user's age verification to check whether he is adult or not and check the vital status of the user by QID")]
    [OpenApiParameter("qId", Description = "User QID", In = ParameterLocation.Path, Required = true,
        Type = typeof(long))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(UserDetailsResponse))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetUserVerificationById(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "userVerification/isValid/{qId}")]
        HttpRequestData req, long qId)
    {
        if (!req.IsAuthorized(qId))
        {
            logger.LogWarning("Unauthorized ({QId}) Access!!!", qId);
            return await req.WriteUnauthorizedResponseAsync();
        }

        await using EDWDbContext dbContext = await _dbContextFactory!.CreateDbContextAsync();
        UserDetailsResponse user = new();
        try
        {
            var result = await dbContext.PersonMoiDetail!.AsQueryable()
                .AsNoTracking().FirstOrDefaultAsync(c => c.QId == qId.ToString());

            if (result == null)
            {
                return await req.WriteNoContentResponseAsync();
            }

            result.Dob?.ThrowIfNull();
            user.IsAdult = result.Dob.HasValue && IsAdult(result.Dob.Value);
            user.Age = CalculateAge(result.Dob!.Value);
            return await req.WriteOkResponseAsync(user);
        }
        catch (Exception exception)
        {
            return exception.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}