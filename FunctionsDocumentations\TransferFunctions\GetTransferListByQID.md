# Get Transfer List By QID

## Overview
Retrieves a list of transfer requests for a given submitter's QID with optional filtering and pagination.

## Endpoint
```http
GET /transfers/submitter-qid/{qid}
```

## Authorization
- Function-level authorization
- QID-based access control
- Requires valid submitter QID

## Parameters

### Path Parameters
- `qId` (long, required): Submitter's QID

### Query Parameters
- `status` (string, optional): Filter by status category
  - InProcess: Submitted, Rework, Reworked, ConditionallyApproved, ResubmitOriginalsRequested
  - Archived: Approved, Cancelled, CancelledByEServ
- `skip` (int, optional): Number of records to skip for pagination
- `take` (int, optional): Number of records to take for pagination

## Response

### Success Response (200 OK)
Returns a list of transfer requests with the following structure:
```json
[
  {
    "qId": "long",
    "reqNumber": "string",
    "fNameEn": "string",
    "mNameEn": "string",
    "lNameEn": "string",
    "fNameAr": "string",
    "mNameAr": "string",
    "lNameAr": "string",
    "status": "string",
    "statusDescriptionEn": "string",
    "statusDescriptionAr": "string",
    "submittedAt": "string (UTC)"
  }
]
```

### No Content Response (204)
Returned when no records are found for the given criteria.

### Error Responses
- 400 Bad Request: Invalid parameters
- 401 Unauthorized: Invalid QID or unauthorized access

## Implementation Details
- Uses Entity Framework Core with AsNoTracking for optimal read performance
- Implements pagination for large result sets
- Supports multilingual names (English/Arabic)
- Includes status descriptions in both English and Arabic
- Orders results by status
- Implements efficient query optimization using joins

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control based on submitter QID

## Performance Optimization
- Uses AsNoTracking for read-only operations
- Implements pagination to handle large datasets
- Optimizes database queries using proper joins
- Efficient status-based filtering
