﻿namespace EServiceFunctions.RequestResponseModels.Appointment;

[ExcludeFromCodeCoverage]
public class GetUpcomingConfirmedAppointmentListResponse
{
    public long AppointmentId { get; set; }
    public long QId { get; set; }
    public DateTime? QIdExpiryDt { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? NationalityCode { get; set; }
    public string? NationalityEn { get; set; }
    public string? NationalityAr { get; set; }
    public DateTime? Dob { get; set; }
    public string? HCNumber { get; set; }
    public DateTime? HCExpiryDate { get; set; }
    public string? GenderCode { get; set; }
    public string? GenderEn { get; set; }
    public string? GenderAr { get; set; }
    public string? PhoneMobile { get; set; }
    public string? AggignedHCntCode { get; set; }
    public string? AggignedHCntNameEn { get; set; }
    public string? AggignedHCntNameAr { get; set; }
    public string? AppointmentHCntCode { get; set; }
    public string? AppointmentHCntNameEn { get; set; }
    public string? AppointmentHCntNameAr { get; set; }
    public string? ClinicCode { get; set; }
    public string? ClinicNameEn { get; set; }
    public string? ClinicNameAr { get; set; }
    public string? GisAddressStreet { get; set; }
    public string? GisAddressBuilding { get; set; }
    public string? GisAddressZone { get; set; }
    public string? GisAddressUnit { get; set; }
    public string? PhysicianId { get; set; }
    public string? PhysicianFullNameEn { get; set; }
    public string? PhysicianFullNameAr { get; set; }
    public DateTime? BookedDateTime { get; set; }
    public DateTime? AppointmentDateTime { get; set; }
    public string? AppointmentType { get; set; }
    public string? AppointmentStatus { get; set; }
    public string? AppointmentLocation { get; set; }
    public string? AppointmentFacility { get; set; }
}