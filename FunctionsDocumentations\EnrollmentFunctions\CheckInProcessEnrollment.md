# CheckInProcessEnrollment

## Overview
Checks if there are any in-process enrollments for a given applicant and submitter QId combination.

## Endpoint
- **Route**: `enrollments/inprocess-validation/{applicantQId}/{submitterQId}`
- **Method**: GET
- **Authorization**: Function level with submitter QId validation

## Parameters
- **applicantQId** (path, required): Applicant's QId
- **submitterQId** (path, required): Submitter's QId

## Response
- **200 OK**: Returns in-process status
  ```json
  {
    "IsInprocessExist": "boolean"
  }
  ```
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Invalid submitter QId access

## Business Logic
1. Validates submitter QId authorization
2. Queries Enrollment table with Status join
3. Checks for any enrollments where:
   - Applicant QId matches
   - Submitter QId matches
   - Status category is "InProcess"
4. Returns boolean indicating existence

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient Any() operation instead of loading entities
- Optimized join with Status table
