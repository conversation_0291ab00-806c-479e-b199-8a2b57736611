# GetMHDSStatsByQID

## Overview
Retrieves statistics for Medicine Home Delivery Service (MHDS) requests associated with a submitter's Qatar ID.

## Endpoint
- **Route**: `mhds/submitter-qid/{qId}/stats`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **qId** (path, required): Submitter's Qatar ID number
- **status** (query, optional): Filter by status category
  - InProcess: Submitted, PaymentReceived, ReturnedForPayment
  - Archived: Approved, Cancelled, Returned

## Response
- **200 OK**: Returns request count
  ```json
  {
    "Count": integer
  }
  ```
- **400 Bad Request**: Invalid parameters
- **401 Unauthorized**: Invalid QID authorization

## Business Logic
1. Validates user authorization against QID
2. Queries MHDSRequestDetails table
3. Joins with Status table
4. Filters by:
   - Submitter QID match
   - Optional status category or specific status
5. Returns count of matching requests

## Query Optimization
- Uses joins for efficient status filtering
- AsQueryable for better query performance
- Count operation instead of retrieving full records

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Request origin validation
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Unauthorized access logging
