# Update Registration By Request Number

## Overview
Updates an existing registration request while maintaining its history and status management.

## Endpoint
- **Route**: `registrations/{reqNumber}`
- **Method**: PUT
- **Authorization**: Function level with submitter validation

## Parameters
- **reqNumber** (path, required): Registration request number

## Headers
- **X-RequestOrigin** (required): Source of the update request

## Request Body
```json
{
  "SubmitterQId": "long",
  "SubmitterEmail": "string",
  "SubmitterMobile": "string",
  "IsDraft": "boolean",
  "FNameEn": "string",
  "MNameEn": "string",
  "LNameEn": "string",
  "FNameAr": "string",
  "MNameAr": "string",
  "LNameAr": "string"
}
```

## Response
- **200 OK**: Returns updated registration
  ```json
  {
    "ReqNumber": "string",
    "Status": "string",
    "UpdatedAt": "string",
    "UpdateSource": "string",
    "RegistrationDetails": "object"
  }
  ```
- **400 Bad Request**: Invalid request body or request number
- **401 Unauthorized**: Invalid submitter authorization
- **500 Internal Server Error**: Database operation error

## Business Logic
1. Validates request origin header
2. Validates submitter authorization
3. Retrieves existing registration
4. Updates modifiable fields
5. Maintains history and timestamps

## Status Management
- **Draft Mode** (IsDraft = true):
  * Status set to "Saved"
  * Further modifications allowed
- **Final Submission** (IsDraft = false):
  * Status set to "Ready"
  * Proceeds to processing

## Field Preservation
- Request Number (ReqNumber)
- Internal Status (StatusInternal)
- Serial Number (SN)
- Creation Timestamp (CreatedAt)
- Creation Source (CreateSource)

## Security Considerations
- Function-level authorization
- Submitter validation
- Request origin validation
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Database error handling

## Database Operations
- Single record update
- Status management
- Timestamp tracking
- Source recording

## Multilingual Support
- English name fields (FNameEn, MNameEn, LNameEn)
- Arabic name fields (FNameAr, MNameAr, LNameAr)
- Supports bilingual data updates
