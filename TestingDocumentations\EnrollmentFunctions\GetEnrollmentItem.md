# Get Enrollment Item Tests

## Test Cases

### 1. TestGetEnrollmentItemByReqNumber

Tests successful retrieval of enrollment by request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentItemByReqNumber()
{
    // Arrange
    var request = GetRestRequest("enrollments/{reqNumber}");
    request.AddOrUpdateHeader(JwtClaimsQId, "22222222267");
    request.AddUrlSegment("reqNumber", "52696VUZW");
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222267);

    // Act
    var response = await _client.GetAsync<GetEnrollmentItemResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal("TEST1", response.FNameEn);
    Equal(33322222291, response.QId);
    Equal(CancelledByEServ, response.Status);
}
```

### 2. TestGetEnrollmentItemByReqNumberNotFound

Tests handling of non-existent request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentItemByReqNumberNotFound()
{
    // Arrange
    var request = GetRestRequest("enrollments/{reqNumber}");
    request.AddUrlSegment("reqNumber", "52696VUZ");

    // Act
    var response = await _client.GetAsync<GetEnrollmentItemResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    Null(response);
}
```

## Request Details

### Endpoint
```
GET enrollments/{reqNumber}
```

### Headers
- `JwtClaimsQId`: "22222222267"

### URL Parameters
- `reqNumber`: Enrollment request number

## Response Model

### GetEnrollmentItemResponse
```csharp
public class GetEnrollmentItemResponse
{
    public string FNameEn { get; set; }
    public long QId { get; set; }
    public string Status { get; set; }
}
```

## Test Data

### Success Case
- Request Number: "52696VUZW"
- Expected Response:
  - FNameEn: "TEST1"
  - QId: 33322222291
  - Status: CancelledByEServ

### Not Found Case
- Request Number: "52696VUZ"
- Expected Response: null

## Validation Rules

### Response Validation
1. Success case:
   - Response not null
   - Matching first name
   - Matching QID
   - Correct status

2. Not found case:
   - Response is null

## Error Cases

1. **Invalid Request Number**
   - Malformed request number
   - Non-existent request number

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims

3. **Data Access Errors**
   - Database connection issues
   - Data corruption

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Request Number Format**
   - Case sensitive
   - Specific length requirement

3. **Response Handling**
   - Null response for not found
   - Full details for found items
