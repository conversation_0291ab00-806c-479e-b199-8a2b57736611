# Get Enrollment List Tests

## Test Case: TestGetEnrollmentListByQId

Tests the retrieval of enrollments for a specific QID.

### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentListByQId()
{
    // Arrange
    var request = GetRestRequest("enrollments/submitter-qid/{qId}");
    request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
    request.AddUrlSegment("qId", 22222222270);
    request.AddHeader("IsApp", true);

    // Act
    var response = await _client.GetAsync<List<GetEnrollmentListResponse>>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    NotNull(response[0]);
    Equal("TEST1", response[0].FNameEn);
    Equal(33322222245, response[0].QId);
    Equal(Approved, response[0].Status);
}
```

## Request Details

### Endpoint
```
GET enrollments/submitter-qid/{qId}
```

### Headers
- `JwtClaimsQId`: "22222222270"
- `IsApp`: true

### URL Parameters
- `qId`: 22222222270

## Response Model

### GetEnrollmentListResponse
```csharp
public class GetEnrollmentListResponse
{
    public string FNameEn { get; set; }
    public long QId { get; set; }
    public string Status { get; set; }
}
```

## Validation Rules

### Response Validation
1. Response object not null
2. First enrollment item not null
3. First enrollment has:
   - FNameEn = "TEST1"
   - QId = 33322222245
   - Status = Approved

## Test Data

### Test QID
- Submitter QID: 22222222270
- Expected Applicant QID: 33322222245

### Expected Status
- Approved status for test enrollment

## Error Cases

1. **Invalid QID Format**
   - Non-numeric QID
   - QID length not 11 digits

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims

3. **Not Found Cases**
   - No enrollments for QID
   - Invalid QID

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Response Format**
   - Returns list of enrollments
   - Each enrollment has basic details

3. **Performance**
   - Multiple enrollments possible
   - Pagination may be needed
