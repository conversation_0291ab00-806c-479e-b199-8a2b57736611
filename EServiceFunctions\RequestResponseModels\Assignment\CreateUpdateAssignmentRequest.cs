﻿using System.ComponentModel.DataAnnotations;

namespace EServiceFunctions.RequestResponseModels.Assignment;

public class CreateUpdateAssignmentRequest
{
    public long QId { get; set; }
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? HCNumber { get; set; }
    public string? Nationality { get; set; }
    public DateTime? Dob { get; set; }
    public string? CurrentAssignedHC { get; set; }
    public string? CurrentPhysician { get; set; }
    public string? SelectedPhysician { get; set; }
    public Guid? AttachId1 { get; set; }
    public Guid? AttachId2 { get; set; }
    public Guid? AttachId3 { get; set; }
    public Guid? AttachId4 { get; set; }
    public Guid? AttachId5 { get; set; }
    public Guid? AttachId6 { get; set; }
    [Required]
    public string? ChangeReason { get; set; }
    public string? SubmittedBy { get; set; }
    public DateTime? SubmittedAt { get; set; }
    public long? SubmitterQId { get; set; }
    public string? SubmitterEmail { get; set; }
    public string? SubmitterMobile { get; set; }

}