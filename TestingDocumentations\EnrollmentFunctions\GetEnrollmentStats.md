# Get Enrollment Statistics Tests

## Test Case: TestGetEnrollmentStatsByQId

Tests the retrieval of enrollment statistics for a specific QID.

### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentStatsByQId()
{
    // Arrange
    var request = GetRestRequest("enrollments/submitter-qid/{qId}/stats");
    request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
    request.AddUrlSegment("qId", 22222222270);
    request.AddHeader("IsApp", true);

    // Act
    var response = await _client.GetAsync<GetEnrollmentStatsResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.Count > 0);
}
```

## Request Details

### Endpoint
```
GET enrollments/submitter-qid/{qId}/stats
```

### Headers
- `JwtClaimsQId`: "22222222270"
- `IsApp`: true

### URL Parameters
- `qId`: 22222222270

## Response Model

### GetEnrollmentStatsResponse
```csharp
public class GetEnrollmentStatsResponse
{
    public int Count { get; set; }
}
```

## Validation Rules

### Response Validation
1. Response object not null
2. Count greater than 0

## Test Data

### Test QID
- Submitter QID: 22222222270

## Error Cases

1. **Invalid QID Format**
   - Non-numeric QID
   - QID length not 11 digits

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims

3. **Not Found Cases**
   - No enrollments for QID
   - Invalid QID

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Response Format**
   - Returns count of enrollments
   - Simple statistics object

3. **Performance**
   - Quick count operation
   - No detailed data returned
