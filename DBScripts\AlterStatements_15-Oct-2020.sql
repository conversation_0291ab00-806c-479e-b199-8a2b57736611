﻿--WI#875

IF EXISTS (SELECT *
           FROM sys.tables
           WHERE name = 'AppointmentRequestType'
             AND type = 'U')
    DROP TABLE [AppointmentRequestType]; -- MD
GO
CREATE TABLE [AppointmentRequestType]
(
    [Code]          nvarchar(255) PRIMARY KEY,
    [DescriptionEn] nvarchar(255),
    [DescriptionAr] nvarchar(255)
)
GO
INSERT [dbo].[AppointmentRequestType] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (N'Cancel', N'Cancel', N'ملغي')
GO
INSERT [dbo].[AppointmentRequestType] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (N'New', N'New', N'جديد')
GO
INSERT [dbo].[AppointmentRequestType] ([Code], [DescriptionEn], [DescriptionAr])
VALUES (N'Reschedule', N'Reschedule', N'اعادة جدولة الموعد') -- corrected the values in prod DB
GO

ALTER TABLE [Appointment]
    ADD FOREIGN KEY (RequestType) REFERENCES [AppointmentRequestType] (Code);
GO