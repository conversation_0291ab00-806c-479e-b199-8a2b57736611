﻿namespace EServiceFunctions.Models.ReleaseItem;

public class ReleaseItemContext(DbContextOptions<ReleaseItemContext> options) : DbContext(options)
{
    public virtual DbSet<ReleaseItem>? ReleaseItem { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<ReleaseItem>(entity =>
        {
            entity.HasIndex(e => e.ItemCode)
                .HasDatabaseName("UQ__ReleaseI__3ECC0FEA56236561")
                .IsUnique();

            entity.Property(e => e.CategoryCode).HasMaxLength(255);

            entity.Property(e => e.CategoryNameAr).HasMaxLength(255);

            entity.Property(e => e.CategoryNameEn).HasMaxLength(255);

            entity.Property(e => e.CreatedAt).HasColumnType("datetime");

            entity.Property(e => e.ItemCode).HasMaxLength(255);

            entity.Property(e => e.ItemNameAr).HasMaxLength(255);

            entity.Property(e => e.ItemNameEn).HasMaxLength(255);
        });
    }
}