# CreateEnrollment

## Overview
Creates a new enrollment request with optional EDW data integration.

## Endpoint
- **Route**: `enrollments`
- **Method**: POST
- **Authorization**: Function level with submitter QId validation

## Headers
- **X-RequestOrigin** (required): Source of the request
- **X-PhccEnvironment** (optional): Environment for EDW integration

## Request Body
```json
{
  "SubmitterQId": "long",
  "QId": "long",
  "FNameEn": "string",
  "MNameEn": "string",
  "LNameEn": "string",
  "FNameAr": "string",
  "MNameAr": "string",
  "LNameAr": "string",
  "Nationality": "string",
  "HCNumber": "string"
  // Additional enrollment fields...
}
```

## Response
- **201 Created**: Returns created enrollment
- **400 Bad Request**: Invalid request body or missing headers
- **401 Unauthorized**: Invalid submitter QId access
- **500 Internal Server Error**: Database operation failure

## Business Logic
1. Validates request headers and body
2. Validates submitter QId authorization
3. Generates unique request number
4. Sets submission timestamps
5. If nationality is empty:
   - Retrieves user details from EDW
   - Updates enrollment with EDW data
6. Creates enrollment record
7. Returns created enrollment

## Data Integration
- EDW Integration via SP_GET_USER_AND_DEPENDENTS_DETAILS_V2
- Retrieves:
  - Names (English/Arabic)
  - Nationality
  - HC Number and expiry
  
## Error Handling
- Validates request body completeness
- Handles EDW integration failures gracefully
- Proper DB exception handling
