﻿namespace EServiceFunctions.RequestResponseModels.Appointment;

public class UpcomingConfirmedAppointmentListResponse
{
    public long AppointmentId { get; set; }  
    public string? FNameEn { get; set; }
    public string? MNameEn { get; set; }
    public string? LNameEn { get; set; }
    public string? FNameAr { get; set; }
    public string? MNameAr { get; set; }
    public string? LNameAr { get; set; }
    public string? ClinicCode { get; set; }
    public string? ClinicNameEn { get; set; }
    public string? ClinicNameAr { get; set; }
    public string? PhysicianId { get; set; }
    public string? PhysicianFullNameEn { get; set; }
    public string? PhysicianFullNameAr { get; set; }
    public DateTime? AppointmentDateTime { get; set; }
    public string? AppointmentType { get; set; }
    public string? AppointmentHCntCode { get; set; }
    public string? AppointmentHCntNameEn { get; set; }
    public string? AppointmentHCntNameAr { get; set; }
}