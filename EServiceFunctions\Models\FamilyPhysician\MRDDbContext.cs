﻿using static Microsoft.EntityFrameworkCore.QueryTrackingBehavior;

namespace EServiceFunctions.Models.FamilyPhysician;

public sealed class MRDDbContext : DbContext
{
    public MRDDbContext(DbContextOptions<MRDDbContext> options)
        : base(options)
    {
         ChangeTracker.QueryTrackingBehavior = NoTracking;
    }

    public DbSet<MAP_MOI_OccupationType> MAP_MOI_OccupationTypes { get; set; } = null!;
    public DbSet<MAP_MOI_VisaType> MAP_MOI_VisaTypes { get; set; } = null!;
    public DbSet<MRD_Nationality> MRD_Nationalities { get; set; } = null!;
    public DbSet<MRD_Physician_Specialty> MRD_Physician_Specialties { get; set; } = null!;
    public DbSet<MRD_Family_Physician> MRD_Family_Physicians { get; set; } = null!;
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // modelBuilder.Entity<MRD_Family_Physician>().HasNoKey();
        // modelBuilder.Entity<MRD_Nationality>().HasNoKey();
        
        modelBuilder.Entity<MAP_MOI_OccupationType>(entity =>
        {
            entity.HasKey(e => e.OCCUPATION_CODE)
                .HasName("idoctPkey");
        });

        modelBuilder.Entity<MAP_MOI_VisaType>(entity =>
        {
            entity.HasKey(e => e.VISA_TYPE)
                .HasName("idxvtPkey");
        });

        modelBuilder.Entity<MRD_Family_Physician>(entity =>
        {
            entity.Property(e => e.FAC_CODE).IsFixedLength();
        });

        modelBuilder.Entity<MRD_Nationality>(entity =>
        {
            entity.Property(e => e.ALPHA2_CODE).IsFixedLength();
            entity.Property(e => e.ALPHA3_CODE).IsFixedLength();
            entity.Property(e => e.COUNTRY_CODE).IsFixedLength();
            entity.Property(e => e.IS_GCC).IsFixedLength();
            entity.Property(e => e.IS_MENA).IsFixedLength();
        });

        modelBuilder.Entity<MRD_Physician_Specialty>(entity =>
        {
            entity.HasKey(e => e.SPECIALTY_CODE)
                .HasName("PK__MRD_Phys__09281CD37A263DAD");

            entity.Property(e => e.SPECIALTY_CODE).IsFixedLength();
        });
    }
}