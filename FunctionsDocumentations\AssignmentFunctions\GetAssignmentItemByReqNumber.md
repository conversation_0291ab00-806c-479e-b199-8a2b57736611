# GetAssignmentItemByReqNumber

## Overview
Retrieves a specific assignment by its request number.

## Endpoint
- **Route**: `assignments/{reqNumber}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `reqNumber` (path, required): Assignment request number

## Responses
- **200 OK**: Returns `GetAssignmentItemResponse`
  ```json
  {
    "ReqNumber": "string",
    "QId": "string",
    "Status": "string",
    "StatusDescriptionEn": "string",
    "StatusDescriptionAr": "string",
    "SubmitterQId": "string",
    "SubmitterEmail": "string",
    "SubmitterMobile": "string"
  }
  ```
- **204 No Content**: Assignment not found
- **400 Bad Request**: Invalid request number
- **401 Unauthorized**: Authentication failed

## Business Logic
1. Validates request number format
2. Retrieves assignment with status information
3. Verifies submitter's authorization
4. Maps assignment data to response model

## Dependencies
- AssignmentContext
- Status entity

## Helper Methods
### MapAssignmentToGetAssignmentItemResponse
Converts Assignment entity to GetAssignmentItemResponse model with proper field mapping.
