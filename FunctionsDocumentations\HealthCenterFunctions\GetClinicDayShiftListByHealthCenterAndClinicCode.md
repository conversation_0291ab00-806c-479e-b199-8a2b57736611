# GetClinicDayShiftListByHealthCenterAndClinicCode

## Overview
Retrieves detailed shift information for a specific clinic in a health center, including available dates for the next 5 months.

## Endpoint
- **Route**: `clinic-day-shift/{hcCode}/{clinicCode}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **hcCode** (path, required): Health center code
- **clinicCode** (path, required): Clinic code

## Response
- **200 OK**: Returns list of shift data
  ```json
  [
    {
      "AvailableDate": "string",    // UTC formatted date
      "ShiftCode": "string",
      "ShiftDescriptionEn": "string",
      "ShiftDescriptionAr": "string"
    }
  ]
  ```
- **204 No Content**: No shifts found

## Business Logic
1. Calculates date range:
   - Start: Current date
   - End: Last day of 5 months ahead
2. Retrieves clinic days shift data within range
3. Joins with shift descriptions
4. Returns available dates with shift information

## Date Range Calculation
- Current date as start
- 5 months forward for end date
- Uses last day of end month

## Data Sources
- ClinicDaysShift: Available dates and shift codes
- Shift: Shift descriptions and details

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient date range filtering
- Proper join operations

## Multilingual Support
- Bilingual shift descriptions (English/Arabic)
