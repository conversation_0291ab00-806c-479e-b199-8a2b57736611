# Update Transfer Tests

## Test Cases

### 1. TestUpdateTransferByReqNumber_Should_Return_OK

Tests successful update of transfer by request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateTransferByReqNumber_Should_Return_OK()
{
    // Arrange
    var request = GetRestRequest("transfers/{reqNumber}");
    request = CreateUpdateTransfer(request);
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    request.AddUrlSegment("reqNumber", "NELRRPFMSHF");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateTransfer");

    // Act
    var response = await _client.PutAsync(request);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(OK == response.StatusCode);
}
```

### 2. TestUpdateTransferByInvalidReqNumber

Tests update with invalid request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestUpdateTransferByInvalidReqNumber()
{
    // Arrange
    var request = GetRestRequest("transfers/{reqNumber}");
    request = CreateUpdateTransfer(request);
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    request.Method = Method.Put;
    request.AddUrlSegment("reqNumber", "InvalidReqNumber");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-UpdateTransfer");
    
    // Act
    var response = await _client.ExecuteAsync(request);
    
    // Assert
    True(response.StatusCode == BadRequest);
}
```

## Request Details

### Endpoint
```
PUT transfers/{reqNumber}
```

### Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: "UnitTest-UpdateTransfer"

### URL Parameters
- `reqNumber`: Transfer request number

## Request Model

### CreateUpdateTransferRequest
```csharp
public class CreateUpdateTransferRequest
{
    // Personal Information
    public long QId { get; set; }
    public string FNameEn { get; set; }
    public string MNameEn { get; set; }
    public string LNameEn { get; set; }
    public string FNameAr { get; set; }
    public string MNameAr { get; set; }
    public string LNameAr { get; set; }
    public string Nationality { get; set; }
    public DateTime Dob { get; set; }
    public bool Consent { get; set; }

    // Healthcare Information
    public string HCNumber { get; set; }
    public string CurrentHC { get; set; }
    public string CatchmentHC { get; set; }
    public string PrefHC { get; set; }

    // Address Information
    public int BNo { get; set; }
    public int ZNo { get; set; }
    public int SNo { get; set; }
    public int UNo { get; set; }

    // Attachments
    public Guid AttachId1 { get; set; }
    public Guid AttachId2 { get; set; }
    public Guid AttachId3 { get; set; }
    public Guid AttachId4 { get; set; }
    public Guid AttachId5 { get; set; }
    public Guid AttachId6 { get; set; }

    // Transfer Details
    public string TransferReason { get; set; }

    // Submission Information
    public string SubmittedBy { get; set; }
    public long SubmitterQId { get; set; }
    public string SubmitterEmail { get; set; }
    public string SubmitterMobile { get; set; }
    public DateTime GisAddressUpdatedAt { get; set; }
    public bool IsGisAddressManualyEntered { get; set; }
}
```

## Test Data

### Success Case
- Request Number: "NELRRPFMSHF"
- Generated test data using CreateUpdateTransfer
- Expected: OK status code

### Error Case
- Request Number: "InvalidReqNumber"
- Generated test data using CreateUpdateTransfer
- Expected: BadRequest status code

## Validation Rules

### Success Case Validation
1. Response not null
2. Status code is OK

### Error Case Validation
1. Status code is BadRequest

## Error Cases

1. **Invalid Request Number**
   - Non-existent request
   - Malformed number
   - Invalid format

2. **Invalid Data**
   - Missing required fields
   - Invalid field formats
   - Data validation failures

3. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID
   - Unauthorized access

## Notes

1. **Test Data**
   - Uses same data generator as Create
   - Consistent test patterns
   - Valid field formats

2. **Request Format**
   - Complex object structure
   - Multiple data categories
   - File attachments

3. **Performance**
   - Data generation overhead
   - Attachment handling
   - Response validation

4. **State Management**
   - Request number validation
   - Update verification
   - Error handling
