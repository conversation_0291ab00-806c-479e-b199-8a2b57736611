﻿using EServiceFunctions.Models.EServiceGeneric;

namespace UnitTestEServiceFunctions;

public class UnitTestMiscellaneousFunctions(ITestOutputHelper output)
{
    private readonly ILogger<MiscellaneousFunctions> _logger = Mock.Of<ILogger<MiscellaneousFunctions>>();
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRequestStatusStatsByQId_Should_Return_UnauthorizedResult_When_QId_Does_Not_Match_Claims()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();
        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, QId); // Add a different QId in the claims

        // Act
        var response = await miscellaneousFunctions.GetRequestStatusStatsByQId(mockHttpRequestData, LongQId1); // Request a different QId
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssemblyVersion_Should_Return_Version_Information()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();
        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await miscellaneousFunctions.GetAssemblyVersion(mockHttpRequestData);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        NotNull(responseString);
        // The response is just the version number, so we don't need to check for the word "version"
        NotEmpty(responseString);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRequestStatusStatsByQId_Should_Return_BadRequest_When_Exception_Occurs()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();

        // Setup to throw exception when creating context
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database connection error"));

        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, QId);

        // Act
        var response = await miscellaneousFunctions.GetRequestStatusStatsByQId(mockHttpRequestData, long.Parse(QId));
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAllStaticDDLs_Should_Return_BadRequest_When_Exception_Occurs()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();

        // Setup to throw exception when creating context
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database connection error"));

        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await miscellaneousFunctions.GetAllStaticDDLs(mockHttpRequestData);
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetApiHealth_Should_Return_OkObjectResult()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();
        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await miscellaneousFunctions.GetApiHealth(mockHttpRequestData);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        Equal("\"API is healthy\"", responseString.Trim());
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAssemblyVersion_Should_Return_OkObjectResult()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();
        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);

        // Act
        var response = await miscellaneousFunctions.GetAssemblyVersion(mockHttpRequestData);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        NotNull(responseString);
        NotEqual("\"Version not found\"", responseString.Trim());
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetRequestStatusStatsByQId_Should_Handle_Database_Exception()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Database error", new Exception("Inner exception")));

        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, QId);

        // Act
        var response = await miscellaneousFunctions.GetRequestStatusStatsByQId(mockHttpRequestData, long.Parse(QId));
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAllStaticDDLs_Should_Handle_Database_Exception()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var mockDbContextFactory = new Mock<IDbContextFactory<EServiceDbContext>>();
        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Database error", new Exception("Inner exception")));

        var miscellaneousFunctions = new MiscellaneousFunctions(mockDbContextFactory.Object, _logger);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, QId);

        // Act
        var response = await miscellaneousFunctions.GetAllStaticDDLs(mockHttpRequestData);
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }
}
