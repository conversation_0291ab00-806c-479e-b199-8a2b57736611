﻿namespace EServiceFunctions.Models.Appointment;

public class AppointmentContext(DbContextOptions<AppointmentContext> options) : DbContext(options)
{
    public virtual DbSet<Appointment>? Appointment { get; set; }
    public virtual DbSet<Status>? Status { get; set; }
    public virtual DbSet<AppointmentRequestType>? AppointmentRequestType { get; set; }
    public virtual DbSet<ESEligibleAppointmentType>? ESEligibleAppointmentTypes { get; set; }
    public virtual DbSet<Shift>? Shift { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<Appointment>(entity =>
        {
            entity.HasIndex(e => e.ReqNumber)
                .HasDatabaseName("UQ__Appointm__237F342303ED4D4E")
                .IsUnique();

            entity.Property(e => e.AmPm).HasMaxLength(255);

            entity.Property(e => e.Clinic).HasMaxLength(255);

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.Property(e => e.Dob).HasColumnType("date");

            entity.Property(e => e.FNameAr).HasMaxLength(255);

            entity.Property(e => e.FNameEn).HasMaxLength(255);

            entity.Property(e => e.Gender).HasMaxLength(255);

            entity.Property(e => e.SecondaryPhoneMobile).HasMaxLength(255);

            entity.Property(e => e.ClinicTime).HasMaxLength(255);

            entity.Property(e => e.HCNumber).HasMaxLength(255);

            entity.Property(e => e.LNameAr).HasMaxLength(255);

            entity.Property(e => e.LNameEn).HasMaxLength(255);

            entity.Property(e => e.MNameAr).HasMaxLength(255);

            entity.Property(e => e.MNameEn).HasMaxLength(255);

            entity.Property(e => e.Nationality).HasMaxLength(255);

            entity.Property(e => e.PrefContactTime).HasMaxLength(255);

            entity.Property(e => e.PrefDate).HasColumnType("date");

            entity.Property(e => e.ReqNumber).HasMaxLength(255);

            entity.Property(e => e.RequestType).HasMaxLength(255);

            entity.Property(e => e.SN).HasMaxLength(255);

            entity.Property(e => e.SelectedHC).HasMaxLength(255);

            entity.Property(e => e.Status).HasMaxLength(255);

            entity.Property(e => e.StatusInternal).HasMaxLength(255);

            entity.Property(e => e.SubmittedAt).HasColumnType("datetime");

            entity.Property(e => e.SubmittedBy).HasMaxLength(255);

            entity.Property(e => e.SubmitterEmail).HasMaxLength(255);

            entity.Property(e => e.SubmitterMobile).HasMaxLength(255);

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);

            entity.HasOne(d => d.StatusNavigation)
                .WithMany(p => p.Appointment)
                .HasForeignKey(d => d.Status)
                .HasConstraintName("FK__Appointme__Statu__11D4A34F");

            entity.HasOne(d => d.AppointmentRequestTypeNavigation)
                .WithMany(p => p.Appointment)
                .HasForeignKey(d => d.RequestType)
                .HasConstraintName("FK__Appointme__Reque__1758727B");

            entity.HasOne(d => d.AmPmShiftNavigation)
                .WithMany(p => p.AppointmentAmPm)
                .HasForeignKey(d => d.AmPm)
                .HasConstraintName("FK__Appointmen__AmPm__269AB60B");

            entity.HasOne(d => d.PrefContactTimeShiftNavigation)
                .WithMany(p => p.AppointmentPrefContactTime)
                .HasForeignKey(d => d.PrefContactTime)
                .HasConstraintName("FK__Appointme__PrefC__278EDA44");
        });

        modelBuilder.Entity<Status>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Status__A25C5AA68EAE98DF");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);

            entity.Property(e => e.Category).HasMaxLength(255);
        });

        modelBuilder.Entity<AppointmentRequestType>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Appointm__A25C5AA6D0B5243F");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);
        });

        modelBuilder.Entity<Shift>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Shift__A25C5AA613765CB3");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);
        });
        
        modelBuilder.Entity<ESEligibleAppointmentType>(entity =>
        {
                entity.ToTable("ESEligibleAppointmentType");
                entity.Property(e => e.AppointmentType).HasColumnName("AppointmentType").HasMaxLength(255);
                entity.Property(e => e.AppointmentClinic).HasColumnName("AppointmentClinicEN").HasMaxLength(255);
                entity.Property(e => e.ClinicCode).HasColumnName("ClinicCode");
                entity.HasNoKey();
        });
    }
}
