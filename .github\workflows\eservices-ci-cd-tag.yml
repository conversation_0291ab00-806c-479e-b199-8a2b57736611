# minimal yaml approach.
# repo contains trigger definition and simple workflow call.
name: EServices RestAPI CI-CD
run-name: Release-1.1.357.${{github.run_number}} by @${{ github.actor }}  # change the tag name here and update the tag name in child workflow
on:
    workflow_dispatch: # disbale this trigger after testing

jobs:
    cicd-job:
        name: EServices RestAPI CI/CD
        uses: public-health-care-center-CORP/WorkflowRepo/.github/workflows/ci-cd-eservices-restapi-tag.yml@main
        secrets: inherit
