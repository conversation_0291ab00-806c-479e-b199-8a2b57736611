﻿using EServiceFunctions.Models.Appointment;
using EServiceFunctions.Models.HmcLiveFeeds;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.Appointment;
using EServiceFunctions.RequestResponseModels.EDWDropDownList;
using EServiceFunctions.RequestResponseModels.UserProfile;
using MockDataLibrary;

namespace UnitTestEServiceFunctions;

public class TestEdwDbContextForAppointment : IDbContextFactory<EDWDbContext>
{
    public EDWDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<EDWDbContext>()
            .UseInMemoryDatabase(databaseName: "TestEdwDbContextForAppointment")
            .Options;
        var context = new EDWDbContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.PersonMoiDetail.AddRange(GetPersonMoiDetailsAppointmentEdw());
        context.UpcomingConfirmedAppointmentListResponseFromEDW.AddRange(GetGetUpcomingConfirmedAppointmentList());
        context.GetLanguageListResponse.AddRange(GetLanguageListResponses());
        context.GetMoiDependentsResponse.AddRange(GetGetMoiDependentsResponseForAppointment());
        context.UserProfileResponseFromEDW.AddRange(GetUserProfileResponseForAppointment());
        context.MedicationRefillDetailsProd.AddRange(GetMedicationRefillDetailsProdForAppointment());
        context.MedicationRefillDetailsStg.AddRange(GetMedicationRefillDetailsStgForAppointment());

        context.SaveChanges();
        return context;
    }
}

public class TestAppointmentDbContext : IDbContextFactory<AppointmentContext>
{
    public AppointmentContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<AppointmentContext>()
            .UseInMemoryDatabase(databaseName: "TestAppointmentDbContext")
            .Options;

        var context = new AppointmentContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.Appointment.AddRange(GetAppointments());
        context.Status.AddRange(GetAppointmentStatus());
        context.AppointmentRequestType.AddRange(GetAppointmentRequestTypes());
        context.Shift.AddRange(GetAppointmentShifts());
        context.SaveChanges();
        return context;
    }
}

public class TestHmcLiveFeedContext : IDbContextFactory<HmcLiveFeedContext>
{
    public HmcLiveFeedContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<HmcLiveFeedContext>()
            .UseInMemoryDatabase(databaseName: "hmcLiveFeedsDb")
            .Options;

        var context = new HmcLiveFeedContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.AppointmentBookings.AddRange(GetAppointmentBookings());
        context.SaveChanges();
        return context;
    }
}

public static class MockDataForAppointmentFunctions
{
    public static IEnumerable<AppointmentBooking> GetAppointmentBookings()
    {
        return new List<AppointmentBooking>
        {
            new()
            {
                HcNumber = "HC123456",
                QId =   GenerateMoqData.GetMoqUser().UserQId.ToString(),
                AppointmentId = 1,
                InitialAppointmentDateTime = Now.AddDays(-7),
                AppointmentDateTime = Now.AddDays(7),
                AppointmentTypeCode = "Pre Marital New",
                AppointmentStatus = "SCHEDULED",
                ClinicTypeCode = "GENERAL",
                AppointmentHcCode = "HCC001",
                AppointmentPhysicianId = "PHY001",
                AppointmentDuration = 30,
                AppointmentConsultationType = "IN_PERSON",
                InsertDate = Now.AddDays(-7),
                UpdateDate = Now.AddDays(-5),
                InsertMessageId = "INS001",
                UpdateMessageId = "UPD001"
            },
            new()
            {
                HcNumber = "HC789012",
                QId =  GenerateMoqData.GetMoqUser().UserQId.ToString(),
                AppointmentId = 2,
                InitialAppointmentDateTime = Now.AddDays(-14),
                AppointmentDateTime = Now.AddDays(14),
                AppointmentTypeCode = "Pre Marital FU",
                AppointmentStatus = "CONFIRMED",
                ClinicTypeCode = "SPECIALIST",
                AppointmentHcCode = "HCC002",
                AppointmentPhysicianId = "PHY002",
                AppointmentDuration = 45,
                AppointmentConsultationType = "VIRTUAL",
                InsertDate = Now.AddDays(-20),
                UpdateDate = Now.AddDays(-2),
                InsertMessageId = "INS002",
                UpdateMessageId = "UPD002"
            }
        };
    }

    public static IEnumerable<MedicationRefillDtls> GetMedicationRefillDetailsProdForAppointment()
    {
        var medicationRefill = new MedicationRefillDtls
        {
            PersonId = 1,
            QId = QId,
            HcNumber = HcNumber1,
            NameFirst = FirstNameEn,
            NameMiddle = MiddleNameEn,
            NameLast = LastNameEn,
            AgeYears = Age,
            MobilePhone = MobilePhone,
            PrescriptionRefillDueDate = new DateTime(2023, 10, 10),
            LastDispenseDate = new DateTime(2023, 08, 10),
            LastDispenseFacCode = "WAJ",
            PrescriptionDate = new DateTime(2023, 10, 10),
            PrescriptionFacCode = "WAJ",
            PrescribedMedication = "mometasone nasaL",
            SupplyDuration = 15,
            PrescriptionOrderId = 8448095541,
            PrescribedByCorpId = "2683232",
            PrescribedByNameEng = "Dr. Aktham Shraiba",
            PrescribedByNameAra = "د. أكثم شريبة",
            RefillsRemaining = 10,
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<MedicationRefillDtls> { medicationRefill };
    }

    public static IEnumerable<EServPersonasMedicationRefill> GetMedicationRefillDetailsStgForAppointment()
    {
        var medicationRefillDetailsList = new List<EServPersonasMedicationRefill>
        {
            new()
            {
                QId = QId,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 4",
                SupplyDuration = 1,
                PrescriptionOrderId = 1,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId1,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 5",
                SupplyDuration = 1,
                PrescriptionOrderId = 2,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId2,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 6",
                SupplyDuration = 1,
                PrescriptionOrderId = 3,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            }
        };
        return medicationRefillDetailsList;
    }

    public static IEnumerable<UserProfileFromEDW> GetUserProfileResponseForAppointment()
    {
        var userProfileFromEdw = new List<UserProfileFromEDW>
        {
            new()
            {
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber1,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            },
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber2,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            }
        };
        return userProfileFromEdw;
    }

    public static IEnumerable<GetMoiDependentsResponse> GetGetMoiDependentsResponseForAppointment()
    {
        var getMoiDependentsResponse = new List<GetMoiDependentsResponse>
        {
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 2,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 3,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            }
        };
        return getMoiDependentsResponse;
    }

    public static IEnumerable<GetLanguageListResponse> GetLanguageListResponses()
    {
        var getLanguageListResponse = new List<GetLanguageListResponse>
        {
            new()
            {
                NatCode = "en",
                NatNameEn = "English",
                NatNameAr = "الإنجليزية"
            },
            new()
            {
                NatCode = "ar",
                NatNameEn = "Arabic",
                NatNameAr = "العربية"
            },
            new()
            {
                NatCode = "fr",
                NatNameEn = "French",
                NatNameAr = "الفرنسية"
            }
        };
        return getLanguageListResponse;
    }

    public static IEnumerable<GetUpcomingConfirmedAppointmentListResponseFromEdw>
        GetGetUpcomingConfirmedAppointmentList()
    {
        var getUpcomingConfirmedAppointmentListResponseFromEdw =
            new List<GetUpcomingConfirmedAppointmentListResponseFromEdw>
            {
                new()
                {
                    AppointmentId = "AP00001",
                    QId = LongQId,
                    QIdExpiryDt = new Faker().Date.Future(),
                    FNameEn = FirstNameEn,
                    MNameEn = MiddleNameEn,
                    LNameEn = LastNameEn,
                    FNameAr = FirstNameAr,
                    MNameAr = MiddleNameAr,
                    LNameAr = LastNameAr,
                    NationalityCode = NationalityCode,
                    NationalityEn = NationalityEn,
                    NationalityAr = NationalityAr,
                    Dob = new Faker().Date.Past(),
                    HCNumber = HcNumber1,
                    HCExpiryDate = new Faker().Date.Future(),
                    GenderCode = GenderCode,
                    GenderEn = GenderEn,
                    GenderAr = GenderAr,
                    PhoneMobile = MobilePhone,
                    AggignedHCntCode = "OBK",
                    AggignedHCntNameEn = "Omar Bin Khattab",
                    AggignedHCntNameAr = "عمر بن الخطاب",
                    AppointmentHCntCode = "OBK",
                    AppointmentHCntNameEn = "Omar Bin Khattab",
                    AppointmentHCntNameAr = "عمر بن الخطاب",
                    ClinicCode = "OBK001",
                    ClinicNameEn = "OBK Clinic 1",
                    ClinicNameAr = "عيادة عمر بن الخطاب 1",
                    GisAddressStreet = new Faker().Address.StreetAddress(),
                    GisAddressBuilding = new Faker().Address.BuildingNumber(),
                    GisAddressZone = new Faker().Address.City(),
                    GisAddressUnit = new Faker().Address.City(),
                    PhysicianId = "123456",
                    PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                    PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                    BookedDateTime = new Faker().Date.Past(),
                    AppointmentDateTime = new Faker().Date.Future(),
                    AppointmentType = "Appointment",
                    AppointmentStatus = "Confirmed",
                    AppointmentLocation = "OBK",
                    AppointmentFacility = "OBK"
                }
            };

        return getUpcomingConfirmedAppointmentListResponseFromEdw;
    }

    public static IEnumerable<Shift> GetAppointmentShifts()
    {
        var shift = new List<Shift>
        {
            new()
            {
                Code = "AM",
                DescriptionEn = "AM",
                DescriptionAr = "صباحا"
            },
            new()
            {
                Code = "PM",
                DescriptionEn = "PM",
                DescriptionAr = "مساء"
            }
        };

        return shift;
    }

    public static IEnumerable<Appointment> GetAppointments()
    {
        var appointments = new List<Appointment>
        {
            new()
            {
                Id = 1,
                QId = LongQId,
                ReqNumber = "REQ0000001",
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Gender = GenderEn,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                CancellationCall = false,
                Consent = true,
                Nationality = NationalityEn,
                Dob = new DateTime(1986, 09, 28),
                HCNumber = HcNumber1,
                Clinic = "OBK",
                ClinicTime = "10:00 AM",
                PrefDate = new DateTime(2023, 10, 10),
                AmPm = "AM",
                SelectedHC = HealthCenterNameEn,
                RequestType = "New",
                PrefContactTime = "10:00 AM",
                SubmittedBy = "Mohamed Fazrin",
                SubmittedAt = new DateTime(2023, 10, 10),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new DateTime(2023, 10, 10),
                UpdatedAt = new DateTime(2023, 10, 10),
                Status = "New",
                StatusInternal = "New",
                SN = "*********",
                AppointmentId = 1,
                CreateSource = "EService",
                UpdateSource = "EService"
            },
            new()
            {
                Id = 2,
                QId = LongQId,
                ReqNumber = "REQ0000002",
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Gender = GenderEn,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                CancellationCall = false,
                Consent = true,
                Nationality = NationalityEn,
                Dob = new DateTime(1986, 09, 28),
                HCNumber = HcNumber1,
                Clinic = "OBK",
                ClinicTime = "10:00 AM",
                PrefDate = new DateTime(2023, 10, 10),
                AmPm = "AM",
                SelectedHC = HealthCenterNameEn,
                RequestType = "Cancel",
                PrefContactTime = "10:00 AM",
                SubmittedBy = "Mohamed Fazrin",
                SubmittedAt = new DateTime(2023, 10, 10),
                SubmitterQId = LongQId,
                SubmitterEmail = UserEmail,
                SubmitterMobile = MobilePhone,
                CreatedAt = new DateTime(2023, 10, 10),
                UpdatedAt = new DateTime(2023, 10, 10),
                Status = "Cancel",
                StatusInternal = "Cancel",
                SN = "*********",
                AppointmentId = 1,
                CreateSource = "EService",
                UpdateSource = "EService"
            }
        };

        return appointments;
    }

    public static IEnumerable<Status> GetAppointmentStatus()
    {
        var status = new List<Status>
        {
            new()
            {
                Code = New,
                DescriptionEn = New,
                DescriptionAr = "جديد",
                Category = New
            },
            new()
            {
                Code = Approved,
                DescriptionEn = "Request Completed",
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = Cancelled,
                DescriptionEn = "Request Cancelled",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = CancelledByEServ,
                DescriptionEn = "Cancelled by EServices",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = "CancelledByRegistration",
                DescriptionEn = "Cancelled by Registration",
                DescriptionAr = "ألغيت بالتسجيل",
                Category = Archived
            },
            new()
            {
                Code = CancelledFrom107,
                DescriptionEn = "Cancelled from 107",
                DescriptionAr = "تم إلغاء الطلب",
                Category = Archived
            },
            new()
            {
                Code = ConditionallyApproved,
                DescriptionEn = "Request Tentatively Approved",
                DescriptionAr = "تمت الموافقة المبدئية للطلب",
                Category = InProcess
            },
            new()
            {
                Code = Confirmed,
                DescriptionEn = Confirmed,
                DescriptionAr = "تم إستكمال الطلب",
                Category = Archived
            },
            new()
            {
                Code = InProgress,
                DescriptionEn = "Request In Progress",
                DescriptionAr = "الطلب قيد الاجراء",
                Category = InProcess
            },
            new()
            {
                Code = PaymentReceived,
                DescriptionEn = "Payment Received",
                DescriptionAr = "تمت عملية الدفع",
                Category = InProcess
            },
            new()
            {
                Code = PendingOrder,
                DescriptionEn = "Request Pending",
                DescriptionAr = "الطلب قيد الإنتظار",
                Category = InProcess
            },
            new()
            {
                Code = "ProceedToRegistration",
                DescriptionEn = "Proceed to Registration",
                DescriptionAr = "الشروع في التسجيل",
                Category = InProcess
            }
        };

        return status;
    }

    public static IEnumerable<AppointmentRequestType>
        GetAppointmentRequestTypes()
    {
        var appointmentRequestType = new List<AppointmentRequestType>
        {
            new()
            {
                Code = "001",
                DescriptionEn = "New Appointment",
                DescriptionAr = "موعد جديد"
            },
            new()
            {
                Code = "002",
                DescriptionEn = "Reschedule Appointment",
                DescriptionAr = "إعادة جدولة الموعد"
            },
            new()
            {
                Code = "003",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            },
            new()
            {
                Code = "004",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            },
            new()
            {
                Code = "005",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            },
            new()
            {
                Code = "006",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            },
            new()
            {
                Code = "007",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            },
            new()
            {
                Code = "008",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            },
            new()
            {
                Code = "009",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            },
            new()
            {
                Code = "010",
                DescriptionEn = "Cancel Appointment",
                DescriptionAr = "إلغاء الموعد"
            }
        };

        return appointmentRequestType;
    }

    public static IEnumerable<PersonMoiDetail> GetPersonMoiDetailsAppointmentEdw()
    {
        var personMoiDetail = new PersonMoiDetail
        {
            QId = QId,
            Dob = new DateTime(1986, 09, 28),
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<PersonMoiDetail> { personMoiDetail };
    }
}

public class UnitTestAppointmentFunctions(ITestOutputHelper output)
{
    private readonly ILogger<AppointmentFunctions> _logger = Mock.Of<ILogger<AppointmentFunctions>>();

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentListByQId_Should_Return_OkObjectResult_For_Valid_QId()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentListByQId_Should_Return_UnAuthorized__For_Different_QId()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_BadRequest_For_Invalid_QId_Without_IsAuthValReq_Header()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Remove(IsAuthValReq);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_UnAuthorized_For_Invalid_QId_For_IsAuthValReq_True()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_BadRequest_For_Different_QId_For_IsAuthValReq_False()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Remove(IsAuthValReq);
        mockHttpRequestData.Headers.Add(IsAuthValReq, "false");

        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentListByQId_Should_Return_Ok_For_ValidQId_And_Valid_ClinicCode()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_Ok_For_ValidQId_And_ClinicCode_IsAuthValReq_False()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Remove(IsAuthValReq);
        mockHttpRequestData.Headers.Add(IsAuthValReq, "false");
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentListByQId_Should_Return_Unauthorized_For_InvalidQId()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData =
            MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId1);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var status = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, status.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    [Trait(Category, UnHappyPath)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_NoContent_For_ValidQId_And_Invalid_ClinicCode()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=Omar";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);

        // Assert
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_OkObject_For_ValidQId_And_Empty_ClinicCode()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_NoContentResult_For_Valid_QId_With_Invalid_Status()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New1&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);

        // Assert
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentListByQId_Should_Return_OkObjectResult_For_Invalid_Skip()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = "?status=New&requestType=New&skip=a&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentListByQId_Should_Return_OkObjectResult_For_Invalid_Take()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=a";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_OkObjectResult_For_Invalid_Take_And_Skip()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = "?status=New&requestType=New&skip=a&take=a";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_GetEServRequestedAppointmentListByQId_Should_Return_BadRequestObjectResult_For_Invalid_RequestType()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=InvalidRequestType&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAppointmentStatsByQId_Should_Return_Ok_With_1()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, LongQId);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var status = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, status.StatusCode);
        var stats = DeserializeObject<GetAppointmentStatsResponse>(result);
        Equal(1, stats.Count);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAppointmentStatsByQId_InvalidQId_Should_Return_UnAuthorized()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData =
            MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, 0);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var status = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, status.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentByReqNumber_Should_Return_Ok_For_Valid_ReqNumber()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string reqNumber = "REQ0000001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response =
            await appointmentFunctions.GetEServRequestedAppointmentByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);

        var okResponseObject = DeserializeObject<GetAppointmentItemResponse>(result);
        Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEServRequestedAppointmentByReqNumber_NoContent_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string reqNumber = "REQ000034343";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response =
            await appointmentFunctions.GetEServRequestedAppointmentByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        // output.WriteLine(GetResponseText(okResponse));
        LogResponse(output, response);
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckAppointmentChangeRequestExist_Should_Return_Ok_For_Valid_AppointmentId()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const long appointmentNumber = 1;
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response =
            await appointmentFunctions.CheckAppointmentChangeRequestExist(mockHttpRequestData, appointmentNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task
        Test_CheckAppointmentChangeRequestExist_Should_Return_OK_For_Invalid_AppointmentId_With_FalseValues()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const long appointmentNumber = 8728172811;
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response =
            await appointmentFunctions.CheckAppointmentChangeRequestExist(mockHttpRequestData, appointmentNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAppointment_Should_Return_BadObjectResult_For_Required_Header_Missing()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        Appointment appointment = new Faker<Appointment>()
            .RuleFor(a => a.Id, f => f.Random.Int(1, 100))
            .RuleFor(a => a.QId, _ => LongQId)
            .RuleFor(a => a.FNameEn, _ => FirstNameEn)
            .RuleFor(a => a.MNameEn, _ => MiddleNameEn)
            .RuleFor(a => a.LNameEn, _ => LastNameEn)
            .RuleFor(a => a.FNameAr, _ => FirstNameAr)
            .RuleFor(a => a.MNameAr, _ => MiddleNameAr)
            .RuleFor(a => a.LNameAr, _ => LastNameAr)
            .RuleFor(a => a.Gender, _ => GenderEn)
            .RuleFor(a => a.SecondaryPhoneMobile, _ => SecondaryMobilePhone)
            .RuleFor(a => a.CancellationCall, _ => false)
            .RuleFor(a => a.Consent, _ => true)
            .RuleFor(a => a.Nationality, _ => NationalityEn)
            .RuleFor(a => a.Dob, _ => new DateTime(1986, 09, 28))
            .RuleFor(a => a.HCNumber, _ => HcNumber1)
            .RuleFor(a => a.Clinic, _ => "OBK")
            .RuleFor(a => a.ClinicTime, _ => "10:00 AM")
            .RuleFor(a => a.PrefDate, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.AmPm, _ => "AM")
            .RuleFor(a => a.SelectedHC, _ => HealthCenterNameEn)
            .RuleFor(a => a.RequestType, _ => "New")
            .RuleFor(a => a.PrefContactTime, _ => "10:00 AM")
            .RuleFor(a => a.SubmittedBy, _ => "Mohamed Fazrin")
            .RuleFor(a => a.SubmittedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.SubmitterQId, _ => LongQId)
            .RuleFor(a => a.SubmitterEmail, _ => UserEmail)
            .RuleFor(a => a.SubmitterMobile, _ => MobilePhone)
            .RuleFor(a => a.CreatedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.UpdatedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.Status, _ => "New")
            .RuleFor(a => a.StatusInternal, _ => "New")
            .RuleFor(a => a.SN, _ => "*********")
            .RuleFor(a => a.AppointmentId, _ => 1)
            .RuleFor(a => a.CreateSource, _ => "EService")
            .RuleFor(a => a.UpdateSource, _ => "EService")
            .Generate();

        var json = SerializeObject(appointment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response =
            await appointmentFunctions.CreateAppointment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAppointment_Should_Return_BadObjectResult_For_Empty_Input_Object()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        Appointment appointment = new();
        var json = SerializeObject(appointment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.CreateAppointment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestCreateAppointment_Should_Return_Created_For_Valid_Input_Object()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        Appointment appointment = new Faker<Appointment>()
            .RuleFor(a => a.Id, f => f.Random.Int(1, 100))
            .RuleFor(a => a.QId, _ => LongQId)
            .RuleFor(a => a.FNameEn, _ => FirstNameEn)
            .RuleFor(a => a.MNameEn, _ => MiddleNameEn)
            .RuleFor(a => a.LNameEn, _ => LastNameEn)
            .RuleFor(a => a.FNameAr, _ => FirstNameAr)
            .RuleFor(a => a.MNameAr, _ => MiddleNameAr)
            .RuleFor(a => a.LNameAr, _ => LastNameAr)
            .RuleFor(a => a.Gender, _ => GenderEn)
            .RuleFor(a => a.SecondaryPhoneMobile, _ => SecondaryMobilePhone)
            .RuleFor(a => a.CancellationCall, _ => false)
            .RuleFor(a => a.Consent, _ => true)
            .RuleFor(a => a.Nationality, _ => NationalityEn)
            .RuleFor(a => a.Dob, _ => new DateTime(1986, 09, 28))
            .RuleFor(a => a.HCNumber, _ => HcNumber1)
            .RuleFor(a => a.Clinic, _ => "OBK")
            .RuleFor(a => a.ClinicTime, _ => "10:00 AM")
            .RuleFor(a => a.PrefDate, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.AmPm, _ => "AM")
            .RuleFor(a => a.SelectedHC, _ => HealthCenterNameEn)
            .RuleFor(a => a.RequestType, _ => "New")
            .RuleFor(a => a.PrefContactTime, _ => "10:00 AM")
            .RuleFor(a => a.SubmittedBy, _ => "Mohamed Fazrin")
            .RuleFor(a => a.SubmittedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.SubmitterQId, _ => LongQId)
            .RuleFor(a => a.SubmitterEmail, _ => UserEmail)
            .RuleFor(a => a.SubmitterMobile, _ => MobilePhone)
            .RuleFor(a => a.CreatedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.UpdatedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.Status, _ => "New")
            .RuleFor(a => a.StatusInternal, _ => "New")
            .RuleFor(a => a.StatusNavigation,
                _ => new Status { Code = "New", DescriptionEn = "New", DescriptionAr = "جديد", Category = "New" })
            .RuleFor(a => a.PrefContactTimeShiftNavigation,
                _ => new Shift { Code = "AM", DescriptionEn = "AM", DescriptionAr = "صباحا" })
            .RuleFor(a => a.AppointmentRequestTypeNavigation,
                _ => new AppointmentRequestType
                    { Code = "001", DescriptionEn = "New Appointment", DescriptionAr = "موعد جديد" })
            .RuleFor(a => a.SN, _ => "*********")
            .RuleFor(a => a.AppointmentId, _ => 1)
            .RuleFor(a => a.CreateSource, _ => "EService")
            .RuleFor(a => a.UpdateSource, _ => "EService")
            .Generate();

        var json = SerializeObject(appointment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response =
            await appointmentFunctions.CreateAppointment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAppointment_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        Appointment appointment = new Faker<Appointment>()
            .RuleFor(a => a.Id, f => f.Random.Int(1, 100))
            .RuleFor(a => a.QId, _ => LongQId)
            .RuleFor(a => a.ReqNumber, _ => "REQ0000001")
            .RuleFor(a => a.FNameEn, _ => FirstNameEn)
            .RuleFor(a => a.MNameEn, _ => MiddleNameEn)
            .RuleFor(a => a.LNameEn, _ => LastNameEn)
            .RuleFor(a => a.FNameAr, _ => FirstNameAr)
            .RuleFor(a => a.MNameAr, _ => MiddleNameAr)
            .RuleFor(a => a.LNameAr, _ => LastNameAr)
            .RuleFor(a => a.Gender, _ => GenderEn)
            .RuleFor(a => a.SecondaryPhoneMobile, _ => SecondaryMobilePhone)
            .RuleFor(a => a.CancellationCall, _ => false)
            .RuleFor(a => a.Consent, _ => true)
            .RuleFor(a => a.Nationality, _ => NationalityEn)
            .RuleFor(a => a.Dob, _ => new DateTime(1986, 09, 28))
            .RuleFor(a => a.HCNumber, _ => HcNumber1)
            .RuleFor(a => a.ClinicTime, _ => "10:00 AM")
            .RuleFor(a => a.PrefDate, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.AmPm, _ => "AM")
            .RuleFor(a => a.SelectedHC, _ => HealthCenterNameEn)
            .RuleFor(a => a.RequestType, _ => "New")
            .RuleFor(a => a.PrefContactTime, _ => "10:00 AM")
            .RuleFor(a => a.SubmittedBy, _ => "Mohamed Fazrin")
            .RuleFor(a => a.SubmittedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.SubmitterQId, _ => LongQId)
            .RuleFor(a => a.SubmitterEmail, _ => UserEmail)
            .RuleFor(a => a.SubmitterMobile, _ => MobilePhone)
            .RuleFor(a => a.CreatedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.UpdatedAt, _ => new DateTime(2023, 10, 10))
            .RuleFor(a => a.Status, _ => "New")
            .RuleFor(a => a.StatusInternal, _ => "New")
            .RuleFor(a => a.SN, _ => "*********")
            .RuleFor(a => a.AppointmentId, _ => 1)
            .RuleFor(a => a.CreateSource, _ => "EService")
            .RuleFor(a => a.UpdateSource, _ => "EService")
            .Generate();

        const string reqNumber = "REQ0000001";

        var json = SerializeObject(appointment);
        var mockHttpRequestData =
            MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.UpdateAppointmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAppointment_Should_Return_BadObjectResult_For_Empty_Input()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        Appointment appointment = new();
        const string reqNumber = "REQ0000001";
        var json = SerializeObject(appointment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.UpdateAppointmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteAppointmentByReqNumber()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string reqNumber = "REQ0000001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.DeleteAppointmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetUpcomingConfirmedAppointmentByAppointmentId_Should_Return_Ok_For_Valid_AppointmentId()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response =
            await appointmentFunctions.GetUpcomingConfirmedAppointmentByAppointmentId(mockHttpRequestData, 1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetEServRequestedListByQId_NoClinicCodes_Should_Return_BadRequest()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Remove(EligibleClinicCodes);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetEServRequestedListByQId_InvalidRequestType_ThenReturnsBadRequest()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=InvalidRequestType&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetEServRequestedListByQId_InvalidStatus_ThenReturnsNoContent()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=InvalidStatus&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Remove(EligibleClinicCodes);
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "InvalidClinicCode");
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetEServRequestedListByQId_ValidRequest_ClinicFilter_NoMatch_ThenReturnsNoContent()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=Omar";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.NoContent, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetEServRequestedListByQId_ValidRequest_ClinicFilter_Match_ThenReturnsOK()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetEServRequestedListByQId_Without_Headers_Then_Returns_Unauthorized()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Clear();
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetEServRequestedAppointmentListByQId(mockHttpRequestData, LongQId1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.Unauthorized, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetAppointmentStatsByQId_Returns_Count_One()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=1&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, LongQId);

        // Assert
        var result = DeserializeObject<GetAppointmentStatsResponse>(response.ReadBodyToEnd());
        Equal(1, result.Count);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetAppointmentStatsByQId_InvalidStatus_ThenReturns_Count_Zero()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=InvalidStatus&requestType=New&skip={Skip}&take=10&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, LongQId);

        // Assert
        var result = DeserializeObject<GetAppointmentStatsResponse>(response.ReadBodyToEnd());
        Equal(0, result.Count);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetAppointmentStatsByQId_NoClinicCodes_ThenReturnsBadRequest()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Remove(EligibleClinicCodes);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetAppointmentStatsByQId_NoRequestType_ThenReturnsBadRequest()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&skip={Skip}&take=10&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetAppointmentStatsByQId_ValidRequest_ClinicFilter_NoMatch_ThenReturnsCountZero()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=Omar";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        mockHttpRequestData.Headers.Remove(EligibleClinicCodes);
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "InvalidClinicCode");
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, LongQId);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        var result = DeserializeObject<GetAppointmentStatsResponse>(response.ReadBodyToEnd());
        Equal(0, result.Count);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task TestGetAppointmentStatsByQId_ValidRequest_ClinicFilter_Match_ThenReturnsCount()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        const string queryParams = $"?status=New&requestType=New&skip={Skip}&take=10&clinicCode=OBK";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentStatsByQId(mockHttpRequestData, LongQId);

        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
        var result = DeserializeObject<GetAppointmentStatsResponse>(response.ReadBodyToEnd());
        Equal(1, result.Count);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProgressPmeAppointmentV1_Should_Return_True_For_InProgressPmeRequest()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "OBK"); // Clinic code that matches test data
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.CheckInProgressPmeAppointmentV1(mockHttpRequestData, LongQId);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var mockResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, mockResponse.StatusCode);
        var responseObject = DeserializeObject<CheckInProcessConfirmedAppointmentResponse>(result);
        True(responseObject.IsInProcessConfirmedRequestExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProgressPmeAppointmentV1_Should_Return_False_For_NoMatchingAppointments()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "999"); // Non-matching clinic code
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.CheckInProgressPmeAppointmentV1(mockHttpRequestData, 99999999999); // Non-existent QID
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var mockResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, mockResponse.StatusCode);
        var responseObject = DeserializeObject<CheckInProcessConfirmedAppointmentResponse>(result);
        False(responseObject.IsInProcessConfirmedRequestExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAppointmentsListByQIdV1_Should_Return_NoContent_For_Valid_QIdList()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        var qIdList = new QIdListRequest { QIds = [LongQId.ToString()] };
        var json = SerializeObject(qIdList);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "OBK"); // Clinic code that matches test data
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentsListByQIdV1(mockHttpRequestData);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var mockResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, mockResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAppointmentsListByQIdV1_Should_Return_NoContent_For_NoMatchingAppointments()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();
        var qIdList = new QIdListRequest { QIds = ["99999999999"] }; // Non-existent QID
        var json = SerializeObject(qIdList);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "999"); // Non-matching clinic code
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.GetAppointmentsListByQIdV1(mockHttpRequestData);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var mockResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, mockResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAppointmentByReqNumber_Should_Return_BadRequest_For_Invalid_RequestType()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        // Create appointment with invalid request type
        var appointment = new Faker<CreateUpdateAppointmentRequest>()
            .RuleFor(a => a.SubmitterQId, _ => LongQId)
            .RuleFor(a => a.QId, _ => LongQId)
            .RuleFor(a => a.Clinic, _ => "OBK")
            .RuleFor(a => a.RequestType, _ => "InvalidRequestType") // Invalid request type
            .RuleFor(a => a.PrefDate, _ => DateTime.Now.AddDays(7))
            .RuleFor(a => a.AmPm, _ => "AM")
            .Generate();

        var json = SerializeObject(appointment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: "PUT");
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "OBK");
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.UpdateAppointmentByReqNumber(mockHttpRequestData, "REQ0000001");
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var mockResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, mockResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAppointmentByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        // Create valid appointment request but with different submitter QID
        var appointment = new Faker<CreateUpdateAppointmentRequest>()
            .RuleFor(a => a.SubmitterQId, _ => 99999999999) // Different QID than in JWT claims
            .RuleFor(a => a.QId, _ => LongQId)
            .RuleFor(a => a.Clinic, _ => "OBK")
            .RuleFor(a => a.RequestType, _ => "New")
            .RuleFor(a => a.PrefDate, _ => DateTime.Now.AddDays(7))
            .RuleFor(a => a.AmPm, _ => "AM")
            .Generate();

        var json = SerializeObject(appointment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: "PUT");
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "OBK");
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.UpdateAppointmentByReqNumber(mockHttpRequestData, "REQ0000001");
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var mockResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, mockResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAppointment_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockAppointmentContext = new TestAppointmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForAppointment();
        var mockHmcLiveFeeds = new TestHmcLiveFeedContext();

        // Create valid appointment request but with different submitter QID
        var appointment = new Faker<CreateUpdateAppointmentRequest>()
            .RuleFor(a => a.SubmitterQId, _ => 99999999999) // Different QID than in JWT claims
            .RuleFor(a => a.QId, _ => LongQId)
            .RuleFor(a => a.Clinic, _ => "OBK")
            .RuleFor(a => a.RequestType, _ => "New")
            .RuleFor(a => a.PrefDate, _ => DateTime.Now.AddDays(7))
            .RuleFor(a => a.AmPm, _ => "AM")
            .Generate();

        var json = SerializeObject(appointment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Add(EligibleClinicCodes, "OBK");
        var appointmentFunctions = new AppointmentFunctions(mockAppointmentContext, mockEdwDbContext, mockHmcLiveFeeds, _logger);

        // Act
        var response = await appointmentFunctions.CreateAppointment(mockHttpRequestData);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var mockResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, mockResponse.StatusCode);
    }
}