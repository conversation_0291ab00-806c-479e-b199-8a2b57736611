# Get Transfer Statistics Tests

## Test Cases

### 1. TestGetTransferStatsByQId

Tests retrieval of transfer statistics by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetTransferStatsByQId()
{
    // Arrange
    var request = GetRestRequest("transfers/submitter-qid/{qId}/stats");
    request.AddUrlSegment("qId", GetMoqUser().UserQId);
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.GetAsync<GetTransferStatsResponse>(request);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.Count > 0);
}
```

### 2. TestGetTransferStatsByInvalidQId

Tests statistics retrieval with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetTransferStatsByInvalidQId()
{
    // Arrange
    var request = GetRestRequest("transfers/submitter-qid/{qId}/stats");
    request.Method = Method.Get;
    request.AddUrlSegment("qId", 22222222273);
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222273);

    // Act
    var response = await _client.ExecuteAsync(request);

    // Assert
    True(response.StatusCode == NoContent);
}
```

## Request Details

### Endpoint
```
GET transfers/submitter-qid/{qId}/stats
```

### Headers
- `JwtClaimsQId`: QID of the requester

### URL Parameters
- `qId`: Submitter QID

## Response Model

### GetTransferStatsResponse
```csharp
public class GetTransferStatsResponse
{
    public int Count { get; set; }
}
```

## Test Data

### Success Case
- QID: From GetMoqUser().UserQId
- Expected: Count > 0

### Error Case
- QID: 22222222273
- Expected: NoContent status code

## Validation Rules

### Success Case Validation
1. Response not null
2. Count greater than 0

### Error Case Validation
1. Status code is NoContent

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Unauthorized access

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - QID mismatch

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Response Format**
   - Simple count response
   - Zero for no matches
   - Null check required

3. **Performance**
   - Quick count operation
   - Efficient query execution
   - Status-based filtering
