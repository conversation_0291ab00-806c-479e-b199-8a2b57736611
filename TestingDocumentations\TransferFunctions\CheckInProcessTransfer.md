# Check In-Process Transfer Tests

## Test Cases

### 1. TestCheckInProcessTransferByQId

Tests validation of in-process transfer by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckInProcessTransferByQId()
{
    // Arrange
    var request = GetRestRequest("transfers/{qId}/inprocess-validation");
    request.AddUrlSegment("qId", 94733427819);

    // Act
    var response = await _client.GetAsync<CheckInProcessTransferResponse>(request);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.IsInprocessExist);
}
```

### 2. TestCheckInProcessTransferByInvalidQId

Tests in-process validation with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestCheckInProcessTransferByInvalidQId()
{
    // Arrange
    var request = GetRestRequest("transfers/{qId}/inprocess-validation");
    request.AddUrlSegment("qId", 94733427818);

    // Act
    var response = await _client.GetAsync<CheckInProcessTransferResponse>(request);

    // Assert
    NotNull(response);
    False(response.IsInprocessExist);
}
```

## Request Details

### Endpoint
```
GET transfers/{qId}/inprocess-validation
```

### URL Parameters
- `qId`: QID to check for in-process transfer

## Response Model

### CheckInProcessTransferResponse
```csharp
public class CheckInProcessTransferResponse
{
    public bool IsInprocessExist { get; set; }
}
```

## Test Data

### Success Case
- QID: 94733427819
- Expected: IsInprocessExist = true

### Error Case
- QID: 94733427818
- Expected: IsInprocessExist = false

## Validation Rules

### Success Case Validation
1. Response not null
2. IsInprocessExist is true

### Error Case Validation
1. Response not null
2. IsInprocessExist is false

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Invalid format

2. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

3. **Data State Errors**
   - Inconsistent transfer state
   - Corrupted data
   - Race conditions

## Notes

1. **Purpose**
   - Validates in-process transfer
   - Prevents duplicate submissions
   - Status verification

2. **Response Format**
   - Simple boolean response
   - Clear status indication
   - Null check required

3. **Performance**
   - Quick validation check
   - Status-based query
   - Efficient execution
