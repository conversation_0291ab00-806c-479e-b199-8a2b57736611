# Delete MHDS Tests

## Test Cases

### 1. TestDeleteMhdsByReqNumber

Tests successful deletion of an MHDS request.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteMhdsByReqNumber()
{
    // Arrange
    var getReqNumber = CreateMhdsRequest(22222222270.ToString());
    getReqNumber.AddOrUpdateHeader(JwtClaimsQId, 22222222270);
    var testResponse = await _client.PostAsync<CreateMHDSResponse>(getReqNumber);
    var reqNumber = testResponse?.ReqNumber;
    var request = GetRestRequest("mhds/{reqNumber}");
    request.AddUrlSegment("reqNumber", reqNumber!);
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222270);

    // Act
    var response = await _client.DeleteAsync(request);

    // Assert
    True(response is not null);
    True(response.IsSuccessStatusCode);
    True(response.StatusCode == NoContent);
}
```

### 2. TestDeleteMhdsByReqNumberWithInvalidReqNumber

Tests deletion with invalid request number.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestDeleteMhdsByReqNumberWithInvalidReqNumber()
{
    // Arrange
    var request = GetRestRequest("mhds/{reqNumber}");
    request.AddUrlSegment("reqNumber", "InvalidReqNumber");
    request.Method = Method.Delete;

    // Act
    var response = await _client.ExecuteAsync(request);

    // Assert
    True(response is not null);
    Equal(BadRequest, response.StatusCode);
}
```

## Request Details

### Endpoint
```
DELETE mhds/{reqNumber}
```

### Headers
- `JwtClaimsQId`: "22222222270"

### URL Parameters
- `reqNumber`: MHDS request number

## Test Data

### Success Case
1. Create new MHDS request
2. Get request number
3. Delete using request number
4. Expected: NoContent response

### Error Case
- Request Number: "InvalidReqNumber"
- Expected: BadRequest response

## Validation Rules

### Success Case Validation
1. Response not null
2. Success status code
3. NoContent status code

### Error Case Validation
1. Response not null
2. BadRequest status code

## Error Cases

1. **Invalid Request Number**
   - Non-existent request number
   - Malformed request number
   - Already deleted request

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims
   - Insufficient permissions

3. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

## Notes

1. **Test Setup**
   - Creates test data first
   - Verifies creation
   - Performs deletion
   - Verifies deletion

2. **Cleanup**
   - Automatic cleanup via NoContent
   - No additional cleanup needed
   - System handles cascading deletes

3. **Performance**
   - Multiple API calls
   - Async operations
   - Error handling
