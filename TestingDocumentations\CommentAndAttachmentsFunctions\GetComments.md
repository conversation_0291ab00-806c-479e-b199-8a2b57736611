# Get Comments Tests

## Overview

These tests verify the functionality of retrieving comments from the system. The test suite covers various scenarios including filtering, pagination, and error handling.

## Test Cases

### TestGetComments_ValidRequest_ReturnsOk

Tests the successful retrieval of comments for a valid entity.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetComments_ValidRequest_ReturnsOk()
{
    // Arrange
    var entityId = "TEST-123";
    var request = GetRestRequest($"comments/{entityId}");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.ExecuteAsync<List<CommentResponse>>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
    NotNull(response.Data);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: Valid entity ID
- **Expected Output**: 
  - 200 OK response
  - List of comments
- **Validation Points**:
  - Response success
  - Data presence
  - Content validation

### TestGetComments_WithPagination_ReturnsOk

Tests comment retrieval with pagination parameters.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetComments_WithPagination_ReturnsOk()
{
    // Arrange
    var request = GetRestRequest("comments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    request.AddQueryParameter("page", "1");
    request.AddQueryParameter("pageSize", "10");
    request.AddQueryParameter("entityId", "TEST-123");

    // Act
    var response = await _client.ExecuteAsync<PaginatedResponse<CommentResponse>>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    NotNull(response.Data);
    NotNull(response.Data.Items);
    Equal(10, response.Data.PageSize);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Pagination parameters
  - Entity ID
- **Expected Output**: 
  - Paginated response
  - Comment list
- **Validation Points**:
  - Page size
  - Total count
  - Data validity

## Request/Response Models

### CommentFilter
```csharp
public class CommentFilter
{
    public string? EntityId { get; set; }
    public string? Type { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}
```

### PaginatedResponse<T>
```csharp
public class PaginatedResponse<T>
{
    public List<T> Items { get; set; }
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
}
```

## Query Parameters

1. **Pagination**
   ```csharp
   page      // Page number (default: 1)
   pageSize  // Items per page (default: 10)
   ```

2. **Filtering**
   ```csharp
   entityId  // Filter by entity
   type      // Comment type
   fromDate  // Start date
   toDate    // End date
   ```

3. **Sorting**
   ```csharp
   sortBy    // Field to sort by
   sortOrder // asc/desc
   ```

## Implementation Details

### 1. Query Building
```csharp
private IQueryable<Comment> BuildCommentQuery(CommentFilter filter)
{
    var query = _context.Comments.AsQueryable();

    if (!string.IsNullOrEmpty(filter.EntityId))
        query = query.Where(c => c.EntityId == filter.EntityId);

    if (!string.IsNullOrEmpty(filter.Type))
        query = query.Where(c => c.Type == filter.Type);

    if (filter.FromDate.HasValue)
        query = query.Where(c => c.CreatedAt >= filter.FromDate);

    if (filter.ToDate.HasValue)
        query = query.Where(c => c.CreatedAt <= filter.ToDate);

    return query;
}
```

### 2. Pagination Implementation
```csharp
private async Task<PaginatedResponse<T>> CreatePaginatedResponse<T>(
    IQueryable<T> query,
    int page,
    int pageSize)
{
    var totalCount = await query.CountAsync();
    var items = await query
        .Skip((page - 1) * pageSize)
        .Take(pageSize)
        .ToListAsync();

    return new PaginatedResponse<T>
    {
        Items = items,
        TotalCount = totalCount,
        Page = page,
        PageSize = pageSize,
        TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
    };
}
```

### 3. Response Mapping
```csharp
private CommentResponse MapToResponse(Comment comment)
{
    return new CommentResponse
    {
        CommentId = comment.Id,
        EntityId = comment.EntityId,
        Content = comment.Content,
        Type = comment.Type,
        CreatedAt = comment.CreatedAt,
        Attachments = MapAttachments(comment.Attachments)
    };
}
```

## Performance Optimization

### 1. Query Optimization
```csharp
private IQueryable<Comment> OptimizeQuery(IQueryable<Comment> query)
{
    return query
        .Include(c => c.Attachments)
        .AsSplitQuery()
        .AsNoTracking();
}
```

### 2. Caching Strategy
```csharp
private async Task<List<CommentResponse>> GetCachedComments(string entityId)
{
    var cacheKey = $"comments_{entityId}";
    return await _cache.GetOrAddAsync(
        cacheKey,
        async () => await FetchComments(entityId),
        TimeSpan.FromMinutes(5)
    );
}
```

## Error Handling

### 1. Parameter Validation
```csharp
private void ValidateParameters(CommentFilter filter)
{
    if (filter.Page < 1)
        throw new ArgumentException("Page must be greater than 0");

    if (filter.PageSize < 1 || filter.PageSize > MaxPageSize)
        throw new ArgumentException($"PageSize must be between 1 and {MaxPageSize}");

    if (filter.FromDate > filter.ToDate)
        throw new ArgumentException("FromDate cannot be later than ToDate");
}
```

### 2. Not Found Handling
```csharp
private async Task<Comment> GetCommentOrThrow(string commentId)
{
    var comment = await _context.Comments.FindAsync(commentId);
    if (comment == null)
        throw new NotFoundException($"Comment {commentId} not found");
    return comment;
}
```

## Notes

- Implements efficient pagination
- Supports flexible filtering
- Optimizes database queries
- Implements caching
- Handles all error cases

## Recommendations

1. **Query Optimization**
   - Use appropriate indexes
   - Implement query caching
   - Optimize includes

2. **Response Size**
   - Implement compression
   - Limit attachment data
   - Use projection

3. **Caching Strategy**
   - Cache common queries
   - Implement cache invalidation
   - Use distributed cache
