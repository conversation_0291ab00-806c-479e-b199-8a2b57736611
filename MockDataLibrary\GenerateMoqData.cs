﻿using Bogus;

using MockDataLibrary.MockModels;

using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;

namespace MockDataLibrary;

public static class GenerateMoqData
{
    public static long? ToLong(this string value)
    {
        return !string.IsNullOrWhiteSpace(value) && long.TryParse(value, out long result) ? result : (long?)null;
    }

    public static long RandomNumber(int length) => new Faker().Internet.Random.String2(length, "0123456789").ToLong() ?? 0;
    public static string FirstNameEnglish() => new Faker().Name.FirstName();
    public static string MiddleNameEnglish() => new Faker().Name.FirstName();
    public static string LastNameEnglish() => new Faker().Name.LastName();
    public static string FirstNameArabic() => new Faker("ar").Name.FirstName();
    public static string MiddleNameArabic() => new Faker("ar").Name.FirstName();
    public static string LastNameArabic() => new Faker("ar").Name.LastName();
    public static string FullNameEnglish() => $"{FirstNameEnglish()} {MiddleNameEnglish()} {LastNameEnglish()}";
    public static string FullNameArabic() => $"{FirstNameArabic()} {MiddleNameArabic()} {LastNameArabic()}";

    public static TempUser GetMoqUser()
    {
        return MoqUser;
    }

    private static TempUser MoqUser { get; } = new()
    {
        UserQId = 22222222272,
        UserEmail = "<EMAIL>",
        UserMobileNumber = "+97470559257"
    };

    public static IEnumerable<VisaType> MockVisaTypes()
    {
        var visaTypes = new List<VisaType>
        {
            new()
            {
                VisaTypeCode = "00",
                VisaDescriptionEn = "Miscellanuous Procedures",
                VisaDescriptionAr = "اجراءات متنوعة"
            },
            new()
            {
                VisaTypeCode = "01",
                VisaDescriptionEn = "Family Residence Entry",
                VisaDescriptionAr = "دخول لاقامة عائلية"
            },
            new()
            {
                VisaTypeCode = "02",
                VisaDescriptionEn = "Called for Work - Personal Sponsor",
                VisaDescriptionAr = "استقدام للعمل- كفالة شخصية"
            },
            new()
            {
                VisaTypeCode = "03",
                VisaDescriptionEn = "Called for Work - Organizational Sponsor",
                VisaDescriptionAr = "استقدام للعمل - كفالة منشأة"
            },
            new()
            {
                VisaTypeCode = "04",
                VisaDescriptionEn = "Work Visit",
                VisaDescriptionAr = "زيارة عمل"
            },
            new()
            {
                VisaTypeCode = "05",
                VisaDescriptionEn = "Personal visit",
                VisaDescriptionAr = "زيارة شخصية"
            },
            new()
            {
                VisaTypeCode = "06",
                VisaDescriptionEn = "Tourism Visit",
                VisaDescriptionAr = "زيارة سياحية"
            },
            new()
            {
                VisaTypeCode = "07",
                VisaDescriptionEn = "Businessmen Visit",
                VisaDescriptionAr = "زيارة رجال الاعمال"
            },
            new()
            {
                VisaTypeCode = "08",
                VisaDescriptionEn = "GCC Residents Visit",
                VisaDescriptionAr = "زيارة مقيمي دول مجلس التعاون"
            },
            new()
            {
                VisaTypeCode = "09",
                VisaDescriptionEn = "GCC Citizens Companions Visit",
                VisaDescriptionAr = "زيارة مرافقي مواطني مجلس التعاون"
            },
            new()
            {
                VisaTypeCode = "10",
                VisaDescriptionEn = "Heavy Vehicles Drivers Visit",
                VisaDescriptionAr = "تأشيرة سائقي الشاحنات"
            },
            new()
            {
                VisaTypeCode = "11",
                VisaDescriptionEn = "Sailors Passing Visa",
                VisaDescriptionAr = "مرور البحارة"
            },
            new()
            {
                VisaTypeCode = "12",
                VisaDescriptionEn = "Passing Visa",
                VisaDescriptionAr = "مرور العبور"
            },
            new()
            {
                VisaTypeCode = "13",
                VisaDescriptionEn = "Special Visa",
                VisaDescriptionAr = "تأشيرة خاصة"
            },
            new()
            {
                VisaTypeCode = "14",
                VisaDescriptionEn = "External Visa",
                VisaDescriptionAr = "تأشيرة خارجية"
            },
            new()
            {
                VisaTypeCode = "15",
                VisaDescriptionEn = "Official Visa",
                VisaDescriptionAr = "تأشيرة رسمية"
            },
            new()
            {
                VisaTypeCode = "16",
                VisaDescriptionEn = "Called for Visit",
                VisaDescriptionAr = "استقدام للزيارة"
            },
            new()
            {
                VisaTypeCode = "18",
                VisaDescriptionEn = "Shared Tourism Visit",
                VisaDescriptionAr = "زيارة سياحية مشتركة"
            },
            new()
            {
                VisaTypeCode = "20",
                VisaDescriptionEn = "New Born Visa",
                VisaDescriptionAr = "تأشيرة أطفال حديثي الولادة"
            },
            new()
            {
                VisaTypeCode = "21",
                VisaDescriptionEn = "Return Visa",
                VisaDescriptionAr = "تأشيرة عودة"
            },
            new()
            {
                VisaTypeCode = "22",
                VisaDescriptionEn = "Student Visa",
                VisaDescriptionAr = "تأشيرة طالب"
            },
            new()
            {
                VisaTypeCode = "23",
                VisaDescriptionEn = "Work Visit for Sailors",
                VisaDescriptionAr = "زيارة عمل للبحارة"
            },
            new()
            {
                VisaTypeCode = "24",
                VisaDescriptionEn = "Property Ownership",
                VisaDescriptionAr = "تملك عقار"
            },
            new()
            {
                VisaTypeCode = "25",
                VisaDescriptionEn = "Investment",
                VisaDescriptionAr = "استثمار"
            },
            new()
            {
                VisaTypeCode = "26",
                VisaDescriptionEn = "Benefiting from a residential unit",
                VisaDescriptionAr = "انتفاع بوحدة سكنية"
            },
            new()
            {
                VisaTypeCode = "28",
                VisaDescriptionEn = "Unknown Visa Type",
                VisaDescriptionAr = "غير معروف"
            },
            new()
            {
                VisaTypeCode = "40",
                VisaDescriptionEn = "WORK",
                VisaDescriptionAr = "تأشيرة العمل"
            },
            new()
            {
                VisaTypeCode = "41",
                VisaDescriptionEn = "FAMILY",
                VisaDescriptionAr = "التأشيرة العائلية"
            },
            new()
            {
                VisaTypeCode = "42",
                VisaDescriptionEn = "TOURIST",
                VisaDescriptionAr = "التأشيرة السياحية"
            },
            new()
            {
                VisaTypeCode = "43",
                VisaDescriptionEn = "GOVERNMENT",
                VisaDescriptionAr = "التأشيرة الحكومية"
            },
            new()
            {
                VisaTypeCode = "44",
                VisaDescriptionEn = "COMERCIAL",
                VisaDescriptionAr = "التأشيرة التجارية"
            },
            new()
            {
                VisaTypeCode = "45",
                VisaDescriptionEn = "TRANSIT",
                VisaDescriptionAr = "تأشيرة المرور"
            },
            new()
            {
                VisaTypeCode = "46",
                VisaDescriptionEn = "SPORT",
                VisaDescriptionAr = "تأشيرة الرياضة"
            },
            new()
            {
                VisaTypeCode = "47",
                VisaDescriptionEn = "MISSION",
                VisaDescriptionAr = "تأشيرة المهمات"
            },
            new()
            {
                VisaTypeCode = "49",
                VisaDescriptionEn = "CONFERENCE",
                VisaDescriptionAr = "تأشيرة المؤتمرات"
            },
            new()
            {
                VisaTypeCode = "NR",
                VisaDescriptionEn = "Not Required",
                VisaDescriptionAr = "غير مطلوب"
            }
        };
        return visaTypes;
    }

    public static IEnumerable<Occupation> MockOccupations()
    {
        int key = 1;
        var occupations = new List<Occupation>();
        for (int x = 1; x < 1000; x++)
        {
            switch (x)
            {
                case < 10:
                    occupations.Add(new Occupation { Id = key++, Code = "00" + x });
                    break;
                case < 100:
                    occupations.Add(new Occupation { Id = key++, Code = "0" + x });
                    break;
                default:
                    occupations.Add(new Occupation { Id = key++, Code = x.ToString() });
                    break;
            }
        }

        return occupations;
    }

    public static IEnumerable<HealthCenter> MockHealthCenters()
    {
        var healthCenters = new List<HealthCenter>
        {
            new() { Id = 1, Name = "Abu Nakhla Health Center" },
            new() { Id = 2, Name = "Abu Bakr Al Siddiq Health Center" },
            new() { Id = 3, Name = "Airport Health Center" },
            new() { Id = 4, Name = "Al Daayen Health Center" },
            new() { Id = 5, Name = "Gharrafat Al Rayyan Health Center" },
            new() { Id = 6, Name = "Leghwairiya Health Center" },
            new() { Id = 7, Name = "Al Jumailiya Health Center" },
            new() { Id = 8, Name = "Al Karaana Health Center" },
            new() { Id = 9, Name = "Al Kaaban Health Center" },
            new() { Id = 10, Name = "Al Khor Health Center" },
            new() { Id = 11, Name = "Leabaib Health Center" },
            new() { Id = 12, Name = "Mesaimeer Health Center" },
            new() { Id = 13, Name = "Madinat Khalifa Health Center" },
            new() { Id = 14, Name = "Al Mashaf Health Center" },
            new() { Id = 15, Name = "Muaither Health Center" },
            new() { Id = 16, Name = "Omar Bin Khattab Health Center" },
            new() { Id = 17, Name = "Qatar University Health Center" },
            new() { Id = 18, Name = "Rawdat Al Khail Health Center" },
            new() { Id = 19, Name = "Al Ruwais Health Center" },
            new() { Id = 20, Name = "Al Rayyan Health Center" },
            new() { Id = 21, Name = "Al Sadd Health Center" },
            new() { Id = 22, Name = "Al Shamal Health Center" },
            new() { Id = 23, Name = "Al Sheehaniya Health Center" },
            new() { Id = 24, Name = "South Wakra Health Center" },
            new() { Id = 25, Name = "Al Thumama Health Center" },
            new() { Id = 26, Name = "Umm Ghuwailina Health Center" },
            new() { Id = 27, Name = "Umm Slal Health Center" },
            new() { Id = 28, Name = "Umm Al Seneem Health Center" },
            new() { Id = 29, Name = "Virtual Care Services Facility" },
            new() { Id = 30, Name = "Al Waab Health Center" },
            new() { Id = 31, Name = "Al Wajbah Health Center" },
            new() { Id = 32, Name = "Al Wakra Health Center" },
            new() { Id = 33, Name = "West Bay Health Center" }
        };
        return healthCenters;
    }

    public static IEnumerable<Language> MockLanguages()
    {
        var languages = new List<Language>
        {
            new() { NatCode = "ak", NatNameEn = "Akan", NatNameAr = "الأكانية" },
            new() { NatCode = "ar", NatNameEn = "Arabic", NatNameAr = "العربية" },
            new() { NatCode = "az", NatNameEn = "Azerbaijani", NatNameAr = "الاذربيجانيه" },
            new() { NatCode = "bn", NatNameEn = "Bengali", NatNameAr = "البنغالية" },
            new() { NatCode = "cs", NatNameEn = "Czech", NatNameAr = "التشيكية" },
            new() { NatCode = "da", NatNameEn = "Danish", NatNameAr = "الدانماركية" },
            new() { NatCode = "de", NatNameEn = "German", NatNameAr = "الألمانية" },
            new() { NatCode = "en", NatNameEn = "English", NatNameAr = "الإنكليزية" },
            new() { NatCode = "es", NatNameEn = "Spanish", NatNameAr = "الإسبانية" },
            new() { NatCode = "fa", NatNameEn = "Persian", NatNameAr = "فارسي" },
            new() { NatCode = "fr", NatNameEn = "French", NatNameAr = "الفرنسية" },
            new() { NatCode = "gu", NatNameEn = "Gujarati", NatNameAr = "الجوجاراتية" },
            new() { NatCode = "hi", NatNameEn = "Hindi", NatNameAr = "الهندية" },
            new() { NatCode = "hr", NatNameEn = "Croatian", NatNameAr = "الكرواتية" },
            new() { NatCode = "id", NatNameEn = "Indonesian", NatNameAr = "الإندونيسية" },
            new() { NatCode = "ig", NatNameEn = "Igbo", NatNameAr = "إيغبو" },
            new() { NatCode = "kn", NatNameEn = "Kannada", NatNameAr = "الكندية" },
            new() { NatCode = "ks", NatNameEn = "Kashmiri", NatNameAr = "الكشميري" },
            new() { NatCode = "ku", NatNameEn = "Kurdish", NatNameAr = "الكرديه" },
            new() { NatCode = "ml", NatNameEn = "Malayalam", NatNameAr = "المالايلامية" },
            new() { NatCode = "mr", NatNameEn = "Marathi", NatNameAr = "الماراثية" },
            new() { NatCode = "ms", NatNameEn = "Malay", NatNameAr = "الماليزية" },
            new() { NatCode = "my", NatNameEn = "Burmese", NatNameAr = "البورميه" },
            new() { NatCode = "ne", NatNameEn = "Nepali", NatNameAr = "النيباليه" },
            new() { NatCode = "nl", NatNameEn = "Dutch", NatNameAr = "الهولندية" },
            new() { NatCode = "no", NatNameEn = "Norwegian", NatNameAr = "النرويجية ‏" },
            new() { NatCode = "pa", NatNameEn = "Punjabi", NatNameAr = "البنجابية" },
            new() { NatCode = "pl", NatNameEn = "Polish", NatNameAr = "البولندية" },
            new() { NatCode = "ps", NatNameEn = "Pashto", NatNameAr = "الباشتو" },
            new() { NatCode = "ro", NatNameEn = "Romanian", NatNameAr = "الرومانية" },
            new() { NatCode = "ru", NatNameEn = "Russian", NatNameAr = "الروسية" },
            new() { NatCode = "sd", NatNameEn = "Sindhi", NatNameAr = "السندهيه" },
            new() { NatCode = "si", NatNameEn = "Sinhala", NatNameAr = "السنهالية" },
            new() { NatCode = "sk", NatNameEn = "Slovak", NatNameAr = "السلوفاكية" },
            new() { NatCode = "so", NatNameEn = "Somali", NatNameAr = "الصوماليه" },
            new() { NatCode = "sv", NatNameEn = "Swedish", NatNameAr = "السويدية" },
            new() { NatCode = "sw", NatNameEn = "Swahili", NatNameAr = "السواحلية" },
            new() { NatCode = "ta", NatNameEn = "Tamil", NatNameAr = "التاميلية" },
            new() { NatCode = "te", NatNameEn = "Telugu", NatNameAr = "التيلوغية" },
            new() { NatCode = "tl", NatNameEn = "Tagalog", NatNameAr = "التغالوغيه" },
            new() { NatCode = "tr", NatNameEn = "Turkish", NatNameAr = "التركية" },
            new() { NatCode = "uk", NatNameEn = "Ukrainian", NatNameAr = "الأوكرانية" },
            new() { NatCode = "ur", NatNameEn = "Urdu", NatNameAr = "الأوردية" },
            new() { NatCode = "yo", NatNameEn = "Yoruba", NatNameAr = "اليوروبا" }
        };
        return languages;
    }

    public static IEnumerable<Nationality> MockNationality()
    {
        var nationalities = new List<Nationality>
        {
            new() { NatCode = "000", NatNameEn = "No Nationality", NatNameAr = "لا جنسية" },
            new() { NatCode = "004", NatNameEn = "Afghanistan", NatNameAr = "أفغانستان" },
            new() { NatCode = "008", NatNameEn = "Albania", NatNameAr = "ألبانيا" },
            new() { NatCode = "010", NatNameEn = "Antarctica", NatNameAr = "أنتاركتيكا" },
            new() { NatCode = "012", NatNameEn = "Algeria", NatNameAr = "الجزائر" },
            new() { NatCode = "016", NatNameEn = "American Samoa", NatNameAr = "ساموا الأمريكية" },
            new() { NatCode = "020", NatNameEn = "Andorra", NatNameAr = "أندورا" },
            new() { NatCode = "024", NatNameEn = "Angola", NatNameAr = "أنغولا" },
            new() { NatCode = "028", NatNameEn = "Antigua and Barbuda", NatNameAr = "أنتيغوا وبربودا" },
            new() { NatCode = "031", NatNameEn = "Azerbaijan", NatNameAr = "أذربيجان" },
            new() { NatCode = "032", NatNameEn = "Argentina", NatNameAr = "الأرجنتين" },
            new() { NatCode = "036", NatNameEn = "Australia", NatNameAr = "أستراليا" },
            new() { NatCode = "040", NatNameEn = "Austria", NatNameAr = "النمسا" },
            new() { NatCode = "044", NatNameEn = "Bahamas", NatNameAr = "الباهاما" },
            new() { NatCode = "048", NatNameEn = "Bahrain", NatNameAr = "البحرين" },
            new() { NatCode = "050", NatNameEn = "Bangladesh", NatNameAr = "بنغلاديش" },
            new() { NatCode = "051", NatNameEn = "Armenia", NatNameAr = "أرمينيا" },
            new() { NatCode = "052", NatNameEn = "Barbados", NatNameAr = "بربادوس" },
            new() { NatCode = "056", NatNameEn = "Belgium", NatNameAr = "بلجيكا" },
            new() { NatCode = "060", NatNameEn = "Bermuda", NatNameAr = "برمودا" },
            new() { NatCode = "064", NatNameEn = "Bhutan", NatNameAr = "بوتان" },
            new() { NatCode = "068", NatNameEn = "Bolivia", NatNameAr = "بوليفيا" },
            new() { NatCode = "070", NatNameEn = "Bosnia and Herzegovina", NatNameAr = "البوسنة والهرسك" },
            new() { NatCode = "072", NatNameEn = "Botswana", NatNameAr = "بوتسوانا" },
            new() { NatCode = "074", NatNameEn = "Bouvet Island", NatNameAr = "جزيرة بوفيت" },
            new() { NatCode = "076", NatNameEn = "Brazil", NatNameAr = "البرازيل" },
            new() { NatCode = "084", NatNameEn = "Belize", NatNameAr = "بليز" },
            new()
            {
                NatCode = "086", NatNameEn = "British Indian Ocean Territory",
                NatNameAr = "إقليم المحيط الهندي البريطاني"
            },
            new() { NatCode = "090", NatNameEn = "Solomon Islands", NatNameAr = "جزر سليمان" },
            new() { NatCode = "092", NatNameEn = "Virgin Islands, British", NatNameAr = "جزر فيرجن البريطانية" },
            new() { NatCode = "096", NatNameEn = "Brunei Darussalam", NatNameAr = "بروناي دار السلام" },
            new() { NatCode = "100", NatNameEn = "Bulgaria", NatNameAr = "بلغاريا" },
            new() { NatCode = "104", NatNameEn = "Myanmar", NatNameAr = "ميانمار" },
            new() { NatCode = "108", NatNameEn = "Burundi", NatNameAr = "بوروندي" },
            new() { NatCode = "112", NatNameEn = "Belarus", NatNameAr = "روسيا البيضاء" },
            new() { NatCode = "116", NatNameEn = "Cambodia", NatNameAr = "كمبوديا" },
            new() { NatCode = "120", NatNameEn = "Cameroon", NatNameAr = "الكاميرون" },
            new() { NatCode = "124", NatNameEn = "Canada", NatNameAr = "كندا" },
            new() { NatCode = "132", NatNameEn = "Cape Verde", NatNameAr = "الرأس الأخضر" },
            new() { NatCode = "136", NatNameEn = "Cayman Islands", NatNameAr = "جزر كايمان" },
            new() { NatCode = "140", NatNameEn = "Central African Republic", NatNameAr = "جمهورية أفريقيا الوسطى" },
            new() { NatCode = "144", NatNameEn = "Sri Lanka", NatNameAr = "سريلانكا" },
            new() { NatCode = "148", NatNameEn = "Chad", NatNameAr = "تشاد" },
            new() { NatCode = "152", NatNameEn = "Chile", NatNameAr = "تشيلي" },
            new() { NatCode = "156", NatNameEn = "China", NatNameAr = "الصين" },
            new() { NatCode = "158", NatNameEn = "Taiwan, Province of China", NatNameAr = "تايوان" },
            new() { NatCode = "162", NatNameEn = "Christmas Island", NatNameAr = "جزيرة الكريسماس" },
            new() { NatCode = "166", NatNameEn = "Cocos (Keeling) Islands", NatNameAr = "جزر كوكوس (كيلينغ)" },
            new() { NatCode = "170", NatNameEn = "Colombia", NatNameAr = "كولومبيا" },
            new() { NatCode = "174", NatNameEn = "Comoros", NatNameAr = "جزر القمر" },
            new() { NatCode = "175", NatNameEn = "Mayotte", NatNameAr = "مايوت" },
            new() { NatCode = "178", NatNameEn = "Congo (Brazzaville)", NatNameAr = "جمهورية الكونغو" },
            new() { NatCode = "180", NatNameEn = "Congo (Kinshasa)", NatNameAr = "جمهورية الكونغو الديمقراطية" },
            new() { NatCode = "184", NatNameEn = "Cook Islands", NatNameAr = "جزر كوك" },
            new() { NatCode = "188", NatNameEn = "Costa Rica", NatNameAr = "كوستاريكا" },
            new() { NatCode = "191", NatNameEn = "Croatia", NatNameAr = "كرواتيا" },
            new() { NatCode = "192", NatNameEn = "Cuba", NatNameAr = "كوبا" },
            new() { NatCode = "196", NatNameEn = "Cyprus", NatNameAr = "قبرص" },
            new() { NatCode = "203", NatNameEn = "Czech Republic", NatNameAr = "جمهورية التشيك" },
            new() { NatCode = "204", NatNameEn = "Benin", NatNameAr = "بنين" },
            new() { NatCode = "208", NatNameEn = "Denmark", NatNameAr = "الدنمارك" },
            new() { NatCode = "212", NatNameEn = "Dominica", NatNameAr = "دومينيكا" },
            new() { NatCode = "214", NatNameEn = "Dominican Republic", NatNameAr = "جمهورية الدومينيكان" },
            new() { NatCode = "218", NatNameEn = "Ecuador", NatNameAr = "الإكوادور" },
            new() { NatCode = "222", NatNameEn = "El Salvador", NatNameAr = "السلفادور" },
            new() { NatCode = "226", NatNameEn = "Equatorial Guinea", NatNameAr = "غينيا الاستوائية" },
            new() { NatCode = "231", NatNameEn = "Ethiopia", NatNameAr = "إثيوبيا" },
            new() { NatCode = "232", NatNameEn = "Eritrea", NatNameAr = "إريتريا" },
            new() { NatCode = "233", NatNameEn = "Estonia", NatNameAr = "إستونيا" },
            new() { NatCode = "234", NatNameEn = "Faroe Islands", NatNameAr = "جزر فارو" },
            new() { NatCode = "238", NatNameEn = "Falkland Islands (Malvinas)", NatNameAr = "جزر فوكلاند (مالفيناس)" },
            new()
            {
                NatCode = "239", NatNameEn = "South Georgia and the South Sandwich Islands",
                NatNameAr = "جورجيا الجنوبية وجزر ساندويتش الجنوبية"
            },
            new() { NatCode = "242", NatNameEn = "Fiji", NatNameAr = "فيجي" },
            new() { NatCode = "246", NatNameEn = "Finland", NatNameAr = "فنلندا" },
            new() { NatCode = "248", NatNameEn = "Åland Islands", NatNameAr = "جزر أولاند" },
            new() { NatCode = "250", NatNameEn = "France", NatNameAr = "فرنسا" },
            new() { NatCode = "254", NatNameEn = "French Guiana", NatNameAr = "غويانا الفرنسية" },
            new() { NatCode = "258", NatNameEn = "French Polynesia", NatNameAr = "بولينيزيا الفرنسية" },
            new()
            {
                NatCode = "260", NatNameEn = "French Southern Territories", NatNameAr = "المقاطعات الجنوبية الفرنسية"
            },
            new() { NatCode = "262", NatNameEn = "Djibouti", NatNameAr = "جيبوتي" },
            new() { NatCode = "266", NatNameEn = "Gabon", NatNameAr = "الغابون" },
            new() { NatCode = "268", NatNameEn = "Georgia", NatNameAr = "جورجيا" },
            new() { NatCode = "270", NatNameEn = "Gambia", NatNameAr = "غامبيا" },
            new()
            {
                NatCode = "275", NatNameEn = "Palestinian Territory, Occupied", NatNameAr = "الأراضي الفلسطينية المحتلة"
            },
            new() { NatCode = "276", NatNameEn = "Germany", NatNameAr = "ألمانيا" },
            new() { NatCode = "288", NatNameEn = "Ghana", NatNameAr = "غانا" },
            new() { NatCode = "292", NatNameEn = "Gibraltar", NatNameAr = "جبل طارق" },
            new() { NatCode = "296", NatNameEn = "Kiribati", NatNameAr = "كيريباتي" },
            new() { NatCode = "300", NatNameEn = "Greece", NatNameAr = "اليونان" },
            new() { NatCode = "304", NatNameEn = "Greenland", NatNameAr = "جرينلاند" },
            new() { NatCode = "308", NatNameEn = "Grenada", NatNameAr = "غرينادا" },
            new() { NatCode = "312", NatNameEn = "Guadeloupe", NatNameAr = "جوادلوب" },
            new() { NatCode = "316", NatNameEn = "Guam", NatNameAr = "غوام" },
            new() { NatCode = "320", NatNameEn = "Guatemala", NatNameAr = "غواتيمالا" },
            new() { NatCode = "324", NatNameEn = "Guinea", NatNameAr = "غينيا" },
            new() { NatCode = "328", NatNameEn = "Guyana", NatNameAr = "غيانا" },
            new() { NatCode = "332", NatNameEn = "Haiti", NatNameAr = "هايتي" },
            new()
            {
                NatCode = "334", NatNameEn = "Heard Island and McDonald Islands",
                NatNameAr = "جزيرة هيرد وجزر ماكدونالد"
            },
            new()
            {
                NatCode = "336", NatNameEn = "Holy See (Vatican City State)",
                NatNameAr = "الكرسي الرسولي (دولة الفاتيكان)"
            },
            new() { NatCode = "340", NatNameEn = "Honduras", NatNameAr = "هندوراس" },
            new() { NatCode = "344", NatNameEn = "Hong Kong", NatNameAr = "هونغ كونغ" },
            new() { NatCode = "348", NatNameEn = "Hungary", NatNameAr = "المجر" },
            new() { NatCode = "352", NatNameEn = "Iceland", NatNameAr = "أيسلندا" },
            new() { NatCode = "356", NatNameEn = "India", NatNameAr = "الهند" },
            new() { NatCode = "360", NatNameEn = "Indonesia", NatNameAr = "أندونيسيا" },
            new() { NatCode = "364", NatNameEn = "Iran, Islamic Republic of", NatNameAr = "جمهورية إيران الإسلامية" },
            new() { NatCode = "368", NatNameEn = "Iraq", NatNameAr = "العراق" },
            new() { NatCode = "372", NatNameEn = "Ireland", NatNameAr = "أيرلندا" },
            new() { NatCode = "376", NatNameEn = "Israel", NatNameAr = "إسرائيل" },
            new() { NatCode = "380", NatNameEn = "Italy", NatNameAr = "إيطاليا" },
            new() { NatCode = "384", NatNameEn = "Côte d'Ivoire", NatNameAr = "ساحل العاج" },
            new() { NatCode = "388", NatNameEn = "Jamaica", NatNameAr = "جامايكا" },
            new() { NatCode = "392", NatNameEn = "Japan", NatNameAr = "اليابان" },
            new() { NatCode = "398", NatNameEn = "Kazakhstan", NatNameAr = "كازاخستان" },
            new() { NatCode = "400", NatNameEn = "Jordan", NatNameAr = "الأردن" },
            new() { NatCode = "404", NatNameEn = "Kenya", NatNameAr = "كينيا" },
            new()
            {
                NatCode = "408", NatNameEn = "Korea, Democratic People's Republic of",
                NatNameAr = "جمهورية كوريا الشعبية الديمقراطية"
            },
            new() { NatCode = "410", NatNameEn = "Korea, Republic of", NatNameAr = "جمهورية كوريا" },
            new() { NatCode = "414", NatNameEn = "Kuwait", NatNameAr = "الكويت" },
            new() { NatCode = "417", NatNameEn = "Kyrgyzstan", NatNameAr = "قيرغيزستان" },
            new()
            {
                NatCode = "418", NatNameEn = "Lao People's Democratic Republic",
                NatNameAr = "جمهورية لاو الديمقراطية الشعبية"
            },
            new() { NatCode = "422", NatNameEn = "Lebanon", NatNameAr = "لبنان" },
            new() { NatCode = "426", NatNameEn = "Lesotho", NatNameAr = "ليسوتو" },
            new() { NatCode = "428", NatNameEn = "Latvia", NatNameAr = "لاتفيا" },
            new() { NatCode = "430", NatNameEn = "Liberia", NatNameAr = "ليبيريا" },
            new() { NatCode = "434", NatNameEn = "Libya", NatNameAr = "ليبيا" },
            new() { NatCode = "438", NatNameEn = "Liechtenstein", NatNameAr = "ليختنشتاين" },
            new() { NatCode = "440", NatNameEn = "Lithuania", NatNameAr = "ليتوانيا" },
            new() { NatCode = "442", NatNameEn = "Luxembourg", NatNameAr = "لوكسمبورغ" },
            new() { NatCode = "446", NatNameEn = "Macao", NatNameAr = "ماكاو" },
            new() { NatCode = "450", NatNameEn = "Madagascar", NatNameAr = "مدغشقر" },
            new() { NatCode = "454", NatNameEn = "Malawi", NatNameAr = "ملاوي" },
            new() { NatCode = "458", NatNameEn = "Malaysia", NatNameAr = "ماليزيا" },
            new() { NatCode = "462", NatNameEn = "Maldives", NatNameAr = "جزر المالديف" },
            new() { NatCode = "466", NatNameEn = "Mali", NatNameAr = "مالي" },
            new() { NatCode = "470", NatNameEn = "Malta", NatNameAr = "مالطا" },
            new() { NatCode = "474", NatNameEn = "Martinique", NatNameAr = "مارتينيك" },
            new() { NatCode = "478", NatNameEn = "Mauritania", NatNameAr = "موريتانيا" },
            new() { NatCode = "480", NatNameEn = "Mauritius", NatNameAr = "موريشيوس" },
            new() { NatCode = "484", NatNameEn = "Mexico", NatNameAr = "المكسيك" },
            new() { NatCode = "492", NatNameEn = "Monaco", NatNameAr = "موناكو" },
            new() { NatCode = "496", NatNameEn = "Mongolia", NatNameAr = "منغوليا" },
            new() { NatCode = "498", NatNameEn = "Moldova, Republic of", NatNameAr = "جمهورية مولدوفا" },
            new() { NatCode = "499", NatNameEn = "Montenegro", NatNameAr = "الجبل الأسود" },
            new() { NatCode = "500", NatNameEn = "Montserrat", NatNameAr = "مونتسيرات" },
            new() { NatCode = "504", NatNameEn = "Morocco", NatNameAr = "المغرب" },
            new() { NatCode = "508", NatNameEn = "Mozambique", NatNameAr = "موزمبيق" },
            new() { NatCode = "512", NatNameEn = "Oman", NatNameAr = "عمان" },
            new() { NatCode = "516", NatNameEn = "Namibia", NatNameAr = "ناميبيا" },
            new() { NatCode = "520", NatNameEn = "Nauru", NatNameAr = "ناورو" },
            new() { NatCode = "524", NatNameEn = "Nepal", NatNameAr = "نيبال" },
            new() { NatCode = "528", NatNameEn = "Netherlands", NatNameAr = "هولندا" },
            new() { NatCode = "531", NatNameEn = "Curaçao", NatNameAr = "كوراساو" },
            new() { NatCode = "533", NatNameEn = "Aruba", NatNameAr = "أروبا" },
            new()
            {
                NatCode = "534", NatNameEn = "Sint Maarten (Dutch part)", NatNameAr = "سينت مارتن (الجزء الهولندي)"
            },
            new()
            {
                NatCode = "535", NatNameEn = "Bonaire, Sint Eustatius and Saba",
                NatNameAr = "بونير وسانت أوستاتيوس وسابا"
            },
            new() { NatCode = "540", NatNameEn = "New Caledonia", NatNameAr = "كاليدونيا الجديدة" },
            new() { NatCode = "548", NatNameEn = "Vanuatu", NatNameAr = "فانواتو" },
            new() { NatCode = "554", NatNameEn = "New Zealand", NatNameAr = "نيوزيلندا" },
            new() { NatCode = "558", NatNameEn = "Nicaragua", NatNameAr = "نيكاراغوا" },
            new() { NatCode = "562", NatNameEn = "Niger", NatNameAr = "النيجر" },
            new() { NatCode = "566", NatNameEn = "Nigeria", NatNameAr = "نيجيريا" },
            new() { NatCode = "570", NatNameEn = "Niue", NatNameAr = "نيوي" },
            new() { NatCode = "574", NatNameEn = "Norfolk Island", NatNameAr = "جزيرة نورفولك" },
            new() { NatCode = "578", NatNameEn = "Norway", NatNameAr = "النرويج" },
            new() { NatCode = "580", NatNameEn = "Northern Mariana Islands", NatNameAr = "جزر ماريانا الشمالية" },
            new()
            {
                NatCode = "581", NatNameEn = "United States Minor Outlying Islands",
                NatNameAr = "جزر الولايات المتحدة البعيدة الصغرى"
            },
            new()
            {
                NatCode = "583", NatNameEn = "Micronesia, Federated States of", NatNameAr = "ولايات ميكرونيسيا الموحدة"
            },
            new() { NatCode = "584", NatNameEn = "Marshall Islands", NatNameAr = "جزر مارشال" },
            new() { NatCode = "585", NatNameEn = "Palau", NatNameAr = "بالاو" },
            new() { NatCode = "586", NatNameEn = "Pakistan", NatNameAr = "باكستان" },
            new() { NatCode = "591", NatNameEn = "Panama", NatNameAr = "بنما" },
            new() { NatCode = "598", NatNameEn = "Papua New Guinea", NatNameAr = "بابوا غينيا الجديدة" },
            new() { NatCode = "600", NatNameEn = "Paraguay", NatNameAr = "باراغواي" },
            new() { NatCode = "604", NatNameEn = "Peru", NatNameAr = "بيرو" },
            new() { NatCode = "608", NatNameEn = "Philippines", NatNameAr = "الفلبين" },
            new() { NatCode = "612", NatNameEn = "Pitcairn", NatNameAr = "بيتكيرن" },
            new() { NatCode = "616", NatNameEn = "Poland", NatNameAr = "بولندا" },
            new() { NatCode = "620", NatNameEn = "Portugal", NatNameAr = "البرتغال" },
            new() { NatCode = "624", NatNameEn = "Guinea-Bissau", NatNameAr = "غينيا بيساو" },
            new() { NatCode = "626", NatNameEn = "Timor-Leste", NatNameAr = "تيمور الشرقية" },
            new() { NatCode = "630", NatNameEn = "Puerto Rico", NatNameAr = "بورتوريكو" },
            new() { NatCode = "634", NatNameEn = "Qatar", NatNameAr = "قطر" },
            new() { NatCode = "638", NatNameEn = "Réunion", NatNameAr = "لا ريونيون" },
            new() { NatCode = "642", NatNameEn = "Romania", NatNameAr = "رومانيا" },
            new() { NatCode = "643", NatNameEn = "Russian Federation", NatNameAr = "الاتحاد الروسي" },
            new() { NatCode = "646", NatNameEn = "Rwanda", NatNameAr = "رواندا" },
            new() { NatCode = "652", NatNameEn = "Saint Barthélemy", NatNameAr = "سان بارتيليمي" },
            new()
            {
                NatCode = "654", NatNameEn = "Saint Helena, Ascension and Tristan da Cunha",
                NatNameAr = "سانت هيلانة وأسينشين وتريستان دا كونها"
            },
            new() { NatCode = "659", NatNameEn = "Saint Kitts and Nevis", NatNameAr = "سانت كيتس ونيفيس" },
            new() { NatCode = "660", NatNameEn = "Anguilla", NatNameAr = "أنغويلا" },
            new() { NatCode = "662", NatNameEn = "Saint Lucia", NatNameAr = "سانت لوسيا" },
            new()
            {
                NatCode = "663", NatNameEn = "Saint Martin (French part)", NatNameAr = "سانت مارتن (الجزء الفرنسي)"
            },
            new() { NatCode = "666", NatNameEn = "Saint Pierre and Miquelon", NatNameAr = "سان بيير وميكلون" },
            new()
            {
                NatCode = "670", NatNameEn = "Saint Vincent and the Grenadines", NatNameAr = "سانت فنسنت وجزر غرينادين"
            },
            new() { NatCode = "674", NatNameEn = "San Marino", NatNameAr = "سان مارينو" },
            new() { NatCode = "678", NatNameEn = "Sao Tome and Principe", NatNameAr = "ساو تومي وبرينسيبي" },
            new() { NatCode = "682", NatNameEn = "Saudi Arabia", NatNameAr = "المملكة العربية السعودية" },
            new() { NatCode = "686", NatNameEn = "Senegal", NatNameAr = "السنغال" },
            new() { NatCode = "688", NatNameEn = "Serbia", NatNameAr = "صربيا" },
            new() { NatCode = "690", NatNameEn = "Seychelles", NatNameAr = "سيشيل" },
            new() { NatCode = "694", NatNameEn = "Sierra Leone", NatNameAr = "سيراليون" },
            new() { NatCode = "702", NatNameEn = "Singapore", NatNameAr = "سنغافورة" },
            new() { NatCode = "703", NatNameEn = "Slovakia", NatNameAr = "سلوفاكيا" },
            new() { NatCode = "704", NatNameEn = "Viet Nam", NatNameAr = "فيتنام" },
            new() { NatCode = "705", NatNameEn = "Slovenia", NatNameAr = "سلوفينيا" },
            new() { NatCode = "706", NatNameEn = "Somalia", NatNameAr = "الصومال" },
            new() { NatCode = "710", NatNameEn = "South Africa", NatNameAr = "جنوب أفريقيا" },
            new() { NatCode = "716", NatNameEn = "Zimbabwe", NatNameAr = "زيمبابوي" },
            new() { NatCode = "724", NatNameEn = "Spain", NatNameAr = "إسبانيا" },
            new() { NatCode = "728", NatNameEn = "South Sudan", NatNameAr = "جنوب السودان" },
            new() { NatCode = "729", NatNameEn = "Sudan", NatNameAr = "السودان" },
            new() { NatCode = "732", NatNameEn = "Western Sahara", NatNameAr = "الصحراء الغربية" },
            new() { NatCode = "740", NatNameEn = "Suriname", NatNameAr = "سورينام" },
            new() { NatCode = "744", NatNameEn = "Svalbard and Jan Mayen", NatNameAr = "سفالبارد ويان ماين" },
            new() { NatCode = "748", NatNameEn = "Swaziland", NatNameAr = "سوازيلاند" },
            new() { NatCode = "752", NatNameEn = "Sweden", NatNameAr = "السويد" },
            new() { NatCode = "756", NatNameEn = "Switzerland", NatNameAr = "سويسرا" },
            new() { NatCode = "760", NatNameEn = "Syrian Arab Republic", NatNameAr = "الجمهورية العربية السورية" },
            new() { NatCode = "762", NatNameEn = "Tajikistan", NatNameAr = "طاجيكستان" },
            new() { NatCode = "764", NatNameEn = "Thailand", NatNameAr = "تايلاند" },
            new() { NatCode = "768", NatNameEn = "Togo", NatNameAr = "توغو" },
            new() { NatCode = "772", NatNameEn = "Tokelau", NatNameAr = "توكيلاو" },
            new() { NatCode = "776", NatNameEn = "Tonga", NatNameAr = "تونغا" },
            new() { NatCode = "780", NatNameEn = "Trinidad and Tobago", NatNameAr = "ترينيداد وتوباغو" },
            new() { NatCode = "784", NatNameEn = "United Arab Emirates", NatNameAr = "الإمارات العربية المتحدة" },
            new() { NatCode = "788", NatNameEn = "Tunisia", NatNameAr = "تونس" },
            new() { NatCode = "792", NatNameEn = "Turkey", NatNameAr = "تركيا" },
            new() { NatCode = "795", NatNameEn = "Turkmenistan", NatNameAr = "تركمانستان" },
            new() { NatCode = "796", NatNameEn = "Turks and Caicos Islands", NatNameAr = "جزر تركس وكايكوس" },
            new() { NatCode = "798", NatNameEn = "Tuvalu", NatNameAr = "توفالو" },
            new() { NatCode = "800", NatNameEn = "Uganda", NatNameAr = "أوغندا" },
            new() { NatCode = "804", NatNameEn = "Ukraine", NatNameAr = "أوكرانيا" },
            new()
            {
                NatCode = "807", NatNameEn = "Macedonia, the former Yugoslav Republic of",
                NatNameAr = "مقدونيا، جمهورية يوغوسلافيا السابقة"
            },
            new() { NatCode = "818", NatNameEn = "Egypt", NatNameAr = "مصر" },
            new() { NatCode = "826", NatNameEn = "United Kingdom", NatNameAr = "المملكة المتحدة" },
            new() { NatCode = "831", NatNameEn = "Guernsey", NatNameAr = "غيرنزي" },
            new() { NatCode = "832", NatNameEn = "Jersey", NatNameAr = "جيرسي" },
            new() { NatCode = "833", NatNameEn = "Isle of Man", NatNameAr = "جزيرة آيل أوف مان" },
            new()
            {
                NatCode = "834", NatNameEn = "Tanzania, United Republic of", NatNameAr = "تنزانيا، جمهورية المتحدة"
            },
            new() { NatCode = "840", NatNameEn = "United States", NatNameAr = "الولايات المتحدة" },
            new() { NatCode = "850", NatNameEn = "Virgin Islands, U.S.", NatNameAr = "جزر فيرجن، الولايات المتحدة" },
            new() { NatCode = "854", NatNameEn = "Burkina Faso", NatNameAr = "بوركينا فاسو" },
            new() { NatCode = "858", NatNameEn = "Uruguay", NatNameAr = "أوروغواي" },
            new() { NatCode = "860", NatNameEn = "Uzbekistan", NatNameAr = "أوزبكستان" },
            new()
            {
                NatCode = "862", NatNameEn = "Venezuela, Bolivarian Republic of",
                NatNameAr = "فنزويلا، جمهورية بوليفارية"
            },
            new() { NatCode = "876", NatNameEn = "Wallis and Futuna", NatNameAr = "واليس وفوتونا" },
            new() { NatCode = "882", NatNameEn = "Samoa", NatNameAr = "ساموا" },
            new() { NatCode = "887", NatNameEn = "Yemen", NatNameAr = "اليمن" },
            new() { NatCode = "894", NatNameEn = "Zambia", NatNameAr = "زامبيا" },
            new() { NatCode = "990", NatNameEn = "Qatary Travel Document", NatNameAr = "وثيقة سفر قطرية" },
            new() { NatCode = "991", NatNameEn = "Republic of Kosovo", NatNameAr = "جمهورية كوسوفو" },
            new() { NatCode = "996", NatNameEn = "Interpol", NatNameAr = "الانتربول. الشرطة الدولية" },
            new() { NatCode = "997", NatNameEn = "Northern Cyprus", NatNameAr = "شمال قبرص" },
            new() { NatCode = "998", NatNameEn = "Travel Document", NatNameAr = "وثيقة سفر" },
            new() { NatCode = "999", NatNameEn = "United Nations", NatNameAr = "الأمم المتحدة" },
            new() { NatCode = "M01", NatNameEn = "East Asian", NatNameAr = "شرق آسيا" },
            new() { NatCode = "M05", NatNameEn = "UnKnown", NatNameAr = "غير معروف" }
        };
        return nationalities;
    }

    public static Image<Rgba32> GenerateSampleImage(int width, int height)
    {
        var image = new Image<Rgba32>(width, height);
        var random = new Random();
        var r = random.Next(0, 255);
        var g = random.Next(0, 255);
        var b = random.Next(0, 255);
        image.Mutate(x => x.BackgroundColor(new Rgba32(r, g, b)));
        return image;
    }
}

public class TempUser
{
    [Key] public long UserQId { get; init; }
    public string? UserEmail { get; init; }
    public string? UserMobileNumber { get; init; }
}