# CheckInProgressPMEAppointmentV1

## Overview
Checks if a PME Appointment request or a 'New' Cerner PME Appointment exists for the given QID.

## Endpoint
- **Route**: `appointments/exists/{qId}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `qId` (path, required): QID of the person to check appointments for
- `EligibleClinicCodes` (header, required): List of eligible clinic codes

## Responses
- **200 OK**: Returns `CheckInProcessConfirmedAppointmentResponse`
  ```json
  {
    "IsInProcessConfirmedRequestExist": boolean
  }
  ```
- **400 Bad Request**: Invalid request parameters
- **404 Not Found**: No appointment found
- **401 Unauthorized**: Authentication failed
- **500 Internal Server Error**: Server error

## Business Logic
1. Checks for any in-progress PME appointment requests for the given QID
2. Retrieves eligible appointment types based on provided clinic codes
3. Checks for confirmed future PME appointments in HMC Live Feeds
4. Returns true if either an in-progress request or confirmed future appointment exists

## Dependencies
- AppointmentContext
- HmcLiveFeedContext
