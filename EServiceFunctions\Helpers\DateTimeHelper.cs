﻿using static System.DateTime;

namespace EServiceFunctions.Helpers;

[ExcludeFromCodeCoverage]
public static class DateTimeHelper
{
    public static DateTime GetCurrentTime() => UtcNow.AddHours(3);

    public static string ToUtcString(this DateTime? dateTime)
    {
        return dateTime is not null ? dateTime.Value.AsUtcString() : EmptyString;
    }

    private static string AsUtcString(this DateTime dateTime)
    {
        return dateTime.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
    }
}