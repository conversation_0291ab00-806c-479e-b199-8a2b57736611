﻿using EServiceFunctions.Models.CommentAndAttachment;
using EServiceFunctions.RequestResponseModels.CommentAndAttachment;

using static UnitTestEServiceFunctions.MockDataForCommentsAndAttachments;

namespace UnitTestEServiceFunctions;

public class TestCommentAndAttachmentContext : IDbContextFactory<CommentAndAttachmentContext>
{
    public CommentAndAttachmentContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<CommentAndAttachmentContext>()
            .UseInMemoryDatabase("TestCommentAndAttachmentDb")
            .Options;

        var context = new CommentAndAttachmentContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.Comment.AddRange(GetComments());
        context.Attachment.AddRange(GetAttachments());
        context.SaveChanges();
        return context;
    }
}

public static class MockDataForCommentsAndAttachments
{
    public static IEnumerable<Comment> GetComments()
    {
        return new List<Comment>
        {
            new()
            {
                Id = 1,
                ReqNumber = "REQ0000001",
                Action = "Action",
                CommentText = "CommentText",
                CommentType = "CommentType",
                CommenterName = "CommenterName",
                SubmitterQId = 28614415181,
                DisplayToSubmitter = true,
                CreatedAt = GetCurrentTime(),
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest"
            },
            new()
            {
                Id = 2,
                ReqNumber = "REQ0000002",
                Action = "Action",
                CommentText = "CommentText",
                CommentType = "CommentType",
                CommenterName = "CommenterName",
                SubmitterQId = 28614415181,
                DisplayToSubmitter = true,
                CreatedAt = GetCurrentTime(),
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest"
            },
            new()
            {
                Id = 3,
                ReqNumber = "REQ0000003",
                Action = "Action",
                CommentText = "CommentText",
                CommentType = "CommentType",
                CommenterName = "CommenterName",
                SubmitterQId = 28614415181,
                DisplayToSubmitter = true,
                CreatedAt = GetCurrentTime(),
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest"
            }
        };
    }

    public static IEnumerable<Attachment> GetAttachments()
    {
        return new List<Attachment>
        {
            new()
            {
                Id = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AC"),
                AttachmentName = "AttachmentName",
                AttachmentType = "AttachmentType",
                AttachmentData = "AttachmentData",
                SubmitterQId = 28614415181,
                CreatedAt = GetCurrentTime(),
                UpdatedAt = GetCurrentTime(),
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest"
            },
            new()
            {
                Id = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AD"),
                AttachmentName = "AttachmentName",
                AttachmentType = "AttachmentType",
                AttachmentData = "AttachmentData",
                SubmitterQId = 28614415181,
                CreatedAt = GetCurrentTime(),
                UpdatedAt = GetCurrentTime(),
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest"
            },
            new()
            {
                Id = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AE"),
                AttachmentName = "AttachmentName",
                AttachmentType = "AttachmentType",
                AttachmentData = "AttachmentData",
                SubmitterQId = 28614415181,
                CreatedAt = GetCurrentTime(),
                UpdatedAt = GetCurrentTime(),
                CreateSource = "UnitTest",
                UpdateSource = "UnitTest"
            }
        };
    }

    public static string GenerateSamplePngImage(int width, int height)
    {
        using var image = new Image<Rgba32>(width, height);
        var random = new Random();
        var r = random.Next(0, 255);
        var g = random.Next(0, 255);
        var b = random.Next(0, 255);
        image.Mutate(x => x.BackgroundColor(new Rgba32(r, g, b)));
        return image.ToBase64String(PngFormat.Instance).Replace("data:image/png;base64,", EmptyString);
    }

    public static Image<Rgba32> GenerateSampleImage(int width, int height)
    {
        var image = new Image<Rgba32>(width, height);
        var random = new Random();
        var r = random.Next(0, 255);
        var g = random.Next(0, 255);
        var b = random.Next(0, 255);
        image.Mutate(x => x.BackgroundColor(new Rgba32(r, g, b)));
        return image;
    }
}

public class UnitTestCommentsAndAttachment
{
    private readonly ILogger<CommentAndAttachmentFunctions> _logger = Mock.Of<ILogger<CommentAndAttachmentFunctions>>();
    private readonly ITestOutputHelper _testOutputHelper;
    private readonly CommentAndAttachmentFunctions _attachmentFunctions;

    public UnitTestCommentsAndAttachment(ITestOutputHelper testOutputHelper)
    {
        var dbContextFactory = new TestCommentAndAttachmentContext();
        _testOutputHelper = testOutputHelper;
        _attachmentFunctions = new CommentAndAttachmentFunctions(dbContextFactory, _logger);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAttachmentItemById_Should_Return_Ok()
    {
        // Arrange
        var attachmentId = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AC").ToString();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        mockHttpRequestData.Headers.Add("X-SendData", "true");

        // Act
        var response = await _attachmentFunctions.GetAttachmentItemById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAttachment_Should_Return_Created()
    {
        // Arrange
        var attachment = new Faker<Attachment>()
            .RuleFor(a => a.Id, Guid.NewGuid)
            .RuleFor(a => a.AttachmentName, f => f.Random.Word())
            .RuleFor(a => a.AttachmentType, f => f.Random.Word())
            .RuleFor(a => a.AttachmentData, GenerateSamplePngImage(500, 500))
            .RuleFor(a => a.SubmitterQId,  LongQId)
            .RuleFor(a => a.CreatedAt, f => f.Date.Past())
            .RuleFor(a => a.UpdatedAt, f => f.Date.Past())
            .RuleFor(a => a.CreateSource, f => f.Random.Word())
            .RuleFor(a => a.UpdateSource, f => f.Random.Word())
            .Generate();

        var inputAttachment = SerializeObject(attachment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: inputAttachment, method: Post);

        // Act
        var response = await _attachmentFunctions.CreateAttachment(mockHttpRequestData);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAttachmentV2_Should_Return_Created()
    {
        // Arrange
        using var outputStream = new MemoryStream();
        var sampleImageFile = GenerateSampleImage(1800, 1800);
        await sampleImageFile.SaveAsPngAsync(outputStream);
        var filename = $"test{new Faker().Name.FirstName()}.png";
        var fileContent = outputStream.ToArray();
        outputStream.Position = 0;
        var boundary = $"--------------------------{GetCurrentTime().Ticks:x}";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(fileContent, filename, $"multipart/form-data; boundary={boundary}", method: Post);

        // Act
        var response = await _attachmentFunctions.CreateAttachmentV2(mockHttpRequestData);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        _testOutputHelper.WriteLine(GetResponseText(result));

        Equal(HttpStatusCode.Created, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAttachmentById_Should_Return_Ok()
    {
        // Arrange
        var attachmentId = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AC").ToString();
        var attachment = new Faker<CreateUpdateAttachmentRequest>()
            .RuleFor(a => a.AttachmentName, f => f.Random.Word())
            .RuleFor(a => a.AttachmentType, f => f.Random.Word())
            .RuleFor(a => a.AttachmentData, GenerateSamplePngImage(500, 500))
            .RuleFor(a => a.SubmitterQId, LongQId)
            .Generate();
        var inputAttachment = SerializeObject(attachment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: inputAttachment, method: Put);

        // Act
        var response = await _attachmentFunctions.UpdateAttachmentById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAttachmentById_Should_Return_BadRequest_For_Invalid_Attachment_Id()
    {
        // Arrange
        var attachmentId = Guid.NewGuid().ToString();
        var attachment = new Faker<CreateUpdateAttachmentRequest>().RuleFor(a => a.AttachmentName, f => f.Random.Word())
            .RuleFor(a => a.AttachmentType, f => f.Random.Word())
            .RuleFor(a => a.AttachmentData, GenerateSamplePngImage(500, 500))
            .RuleFor(a => a.SubmitterQId, LongQId)
            .Generate();
        var inputAttachment = SerializeObject(attachment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: inputAttachment, method: Put);

        // Act
        var response = await _attachmentFunctions.UpdateAttachmentById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteAttachmentById_Should_Return_Ok()
    {
        // Arrange
        var attachmentId = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AC").ToString();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);

        // Act
        var response = await _attachmentFunctions.DeleteAttachmentById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetCommentByReqNumber_Should_Return_Ok()
    {
        // Arrange
        const string reqNumber = "REQ0000001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        mockHttpRequestData.Headers.Add("X-SendData", "true");

        // Act
        var response = await _attachmentFunctions.GetCommentByReqNumber(mockHttpRequestData, reqNumber);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateComment_Should_Return_Ok()
    {
        // Arrange
        var comment = new Faker<CreateCommentRequest>()
            .RuleFor(c => c.ReqNumber, f => f.Random.Word())
            .RuleFor(c => c.Action, f => f.Random.Word())
            .RuleFor(c => c.CommentText, f => f.Random.Word())
            .RuleFor(c => c.CommentType, f => f.Random.Word())
            .RuleFor(c => c.CommenterName, f => f.Random.Word())
            .RuleFor(c => c.SubmitterQId, LongQId)
            .RuleFor(c => c.DisplayToSubmitter, f => f.Random.Bool())
            .RuleFor(c => c.CreatedAt, f => f.Date.Past())
            .Generate();
        var inputComment = SerializeObject(comment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: inputComment, method: Post);

        // Act
        var response = await _attachmentFunctions.CreateComment(mockHttpRequestData);
        //_testOutputHelper.WriteLine(response.ReadBodyToEnd());

        LogResponse(_testOutputHelper, response);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAttachmentByIdV2_Should_Return_OK()
    {
        // Arrange
        var attachmentId = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AE").ToString();
        var image = GenerateSampleImage(1800, 1800);
        var outputStream = new MemoryStream();
        await image.SaveAsPngAsync(outputStream);
        var filename = $"test{new Faker().Name.FirstName()}.png";
        var fileContent = outputStream.ToArray();
        outputStream.Position = 0;
        var boundary = $"--------------------------{GetCurrentTime().Ticks:x}";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(fileContent, filename, $"multipart/form-data; boundary={boundary}", method: Put);

        // Act
        var response = await _attachmentFunctions.UpdateAttachmentByIdV2(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateAttachmentByIdV2_Should_Return_BadRequest_For_Invalid_Attachment_Id()
    {
        // Arrange
        var attachmentId = Guid.NewGuid().ToString();
        var image = GenerateSampleImage(1800, 1800);
        var outputStream = new MemoryStream();
        await image.SaveAsPngAsync(outputStream);
        var filename = $"test{new Faker().Name.FirstName()}.png";
        var fileContent = outputStream.ToArray();
        outputStream.Position = 0;
        var boundary = $"--------------------------{GetCurrentTime().Ticks:x}";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(fileContent, filename, $"multipart/form-data; boundary={boundary}", method: Put);

        // Act
        var response = await _attachmentFunctions.UpdateAttachmentByIdV2(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAttachmentItemById_Should_Return_BadRequest_For_NonExistent_Id()
    {
        // Arrange
        var attachmentId = Guid.NewGuid().ToString();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _attachmentFunctions.GetAttachmentItemById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAttachmentItemById_Should_Return_BadRequest_For_Invalid_Id_Format()
    {
        // Arrange
        var attachmentId = "invalid-guid-format";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _attachmentFunctions.GetAttachmentItemById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetAttachmentItemById_Should_Return_BadRequest_For_Different_SubmitterQId()
    {
        // Arrange
        var attachmentId = Guid.Parse("F0AB229F-44A8-48B7-9637-0284D81843AC").ToString();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, "99999999999"); // Different QId than in the attachment

        // Act
        var response = await _attachmentFunctions.GetAttachmentItemById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateAttachment_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var attachment = new Faker<Attachment>()
            .RuleFor(a => a.Id, Guid.NewGuid)
            .RuleFor(a => a.AttachmentName, f => f.Random.Word())
            .RuleFor(a => a.AttachmentType, f => f.Random.Word())
            .RuleFor(a => a.AttachmentData, GenerateSamplePngImage(500, 500))
            .RuleFor(a => a.SubmitterQId, 99999999999) // Different QId than in JWT claims
            .Generate();

        var inputAttachment = SerializeObject(attachment);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: inputAttachment, method: Post);

        // Act
        var response = await _attachmentFunctions.CreateAttachment(mockHttpRequestData);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteAttachmentById_Should_Return_BadRequest_For_Invalid_Id()
    {
        // Arrange
        var attachmentId = Guid.NewGuid().ToString(); // Non-existent ID
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);

        // Act
        var response = await _attachmentFunctions.DeleteAttachmentById(mockHttpRequestData, attachmentId);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetCommentByReqNumber_Should_Return_BadRequest_For_Empty_ReqNumber()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _attachmentFunctions.GetCommentByReqNumber(mockHttpRequestData, "");
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetCommentByReqNumber_Should_Return_NoContent_For_NonExistent_ReqNumber()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _attachmentFunctions.GetCommentByReqNumber(mockHttpRequestData, "NON_EXISTENT_REQ");
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }
}