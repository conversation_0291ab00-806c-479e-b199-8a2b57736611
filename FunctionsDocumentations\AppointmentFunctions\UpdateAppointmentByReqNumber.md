# UpdateAppointmentByReqNumber

## Overview
Updates an existing appointment request based on the request number.

## Endpoint
- **Route**: `appointments/{reqNumber}`
- **Method**: PUT
- **Authorization**: Function level

## Parameters
- `reqNumber` (path, required): Appointment request number to update

## Request Body
```json
{
  "QId": "string",
  "HcNumber": "string", // REQUIRED - Health Card Number
  "Status": "string",
  "RequestType": "string",
  "Clinic": "string",
  "PrefDate": "date",
  "PrefContactTime": "string",
  ...
}
```

### Required Fields
- **HcNumber**: Health Card Number is mandatory for all appointment updates. Must be a valid alphanumeric string between 5-20 characters.

## Headers
- `EligibleClinicCodes`: List of eligible clinic codes
- Authorization header with submitter's QID

## Responses
- **200 OK**: Returns updated appointment details
  ```json
  {
    "ReqNumber": "string",
    "Status": "string",
    "Message": "string"
  }
  ```
- **400 Bad Request**: Invalid request data or validation failures
  - Missing Health Card Number:
    ```json
    {
      "code": 400,
      "message": "Health Card No is missing for QID {QID} and requestname Appointment"
    }
    ```
  - Invalid Health Card Format:
    ```json
    {
      "code": 400,
      "message": "Health Card No format is invalid for QID {QID} and requestname Appointment"
    }
    ```
- **401 Unauthorized**: Authentication failed
- **404 Not Found**: Appointment not found
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates request number and data
2. Checks authorization
3. Updates appointment record
4. Returns updated appointment details

## Dependencies
- AppointmentContext
- Status entity
