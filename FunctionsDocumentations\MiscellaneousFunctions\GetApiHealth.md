# GetApiHealth

## Overview
Provides a health check endpoint to verify the API's operational status.

## Endpoint
- **Route**: `health`
- **Method**: GET
- **Authorization**: Anonymous

## Response
- **200 OK**: Returns health status
  ```json
  "API is healthy"
  ```
- **400 Bad Request**: Service unavailable
- **500 Internal Server Error**: Server error

## Business Logic
1. Simple health check implementation
2. No database operations
3. Quick response time
4. Anonymous access allowed

## Security Considerations
- Anonymous access permitted
- Basic logging
- No sensitive information exposed

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging

## Usage
- Monitoring systems
- Load balancers
- Health checks
- Service discovery
