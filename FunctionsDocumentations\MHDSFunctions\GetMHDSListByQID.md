# GetMHDSListByQID

## Overview
Retrieves a list of Medicine Home Delivery Service (MHDS) requests for a user and their dependents.

## Endpoint
- **Route**: `mhds/submitter-qid/{qId}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **qId** (path, required): Submitter's Qatar ID number
- **status** (query, optional): Filter by status category
  - InProcess: Submitted, PaymentReceived, ReturnedForPayment
  - Archived: Approved, Cancelled, Returned
- **skip** (query, optional): Number of records to skip
- **take** (query, optional): Number of records to take

## Response
- **200 OK**: Returns list of MHDS requests
  ```json
  [
    {
      "QId": "long",
      "ReqNumber": "string",
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "Status": "string",
      "StatusDescriptionEn": "string",
      "StatusDescriptionAr": "string",
      "SubmittedAt": "datetime"
    }
  ]
  ```
- **204 No Content**: No requests found
- **400 Bad Request**: Invalid parameters
- **401 Unauthorized**: Invalid QID authorization

## Business Logic
1. Validates user authorization against QID
2. Queries MHDSRequestDetails with Status join
3. Filters by:
   - Submitter QID match
   - Optional status category or specific status
4. Implements pagination with skip/take
5. Orders results by status

## Query Optimization
- Uses AsNoTracking for read-only operations
- Efficient joins with Status table
- Pagination implementation
- Selective field projection

## Security Considerations
- Function-level authorization
- QID validation and authorization
- Logging of all operations

## Multilingual Support
- Bilingual names (English/Arabic)
- Bilingual status descriptions

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Unauthorized access logging
