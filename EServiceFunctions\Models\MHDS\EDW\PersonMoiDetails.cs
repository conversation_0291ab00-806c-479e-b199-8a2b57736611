﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.MHDS.EDW;

[ExcludeFromCodeCoverage]
[Table("PERSON_MOI_DETAILS")]
public class PersonMoiDetail
{
    [Key]
    [Column("QID")]
    public string QId { get; set; }
    [Column("RESIDENCY_STATUS")]
    public string? ResidencyStatus { get; set; }
    [Column("NAME_FIRST_ARA")]
    public string? NameFirstAra { get; set; }
    [Column("NAME_MIDDLE_ARA")]
    public string? NameMiddleAra { get; set; }
    [Column("NAME_LAST_ARA")]
    public string? NameLastAra { get; set; }
    [Column("NAME_FIRST_ENG")]
    public string? NameFirstEng { get; set; }
    [Column("NAME_MIDDLE_ENG")]
    public string? NameMiddleEng { get; set; }
    [Column("NAME_LAST_ENG")]
    public string? NameLastEng { get; set; }
    [Column("GENDER")]
    public string? Gender { get; set; }
    [Column("DOB")]
    public DateTime? Dob { get; set; }
    [Column("NATIONALITY_CD")]
    public string? NationalityCode { get; set; }
    [Column("NATIONALITY_ARA")]
    public string? NationalityAra { get; set; }
    [Column("NATIONALITY_ENG")]
    public string? NationalityEng { get; set; }
    [Column("RPR_EXPIRY_DATE")]
    public DateTime? RprExpiryDate { get; set; }
    [Column("SPONSOR_TYPE_CD")]
    public string? SponsorTypeCode { get; set; }
    [Column("SPONSOR_TYPE")]
    public string? SponsorType { get; set; }
    [Column("SPONSOR_ID")]
    public string? SponsorId { get; set; }
    [Column("SPONSOR_NAME_ENG")]
    public string? SponsorNameEng { get; set; }
    [Column("SPONSOR_NAME_ARA")]
    public string? SponsorNameAra { get; set; }
    [Column("SPONSOR_RELATION_CD")]
    public string? SponsorRelationCode { get; set; }
    [Column("SPONSOR_RELATION_ENG")]
    public string? SponsorRelationEng { get; set; }
    [Column("SPONSOR_RELATION_ARA")]
    public string? SponsorRelationAra { get; set; }
    [Column("ADDRESS_ZONE")]
    public int? AddressZone { get; set; }
    [Column("ADDRESS_STREET")]
    public int? AddressStreet { get; set; }
    [Column("ADDRESS_BUILDING")]
    public int? AddressBuilding { get; set; }
    [Column("ADDRESS_ELECTRICITY_NO")]
    public long? AddressElectricityNo { get; set; }
    [Column("CONTACT_NUMBER")]
    public string? ContactNumber { get; set; }
    [Column("EMAIL_ADDRESS")]
    public string? EmailAddress { get; set; }
    [Column("VISA_PROFESSION_CD")]
    public string? VisaProfessionCode { get; set; }
    [Column("PROFESSION_ARA")]
    public string? ProfessionAra { get; set; }
    [Column("PROFESSION_ENG")]
    public string? ProfessionEng { get; set; }
    [Column("PROFESSION_ISO_CD")]
    public string? ProfessionIsoCode { get; set; }
    [Column("CRD_UPDTMS")]
    public DateTime? CrdUpdtms { get; set; }
    [Column("QID_STATUS")]
    public string? QIdStatus { get; set; }
    [Column("SPO_SMP")]
    public DateTime? SpoSmp { get; set; }
    [Column("ADR_UPDSMP")]
    public DateTime? AdrUpdsmp { get; set; }
    [Column("NQP_SMP")]
    public DateTime? NqpSmp { get; set; }
    [Column("VSA_STYEXPDTE")]
    public DateTime? VsaStyexpdte { get; set; }
    [Column("VSA_UPDSMP")]
    public DateTime? VsaUpdsmp { get; set; }
    [Column("ETL_LOAD_DATETIME")]
    public DateTime? EtlLoadDatetime { get; set; }
    [Column("ETL_UPDATE_DATETIME")]
    public DateTime? EtlUpdateDatetime { get; set; }
    [Column("ADDRESS_UNIT_NO")]
    public int? AddressUnitNo { get; set; }
        
}