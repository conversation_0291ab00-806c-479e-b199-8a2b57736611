# Enrollment Functions Test Documentation

## Overview

This document covers the test suite for enrollment-related functionality in the EServices REST API. The tests are implemented in `TestEnrollmentFunctions.cs` and verify various enrollment operations including retrieval, creation, updates, and deletions.

## Test Cases

### 1. Get Enrollment List By QID
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentListByQId()
```
Verifies retrieval of enrollments for a specific QID.
- **Endpoint**: `enrollments/submitter-qid/{qId}`
- **Method**: GET
- **Expected Response**: List of enrollments with details like FNameEn, QId, and Status
- **Validation**:
  - Response not null
  - First enrollment has expected values
  - Status is "Approved"

### 2. Get Enrollment Statistics
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentStatsByQId()
```
Retrieves enrollment statistics for a QID.
- **Endpoint**: `enrollments/submitter-qid/{qId}/stats`
- **Method**: GET
- **Validation**: Count greater than 0

### 3. Check In-Process Enrollment
Two test cases verify the in-process enrollment validation:

#### False Case
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckInProcessEnrollmentFalse()
```
- **Endpoint**: `enrollments/inprocess-validation/{applicantQId}/{submitterQId}`
- **Expected**: IsInprocessExist = false

#### True Case
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckInProcessEnrollmentTrue()
```
- **Expected**: IsInprocessExist = true

### 4. Get Enrollment By Request Number
Two test cases for enrollment retrieval by request number:

#### Success Case
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentItemByReqNumber()
```
- **Endpoint**: `enrollments/{reqNumber}`
- **Validation**:
  - FNameEn = "TEST1"
  - QId = 33322222291
  - Status = CancelledByEServ

#### Not Found Case
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetEnrollmentItemByReqNumberNotFound()
```
- **Expected**: Null response

### 5. Create Enrollment
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCreateEnrollment()
```
Tests enrollment creation with generated test data.
- **Endpoint**: `enrollments`
- **Method**: POST
- **Validation**:
  - Request number length = 11
  - Request number not empty

### 6. Update Enrollment
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateEnrollmentByReqNumber_Should_Return_OK()
```
Tests enrollment update functionality.
- **Endpoint**: `enrollments/{reqNumber}`
- **Method**: PUT
- **Expected**: OK status code

### 7. Delete Enrollment
Two test cases for enrollment deletion:

#### Success Case
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestDeleteEnrollmentByReqNumber()
```
- **Method**: DELETE
- **Validation**: OK status code

#### Bad Request Case
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestDeleteEnrollmentByReqNumber_Should_Return_BadRequest()
```
- **Expected**: BadRequest status code

## Test Data Generation

### Enrollment Generation
```csharp
public static CreateUpdateEnrollmentRequest GenerateEnrollment()
```
Generates test enrollment data using Faker with the following fields:
- QId (11 digits)
- HC Number (10 alphanumeric)
- Names (English and Arabic)
- Nationality
- Attachment IDs
- Submitter information
- Contact details
- Consent

## Common Headers

The tests use these common headers:
- `JwtClaimsQId`: Authentication QID
- `IsApp`: Application identifier
- `RequestOrigin`: Origin identifier for tracking

## Response Models

### GetEnrollmentListResponse
- FNameEn: First name in English
- QId: Identification number
- Status: Enrollment status

### GetEnrollmentStatsResponse
- Count: Number of enrollments

### CheckInProcessEnrollmentResponse
- IsInprocessExist: Boolean indicating in-process status

### GetEnrollmentItemResponse
- FNameEn: First name in English
- QId: Identification number
- Status: Enrollment status

### CreateEnrollmentResponse
- ReqNumber: Generated request number

## Error Handling

The test suite covers various error scenarios:
1. Not found cases for enrollment retrieval
2. Bad request cases for deletion
3. Validation of required fields in responses

## Notes

1. **Test Data**
   - Uses Faker for generating realistic test data
   - Includes both English and Arabic name fields
   - Generates unique attachment IDs

2. **Authentication**
   - Requires JWT QID claims
   - Different QIDs used for different operations

3. **Request Numbers**
   - 11 characters in length
   - Used for updating and deleting enrollments

4. **Status Codes**
   - OK (200) for successful operations
   - BadRequest (400) for invalid operations
   - Null responses for not found cases
