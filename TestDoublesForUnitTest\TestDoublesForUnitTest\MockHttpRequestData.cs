﻿using System.Net;
using System.Security.Claims;

using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;

namespace TestDoublesForUnitTest;

public class MockHttpRequestData(
    FunctionContext functionContext,
    Stream? body = null,
    string method = "GET",
    string? url = null)
    : HttpRequestData(functionContext)
{
    public override Stream Body { get; } = body ?? new MemoryStream();

    public override HttpHeadersCollection Headers { get; } = [];

    public override IReadOnlyCollection<IHttpCookie> Cookies { get; } = new List<IHttpCookie>();

    public override Uri Url { get; } = new(url ?? "https://localhost");

    public override IEnumerable<ClaimsIdentity> Identities { get; } = new List<ClaimsIdentity>();

    public override string Method { get; } = method;

    public override HttpResponseData CreateResponse()
    {
        return new MockHttpResponseData(FunctionContext, HttpStatusCode.OK);
    }
}