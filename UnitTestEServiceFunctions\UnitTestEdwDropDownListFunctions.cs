using EServiceFunctions.Models.FamilyPhysician;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.Appointment;
using EServiceFunctions.RequestResponseModels.EDWDropDownList;
using EServiceFunctions.RequestResponseModels.UserProfile;

using static UnitTestEServiceFunctions.MockDataForEdwDropDownListFunctions;

namespace UnitTestEServiceFunctions;

public class TestEdwDbContextForEdwDropDownList : IDbContextFactory<EDWDbContext>
{
    public EDWDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<EDWDbContext>()
            .UseInMemoryDatabase(databaseName: "TestEdwDbForEdwDropDownList")
            .Options;

        var context = new EDWDbContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.PersonMoiDetail.AddRange(GetPersonMoiDetailsForEdwDropDownList());
        context.UserProfileResponseFromEDW!.AddRange(GetUserProfileResponseEdwDropDownList());
        context.GetMoiDependentsResponse.AddRange(GetGetMoiDependentsResponseForEdwDropDownList());
        context.GetLanguageListResponse.AddRange(GetGetLanguageListResponseEdwDropDownList());
        context.UpcomingConfirmedAppointmentListResponseFromEDW.AddRange(
            GetGetUpcomingConfirmedAppointmentListResponseForEdwDropDownList());
        context.MedicationRefillDetailsProd!.AddRange(GetMedicationRefillDetailsProdEdwDropDownList());
        context.MedicationRefillDetailsStg!.AddRange(GetMedicationRefillDetailsStgEdwDropDownList());

        context.SaveChanges();
        return context;
    }
}

public class TestMrdDbContextForEdw : IDbContextFactory<MRDDbContext>
{
    public MRDDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<MRDDbContext>()
            .UseInMemoryDatabase(databaseName: "TestMrdDbForEdwDropDownList")
            .Options;
        var context = new MRDDbContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.MAP_MOI_OccupationTypes.AddRange(GetOccupationTypesForEdwDropDownList());
        context.MAP_MOI_VisaTypes.AddRange(GetVisaTypeForEdwDropDownList());
        context.MRD_Physician_Specialties.AddRange(GetPhysicianSpecialtiesForEdwDropDownList());
        context.MRD_Nationalities.AddRange(GetNationalitiesForEdwDropDownList());
        context.MRD_Family_Physicians.AddRange(GetFamilyPhysiciansForEdwDropDownList());

        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public static class MockDataForEdwDropDownListFunctions
{
    public static IEnumerable<PersonMoiDetail> GetPersonMoiDetailsForEdwDropDownList()
    {
        var personMoiDetail = new PersonMoiDetail
        {
            QId = QId,
            Dob = new DateTime(1986, 09, 28),
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<PersonMoiDetail> { personMoiDetail };
    }

    public static IEnumerable<UserProfileFromEDW> GetUserProfileResponseEdwDropDownList()
    {
        var userProfileFromEdw = new List<UserProfileFromEDW>
        {
            new()
            {
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber1,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            },
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber2,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            }
        };
        return userProfileFromEdw;
    }

    public static IEnumerable<GetMoiDependentsResponse> GetGetMoiDependentsResponseForEdwDropDownList()
    {
        var getMoiDependentsResponse = new List<GetMoiDependentsResponse>
        {
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 2,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 3,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            }
        };
        return getMoiDependentsResponse;
    }

    public static IEnumerable<GetLanguageListResponse> GetGetLanguageListResponseEdwDropDownList()
    {
        var getLanguageListResponse = new List<GetLanguageListResponse>
        {
            new()
            {
                NatCode = "en",
                NatNameEn = "English",
                NatNameAr = "الإنجليزية"
            },
            new()
            {
                NatCode = "ar",
                NatNameEn = "Arabic",
                NatNameAr = "العربية"
            },
            new()
            {
                NatCode = "fr",
                NatNameEn = "French",
                NatNameAr = "الفرنسية"
            }
        };
        return getLanguageListResponse;
    }

    public static IEnumerable<GetUpcomingConfirmedAppointmentListResponseFromEdw>
        GetGetUpcomingConfirmedAppointmentListResponseForEdwDropDownList()
    {
        var getUpcomingConfirmedAppointmentListResponseFromEdw =
            new List<GetUpcomingConfirmedAppointmentListResponseFromEdw>
            {
                new()
                {
                    AppointmentId = "AP00001",
                    QId = LongQId,
                    QIdExpiryDt = new Faker().Date.Future(),
                    FNameEn = FirstNameEn,
                    MNameEn = MiddleNameEn,
                    LNameEn = LastNameEn,
                    FNameAr = FirstNameAr,
                    MNameAr = MiddleNameAr,
                    LNameAr = LastNameAr,
                    NationalityCode = NationalityCode,
                    NationalityEn = NationalityEn,
                    NationalityAr = NationalityAr,
                    Dob = new Faker().Date.Past(),
                    HCNumber = HcNumber1,
                    HCExpiryDate = new Faker().Date.Future(),
                    GenderCode = GenderCode,
                    GenderEn = GenderEn,
                    GenderAr = GenderAr,
                    PhoneMobile = MobilePhone,
                    AggignedHCntCode = "OBK",
                    AggignedHCntNameEn = "Omar Bin Khattab",
                    AggignedHCntNameAr = "عمر بن الخطاب",
                    AppointmentHCntCode = "OBK",
                    AppointmentHCntNameEn = "Omar Bin Khattab",
                    AppointmentHCntNameAr = "عمر بن الخطاب",
                    ClinicCode = "OBK001",
                    ClinicNameEn = "OBK Clinic 1",
                    ClinicNameAr = "عيادة عمر بن الخطاب 1",
                    GisAddressStreet = new Faker().Address.StreetAddress(),
                    GisAddressBuilding = new Faker().Address.BuildingNumber(),
                    GisAddressZone = new Faker().Address.City(),
                    GisAddressUnit = new Faker().Address.City(),
                    PhysicianId = "123456",
                    PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                    PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                    BookedDateTime = new Faker().Date.Past(),
                    AppointmentDateTime = new Faker().Date.Future(),
                    AppointmentType = "Appointment",
                    AppointmentStatus = "Confirmed",
                    AppointmentLocation = "OBK",
                    AppointmentFacility = "OBK"
                }
            };

        return getUpcomingConfirmedAppointmentListResponseFromEdw;
    }

    public static IEnumerable<MAP_MOI_OccupationType> GetOccupationTypesForEdwDropDownList()
    {
        var occupationType = new MAP_MOI_OccupationType
        {
            OCCUPATION_CODE = OccupationCode,
            DESCRIPTION_ENGLISH = OccupationDescriptionEn,
            DESCRIPTION_ARABIC_MALE = OccupationDescriptionAr,
            DESCRIPTION_ARABIC_FEMALE = OccupationDescriptionAr,
            LAST_UPDATED_DATE = new DateTime(2023, 10, 10)
        };
        return new List<MAP_MOI_OccupationType> { occupationType };
    }

    public static IEnumerable<MAP_MOI_VisaType> GetVisaTypeForEdwDropDownList()
    {
        var visaType = new MAP_MOI_VisaType
        {
            VISA_TYPE = VisaCode,
            DESCRIPTION_ENGLISH = VisaDescriptionEn,
            DESCRIPTION_ARABIC = VisaDescriptionAr,
            LAST_UPDATED_DATE = new DateTime(2023, 10, 10)
        };
        return new List<MAP_MOI_VisaType> { visaType };
    }

    public static IEnumerable<MRD_Nationality> GetNationalitiesForEdwDropDownList()
    {
        var nationality = new MRD_Nationality
        {
            COUNTRY_CODE = NationalityCode,
            COUNTRY_NAME_EN = NationalityEn,
            COUNTRY_NAME_ARB = NationalityAr,
            ALPHA2_CODE = "LK",
            ALPHA3_CODE = "LKA",
            STANDARD_SYSTEM = "ISO 3166-1 alpha-3",
            IS_QATARI = "N",
            IS_GCC = "N",
            IS_MENA = "N",
            REGION = "Asia",
            ACTIVE_FLG = 1,
            LAST_UPDATE_DATE = new DateTime(2023, 10, 10),
            NATIONALITY = NationalityEn
        };

        return new List<MRD_Nationality> { nationality };
    }

    public static IEnumerable<MRD_Physician_Specialty> GetPhysicianSpecialtiesForEdwDropDownList()
    {
        var physicianSpecialty = new MRD_Physician_Specialty
        {
            SPECIALTY_CODE = "001",
            SPECIALTY_NAME_EN = "Gynaecologist",
            SPECIALTY_NAME_ARB = "طبيب نسائية",
            LAST_LOAD_DATE = new DateTime(2023, 10, 10),
            LAST_UPDATE_DATE = new DateTime(2023, 01, 10)
        };

        return new List<MRD_Physician_Specialty> { physicianSpecialty };
    }

    public static IEnumerable<MRD_Family_Physician> GetFamilyPhysiciansForEdwDropDownList()
    {
        var familyPhysician = new MRD_Family_Physician
        {
            CORP_ID = "123456",
            NAME_EN = "Mohamed Rayyan",
            NAME_ARB = "محمد ريان",
            PHCC_EXP_YRS = 5,
            POSITION_EN = "Senior Software Engineer",
            POSITION_ARB = "مهندس برمجيات كبير",
            CLINICAL_TITLE_EN = "Dr.",
            CLINICAL_TITLE_ARB = "د.",
            FAC_CODE = "OBK",
            HEALTH_CENTER = "Omar Bin Khattab",
            QUALIFICATIONS = "MSc in Software Engineering",
            GENDER_CODE = GenderCode,
            LANGUAGE_CODE = "001",
            TOTAL_EXPERIENCE = 5,
            PHYSICIAN_PHOTO = "https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png",
            SPECIALTY_CODE = "001",
            LAST_UPDATE_DATE = new DateTime(2023, 10, 10),
            LAST_LOAD_DATE = new DateTime(2023, 01, 10),
            OTHER_LANGUAGES = "English",
            OTHER_SPECIALITY = "Gynaecologist",
            STATUS = true,
            PHOTO_CONSENT = true,
            PHYSICIAN_PHOTO_BINARY = "    "u8.ToArray()
        };

        return new List<MRD_Family_Physician> { familyPhysician };
    }

    public static IEnumerable<EServPersonasMedicationRefill> GetMedicationRefillDetailsStgEdwDropDownList()
    {
        var medicationRefillDetailsList = new List<EServPersonasMedicationRefill>
        {
            new()
            {
                QId = QId,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 4",
                SupplyDuration = 1,
                PrescriptionOrderId = 1,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId1,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 5",
                SupplyDuration = 1,
                PrescriptionOrderId = 2,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId2,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 6",
                SupplyDuration = 1,
                PrescriptionOrderId = 3,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            }
        };
        return medicationRefillDetailsList;
    }

    public static IEnumerable<MedicationRefillDtls> GetMedicationRefillDetailsProdEdwDropDownList()
    {
        var medicationRefill = new MedicationRefillDtls
        {
            PersonId = 1,
            QId = QId,
            HcNumber = HcNumber1,
            NameFirst = FirstNameEn,
            NameMiddle = MiddleNameEn,
            NameLast = LastNameEn,
            AgeYears = Age,
            MobilePhone = MobilePhone,
            PrescriptionRefillDueDate = new DateTime(2023, 10, 10),
            LastDispenseDate = new DateTime(2023, 08, 10),
            LastDispenseFacCode = "WAJ",
            PrescriptionDate = new DateTime(2023, 10, 10),
            PrescriptionFacCode = "WAJ",
            PrescribedMedication = "mometasone nasaL",
            SupplyDuration = 15,
            PrescriptionOrderId = 8448095541,
            PrescribedByCorpId = "2683232",
            PrescribedByNameEng = "Dr. Aktham Shraiba",
            PrescribedByNameAra = "د. أكثم شريبة",
            RefillsRemaining = 10,
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<MedicationRefillDtls> { medicationRefill };
    }
}

public class UnitTestEdwDropDownListFunctions
{
    private readonly ILogger<EdwDropDownListFunctions> _logger = Mock.Of<ILogger<EdwDropDownListFunctions>>();
    private readonly ITestOutputHelper _testOutputHelper;
    private readonly EdwDropDownListFunctions _edwDropDownListFunctions;

    public UnitTestEdwDropDownListFunctions(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        _edwDropDownListFunctions = new EdwDropDownListFunctions(new TestEdwDbContextForEdwDropDownList(),
            new TestMrdDbContextForEdw(), _logger);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetOccupationList_Should_Return_Ok()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _edwDropDownListFunctions.GetOccupationList(mockHttpRequestData);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());
        
        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetVisaTypeList_Should_Return_Ok()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _edwDropDownListFunctions.GetVisaTypeList(mockHttpRequestData);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());
        
        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }
    
    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetNationalityList_Should_Return_Ok()
    {
        // Arrange
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await _edwDropDownListFunctions.GetNationalityList(mockHttpRequestData);
        _testOutputHelper.WriteLine(response.ReadBodyToEnd());
        
        // Assert
        Equal(HttpStatusCode.OK, response.StatusCode);
    }
}