﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.FamilyPhysician;

[Table("MAP_MOI_VisaType")]
public class MAP_MOI_VisaType
{
    [Key]
    [StringLength(50)]
    [Unicode(false)]
    public string VISA_TYPE { get; set; } = null!;
    [StringLength(255)]
    [Unicode(false)]
    public string? DESCRIPTION_ENGLISH { get; set; }
    [StringLength(255)]
    public string? DESCRIPTION_ARABIC { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LAST_UPDATED_DATE { get; set; }
}