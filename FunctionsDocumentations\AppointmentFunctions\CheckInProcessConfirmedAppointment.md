# CheckInProcessConfirmedAppointment

## Overview
Checks if there are any in-process or confirmed appointments for the given health card number and clinic code.

## HTTP Method
`POST`

## Route
`/appointments/inprocess-confirmed-check`

## Request Body
```json
{
  "HcNumber": "string",
  "ClinicCode": "string"
}
```

### Required Fields
- **HcNumber**: Health Card Number is mandatory. Must be a valid alphanumeric string.
- **ClinicCode**: Clinic Code is mandatory. Must be a valid clinic code.

## Responses
- **200 OK**: Returns CheckInProcessConfirmedAppointmentResponse
  ```json
  {
    "IsInProcessConfirmedRequestExist": true
  }
  ```
- **400 Bad Request**: Invalid request data or missing required fields
- **500 Internal Server Error**: Server error

## Error Handling
- Validates request body is not null or empty
- Validates Health Card Number is provided
- Validates Clinic Code is provided
- Exception handling with default behavior
- Request URL logging
- Operation logging

## Dependencies
- AppointmentContext
- Status entity
- HmcLiveFeedContext for confirmed appointments check

## Business Logic
1. First checks for in-process appointments in the local database
2. If no in-process appointments found, checks for confirmed appointments via external service
3. Returns true if either in-process or confirmed appointments exist

## Notes
- This function was updated to accept HcNumber and ClinicCode from request body instead of path parameters
- Changed from GET to POST method to accommodate request body