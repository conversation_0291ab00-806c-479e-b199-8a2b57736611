﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.Appointment;

[Table("ESEligibleAppointmentType")]
public class ESEligibleAppointmentType
{
    [StringLength(255)]
    [Column("AppointmentType", TypeName = "varchar(255)")]
    public string AppointmentType { get; set; }

    [Required]
    [StringLength(255)]
    [Column("AppointmentClinicEN", TypeName = "varchar(255)")]
    public string AppointmentClinic { get; set; }

    [Required]
    [Column("ClinicCode")]
    public int ClinicCode { get; set; }
}