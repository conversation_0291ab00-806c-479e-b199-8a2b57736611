﻿namespace EServiceFunctions.Models.Transfer;

public partial class TransferContext(DbContextOptions<TransferContext> options) : DbContext(options)
{
    public virtual DbSet<Status>? Status { get; set; }
    public virtual DbSet<Transfer>? Transfer { get; set; }
    public virtual DbSet<TransferReason>? TransferReason { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Status>(entity =>
        {
            entity.HasKey(e => e.Code)
                .HasName("PK__Status__A25C5AA68EAE98DF");

            entity.Property(e => e.Code)
                .HasMaxLength(255)
                .ValueGeneratedNever();

            entity.Property(e => e.DescriptionAr).HasMaxLength(255);

            entity.Property(e => e.DescriptionEn).HasMaxLength(255);

            entity.Property(e => e.Category).HasMaxLength(255);
        });

        modelBuilder.Entity<Transfer>(entity =>
        {
            entity.HasIndex(e => e.QId)
                .HasDatabaseName("IDX_Transfer_QId");

            entity.HasIndex(e => e.ReqNumber)
                .HasDatabaseName("UQ__Transfer__237F3423CD9312D2")
                .IsUnique();

            entity.HasIndex(e => e.SubmitterQId)
                .HasDatabaseName("IDX_Transfer_SubmitterQId");

            entity.Property(e => e.Id).HasColumnName("Id");
            entity.Property(e => e.QId).HasColumnName("QId");
            entity.Property(e => e.FNameEn).HasColumnName("FNameEn").HasMaxLength(255);
            entity.Property(e => e.MNameEn).HasColumnName("MNameEn").HasMaxLength(255);
            entity.Property(e => e.LNameEn).HasColumnName("LNameEn").HasMaxLength(255);
            entity.Property(e => e.FNameAr).HasColumnName("FNameAr").HasMaxLength(255);
            entity.Property(e => e.MNameAr).HasColumnName("MNameAr").HasMaxLength(255);
            entity.Property(e => e.LNameAr).HasColumnName("LNameAr").HasMaxLength(255);
            entity.Property(e => e.ReqNumber).HasColumnName("ReqNumber").HasMaxLength(255);
            entity.Property(e => e.Nationality).HasColumnName("Nationality").HasMaxLength(255);
            entity.Property(e => e.Dob).HasColumnName("Dob").HasColumnType("date");
            entity.Property(e => e.Consent).HasColumnName("Consent");
            entity.Property(e => e.HCNumber).HasColumnName("HCNumber").HasMaxLength(255);
            entity.Property(e => e.BNo).HasColumnName("BNo");
            entity.Property(e => e.ZNo).HasColumnName("ZNo");
            entity.Property(e => e.SNo).HasColumnName("SNo");
            entity.Property(e => e.UNo).HasColumnName("UNo");
            entity.Property(e => e.CurrentHC).HasColumnName("CurrentHC").HasMaxLength(255);
            entity.Property(e => e.CatchmentHC).HasColumnName("CatchmentHC").HasMaxLength(255);
            entity.Property(e => e.PrefHC).HasColumnName("PrefHC").HasMaxLength(255);
            entity.Property(e => e.AttachId1).HasColumnName("AttachId1");
            entity.Property(e => e.AttachId2).HasColumnName("AttachId2");
            entity.Property(e => e.AttachId3).HasColumnName("AttachId3");
            entity.Property(e => e.AttachId4).HasColumnName("AttachId4");
            entity.Property(e => e.AttachId5).HasColumnName("AttachId5");
            entity.Property(e => e.AttachId6).HasColumnName("AttachId6");
            entity.Property(e => e.TransferReason).HasColumnName("TransferReason").HasMaxLength(255);
            entity.Property(e => e.SubmittedBy).HasColumnName("SubmittedBy").HasMaxLength(255);
            entity.Property(e => e.SubmittedAt).HasColumnName("SubmittedAt").HasColumnType("datetime");
            entity.Property(e => e.SubmitterQId).HasColumnName("SubmitterQId");
            entity.Property(e => e.SubmitterEmail).HasColumnName("SubmitterEmail").HasMaxLength(255);
            entity.Property(e => e.SubmitterMobile).HasColumnName("SubmitterMobile").HasMaxLength(255);
            entity.Property(e => e.CreatedAt).HasColumnName("CreatedAt").HasColumnType("datetime").HasDefaultValueSql("(getdate())");
            entity.Property(e => e.UpdatedAt).HasColumnName("UpdatedAt").HasColumnType("datetime");
            entity.Property(e => e.Status).HasColumnName("Status").HasMaxLength(255);
            entity.Property(e => e.StatusInternal).HasColumnName("StatusInternal").HasMaxLength(255);
            entity.Property(e => e.SN).HasColumnName("SN").HasMaxLength(255);
            entity.Property(e => e.GisAddressUpdatedAt).HasColumnName("GisAddressUpdatedAt");
            entity.Property(e => e.IsGisAddressManualyEntered).HasColumnName("IsGisAddressManualyEntered");
            entity.Property(e => e.CreateSource).HasColumnName("CreateSource").HasMaxLength(255);
            entity.Property(e => e.UpdateSource).HasColumnName("UpdateSource").HasMaxLength(255);

            entity.HasOne(d => d.StatusNavigation)
                .WithMany(p => p.Transfer)
                .HasForeignKey(d => d.Status)
                .HasConstraintName("FK__Transfer__Status__6501FCD8");

            entity.HasOne(d => d.TransferReasonNavigation)
                .WithMany(p => p.Transfer)
                .HasForeignKey(d => d.TransferReason)
                .HasConstraintName("FK_Transfer_TransferReason");
        });

        modelBuilder.Entity<TransferReason>(entity =>
        {
            entity.HasKey(e => e.ReasonCode)
                .HasName("PK__Transfer__A6278DA23531AC06");

            entity.Property(e => e.ReasonCode).HasMaxLength(255);

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.Property(e => e.ReasonAr).HasMaxLength(1000);

            entity.Property(e => e.ReasonEn).HasMaxLength(1000);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}