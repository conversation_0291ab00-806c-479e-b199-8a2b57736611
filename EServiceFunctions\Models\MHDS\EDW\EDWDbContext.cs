﻿using EServiceFunctions.RequestResponseModels.Appointment;
using EServiceFunctions.RequestResponseModels.EDWDropDownList;
using EServiceFunctions.RequestResponseModels.UserProfile;

namespace EServiceFunctions.Models.MHDS.EDW;

public class EDWDbContext(DbContextOptions<EDWDbContext> options) : DbContext(options)
{
    public virtual DbSet<MedicationRefillDtls>? MedicationRefillDetailsProd { get; set; }
    public virtual DbSet<EServPersonasMedicationRefill>? MedicationRefillDetailsStg { get; set; }
    public virtual DbSet<PersonMoiDetail>? PersonMoiDetail { get; set; }
    public virtual DbSet<UserProfileFromEDW>? UserProfileResponseFromEDW { get; set; }
    public virtual DbSet<GetMoiDependentsResponse> GetMoiDependentsResponse { get; set; }
    public virtual DbSet<GetLanguageListResponse> GetLanguageListResponse { get; set; }
    public virtual DbSet<GetUpcomingConfirmedAppointmentListResponseFromEdw> UpcomingConfirmedAppointmentListResponseFromEDW { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<MedicationRefillDtls>();
        modelBuilder.Entity<EServPersonasMedicationRefill>();
        modelBuilder.Entity<PersonMoiDetail>();
        modelBuilder.Entity<UserProfileFromEDW>();
        modelBuilder.Entity<GetMoiDependentsResponse>();
        modelBuilder.Entity<GetUpcomingConfirmedAppointmentListResponseFromEdw>();
        modelBuilder.Entity<GetLanguageListResponse>();
    }
}