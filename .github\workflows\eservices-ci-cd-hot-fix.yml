# minimal yaml approach.
# Hot Fix pipeline
name: Release Hot Fix
run-name: Release Hot Fix by @${{ github.actor }}
on:
    workflow_dispatch: 
      inputs:
        TAG_NAME:   #'Release-*********'  # please provide tag name for each run
            required: true
            type: string
        BRANCH:   #'release/bug-fix'  # please provide branch name for each run
           required: true 
           type: string
jobs:
    cicd-job:
        name: EServices RestAPI CI/CD
        uses: public-health-care-center-CORP/WorkflowRepo/.github/workflows/ci-cd-eservices-restapi-hot-fix.yml@main
        secrets: inherit
        with:
           TAG_NAME: ${{inputs.TAG_NAME}}
           BRANCH: ${{inputs.BRANCH}}
