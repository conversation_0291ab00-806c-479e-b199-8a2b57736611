# Release Item Functions API Documentation

This document provides detailed information about the Release Item Functions API endpoints, which handle the retrieval and management of release items with support for bilingual (English/Arabic) descriptions.

## API Endpoints

### Get Release Item List
- **Endpoint**: `GET /releaseitems`
- **Description**: Retrieves a paginated list of release items with optional filtering by item code
- **Authorization**: Function-level authorization required
- **Parameters**:
  - `itemCode` (query, optional): Filter by specific release item code
  - `skip` (query, optional): Number of records to skip for pagination
  - `take` (query, optional): Number of records to take per page
- **Response Format**:
  ```json
  [
    {
      "ItemCode": "string",
      "ItemNameEn": "string",
      "ItemNameAr": "string",
      "CategoryCode": "string",
      "CategoryNameEn": "string",
      "CategoryNameAr": "string"
    }
  ]
  ```
- **Responses**:
  - 200: Success (List<GetReleaseItemListResponse>)
  - 204: No Content (No items found)
- **Implementation Notes**:
  - Supports pagination through skip/take parameters
  - Results are ordered by ItemCode
  - Uses no-tracking queries for better performance
  - Provides bilingual item and category names

## Technical Implementation Details

### Data Sources
- ReleaseItem Context
  - ReleaseItem table

### Performance Considerations
- Asynchronous operations
- No-tracking queries for read operations
- Pagination support
- Efficient filtering by item code

### Error Handling
- Comprehensive exception logging
- Standardized error responses
- Query parameter validation

## Response Models

### GetReleaseItemListResponse
- **ItemCode**: Unique identifier for the release item
- **ItemNameEn**: English name of the item
- **ItemNameAr**: Arabic name of the item
- **CategoryCode**: Code of the item's category
- **CategoryNameEn**: English name of the category
- **CategoryNameAr**: Arabic name of the category

## Features
- Bilingual Support
  - English and Arabic item names
  - English and Arabic category names
- Flexible Querying
  - Optional item code filtering
  - Pagination support
- Efficient Data Retrieval
  - Optimized database queries
  - No-tracking for read operations

## Dependencies
- Entity Framework Core
- Azure Functions
- OpenAPI/Swagger
- Custom extensions for request handling