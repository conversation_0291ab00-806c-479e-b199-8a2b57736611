# GetSpecialityList

## Overview
Retrieves a list of medical specialties with support for filtering and pagination.

## Endpoint
- **Route**: `specialities`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- **spltyCode** (query, optional): Filter by specialty code
- **skip** (query, optional): Number of records to skip for pagination
- **take** (query, optional): Number of records to take for pagination

## Response
- **200 OK**: Returns list of specialties
  ```json
  [
    {
      "SpltyCode": "string",
      "SpltyNameEn": "string",
      "SpltyNameAr": "string"
    }
  ]
  ```
- **204 No Content**: No specialties found

## Business Logic
1. Queries MRD_Physician_Specialties table
2. Applies optional specialty code filter
3. Implements pagination with skip/take
4. Orders results by specialty code
5. Maps to GetSpecialityResponse model

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient pagination implementation
- Ordered query execution

## Data Handling
- Supports multilingual specialty names (English/Arabic)
- Proper null handling for all fields
- Sequential processing for large result sets
