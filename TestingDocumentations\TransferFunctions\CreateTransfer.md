# Create Transfer Tests

## Test Cases

### 1. TestCreateTransfer

Tests creation of a new transfer request.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task<string> TestCreateTransfer()
{
    // Arrange
    var request = GetRestRequest("transfers");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateTransfer");
    request = CreateUpdateTransfer(request);
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);

    // Act
    var response = await _client.PostAsync<CreateTransferResponse>(request);
    response.ThrowIfNull();
    response.ReqNumber.ThrowIfNull();

    // Assert
    NotNull(response);
    True(!response.ReqNumber.IsNullOrEmpty());
    return response.ReqNumber;
}
```

### 2. TestCreateTransferWithInvalidObject

Tests transfer creation with invalid request object.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestCreateTransferWithInvalidObject()
{
    // Arrange
    var request = GetRestRequest("transfers");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateTransfer");
    request.Method = Method.Post;
    
    var reqBody = new CreateUpdateTransferRequest();
    request.AddJsonBody(reqBody);

    // Act
    var response = await _client.ExecuteAsync(request);
    
    // Assert
    True(response.StatusCode == BadRequest);
}
```

## Request Details

### Endpoint
```
POST transfers
```

### Headers
- `JwtClaimsQId`: User QID
- `RequestOrigin`: "UnitTest-CreateTransfer"

## Request Model

### CreateUpdateTransferRequest
```csharp
public class CreateUpdateTransferRequest
{
    // Personal Information
    public long QId { get; set; }
    public string FNameEn { get; set; }
    public string MNameEn { get; set; }
    public string LNameEn { get; set; }
    public string FNameAr { get; set; }
    public string MNameAr { get; set; }
    public string LNameAr { get; set; }
    public string Nationality { get; set; }
    public DateTime Dob { get; set; }
    public bool Consent { get; set; }

    // Healthcare Information
    public string HCNumber { get; set; }
    public string CurrentHC { get; set; }
    public string CatchmentHC { get; set; }
    public string PrefHC { get; set; }

    // Address Information
    public int BNo { get; set; }
    public int ZNo { get; set; }
    public int SNo { get; set; }
    public int UNo { get; set; }

    // Attachments
    public Guid AttachId1 { get; set; }
    public Guid AttachId2 { get; set; }
    public Guid AttachId3 { get; set; }
    public Guid AttachId4 { get; set; }
    public Guid AttachId5 { get; set; }
    public Guid AttachId6 { get; set; }

    // Transfer Details
    public string TransferReason { get; set; }

    // Submission Information
    public string SubmittedBy { get; set; }
    public long SubmitterQId { get; set; }
    public string SubmitterEmail { get; set; }
    public string SubmitterMobile { get; set; }
    public DateTime GisAddressUpdatedAt { get; set; }
    public bool IsGisAddressManualyEntered { get; set; }
}
```

## Response Model

### CreateTransferResponse
```csharp
public class CreateTransferResponse
{
    public string ReqNumber { get; set; }
}
```

## Test Data Generation

### Mock Data Sources
1. Nationalities
2. Health Centers
3. Transfer Reasons

### Data Generation Rules
- QID: 11-digit random number
- Names: English and Arabic versions
- Health Card Number: "HC" + 8-digit number
- Dates: Past dates within 50 years
- Address: Random valid numbers
- GUIDs: New unique identifiers
- Transfer Reason: One of ["TR1", "TR2", "TR3", "TR4"]
- User Info: From mock user

## Validation Rules

### Success Case Validation
1. Response not null
2. Request number generated
3. Request number not empty

### Error Case Validation
1. Status code is BadRequest

## Error Cases

1. **Invalid Data**
   - Missing required fields
   - Invalid field formats
   - Data validation failures

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID
   - Unauthorized access

3. **System Errors**
   - Database errors
   - Service unavailable
   - Attachment processing errors

## Notes

1. **Test Data**
   - Uses Faker for realistic data
   - Consistent test patterns
   - Valid field formats

2. **Request Format**
   - Complex object structure
   - Multiple data categories
   - File attachments

3. **Performance**
   - Data generation overhead
   - Attachment handling
   - Response validation
