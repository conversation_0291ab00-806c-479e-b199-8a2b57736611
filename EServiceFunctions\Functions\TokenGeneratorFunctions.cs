﻿// Commented on - 05-05-2025 

/*
using EServiceFunctions.Models.Shared;
using JWT.Algorithms;
using JWT.Builder;

using Newtonsoft.Json.Linq;

using static System.Web.HttpUtility;

using static EServiceFunctions.Helpers.KeyVaultHelper;
using static EServiceFunctions.Helpers.RsaCryptoHelper;

namespace EServiceFunctions.Functions;

[ExcludeFromCodeCoverage]
public class TokenGeneratorFunctions(ILogger<TokenGeneratorFunctions> logger)
{
    #region GenerateJWT

    [Function("GenerateJWT")]
    [OpenApiOperation(operationId: "Generate JWT Token", tags: ["Ehtheraz"],
        Summary = "Generate JWT token to Validate Ehtheraz request with QID, email & mobile number",
        Description = "Generate JWT token with 30 minutes expiration time")]
    [OpenApiRequestBody("application/json", typeof(TokenCredentialsModel))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(TokenResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> GenerateJwt(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "generate-token")] HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Http triggered to generate JWT token triggered...");
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = DeserializeObject<TokenCredentialsModel>(requestBody);

            if (data is null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Please pass a valid data in the request body");
            }

            if (data.Email.IsNullOrWhiteSpace() || !data.Email.IsValidEmail())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid email address");
            }

            if (data.QId.IsNullOrWhiteSpace() || !data.QId.IsValidQId())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid QID");
            }

            if (data.Mobile.IsNullOrWhiteSpace() || !data.Mobile.IsValidMobileNumber())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid mobile number");
            }
            
            var attributes = new Dictionary<string, string>
            {
                { "UserQID", data.QId },
                { "UserMobileNumber", data.Mobile },
                { "UserEmail", data.Email },
                { "TransactionId", Guid.NewGuid().ToString() }
            };
            
            string key;
            string issuer;

            try
            {
                  key = GetSecret("JwtKey");
                  issuer = GetSecret("Issuer");
            }
            catch 
            {
                 key = JwtKeyForUnitTest;
                 issuer = "SampleIssuerForUnitTestCases";
            }
            
            logger.LogInformation($"Key: {key}");
            logger.LogInformation($"Issuer: {issuer}");
            
            var token = GenerateEncryptedToken(attributes);

            logger.LogInformation($"Generated token: {token}");
            logger.LogInformation($"Ok Object Result: {req.WriteOkResponseAsync(token)}");
            
            return token.IsNullOrWhiteSpace() 
                ? await req.WriteErrorResponseAsync(BadRequest, "Invalid data in the request body")
                : await req.WriteOkResponseAsync(new TokenResponse(token));
            
            string GenerateEncryptedToken(IReadOnlyDictionary<string, string> claims)
            {
                var expiration =  DateTime.UtcNow.AddMinutes(30);
                var epochTime = (int)expiration.Subtract(new DateTime(1970, 1, 1)).TotalSeconds;

                return JwtBuilder.Create()
                    .WithAlgorithm(new HMACSHA256Algorithm())
                    .WithSecret(key)
                    .AddClaim("iss", issuer)
                    .AddClaim("aud", issuer)
                    .AddClaim("exp", epochTime)
                    .AddClaim("UserQID", claims["UserQID"])
                    .AddClaim("UserMobileNumber", claims["UserMobileNumber"])
                    .AddClaim("UserEmail", claims["UserEmail"])
                    .AddClaim("TransactionId", Guid.NewGuid().ToString())
                    .Encode();
            }
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region ValidateJWT

    [Function("ValidateJWT")]
    [OpenApiParameter("token", Description = "Jwt token", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiOperation(operationId: "Validate JWT Token", tags: ["Ehtheraz"],
        Summary = "Validate JWT token to Validate Ehtheraz request with QID, email & mobile number",
        Description = "Validate JWT token with 30 minutes expiration time")]
    [OpenApiResponseWithBody(Accepted, "application/json", typeof(TokenCredentialsModel))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> ValidateJwt(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "validate-token/{token}")]
        HttpRequestData req, string? token)
    {
        logger.LogInformation("C# HTTP trigger function processed a request");

        if (token.IsNullOrWhiteSpace())
        {
            return await req.WriteErrorResponseAsync(BadRequest, "Please provide a valid token in the request body");
        }

        try
        {
            string key;

            try
            {
                key = GetSecret("JwtKey");
            }
            catch 
            {
                key =JwtKeyForUnitTest;
            }

            var json = JwtBuilder.Create()
                .WithAlgorithm(new HMACSHA256Algorithm())
                .WithSecret(key)
                .MustVerifySignature()
                .Decode(token);
            
            var tokenResponse = DeserializeObject<TokenCredentialsModel>(json);
            
            return tokenResponse is not null
                ? await req.WriteOkResponseAsync(tokenResponse)
                : await req.WriteErrorResponseAsync(BadRequest, "Invalid token");
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region RSADecrypt

    [Function("RSADecrypt")]
    [OpenApiOperation(operationId: "Decrypt cipher text", tags: ["Ehtheraz"],
        Summary = "Decrypt the cipher text token and return AppName, QID, email & mobile number",
        Description = "Token should be send via post request body")]
    [OpenApiRequestBody("application/json", typeof(DecryptRequest))]
    [OpenApiResponseWithBody(Accepted, "application/json", typeof(DecryptedResponse))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> RsaDecrypt(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "rsa/decrypt")]
        HttpRequestData req)
    {
        try
        {
            logger.LogInformation("Http triggered to decrypt the cipher text...");
            string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = DeserializeObject<DecryptRequest>(requestBody);

            if (data is null)
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Please pass a valid data in the request body");
            }

            if (data.CipherText.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Please pass a valid cipher text to decrypt");
            }

            string urlDecodedToken = UrlDecode(data.CipherText!).Replace(" ", "+");

            var decryptedData = Decrypt(urlDecodedToken);

            if (decryptedData.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Invalid data");
            }

            var dataDictionary = new Dictionary<string, string>
            {
                { "DecryptedText", decryptedData }
            };

            var json = SerializeObject(dataDictionary);

            if (!IsValidJson(json)) return await req.WriteErrorResponseAsync(BadRequest, "Invalid data");

            var responseObject = DeserializeObject<DecryptedResponse>(json);

            return responseObject!.DecryptedText.IsNullOrWhiteSpace()
                ? await req.WriteErrorResponseAsync(BadRequest, "Invalid data")
                : await req.WriteOkResponseAsync(responseObject);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }
    
    private bool IsValidJson(string jsonString)
    {
        jsonString = jsonString.Trim();
        if ((!jsonString.StartsWith("{") || !jsonString.EndsWith("}")) && 
            (!jsonString.StartsWith("[") || !jsonString.EndsWith("]"))) return false; 
        try
        {
            _ = JToken.Parse(jsonString);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex.Message);
            return false;
        }
    }

    #endregion

    #region DecryptText

    [Function("DecryptText")]
    [OpenApiParameter("base64CipherText", Description = "Cipher text", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiOperation(operationId: "Decrypt the data", tags: ["Ehtheraz"],
        Summary = "Decrypt the cipher text token and return AppName, QID, email & mobile number",
        Description = "Token should be send via get request")]
    [OpenApiResponseWithBody(Accepted, "application/json", typeof(string))]
    [OpenApiResponseWithBody(BadRequest, "application/json", typeof(object))]
    public async Task<HttpResponseData> DecryptText(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "decrypt-text/{base64CipherText}")]
        HttpRequestData req, string base64CipherText)
    {
        try
        {
            logger.LogInformation("C# HTTP trigger function processed a request");

            if (base64CipherText.IsNullOrWhiteSpace())
            {
                return await req.WriteErrorResponseAsync(BadRequest, "Please provide a valid token in the request body"); 
            }

            string urlDecodedToken = UrlDecode(base64CipherText).Replace(" ", "+");
            var decryptedData = EncryptAsBase64(urlDecodedToken);
            
            return decryptedData.IsNullOrWhiteSpace()
                ? await req.WriteErrorResponseAsync(BadRequest, "Invalid data")
                : await req.WriteOkResponseAsync(decryptedData);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
    
}*/