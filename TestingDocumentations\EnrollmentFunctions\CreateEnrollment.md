# Create Enrollment Tests

## Test Case: TestCreateEnrollment

Tests the creation of a new enrollment with generated test data.

### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCreateEnrollment()
{
    // Arrange
    var request = CreateEnrollment();
    
    // Act
    var response = await _client.PostAsync<CreateEnrollmentResponse>(request);
    testOutputHelper.LogToConsole(response);

    //  Assert
    NotNull(response);
    True(response.ReqNumber.Length > 0);
    True(response.ReqNumber.Length == 11);
}

public static RestRequest CreateEnrollment()
{
    var request = GetRestRequest("enrollments");
    request.AddOrUpdateHeader(RequestOrigin, "UnitTest-CreateEnrollment");
    return request.AddJsonBody(GenerateEnrollment());
}
```

## Request Details

### Endpoint
```
POST enrollments
```

### Headers
- `RequestOrigin`: "UnitTest-CreateEnrollment"

### Request Body
Generated using `GenerateEnrollment()` method:

```csharp
public static CreateUpdateEnrollmentRequest GenerateEnrollment()
{
    var enrollment = new Faker<CreateUpdateEnrollmentRequest>()
        .RuleFor(x => x.QId, RandomNumber(11))
        .RuleFor(x => x.HCNumber, f => f.Random.AlphaNumeric(10))
        .RuleFor(x => x.FNameEn, f => f.Name.FirstName())
        .RuleFor(x => x.MNameEn, MiddleNameEnglish)
        .RuleFor(x => x.LNameEn, f => f.Name.LastName())
        .RuleFor(x => x.FNameAr, FirstNameArabic)
        .RuleFor(x => x.MNameAr, MiddleNameArabic)
        .RuleFor(x => x.LNameAr, LastNameArabic)
        .RuleFor(x => x.Nationality, "634")
        .RuleFor(x => x.AttachId1, NewGuid())
        .RuleFor(x => x.AttachId2, NewGuid())
        .RuleFor(x => x.AttachId3, NewGuid())
        .RuleFor(x => x.AttachId4, NewGuid())
        .RuleFor(x => x.AttachId5, NewGuid())
        .RuleFor(x => x.AttachId6, NewGuid())
        .RuleFor(x => x.SubmittedBy, f => f.Name.FullName())
        .RuleFor(x => x.SubmitterQId, LongQId)
        .RuleFor(x => x.SubmitterEmail, "<EMAIL>")
        .RuleFor(x => x.SubmitterMobile, "+97470559257")
        .RuleFor(x => x.SubmitterNationality, "634")
        .RuleFor(x => x.SubmitterHCNumber, f => f.Random.AlphaNumeric(10))
        .RuleFor(x => x.SubmitterHCntCode, "RYN")
        .RuleFor(x => x.Consent, f => f.Random.Bool())
        .Generate();

    return enrollment;
}
```

## Response Model

### CreateEnrollmentResponse
```csharp
public class CreateEnrollmentResponse
{
    public string ReqNumber { get; set; }
}
```

## Test Data Generation

### Required Fields
1. **Personal Information**
   - QId (11 digits)
   - HC Number (10 alphanumeric)
   - Names (English and Arabic)
   - Nationality

2. **Attachments**
   - 6 attachment GUIDs

3. **Submitter Information**
   - QId
   - Full name
   - Email
   - Mobile
   - Nationality
   - HC Number
   - HC Center Code

4. **Other**
   - Consent boolean

## Validation Rules

### Request Validation
1. All required fields present
2. Valid QID formats
3. Valid attachment GUIDs
4. Valid contact information

### Response Validation
1. Response not null
2. Request number not empty
3. Request number length = 11

## Error Cases

1. **Invalid Data Format**
   - Invalid QID
   - Invalid HC Number
   - Invalid email format
   - Invalid mobile format

2. **Missing Required Fields**
   - Missing names
   - Missing nationality
   - Missing attachments
   - Missing submitter info

3. **System Errors**
   - Database errors
   - Attachment service errors
   - Validation service errors

## Notes

1. **Test Data**
   - Uses Faker for realistic data
   - Consistent format for IDs
   - Valid test email domain

2. **Attachments**
   - Requires 6 attachment IDs
   - Uses GUIDs for uniqueness
   - All attachments required

3. **Performance**
   - Multiple service calls
   - Attachment validation
   - Database operations
