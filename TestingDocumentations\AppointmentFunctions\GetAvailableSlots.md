# Get Available Slots Tests

## Overview

These tests verify the functionality of retrieving available appointment slots through the API. The test suite covers various scenarios including date ranges, service types, and location-specific availability.

## Test Cases

### TestGetAvailableSlots_ValidRequest_ReturnsOk

Tests the successful retrieval of available slots with valid parameters.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetAvailableSlots_ValidRequest_ReturnsOk()
{
    // Arrange
    var request = GetRestRequest("appointments/slots");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    request.AddQueryParameter("startDate", DateTime.Now.Date.ToString("yyyy-MM-dd"));
    request.AddQueryParameter("endDate", DateTime.Now.AddDays(7).Date.ToString("yyyy-MM-dd"));
    request.AddQueryParameter("serviceType", "General");
    request.AddQueryParameter("location", "Main Clinic");

    // Act
    var response = await _client.ExecuteAsync<List<SlotResponse>>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    NotEmpty(response.Data);
    response.Data.ForEach(slot => True(slot.IsAvailable));
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Date range (7 days)
  - Service type
  - Location
- **Expected Output**: 
  - List of available slots
  - Slot details and availability
- **Validation Points**:
  - Response success
  - Non-empty slot list
  - Slot availability

### TestGetAvailableSlots_InvalidDateRange_ReturnsBadRequest

Tests the API's handling of invalid date range parameters.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetAvailableSlots_InvalidDateRange_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest("appointments/slots");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    request.AddQueryParameter("startDate", DateTime.Now.AddDays(7).ToString("yyyy-MM-dd"));
    request.AddQueryParameter("endDate", DateTime.Now.ToString("yyyy-MM-dd"));

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(BadRequest, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Invalid date range (end before start)
- **Expected Output**: 400 BadRequest
- **Validation Points**:
  - Error status code
  - Error message validation

## Response Models

### SlotResponse
```csharp
public class SlotResponse
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool IsAvailable { get; set; }
    public string ServiceType { get; set; }
    public string Location { get; set; }
    public int RemainingSlots { get; set; }
}
```

## Validation Rules

1. **Date Range Validation**
   - Start date must be today or future
   - End date must be after start date
   - Maximum range limit (e.g., 30 days)

2. **Service Type Validation**
   - Must be valid service type
   - Must be active service
   - Must be available at location

3. **Location Validation**
   - Must be valid location
   - Must be operational
   - Must offer requested service

## Query Parameters

1. **Required Parameters**
   ```csharp
   startDate  // Format: yyyy-MM-dd
   endDate    // Format: yyyy-MM-dd
   ```

2. **Optional Parameters**
   ```csharp
   serviceType  // Filter by service
   location     // Filter by location
   duration     // Slot duration in minutes
   ```

## Error Scenarios

1. **Invalid Dates**
   - Past dates
   - Invalid range
   - Format errors

2. **Invalid Filters**
   - Unknown service type
   - Invalid location
   - Invalid duration

3. **System Errors**
   - Service unavailable
   - Location closed
   - Maintenance periods

## Best Practices

### 1. Date Range Validation
```csharp
private bool IsValidDateRange(DateTime startDate, DateTime endDate)
{
    if (startDate < DateTime.Now.Date)
        return false;

    if (endDate <= startDate)
        return false;

    var rangeDays = (endDate - startDate).TotalDays;
    return rangeDays <= MaxRangeDays;
}
```

### 2. Slot Filtering
```csharp
private List<SlotResponse> FilterSlots(List<SlotResponse> slots, string? serviceType, string? location)
{
    var filtered = slots;

    if (!string.IsNullOrEmpty(serviceType))
        filtered = filtered.Where(s => s.ServiceType == serviceType).ToList();

    if (!string.IsNullOrEmpty(location))
        filtered = filtered.Where(s => s.Location == location).ToList();

    return filtered;
}
```

### 3. Availability Check
```csharp
private async Task<bool> IsSlotAvailable(DateTime slotTime, string serviceType, string location)
{
    var existingBookings = await GetBookings(slotTime, serviceType, location);
    var capacity = await GetLocationCapacity(location, serviceType);
    return existingBookings < capacity;
}
```

## Test Helper Methods

### 1. Date Generation
```csharp
private DateTime GetValidFutureDate(int daysAhead = 1)
{
    var date = DateTime.Now.AddDays(daysAhead).Date;
    while (!IsWorkingDay(date))
    {
        date = date.AddDays(1);
    }
    return date;
}
```

### 2. Service Type Helpers
```csharp
private List<string> GetValidServiceTypes()
{
    return new List<string>
    {
        "General",
        "Specialist",
        "Emergency",
        "Follow-up"
    };
}
```

## Performance Considerations

1. **Date Range Limits**
   - Enforce maximum range
   - Implement pagination
   - Cache common queries

2. **Response Optimization**
   - Include only necessary fields
   - Batch similar requests
   - Use compression

3. **Resource Management**
   - Monitor slot availability
   - Update in real-time
   - Handle concurrent requests

## Notes

- Tests cover various date ranges
- Proper validation of parameters
- Comprehensive error handling
- Performance optimization
- Real-time availability updates

## Recommendations

1. **Caching Strategy**
   - Cache common time slots
   - Update on bookings/cancellations
   - Clear cache periodically

2. **Real-time Updates**
   - Implement WebSocket for live updates
   - Notify on availability changes
   - Handle concurrent bookings

3. **User Experience**
   - Show alternative slots
   - Suggest nearby locations
   - Display waiting list options
