# GetUpcomingConfirmedAppointmentByAppointmentId

## Overview
Retrieves a specific upcoming confirmed appointment by its appointment ID.

## Endpoint
- **Route**: `appointments/upcoming-confirmed/{appointmentId}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `appointmentId` (path, required): ID of the appointment to retrieve
- `EligibleClinicCodes` (header, required): List of eligible clinic codes

## Responses
- **200 OK**: Returns appointment details
  ```json
  {
    "AppointmentId": "string",
    "HcNumber": "string",
    "AppointmentDateTime": "datetime",
    "AppointmentType": "string",
    "ClinicType": "string",
    "HealthCenter": "string"
  }
  ```
- **400 Bad Request**: Invalid appointment ID
- **404 Not Found**: Appointment not found
- **500 Internal Server Error**: Server error

## Business Logic
1. Validates appointment ID
2. Retrieves appointment details
3. Checks if appointment is upcoming and confirmed
4. Returns formatted appointment details

## Dependencies
- HmcLiveFeedContext
- EDWDbContext
