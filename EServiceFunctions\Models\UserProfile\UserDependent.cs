﻿using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.UserProfile;

public class UserDependent
{
    public long QId { get; set; }
    public DateTime? Dob { get; set; }
    public long ParentQId { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public bool? IsMinor { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public bool? IsRelationshipActive { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreateSource { get; set; }
    public string? UpdateSource { get; set; }
}