﻿using EServiceFunctions.Models.HmcLiveFeeds;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.Models.UserProfile;
using EServiceFunctions.RequestResponseModels.Appointment;
using EServiceFunctions.RequestResponseModels.EDWDropDownList;
using EServiceFunctions.RequestResponseModels.UserProfile;
using Microsoft.FeatureManagement;
using static TestDoublesForUnitTest.MockHelpers;
using static UnitTestEServiceFunctions.MockDataForUserProfileFunctions;

namespace UnitTestEServiceFunctions;

[ExcludeFromCodeCoverage]
public class TestUserProfileDbContext : IDbContextFactory<UserProfileContext>
{
    public UserProfileContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<UserProfileContext>()
            .UseInMemoryDatabase(databaseName: "TestUserProfileDbContext")
            .Options;

        var dbContext = new UserProfileContext(options);
        dbContext.Database.EnsureDeleted();
        dbContext.Database.EnsureCreated();
        dbContext.UserDependent.AddRange(GetUserDependentList());
        dbContext.UserProfile.AddRange(GetUserProfileList());

        dbContext.SaveChanges();
        return dbContext;
    }
}

[ExcludeFromCodeCoverage]
public class TestEdwContextForUserProfile : IDbContextFactory<EDWDbContext>
{
    public EDWDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<EDWDbContext>()
            .UseInMemoryDatabase(databaseName: "TestEdwDbContextForUserProfile")
            .Options;

        var context = new EDWDbContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.PersonMoiDetail.AddRange(GetPersonMoiDetailsForUserProfileEdw());
        context.UserProfileResponseFromEDW!.AddRange(
            GetUserProfileResponseFromEdw());
        context.GetMoiDependentsResponse.AddRange(GetGetMoiDependentsResponse());
        context.GetLanguageListResponse.AddRange(GetGetLanguageListResponse());
        context.UpcomingConfirmedAppointmentListResponseFromEDW.AddRange(
            GetGetUpcomingConfirmedAppointmentListResponseFromEdw());
        context.MedicationRefillDetailsProd!.AddRange(
            GetMedicationRefillDetailsProd());
        context.MedicationRefillDetailsStg!.AddRange(
            GetMedicationRefillDetailsStg());

        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public static class MockDataForUserProfileFunctions
{
    public static IEnumerable<UserDependent> GetUserDependentList()
    {
        var userDependentList = new List<UserDependent>
        {
            new()
            {
                QId = LongQId + 1,
                ParentQId = LongQId,
                CreatedAt = new Faker().Date.Past(),
                CreateSource = "Unit_Test"
            },
            new()
            {
                QId = LongQId + 2,
                ParentQId = LongQId,
                CreatedAt = new Faker().Date.Past(),
                CreateSource = "Unit_Test"
            },
            new()
            {
                QId = LongQId + 3,
                ParentQId = LongQId,
                CreatedAt = new Faker().Date.Past(),
                CreateSource = "Unit_Test"
            }
        };

        return userDependentList;
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<EServiceFunctions.Models.UserProfile.UserProfile> GetUserProfileList()
    {
        var userProfileList = new List<EServiceFunctions.Models.UserProfile.UserProfile>
        {
            new()
            {
                QId = LongQId,
                PrefSMSLang = "en",
                PrefComMode = "SMS",
                SecondaryPhoneMobile = SecondaryMobilePhone,
                IsActive = true,
                Consent = true,
                CreatedAt = new Faker().Date.Past(),
                CreateSource = "Unit_Test"
            },
            new()
            {
                QId = LongQId - 10,
                PrefSMSLang = "en",
                PrefComMode = "SMS",
                SecondaryPhoneMobile = SecondaryMobilePhone,
                IsActive = true,
                Consent = true,
                CreatedAt = new Faker().Date.Past(),
                CreateSource = "Unit_Test"
            },
            new()
            {
                QId = LongQId - 20,
                PrefSMSLang = "en",
                PrefComMode = "SMS",
                SecondaryPhoneMobile = SecondaryMobilePhone,
                IsActive = true,
                Consent = true,
                CreatedAt = new Faker().Date.Past(),
                CreateSource = "Unit_Test"
            }
        };
        return userProfileList;
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<PersonMoiDetail> GetPersonMoiDetailsForUserProfileEdw()
    {
        var personMoiDetail = new PersonMoiDetail
        {
            QId = QId,
            Dob = new DateTime(1986, 09, 28),
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<PersonMoiDetail> { personMoiDetail };
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<UserProfileFromEDW> GetUserProfileResponseFromEdw()
    {
        var userProfileFromEdw = new List<UserProfileFromEDW>
        {
            new()
            {
                QId = LongQId,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber1,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            },
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Dob = new Faker().Date.Past(),
                NationalityCode = NationalityCode,
                NationalityEn = NationalityEn,
                NationalityAr = NationalityAr,
                GenderCode = GenderCode,
                GenderEn = GenderEn,
                GenderAr = GenderAr,
                VisaCode = VisaCode,
                VisaDescriptionEn = VisaDescriptionEn,
                VisaDescriptionAr = VisaDescriptionAr,
                PhoneMobile = MobilePhone,
                SecondaryPhoneMobile = SecondaryMobilePhone,
                AssignedHealthCenter = "OBK",
                AssignedHealthCenterEn = "Omar Bin Khattab",
                AssignedHealthCenterAr = "عمر بن الخطاب",
                HcNumber = HcNumber2,
                HcExpiryDate = new Faker().Date.Future(),
                GisAddressStreet = new Faker().Address.StreetAddress(),
                GisAddressBuilding = new Faker().Address.BuildingNumber(),
                GisAddressZone = new Faker().Address.City(),
                GisAddressUnit = new Faker().Address.City(),
                GisAddressUpdatedAt = new Faker().Date.Past(),
                PhysicianId = "123456",
                PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                IsStaff = 1
            }
        };
        return userProfileFromEdw;
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<GetMoiDependentsResponse> GetGetMoiDependentsResponse()
    {
        var getMoiDependentsResponse = new List<GetMoiDependentsResponse>
        {
            new()
            {
                QId = LongQId + 1,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 2,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            },
            new()
            {
                QId = LongQId + 3,
                FNameEn = FirstNameEn,
                MNameEn = MiddleNameEn,
                LNameEn = LastNameEn,
                FNameAr = FirstNameAr,
                MNameAr = MiddleNameAr,
                LNameAr = LastNameAr,
                Linked = true
            }
        };
        return getMoiDependentsResponse;
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<GetLanguageListResponse> GetGetLanguageListResponse()
    {
        var getLanguageListResponse = new List<GetLanguageListResponse>
        {
            new()
            {
                NatCode = "en",
                NatNameEn = "English",
                NatNameAr = "الإنجليزية"
            },
            new()
            {
                NatCode = "ar",
                NatNameEn = "Arabic",
                NatNameAr = "العربية"
            },
            new()
            {
                NatCode = "fr",
                NatNameEn = "French",
                NatNameAr = "الفرنسية"
            }
        };
        return getLanguageListResponse;
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<GetUpcomingConfirmedAppointmentListResponseFromEdw>
        GetGetUpcomingConfirmedAppointmentListResponseFromEdw()
    {
        var getUpcomingConfirmedAppointmentListResponseFromEdw =
            new List<GetUpcomingConfirmedAppointmentListResponseFromEdw>
            {
                new()
                {
                    AppointmentId = "AP00001",
                    QId = LongQId,
                    QIdExpiryDt = new Faker().Date.Future(),
                    FNameEn = FirstNameEn,
                    MNameEn = MiddleNameEn,
                    LNameEn = LastNameEn,
                    FNameAr = FirstNameAr,
                    MNameAr = MiddleNameAr,
                    LNameAr = LastNameAr,
                    NationalityCode = NationalityCode,
                    NationalityEn = NationalityEn,
                    NationalityAr = NationalityAr,
                    Dob = new Faker().Date.Past(),
                    HCNumber = HcNumber1,
                    HCExpiryDate = new Faker().Date.Future(),
                    GenderCode = GenderCode,
                    GenderEn = GenderEn,
                    GenderAr = GenderAr,
                    PhoneMobile = MobilePhone,
                    AggignedHCntCode = "OBK",
                    AggignedHCntNameEn = "Omar Bin Khattab",
                    AggignedHCntNameAr = "عمر بن الخطاب",
                    AppointmentHCntCode = "OBK",
                    AppointmentHCntNameEn = "Omar Bin Khattab",
                    AppointmentHCntNameAr = "عمر بن الخطاب",
                    ClinicCode = "OBK001",
                    ClinicNameEn = "OBK Clinic 1",
                    ClinicNameAr = "عيادة عمر بن الخطاب 1",
                    GisAddressStreet = new Faker().Address.StreetAddress(),
                    GisAddressBuilding = new Faker().Address.BuildingNumber(),
                    GisAddressZone = new Faker().Address.City(),
                    GisAddressUnit = new Faker().Address.City(),
                    PhysicianId = "123456",
                    PhysicianFullNameEn = "Dr. " + FirstNameEn + " " + LastNameEn,
                    PhysicianFullNameAr = "د. " + FirstNameAr + " " + LastNameAr,
                    BookedDateTime = new Faker().Date.Past(),
                    AppointmentDateTime = new Faker().Date.Future(),
                    AppointmentType = "Appointment",
                    AppointmentStatus = "Confirmed",
                    AppointmentLocation = "OBK",
                    AppointmentFacility = "OBK"
                }
            };

        return getUpcomingConfirmedAppointmentListResponseFromEdw;
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<EServPersonasMedicationRefill> GetMedicationRefillDetailsStg()
    {
        var medicationRefillDetailsList = new List<EServPersonasMedicationRefill>
        {
            new()
            {
                QId = QId,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 4",
                SupplyDuration = 1,
                PrescriptionOrderId = 1,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId1,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 5",
                SupplyDuration = 1,
                PrescriptionOrderId = 2,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            },
            new()
            {
                QId = QId2,
                PrescriptionRefillDueDate = new Faker().Date.Future(),
                LastDispenseDate = new Faker().Date.Past(),
                LastDispenseFacCode = "OBK",
                PrescriptionDate = new Faker().Date.Past(),
                PrescriptionFacCode = "OBK",
                PrescribedMedication = "Medicine 6",
                SupplyDuration = 1,
                PrescriptionOrderId = 3,
                PrescribedByCorpId = "123456",
                PrescribedByNameEng = "Dr. " + FirstNameEn + " " + LastNameEn,
                PrescribedByNameAra = "د. " + FirstNameAr + " " + LastNameAr,
                EtlLoadDatetime = new Faker().Date.Past(),
                HcNumber = HcNumber1,
                RefillsRemaining = 1
            }
        };
        return medicationRefillDetailsList;
    }

    [ExcludeFromCodeCoverage]
    public static IEnumerable<MedicationRefillDtls> GetMedicationRefillDetailsProd()
    {
        var medicationRefill = new MedicationRefillDtls
        {
            PersonId = 1,
            QId = QId,
            HcNumber = HcNumber1,
            NameFirst = FirstNameEn,
            NameMiddle = MiddleNameEn,
            NameLast = LastNameEn,
            AgeYears = Age,
            MobilePhone = MobilePhone,
            PrescriptionRefillDueDate = new DateTime(2023, 10, 10),
            LastDispenseDate = new DateTime(2023, 08, 10),
            LastDispenseFacCode = "WAJ",
            PrescriptionDate = new DateTime(2023, 10, 10),
            PrescriptionFacCode = "WAJ",
            PrescribedMedication = "mometasone nasaL",
            SupplyDuration = 15,
            PrescriptionOrderId = 8448095541,
            PrescribedByCorpId = "2683232",
            PrescribedByNameEng = "Dr. Aktham Shraiba",
            PrescribedByNameAra = "د. أكثم شريبة",
            RefillsRemaining = 10,
            EtlLoadDatetime = new DateTime(2023, 10, 15)
        };

        return new List<MedicationRefillDtls> { medicationRefill };
    }

    [ExcludeFromCodeCoverage]
    public class TestHmcLiveFeedDbContext : IDbContextFactory<HmcLiveFeedContext>
    {
        public HmcLiveFeedContext CreateDbContext()
        {
            var options = new DbContextOptionsBuilder<HmcLiveFeedContext>()
                .UseInMemoryDatabase(databaseName: "TestHmcLiveFeedDbContext")
                .Options;

            var dbContext = new HmcLiveFeedContext(options);
            dbContext.Database.EnsureDeleted();
            dbContext.Database.EnsureCreated();

            // Add test data for each DbSet
            dbContext.AppointmentBookings.AddRange(GetAppointmentBookingsList());
            dbContext.Patients.AddRange(GetPatientsList());
            dbContext.Visits.AddRange(GetVisitsList());

            dbContext.SaveChanges();
            return dbContext;
        }

        private static List<string> GetQIds()
        {
            return
            [
                "28614415181",
                "28614415182",
                "28614415183",
                "28614415184",
                "28614415185",
                "28614415186",
                "28614415187",
                "28614415188",
                "28614415189",
                "28614415190"
            ];
        }

        private static IEnumerable<AppointmentBooking> GetAppointmentBookingsList()
        {
            var faker = new Faker<AppointmentBooking>()
                .RuleFor(a => a.HcNumber, f => f.Random.AlphaNumeric(10))
                .RuleFor(a => a.QId, f => f.PickRandom(GetQIds()))
                .RuleFor(a => a.AppointmentId, f => f.Random.Long(1, 99999))
                .RuleFor(a => a.InitialAppointmentDateTime, f => f.Date.Future())
                .RuleFor(a => a.AppointmentDateTime, f => f.Date.Future())
                .RuleFor(a => a.AppointmentTypeCode, f => f.Random.AlphaNumeric(10))
                .RuleFor(a => a.AppointmentStatus, f => f.PickRandom("Scheduled", "Cancelled", "Completed"))
                .RuleFor(a => a.ClinicTypeCode, f => f.Random.AlphaNumeric(10))
                .RuleFor(a => a.AppointmentHcCode, f => f.Random.AlphaNumeric(10))
                .RuleFor(a => a.AppointmentPhysicianId, f => f.Random.AlphaNumeric(8))
                .RuleFor(a => a.AppointmentDuration, f => f.Random.Int(15, 60))
                .RuleFor(a => a.AppointmentConsultationType, f => f.PickRandom("Virtual", "Physical", "Phone"))
                .RuleFor(a => a.InsertDate, f => f.Date.Past())
                .RuleFor(a => a.UpdateDate, f => f.Date.Recent())
                .RuleFor(a => a.InsertMessageId, f => f.Random.Guid().ToString())
                .RuleFor(a => a.UpdateMessageId, f => f.Random.Guid().ToString());
            return faker.Generate(5);
        }

        private static IEnumerable<Patient> GetPatientsList()
        {
            var faker = new Faker<Patient>()
                .RuleFor(a => a.QId, f => f.PickRandom(GetQIds()))
                .RuleFor(p => p.QidExpiryDate, f => f.Date.Future())
                .RuleFor(p => p.FirstNameEn, f => f.Name.FirstName())
                .RuleFor(p => p.MiddleNameEn, f => f.Name.FirstName())
                .RuleFor(p => p.LastNameEn, f => f.Name.LastName())
                .RuleFor(p => p.FirstNameAr, f => f.Name.FirstName())
                .RuleFor(p => p.MiddleNameAr, f => f.Name.FirstName())
                .RuleFor(p => p.LastNameAr, f => f.Name.LastName())
                .RuleFor(p => p.DateOfBirth, f => f.Date.Past(50))
                .RuleFor(p => p.NationalityCode, f => f.Address.CountryCode())
                .RuleFor(p => p.GenderCode, f => f.PickRandom("M", "F"))
                .RuleFor(p => p.MobileNumber, f => f.Phone.PhoneNumber())
                .RuleFor(p => p.HcNumber, f => f.Random.AlphaNumeric(10))
                .RuleFor(p => p.HcExpiryDate, f => f.Date.Future())
                .RuleFor(p => p.GisAddressStreet, f => f.Address.StreetName())
                .RuleFor(p => p.GisAddressBuilding, f => f.Address.BuildingNumber())
                .RuleFor(p => p.GisAddressZone, f => f.Address.ZipCode())
                .RuleFor(p => p.GisAddressUnit, f => f.Random.Number(1, 999).ToString())
                .RuleFor(p => p.AssignedFamilyPhysicianId, f => f.Random.AlphaNumeric(8))
                .RuleFor(p => p.AssignedHcCode, f => f.Random.AlphaNumeric(10))
                .RuleFor(p => p.MaritalStatusCode, f => f.PickRandom("S", "M", "D", "W"))
                .RuleFor(p => p.InsertDate, f => f.Date.Past())
                .RuleFor(p => p.UpdateDate, f => f.Date.Recent())
                .RuleFor(p => p.InsertMessageId, f => f.Random.Guid().ToString())
                .RuleFor(p => p.UpdateMessageId, f => f.Random.Guid().ToString());

            return faker.Generate(5);
        }

        private static IEnumerable<Visit> GetVisitsList()
        {
            var faker = new Faker<Visit>()
                .RuleFor(v => v.HcNumber, f => f.Random.AlphaNumeric(10))
                .RuleFor(a => a.QId, f => f.PickRandom(GetQIds()))
                .RuleFor(v => v.VisitNumber, f => f.Random.Long(1000000, 9999999))
                .RuleFor(v => v.FinNumber, f => f.Random.Long(1000000, 9999999))
                .RuleFor(v => v.VisitDateTime, f => f.Date.Past())
                .RuleFor(v => v.VisitHc, f => f.Random.AlphaNumeric(10))
                .RuleFor(v => v.VisitType, f => f.PickRandom("Emergency", "Outpatient", "Inpatient", "Day Care"))
                .RuleFor(v => v.VisitClinic,
                    f => f.PickRandom("Internal Medicine", "Pediatrics", "Surgery", "Cardiology"))
                .RuleFor(v => v.AttendingPhysician, f => f.Name.FullName())
                .RuleFor(v => v.HealthcareService,
                    f => f.PickRandom("Consultation", "Procedure", "Surgery", "Diagnostic"))
                .RuleFor(v => v.AdmitSource, f => f.PickRandom("Emergency", "Direct", "Transfer", "Referral"))
                .RuleFor(v => v.InsertDate, f => f.Date.Past())
                .RuleFor(v => v.UpdateDate, f => f.Date.Recent())
                .RuleFor(v => v.InsertMessageId, f => f.Random.Guid().ToString())
                .RuleFor(v => v.UpdateMessageId, f => f.Random.Guid().ToString());

            return faker.Generate(5);
        }
    }
}

public class UnitTestUserProfileFunctions(ITestOutputHelper output)
{
    private readonly ILogger<UserProfileFunctions> _logger = Mock.Of<ILogger<UserProfileFunctions>>();

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateUserProfile_Should_Return_CreatedResult()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var mockLiveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();
        
        var request = new CreateUpdateUserProfileRequest
        {
            QId = LongQId + 4,
            PrefSMSLang = "en",
            PrefComMode = "SMS",
            SecondaryPhoneMobile = SecondaryMobilePhone,
            Consent = true
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, (LongQId + 4).ToString());

        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, mockLiveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.CreateUserProfile(mockHttpRequestData);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.Created, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateUserProfile_Should_Return_BadRequestObjectResult_When_Request_Is_Null()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var liveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();

        var request = new CreateUpdateUserProfileRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, liveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.CreateUserProfile(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateUserProfile_Should_Return_BadRequestObjectResult_When_Request_Is_Invalid()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var mockLiveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();

        var request = new CreateUpdateUserProfileRequest
        {
            QId = LongQId,
            PrefSMSLang = "en",
            PrefComMode = "SMS",
            SecondaryPhoneMobile = SecondaryMobilePhone,
            Consent = true
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, mockLiveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.CreateUserProfile(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }


    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateUserProfileByQId_Should_Return_OK_When_Request_Is_Valid()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var mockLiveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();

        var request = new CreateUpdateUserProfileRequest
        {
            QId = LongQId,
            PrefSMSLang = "en",
            PrefComMode = "SMS",
            SecondaryPhoneMobile = SecondaryMobilePhone,
            Consent = true
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, mockLiveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.UpdateUserProfileByQId(mockHttpRequestData, 28614415181);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateUserProfileByQId_Should_Return_BadRequestObjectResult_When_Request_Is_Null()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var mockLiveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();

        var json = SerializeObject(null);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, mockLiveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.CreateUserProfile(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateUserProfileByQId_Should_Return_BadRequestObjectResult_When_Request_Is_EmptyObject()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var mockLiveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();

        var requestBody = SerializeObject(new CreateUpdateUserProfileRequest());
        var json = SerializeObject(requestBody);
        var mockHttpRequestData = CreateHttpRequestData(payload: json, method: Post);
        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, mockLiveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.CreateUserProfile(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteUserProfileByQId_Should_Return_OkObjectResult()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var mockLiveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();

        var mockHttpRequestData = CreateHttpRequestData(method: Delete);
        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, mockLiveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.DeleteUserProfileByQId(mockHttpRequestData, 28614415181);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteUserProfileByQId_Should_Return_BadRequestObjectResult_When_QId_Is_Invalid()
    {
        // Arrange
        var mockUserProfileContext = new TestUserProfileDbContext();
        var mockEdwContext = new TestEdwContextForUserProfile();
        var liveFeedContext = new TestHmcLiveFeedDbContext();
        var mockFeatureManagement = Mock.Of<IFeatureManager>();

        var mockHttpRequestData = CreateHttpRequestData(method: Delete);
        var userProfileFunctions =
            new UserProfileFunctions(mockUserProfileContext, mockEdwContext, liveFeedContext, mockFeatureManagement, _logger);

        // Act
        var response = await userProfileFunctions.DeleteUserProfileByQId(mockHttpRequestData, 286144151822);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }
}