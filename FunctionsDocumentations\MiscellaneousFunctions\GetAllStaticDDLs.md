# GetAllStaticDDLs

## Overview
Retrieves all static dropdown list values organized by categories, supporting multilingual descriptions.

## Endpoint
- **Route**: `staticddls`
- **Method**: GET
- **Authorization**: Function level

## Response
- **200 OK**: Returns categorized dropdown lists
  ```json
  [
    {
      "Category": "string",
      "Data": [
        {
          "Code": "string",
          "DescriptionEn": "string",
          "DescriptionAr": "string"
        }
      ]
    }
  ]
  ```
- **204 No Content**: No dropdown data found
- **400 Bad Request**: Invalid request
- **500 Internal Server Error**: Server error

## Business Logic
1. Retrieves submitter information from claims
2. Executes stored procedure `SP_GetAllStaticDropDownList`
3. Groups results by categories
4. Transforms data into hierarchical structure
5. Provides bilingual descriptions

## Query Optimization
- Uses stored procedure for efficient data retrieval
- AsNoTracking for read-only operations
- Efficient data grouping
- Optimized response structure

## Security Considerations
- Function-level authorization
- Claims-based validation
- Logging of all operations

## Error Handling
- Exception handling with default behavior
- Request URL logging
- Operation logging
- Empty result handling

## Database Operations
- Uses stored procedure
- No direct table access
- Read-only transaction
- Efficient data transformation

## Multilingual Support
- English descriptions (DescriptionEn)
- Arabic descriptions (DescriptionAr)
- Category-based organization
