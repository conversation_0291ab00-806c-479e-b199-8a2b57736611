# GetNationalityList

## Overview
Retrieves a list of nationalities from the Medical Records Database.

## Endpoint
- **Route**: `nationalities`
- **Method**: GET
- **Authorization**: Function level

## Response
- **200 OK**: Returns list of nationalities
  ```json
  [
    {
      "NatCode": "string",     // Country/Nationality code
      "NatNamrEn": "string",   // English country name
      "NatNameAr": "string"    // Arabic country name
    }
  ]
  ```
- **204 No Content**: No nationalities found
- **400 Bad Request**: Error processing request

## Business Logic
1. Logs request URL and operation start
2. Queries MRD_Nationalities table
3. Projects to GetNationalityListResponse model
4. Checks for empty result set
5. Returns formatted response

## Data Source
- Table: MRD_Nationalities
- Context: MRDDbContext (Medical Records Database)

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient projection in Select statement
- No unnecessary joins or filters
