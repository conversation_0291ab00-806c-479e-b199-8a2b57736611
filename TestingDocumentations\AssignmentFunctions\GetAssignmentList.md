# GetAssignmentList Tests

## Overview
Tests that verify the functionality of retrieving assignment lists through the API.

## Test Cases

### TestGetAssignmentListByQId
Tests the successful retrieval of assignments for a valid QID.

#### Test Implementation
```csharp
[Fact] //happy path
public async Task TestGetAssignmentListByQId()
{
    // Arrange
    const long qId = 22222222272;
    var request = GetRestRequest("assignments");
    request.AddOrUpdateHeader(JwtClaimsQId, qId);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: Valid QID (22222222272)
- **Expected Output**: 200 OK with assignment list
- **Validation**:
  - Response is not null
  - Response is successful
  - Status code is 200 OK

### TestGetAssignmentListByInvalidQId
Tests error handling when retrieving assignments with an invalid QID.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestGetAssignmentListByInvalidQId()
{
    // Arrange
    var request = GetRestRequest("assignments");
    request.AddOrUpdateHeader(JwtClaimsQId, 0);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(Unauthorized, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Invalid QID (0)
- **Expected Output**: 401 Unauthorized
- **Validation**:
  - Response is not null
  - Status code is 401 Unauthorized

### TestGetAssignmentListByQId_InvalidStatus
Tests retrieval of assignments with an invalid status parameter.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestGetAssignmentListByQId_InvalidStatus()
{
    // Arrange
    const long qId = 22222222272;
    var request = GetRestRequest("assignments");
    request.AddOrUpdateHeader(JwtClaimsQId, qId);
    request.AddQueryParameter("status", "invalid");

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(NoContent, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: 
  - Valid QID (22222222272)
  - Invalid status parameter
- **Expected Output**: 204 NoContent
- **Validation**:
  - Response is not null
  - Status code is 204 NoContent

## Common Patterns
1. **Request Setup**:
   - Uses `GetRestRequest` helper method
   - Adds QID to header
   - Optionally adds query parameters

2. **Response Validation**:
   - Checks for null response
   - Verifies status code
   - Validates response content when applicable

3. **Error Handling**:
   - Uses `ThrowIfNull` for critical validations
   - Logs response for debugging

## Notes
- Tests use consistent QID values across test cases
- Error cases properly validate error responses
- Logging is implemented for debugging purposes
