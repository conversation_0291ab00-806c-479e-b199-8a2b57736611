# Upload Attachments Tests

## Overview

These tests verify the functionality of uploading file attachments to the system. The test suite covers various upload scenarios, file validations, and error handling cases.

## Test Cases

### TestUploadAttachment_ValidFile_ReturnsCreated

Tests the successful upload of a valid file attachment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUploadAttachment_ValidFile_ReturnsCreated()
{
    // Arrange
    var request = GetRestRequest("attachments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    
    var filePath = CreateTestFile(1024); // 1KB test file
    request.AddFile("file", filePath, "application/pdf");
    request.AddParameter("entityId", "TEST-123");
    request.AddParameter("description", "Test attachment");

    // Act
    var response = await _client.ExecuteAsync<AttachmentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(Created, response.StatusCode);
    NotNull(response.Data);
    NotEmpty(response.Data.AttachmentId);
    Equal("application/pdf", response.Data.ContentType);
}
```

### TestUploadMultipleAttachments_ValidFiles_ReturnsCreated

Tests uploading multiple files in a single request.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUploadMultipleAttachments_ValidFiles_ReturnsCreated()
{
    // Arrange
    var request = GetRestRequest("attachments/batch");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    
    // Create test files
    var files = new[]
    {
        (Path: CreateTestFile(1024), Type: "application/pdf"),
        (Path: CreateTestFile(2048), Type: "image/jpeg")
    };

    foreach (var file in files)
    {
        request.AddFile("files", file.Path, file.Type);
    }
    
    request.AddParameter("entityId", "TEST-123");

    // Act
    var response = await _client.ExecuteAsync<BatchUploadResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(Created, response.StatusCode);
    NotNull(response.Data);
    Equal(files.Length, response.Data.SuccessfulUploads.Count);
    Empty(response.Data.FailedUploads);
}
```

## Request/Response Models

### UploadRequest
```csharp
public class UploadRequest
{
    public string EntityId { get; set; }
    public string Description { get; set; }
    public IFormFile File { get; set; }
}
```

### BatchUploadRequest
```csharp
public class BatchUploadRequest
{
    public string EntityId { get; set; }
    public List<IFormFile> Files { get; set; }
}
```

### AttachmentResponse
```csharp
public class AttachmentResponse
{
    public string AttachmentId { get; set; }
    public string FileName { get; set; }
    public string ContentType { get; set; }
    public long Size { get; set; }
    public string Description { get; set; }
    public DateTime UploadedAt { get; set; }
    public string UploadedBy { get; set; }
}
```

### BatchUploadResponse
```csharp
public class BatchUploadResponse
{
    public List<AttachmentResponse> SuccessfulUploads { get; set; }
    public List<FailedUpload> FailedUploads { get; set; }
}
```

## Validation Rules

### 1. File Size Validation
```csharp
private async Task ValidateFileSize(IFormFile file)
{
    if (file.Length == 0)
        throw new ValidationException("File is empty");

    if (file.Length > MaxFileSize)
        throw new ValidationException($"File size exceeds maximum limit of {MaxFileSize} bytes");

    var totalSize = await GetCurrentStorageSize();
    if (totalSize + file.Length > MaxTotalStorage)
        throw new StorageException("Storage quota exceeded");
}
```

### 2. File Type Validation
```csharp
private void ValidateFileType(IFormFile file)
{
    var extension = Path.GetExtension(file.FileName).ToLower();
    if (!AllowedExtensions.Contains(extension))
        throw new ValidationException($"File type {extension} is not allowed");

    if (!AllowedMimeTypes.Contains(file.ContentType))
        throw new ValidationException($"Content type {file.ContentType} is not allowed");
}
```

### 3. Security Validation
```csharp
private async Task ValidateFileSecurity(IFormFile file)
{
    // Check for malware
    var scanResult = await _antivirusService.ScanFileAsync(file);
    if (!scanResult.IsClean)
        throw new SecurityException("File failed security scan");

    // Validate file signature
    if (!await IsValidFileSignature(file))
        throw new SecurityException("Invalid file signature");
}
```

## Implementation Details

### 1. File Processing
```csharp
private async Task<string> ProcessFile(IFormFile file, string entityId)
{
    // Generate unique filename
    var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
    var path = Path.Combine(StoragePath, entityId, fileName);

    // Ensure directory exists
    Directory.CreateDirectory(Path.GetDirectoryName(path));

    // Save file
    using (var stream = new FileStream(path, FileMode.Create))
    {
        await file.CopyToAsync(stream);
    }

    return fileName;
}
```

### 2. Batch Processing
```csharp
private async Task<BatchUploadResponse> ProcessBatch(
    List<IFormFile> files,
    string entityId)
{
    var response = new BatchUploadResponse
    {
        SuccessfulUploads = new List<AttachmentResponse>(),
        FailedUploads = new List<FailedUpload>()
    };

    foreach (var file in files)
    {
        try
        {
            var result = await ProcessSingleFile(file, entityId);
            response.SuccessfulUploads.Add(result);
        }
        catch (Exception ex)
        {
            response.FailedUploads.Add(new FailedUpload
            {
                FileName = file.FileName,
                Error = ex.Message
            });
        }
    }

    return response;
}
```

## Performance Optimization

### 1. Chunked Upload
```csharp
private async Task<string> ProcessLargeFile(Stream fileStream, string fileName)
{
    const int ChunkSize = 1024 * 1024; // 1MB chunks
    var buffer = new byte[ChunkSize];
    var tempPath = Path.GetTempFileName();

    using (var outputStream = File.Create(tempPath))
    {
        int bytesRead;
        while ((bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
        {
            await outputStream.WriteAsync(buffer, 0, bytesRead);
            // Report progress
            ReportProgress(fileStream.Position, fileStream.Length);
        }
    }

    return tempPath;
}
```

### 2. Parallel Processing
```csharp
private async Task<BatchUploadResponse> ProcessBatchParallel(
    List<IFormFile> files,
    string entityId)
{
    var tasks = files.Select(file => ProcessSingleFile(file, entityId));
    var results = await Task.WhenAll(tasks);

    return new BatchUploadResponse
    {
        SuccessfulUploads = results.ToList(),
        FailedUploads = new List<FailedUpload>()
    };
}
```

## Error Handling

### 1. Upload Errors
```csharp
private async Task<AttachmentResponse> SafeUpload(IFormFile file, string entityId)
{
    try
    {
        await ValidateFile(file);
        var fileName = await ProcessFile(file, entityId);
        return await SaveAttachmentMetadata(fileName, file, entityId);
    }
    catch (ValidationException ex)
    {
        _logger.LogWarning("Validation failed: {Message}", ex.Message);
        throw;
    }
    catch (StorageException ex)
    {
        _logger.LogError("Storage error: {Message}", ex.Message);
        await NotifyAdminStorageIssue(ex);
        throw;
    }
    catch (Exception ex)
    {
        _logger.LogError("Unexpected error: {Message}", ex.Message);
        throw new ServiceException("Upload failed", ex);
    }
}
```

## Notes

1. **File Handling**
   - Always validate files before processing
   - Use unique filenames to prevent collisions
   - Clean up temporary files

2. **Security**
   - Scan files for malware
   - Validate file signatures
   - Check user permissions

3. **Performance**
   - Use chunked upload for large files
   - Implement parallel processing for batches
   - Monitor storage usage

## Recommendations

1. **Upload Strategy**
   - Implement resumable uploads
   - Add progress tracking
   - Support drag-and-drop

2. **Security Measures**
   - Add file encryption
   - Implement virus scanning
   - Use secure storage

3. **Performance**
   - Optimize chunk size
   - Add compression
   - Implement caching
