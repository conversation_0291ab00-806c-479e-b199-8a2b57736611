﻿using EServiceFunctions.Models.ReleaseItem;
using EServiceFunctions.RequestResponseModels.ReleaseItem;

namespace UnitTestEServiceFunctions;

[ExcludeFromCodeCoverage]
public class TestReleaseItemContext : IDbContextFactory<ReleaseItemContext>
{
    public ReleaseItemContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<ReleaseItemContext>()
            .UseInMemoryDatabase(databaseName: "ReleaseItem")
            .Options;
        var context = new ReleaseItemContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.ReleaseItem.AddRange(MockDataForReleaseItem.GetReleaseItems());
        context.SaveChanges();
        return context;
    }
}

[ExcludeFromCodeCoverage]
public static class MockDataForReleaseItem
{
    public static IEnumerable<ReleaseItem> GetReleaseItems()
    {
        var releaseItemsList = new List<ReleaseItem>
        {
            new()
            {
                Id = 1,
                ItemCode = "RI001",
                ItemNameAr = "نتائج المختبر",
                ItemNameEn = "Lab results",
                CategoryCode = "Cat001",
                CategoryNameAr = "نتائج المختبر",
                CategoryNameEn = "Lab results",
                CreatedAt = Parse("2020-04-06 08:04:04.273")
            },
            new()
            {
                Id = 2,
                ItemCode = "RI002",
                ItemNameAr = "تقارير الأشعة",
                ItemNameEn = "Radiology Reports",
                CategoryCode = "Cat002",
                CategoryNameAr = "تقارير الأشعة",
                CategoryNameEn = "Radiology Reports",
                CreatedAt = Parse("2020-04-06 08:04:38.013")
            },
            new()
            {
                Id = 3,
                ItemCode = "RI003",
                ItemNameAr = "الأشعة السينية للأسنان",
                ItemNameEn = "Dental X Ray",
                CategoryCode = "Cat003",
                CategoryNameAr = "Radiology Images in CD",
                CategoryNameEn = "(CD) صور الأشعة على قرص مضغوط",
                CreatedAt = Parse("2020-04-06 08:05:02.577")
            },
            new()
            {
                Id = 4,
                ItemCode = "RI004",
                ItemNameAr = "اشعة بانوراما",
                ItemNameEn = "Panoramic",
                CategoryCode = "Cat003",
                CategoryNameAr = "Radiology Images in CD",
                CategoryNameEn = "(CD) صور الأشعة على قرص مضغوط",
                CreatedAt = Parse("2020-04-06 08:05:41.937")
            },
            new()
            {
                Id = 5,
                ItemCode = "RI005",
                ItemNameAr = "الموجات فوق الصوتية",
                ItemNameEn = "Ultrasound",
                CategoryCode = "Cat003",
                CategoryNameAr = "Radiology Images in CD",
                CategoryNameEn = "(CD) صور الأشعة على قرص مضغوط",
                CreatedAt = Parse("2020-04-06 08:06:14.037")
            },
            new()
            {
                Id = 6,
                ItemCode = "RI006",
                ItemNameAr = "التنظير",
                ItemNameEn = "Endoscopy",
                CategoryCode = "Cat003",
                CategoryNameAr = "Radiology Images in CD",
                CategoryNameEn = "(CD) صور الأشعة على قرص مضغوط",
                CreatedAt = Parse("2020-04-06 08:06:45.810")
            },
            new()
            {
                Id = 7,
                ItemCode = "RI007",
                ItemNameAr = "اشعة الاكس راي",
                ItemNameEn = "X Ray",
                CategoryCode = "Cat003",
                CategoryNameAr = "Radiology Images in CD",
                CategoryNameEn = "(CD) صور الأشعة على قرص مضغوط",
                CreatedAt = Parse("2020-04-06 08:07:38.320")
            },
            new()
            {
                Id = 8,
                ItemCode = "RI008",
                ItemNameAr = "لقاءات محددة في نطاق زمني",
                ItemNameEn = "Specific Encounters in a date range",
                CategoryCode = "Cat004",
                CategoryNameAr = "Specific Encounters in a date range",
                CategoryNameEn = "Specific Encounters in a date range",
                CreatedAt = Parse("2020-04-06 08:09:07.150")
            },
            new()
            {
                Id = 9,
                ItemCode = "RI009",
                ItemNameAr = "تقرير طبي",
                ItemNameEn = "Medical Report",
                CategoryCode = "Cat005",
                CategoryNameAr = "Medical Report",
                CategoryNameEn = "Medical Report",
                CreatedAt = Parse("2020-04-06 08:09:30.147")
            },
            new()
            {
                Id = 10,
                ItemCode = "RI010",
                ItemNameAr = "نسخة من التقرير الطبي",
                ItemNameEn = "Copy of Medical Report",
                CategoryCode = "Cat005",
                CategoryNameAr = "Medical Report",
                CategoryNameEn = "Medical Report",
                CreatedAt = Parse("2020-04-06 08:10:06.467")
            },
            new()
            {
                Id = 11,
                ItemCode = "RI011",
                ItemNameAr = "تقرير الحضور",
                ItemNameEn = "Attendance Report",
                CategoryCode = "Cat006",
                CategoryNameAr = "Attendance Report",
                CategoryNameEn = "Attendance Report",
                CreatedAt = Parse("2020-04-06 08:10:31.477")
            },
            new()
            {
                Id = 12,
                ItemCode = "RI012",
                ItemNameAr = "تقارير أخرى",
                ItemNameEn = "Other Reports",
                CategoryCode = "Cat007",
                CategoryNameAr = "Other Reports",
                CategoryNameEn = "Other Reports",
                CreatedAt = Parse("2020-04-06 08:10:59.337")
            }
        };
        return releaseItemsList;
    }
}

public class UnitTestReleaseItemFunctions
{
    private readonly ILogger<ReleaseItemFunctions> _logger = Mock.Of<ILogger<ReleaseItemFunctions>>();
    private readonly ITestOutputHelper _output;
    private readonly ReleaseItemFunctions _releaseItemFunctions;

    public UnitTestReleaseItemFunctions(ITestOutputHelper output)
    {
        _output = output;
        _releaseItemFunctions = new ReleaseItemFunctions(new TestReleaseItemContext(), _logger);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok()
    {
        // Arrange
        const string queryString = "?itemCode=RI001&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        NotEmpty(responseString);
        var responseObject = DeserializeObject<List<GetReleaseItemListResponse>>(responseString);
        Single(responseObject);
        Equal("RI001", responseObject[0].ItemCode);
        Equal("Lab results", responseObject[0].ItemNameEn);
        Equal("Cat001", responseObject[0].CategoryCode);
        Equal("Lab results", responseObject[0].CategoryNameEn);
    }

    [Fact] // happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_NoContent_For_Non_Existing_ItemCode()
    {
        // Arrange
        const string queryString = "?itemCode=RI999&skip=0&take=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }

    [Fact] //happy path
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok_With_All_Items_When_No_QueryString()
    {
        // Arrange
        var queryString = string.Empty;
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseObject = DeserializeObject<List<GetReleaseItemListResponse>>(responseString);
        Equal(12, responseObject.Count);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok_With_Pagination()
    {
        // Arrange
        const string queryString = "?skip=2&take=3";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseObject = DeserializeObject<List<GetReleaseItemListResponse>>(responseString);
        Equal(3, responseObject.Count);
        // Should start with the 3rd item (index 2) in the list
        Equal("RI003", responseObject[0].ItemCode);
        Equal("RI004", responseObject[1].ItemCode);
        Equal("RI005", responseObject[2].ItemCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok_With_Default_Take_When_Only_Skip_Provided()
    {
        // Arrange
        const string queryString = "?skip=10";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseObject = DeserializeObject<List<GetReleaseItemListResponse>>(responseString);
        Equal(2, responseObject.Count); // Only 2 items left after skipping 10
        Equal("RI011", responseObject[0].ItemCode);
        Equal("RI012", responseObject[1].ItemCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok_With_Default_Skip_When_Only_Take_Provided()
    {
        // Arrange
        const string queryString = "?take=5";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseObject = DeserializeObject<List<GetReleaseItemListResponse>>(responseString);
        Equal(5, responseObject.Count);
        Equal("RI001", responseObject[0].ItemCode); // Should start from the beginning
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok_With_Negative_Skip_Converted_To_Positive()
    {
        // Arrange
        const string queryString = "?skip=-2&take=3";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.OK, result.StatusCode);
        var responseObject = DeserializeObject<List<GetReleaseItemListResponse>>(responseString);
        Equal(3, responseObject.Count);
        // Should start with the 3rd item (index 2) since negative skip is converted to positive
        Equal("RI003", responseObject[0].ItemCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok_With_Default_Take_When_Take_Is_Zero()
    {
        // Arrange
        const string queryString = "?take=0";
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
        // No content to deserialize when NoContent is returned
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_NoContent_When_Skip_Exceeds_Available_Items()
    {
        // Arrange
        const string queryString = "?skip=20&take=10"; // Skip more than available items
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_Ok_With_Partial_ItemCode_Match()
    {
        // Arrange
        const string queryString = "?itemCode=RI01"; // Should match RI010, RI011, RI012
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode); // No exact match for "RI01"
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Return_NoContent_With_Case_Sensitive_ItemCode()
    {
        // Arrange
        const string queryString = "?itemCode=ri001"; // Lowercase, should not match RI001
        var request = MockHelpers.CreateHttpRequestData(query: queryString);

        // Act
        var response = await _releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;

        // Assert
        Equal(HttpStatusCode.NoContent, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Handle_Exception()
    {
        // Arrange
        var mockDbContextFactory = new Mock<IDbContextFactory<ReleaseItemContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Test exception"));

        var releaseItemFunctions = new ReleaseItemFunctions(mockDbContextFactory.Object, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
        var errorResponse = DeserializeObject<dynamic>(responseString);
        Equal("Test exception", (string)errorResponse.message);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetReleaseItemList_Should_Handle_DbUpdateException()
    {
        // Arrange
        var mockDbContext = new Mock<ReleaseItemContext>(new DbContextOptions<ReleaseItemContext>());
        var mockDbSet = new Mock<DbSet<ReleaseItem>>();

        // Setup the mock to throw a DbUpdateException when ToListAsync is called
        mockDbContext.Setup(m => m.ReleaseItem).Returns(mockDbSet.Object);
        mockDbSet.As<IQueryable<ReleaseItem>>().Setup(m => m.Provider).Throws(new DbUpdateException("Test DB exception", new Exception()));

        var mockDbContextFactory = new Mock<IDbContextFactory<ReleaseItemContext>>();
        mockDbContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockDbContext.Object);

        var releaseItemFunctions = new ReleaseItemFunctions(mockDbContextFactory.Object, _logger);
        var request = MockHelpers.CreateHttpRequestData();

        // Act
        var response = await releaseItemFunctions.ReleaseItem(request);
        var result = response as MockHttpResponseData;
        var responseString = LogResponse(_output, result);

        // Assert
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
        var errorResponse = DeserializeObject<dynamic>(responseString);
        NotNull(errorResponse.message);
    }
}