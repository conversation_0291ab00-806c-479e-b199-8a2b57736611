# Check In-Process Registration Tests

## Test Cases

### 1. TestCheckInProcessRegistrationByQId

Tests validation of in-process registration by QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckInProcessRegistrationByQId()
{
    // Arrange
    var request = GetRestRequest("registrations/{qId}/inprocess-validation");
    request.AddUrlSegment("qId", 22222222270);

    // Act
    var response = await _client.GetAsync<CheckInProcessRegistrationResponse>(request);
    response.ThrowIfNull();

    // Assert
    True(response.IsInprocessExist);
}
```

### 2. TestCheckInProcessRegistrationByQIdWithInvalidQId

Tests in-process validation with invalid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestCheckInProcessRegistrationByQIdWithInvalidQId()
{
    // Arrange
    var request = GetRestRequest("registrations/{qId}/inprocess-validation");
    request.AddUrlSegment("qId", 33322222230);

    // Act
    var response = await _client.GetAsync<CheckInProcessRegistrationResponse>(request);
    response.ThrowIfNull();

    // Assert
    False(response.IsInprocessExist);
}
```

## Request Details

### Endpoint
```
GET registrations/{qId}/inprocess-validation
```

### URL Parameters
- `qId`: QID to check for in-process registration

## Response Model

### CheckInProcessRegistrationResponse
```csharp
public class CheckInProcessRegistrationResponse
{
    public bool IsInprocessExist { get; set; }
}
```

## Test Data

### Success Case
- QID: 22222222270
- Expected: IsInprocessExist = true

### Error Case
- QID: 33322222230
- Expected: IsInprocessExist = false

## Validation Rules

### Success Case Validation
1. Response not null
2. IsInprocessExist is true

### Error Case Validation
1. Response not null
2. IsInprocessExist is false

## Error Cases

1. **Invalid QID**
   - Non-existent QID
   - Malformed QID
   - Invalid format

2. **System Errors**
   - Database errors
   - Service unavailable
   - Timeout errors

3. **Data State Errors**
   - Inconsistent registration state
   - Corrupted data
   - Race conditions

## Notes

1. **Purpose**
   - Validates in-process registration
   - Prevents duplicate submissions
   - Status verification

2. **Response Format**
   - Simple boolean response
   - Clear status indication
   - Null check required

3. **Performance**
   - Quick validation check
   - Status-based query
   - Efficient execution
