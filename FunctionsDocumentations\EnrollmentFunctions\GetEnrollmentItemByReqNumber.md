# GetEnrollmentItemByReqNumber

## Overview
Retrieves detailed enrollment information for a given request number.

## Endpoint
- **Route**: `enrollments/{reqNumber}`
- **Method**: GET
- **Authorization**: Function level with submitter QId validation

## Parameters
- **reqNumber** (path, required): Enrollment Request Number

## Response
- **200 OK**: Returns enrollment details
  ```json
  {
    "QId": "long",
    "ReqNumber": "string",
    "FNameEn": "string",
    "MNameEn": "string",
    "LNameEn": "string",
    "FNameAr": "string",
    "MNameAr": "string",
    "LNameAr": "string",
    "Status": "string",
    "StatusDescriptionEn": "string",
    "StatusDescriptionAr": "string",
    "SubmittedAt": "string",
    // Additional enrollment details...
  }
  ```
- **204 No Content**: Enrollment not found
- **400 Bad Request**: Invalid request number
- **401 Unauthorized**: Invalid submitter QId access

## Business Logic
1. Validates request number
2. Extracts submitter claims from request
3. Queries Enrollment table with Status navigation property
4. Validates submitter QId authorization
5. Maps to GetEnrollmentItemResponse model
6. Includes status descriptions in both languages

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient Include() for Status navigation
- SingleOrDefault() for exact match query
