﻿using System.Text;

using Azure.Core.Serialization;

using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.DependencyInjection;

using static MockDataLibrary.GlobalConstants;
using static MockDataLibrary.MockModels.MockPersonRecord;

namespace TestDoublesForUnitTest;

public static class MockHelpers
{
    public static HttpRequestData CreateHttpRequestData(byte[]? fileContent = null, string? fileName = "",
        string? contentType = "", string? payload = null, string method = "GET", string? query = null)
    {
        var functionContext = CreateContext(new NewtonsoftJsonObjectSerializer());
        string url = new Uri("http://localhost" + (query ?? "")).ToString();

        MemoryStream bodyStream;

        if (fileContent is { Length: > 0 })
        {
            var boundary = "----" + DateTime.Now.AddHours(3).Ticks.ToString("x");
            var boundaryBytes = Encoding.ASCII.GetBytes("\r\n--" + boundary + "\r\n");
            var trailer = Encoding.ASCII.GetBytes("\r\n--" + boundary + "--\r\n");

            bodyStream = new MemoryStream();
            bodyStream.Write(boundaryBytes, 0, boundaryBytes.Length);

            var header =
                $"Content-Disposition: form-data; name=\"file\"; filename=\"{fileName}\"\r\nContent-Type: {contentType}\r\n\r\n";
            var headerBytes = Encoding.UTF8.GetBytes(header);

            bodyStream.Write(headerBytes, 0, headerBytes.Length);
            bodyStream.Write(fileContent, 0, fileContent.Length);
            bodyStream.Write(trailer, 0, trailer.Length);
            contentType = $"multipart/form-data; boundary={boundary}";
        }
        else
        {
            var input = payload ?? string.Empty;
            bodyStream = new MemoryStream(Encoding.UTF8.GetBytes(input));
            contentType = "application/json";
        }

        var request = new MockHttpRequestData(functionContext, method: method, body: bodyStream, url: url);

        request.Headers.Add(ContentType, contentType);
        request.Headers.Add(PhccEnvironment, "DEV");
        request.Headers.Add(RequestOrigin, UnitTest);
        request.Headers.Add(JwtClaimsQId, QId);
        request.Headers.Add(EligibleClinicCodes, "OBK");
        request.Headers.Add(IsAuthValReq, "true");
        return request;
    }

    private static FunctionContext CreateContext(ObjectSerializer? serializer = null)
    {
        var context = new MockFunctionContext();
        var services = new ServiceCollection();
        services.AddOptions();
        services.AddFunctionsWorkerCore();
        services.Configure<WorkerOptions>(c => { c.Serializer = serializer; });
        context.InstanceServices = services.BuildServiceProvider();
        return context;
    }
}