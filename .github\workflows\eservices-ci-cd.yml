# minimal yaml approach.
# repo contains trigger definition and simple workflow call.
name: EServices RestAPI CI-CD
run-name: Release-1.1.${{github.run_number}} by @${{ github.actor }}
on:
    workflow_dispatch: # disbale this trigger after testing
    push:
            branches:
              - 'develop'
            paths-ignore:
              - 'DBScripts/**'
              - '.github/**'
              - 'README.md'
jobs:
    cicd-job:
        name: EServices RestAPI CI/CD
        uses: public-health-care-center-CORP/WorkflowRepo/.github/workflows/ci-cd-eservices-restapi.yml@main
        secrets: inherit
