using System.Linq.Expressions;

using EServiceFunctions.Models.EServiceGeneric;
using EServiceFunctions.Models.HealthCenter;
using EServiceFunctions.RequestResponseModels.HealthCenter;
using MockDataLibrary;

namespace EServiceFunctions.Functions;

public class HealthCenterFunctions(
    IDbContextFactory<HealthCenterContext> dbContextFactory,
    IDbContextFactory<EServiceContext> dbEServContextFactory,
    ILogger<HealthCenterFunctions> logger)
{
    #region GetHealthCenterList

    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetHealthCenterList")]
    [OpenApiOperation(operationId: "GetHealthCenterList", tags: ["HealthCenter"],
        Summary = "Get Health Center List", Description = " Get the health center list")]
    [OpenApiParameter("hcCode", Description = "Health Center Code", In = ParameterLocation.Query, Required = false,
        Type = typeof(string))]
    [OpenApiParameter("hcPurpose", Description = "Health Center Purpose. Ex: Regular or Covid19",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("clinicCode", Description = "Clinic Code", In = ParameterLocation.Query, Required = false,
        Type = typeof(string))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetHealthCenterListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetHealthCenterList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "healthcenters")]
        HttpRequestData req)
    {
        try
        {
            var submitter = req.GetClaims();
            
            try
            {
                logger.LogInformation("Getting Health Center List {QId}", submitter.QId);

                string hcCode = req.GetCleanedQueryString("hcCode");
                string hcPurpose = req.GetCleanedQueryString("hcPurpose");
                string clinicCode = req.GetCleanedQueryString("clinicCode");
                int skip = req.GetIntQueryParameter("skip");
                int take = req.GetIntQueryParameter("take");

                await using var dbContext = await dbContextFactory.CreateDbContextAsync();

                var query = dbContext.HealthCenter!.AsQueryable().AsNoTracking();

                if (hcCode.IsNotNullOrWhiteSpace())
                {
                    query = query.Where(item => item.HCntCode == hcCode);
                }

                if (hcPurpose.IsNotNullOrWhiteSpace())
                {
                    if (!IsValidHcPurpose(hcPurpose))
                    {
                        return await req.WriteErrorResponseAsync(BadRequest,
                            "Please provide a valid hcPurpose value. ex: Regular Or Covid19");
                    }

                    query = query.Where(item =>
                        item.COVID_HC == (hcPurpose == Covid19 ? 1 : 0));
                }

                if (clinicCode.IsNotNullOrWhiteSpace())
                {
                    var hcList = dbContext.Clinic!
                        .Where(item => item.ClinicCode == clinicCode)
                        .Select(item => item.FAC_CODE).Distinct();

                    query = query.Where(item => hcList.Contains(item.HCntCode));
                }
                
                var healthCenterPredicate = BuildHealthCenterPredicate();
                var response = await query.Where(healthCenterPredicate)
                    .Select(item => new GetHealthCenterListResponse
                    {
                        HCntCode = item.HCntCode,
                        HCntNameEn = item.HCntNameEn,
                        HCntNameAr = item.HCntNameAr,
                        HCntPurpose = item.COVID_HC == 1 ? Covid19 : Regular
                    })
                    .Skip(skip).Take(take).Distinct().ToListAsync();

                if (response.Count == 0)
                {
                    logger.LogInformation("Items not found");
                    return await req.WriteNoContentResponseAsync();
                }

                logger.LogInformation("Returning Health Center List ({Count})", response.Count);
                
                return await req.WriteOkResponseAsync(response);

                static Expression<Func<HealthCenter, bool>> BuildHealthCenterPredicate()
                {
                    return item => item.ORGANISATION == Phcc 
                                   && item.FACILITY_STATUS == Active 
                                   && item.FACILITY_TYPE == GlobalConstants.HealthCenter 
                                   && !string.IsNullOrEmpty(item.REGION_EN);
                }

                static bool IsValidHcPurpose(string hcPurpose)
                {
                    return hcPurpose is Regular or Covid19;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                return await req.WriteErrorResponseAsync();
            }
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetClinicList

    /// <param name="req"></param>
    /// <returns></returns>
    [Function("GetClinicList")]
    [OpenApiOperation(operationId: "GetClinicList", tags: ["HealthCenter"], Summary = "Get Clinic List",
        Description = " Get the clinic list")]
    [OpenApiParameter("hcCode", Description = "Health Center Code", In = ParameterLocation.Query, Required = false,
        Type = typeof(string))]
    [OpenApiParameter("clinicCode", Description = "Clinic Code", In = ParameterLocation.Query, Required = false,
        Type = typeof(string))]
    [OpenApiParameter("isNewAppointment", Description = "For New Appointment", In = ParameterLocation.Query,
        Required = false, Type = typeof(bool))]
    [OpenApiParameter("isEditable", Description = "For Change Appointment", In = ParameterLocation.Query,
        Required = false, Type = typeof(bool))]
    [OpenApiParameter("skip", Description = "Skip Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiParameter("take", Description = "Take Records", In = ParameterLocation.Query, Required = false,
        Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetClinicListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetClinicList(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "clinics")]
        HttpRequestData req)
    {
        try
        {
            var submitter = req.GetClaims();

            logger.LogInformation("Getting Clinic List {QId}", submitter.QId);

            string clinicCode = req.GetCleanedQueryString("clinicCode");
            string hcCode = req.GetCleanedQueryString("hcCode");

            var isNewAppointment = req.GetBooleanQueryParameter("isNewAppointment");
            var isEditable = req.GetBooleanQueryParameter("isEditable");

            int skip = req.GetIntQueryParameter("skip");
            int take = req.GetIntQueryParameter("take");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();
            var query = dbContext.Clinic!.AsQueryable().AsNoTracking();

            if (clinicCode.IsNotNullOrWhiteSpace())
            {
                query = query.Where(item => item.ClinicCode! == clinicCode);
            }

            if (hcCode.IsNotNullOrWhiteSpace())
            {
                query = query.Where(item => item.FAC_CODE! == hcCode);
            }

            if (isNewAppointment)
            {
                query = query.Where(item => item.IS_SELF_REFERRAL == 1);
            }

            if (isEditable)
            {
                query = query.Where(item => item.IS_RESCHEDULE == 1 || item.IS_CANCEL == 1);
            }

            var response = await query.Where(item => item.ACTIVE_IND == 1)
                .Select(item => new GetClinicListResponse
                {
                    ClinicCode = item.ClinicCode!,
                    ClinicNameEn = item.ClinicNameEn!,
                    ClinicNameAr = item.ClinicNameAr!
                })
                .Skip(skip).Take(take).Distinct().ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found");
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Clinic List ({Count})", response.Count);

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetClinicShiftListByHealthCenterCode

    /// <summary>
    /// Get the clinic shift list for the given Health Center Code (required) & Clinic Code/WorkDay/Shift (query params).
    /// </summary>
    /// <param name="req"></param>
    /// <param name="hcCode"></param>
    /// <returns></returns>
    [Function("GetClinicShiftListByHealthCenterCode")]
    [OpenApiOperation(operationId: "GetClinicShiftListByHealthCenterCode", tags: ["HealthCenter"],
        Summary = "Get Clinic & Shift List By Health Center Code",
        Description =
            " Get the clinic shift list for the given Health Center Code (required) & Clinic Code/WorkDay/Shift (query params).")]
    [OpenApiParameter("hcCode", Description = "Health Center Code", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiParameter("clinicCode", Description = "Clinic Code", In = ParameterLocation.Query, Required = false,
        Type = typeof(string))]
    [OpenApiParameter("workDay",
        Description =
            "Work Day. 1-SUN, 2-MON, 3-TUE, 4-WED, 5-THU, 6-FRI, 7-SAT. ex: 1 or 3 or 5 etc. for SUN or TUE  or THU",
        In = ParameterLocation.Query, Required = false, Type = typeof(string))]
    [OpenApiParameter("shift", Description = "Shift. 0-AM or 1-PM or 2-BOTH", In = ParameterLocation.Query,
        Required = false, Type = typeof(int))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetClinicShiftListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetClinicShiftListByHealthCenterCode(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "healthcenters/{hcCode}/clinic-shift")]
        HttpRequestData req, string hcCode)
    {
        try
        {
            var submitter = req.GetClaims();
            
            logger.LogInformation("Getting Clinic & Shift List By Health Center Code {HcCode} - {QId}", hcCode.Trim(), submitter.QId);

            string clinicCode = req.GetCleanedQueryString("clinicCode");
            string workDay = req.GetCleanedQueryString("workDay");
            string shift = req.GetCleanedQueryString("shift");

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            if (workDay.IsNotNullOrWhiteSpace() && (workDay.Length > 1 || workDay[0] < '1' || workDay[0] > '7'))
            {
                 return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Working Day {workDay}");
            }

            if (shift.IsNotNullOrWhiteSpace() && (shift.Length > 1 || shift[0] < '0' || shift[0] > '2'))
            {
                return await req.WriteErrorResponseAsync(BadRequest, $"Invalid Shift {shift}");
            }

            var response = await (from cs in dbContext.ClinicShift
                                  join c in dbContext.Clinic on cs.ClinicCode equals c.ClinicCode
                                  where cs.HCntCode == hcCode.Trim()
                                        && (string.IsNullOrWhiteSpace(clinicCode) || cs.ClinicCode == clinicCode)
                                        && (string.IsNullOrWhiteSpace(workDay) || cs.WorkDays.Contains(workDay))
                                        && (string.IsNullOrWhiteSpace(shift) || cs.ShiftTime == ToInt32(shift))
                                  select new GetClinicShiftListResponse
                                  {
                                      ClinicCode = c.ClinicCode, ClinicNameEn = c.ClinicNameEn,
                                      ClinicNameAr = c.ClinicNameAr, WorkDays = cs.WorkDays,
                                      ShiftTime = Convert.ToString(cs.ShiftTime)
                                  }).Distinct().AsNoTracking().ToListAsync();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for the given health center code {HcCode}", hcCode.Trim());
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Clinic & Shift List By Health Center Code {HcCode}", hcCode.Trim());
            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion

    #region GetClinicDayShiftListByHealthCenterAndClinicCode

    /// <summary>
    ///  Get shifts data list by health center code and clinic code    
    /// </summary>
    /// <param name="req"></param>
    /// <param name="hcCode"></param>
    /// <param name="clinicCode"></param>
    /// <returns></returns>
    [Function("GetClinicDayShiftListByHealthCenterAndClinicCode")]
    [OpenApiOperation(operationId: "GetClinicDayShiftListByHealthCenterAndClinicCode", tags: ["HealthCenter"],
        Summary = "Get shifts data list by health center code and clinic code",
        Description = " Get shifts data list by health center code and clinic code.")]
    [OpenApiParameter("hcCode", Description = "Health Center Code", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiParameter("clinicCode", Description = "Clinic Code", In = ParameterLocation.Path, Required = true,
        Type = typeof(string))]
    [OpenApiResponseWithBody(OK, "application/json", typeof(List<GetShiftDataListResponse>))]
    [OpenApiResponseWithBody(NoContent, "application/json", typeof(object))]
    public async Task<HttpResponseData> GetClinicDayShiftListByHealthCenterAndClinicCode(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "clinic-day-shift/{hcCode}/{clinicCode}")]
        HttpRequestData req, string hcCode, string clinicCode)
    {
        try
        {
            logger.LogInformation("Getting shifts data list by health center code and clinic code");

            clinicCode = clinicCode.RemoveWhitespaces();
            hcCode = hcCode.RemoveWhitespaces();
            var startWithCurrentDate = GetCurrentTime();
            var after5Months = startWithCurrentDate.AddMonths(5);
            var lastDayOfEndMonth = new DateTime(after5Months.Year, after5Months.Month, 1, 0, 0, 0, DateTimeKind.Utc).AddMonths(1).AddDays(-1);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync();

            var clinicDaysShift = await dbContext.ClinicDaysShift!.Where(i =>
                    i.HCntCode! == hcCode && i.ClinicCode! == clinicCode &&
                    i.DATE_FLD >= startWithCurrentDate && i.DATE_FLD <= lastDayOfEndMonth)
                .Select(item => new ClinicDaysShift
                {
                    DATE_FLD = item.DATE_FLD,
                    ShiftCode = item.ShiftCode
                }).AsNoTracking().ToListAsync();

            await using var dbEServContext = await dbEServContextFactory.CreateDbContextAsync();

            var shift = await dbEServContext.Shift!.Distinct().AsNoTracking().ToListAsync();

            var response = (from c in clinicDaysShift
                            join s in shift on c.ShiftCode equals s.Code
                            select new GetShiftDataListResponse
                            {
                                AvailableDate = c.DATE_FLD.ToUtcString(),
                                ShiftCode = c.ShiftCode,
                                ShiftDescriptionEn = s.DescriptionEn,
                                ShiftDescriptionAr = s.DescriptionAr
                            }).ToList();

            if (response.Count == 0)
            {
                logger.LogInformation("Items not found for the given health center code {HcCode} and clinic code {ClinicCode}", hcCode.Trim(), clinicCode.Trim());
                return await req.WriteNoContentResponseAsync();
            }

            logger.LogInformation("Returning Clinic & Shift List By Health Center Code {HcCode} and clinic code {ClinicCode}", hcCode.Trim(), clinicCode.Trim());

            return await req.WriteOkResponseAsync(response);
        }
        catch (Exception ex)
        {
            return ex.DefaultExceptionBehaviour(req, logger);
        }
    }

    #endregion
}