# Covid Vaccination Functions Documentation

## Overview
The Covid Vaccination Functions module provides endpoints for managing COVID-19 vaccination requests in the healthcare system. It implements comprehensive request validation, error handling, and tracing capabilities.

## API Endpoints

### Create Covid Vaccination Request
- **Endpoint**: `POST /covidvaccinationrequest`
- **Description**: Creates a new COVID-19 vaccination request with validation and error handling
- **Authorization**: Function level

#### Request Body
```json
{
    "IDNumber": "string",     // Required: Identification number of the requestor
    "IDType": "string",       // Required: Type of identification document
    "MobileNumber": "string"  // Required: Contact mobile number
}
```

#### Validation Rules
- All fields are mandatory and cannot be empty or whitespace
- Request body must be valid JSON format
- Request object cannot be null or empty

#### Response
- **201 Created**:
  ```json
  {
    "CovidVaccinationURL": "string"  // URL for tracking the vaccination request
  }
  ```
- **400 Bad Request**:
  ```json
  {
    "message": "string",  // Specific error message
    "details": "string"   // Additional error details if available
  }
  ```
  Common error messages:
  - "Invalid request body. Please try again."
  - "ID Number is required."
  - "ID Type is required."
  - "Mobile Number is required."
  - "Invalid request format."

- **500 Internal Server Error**:
  ```json
  {
    "message": "string"  // Generic error message
  }
  ```
  Common error messages:
  - "Unable to create vaccination record. Please try again later."
  - "Request processed but unable to generate response URL."
  - "An unexpected error occurred. Please try again later."

## Technical Implementation

### Request Processing Flow
1. **Request Validation**
   - Parses and validates JSON request body
   - Checks for required fields
   - Validates data format and content

2. **Record Creation**
   - Generates unique GUID for the request
   - Sets initial status as "Ready"
   - Records creation timestamp
   - Saves to database using Entity Framework Core

3. **Response Generation**
   - Retrieves K2 URL from Key Vault
   - Generates tracking URL with request ID
   - Returns URL in response

### Database Operations
- Uses `GeneralWorksContext` with Entity Framework Core
- Implements asynchronous database operations
- Handles database transaction errors
- Uses optimistic concurrency

### Error Handling
1. **Input Validation Errors**
   - JSON parsing errors
   - Missing required fields
   - Invalid data format

2. **Database Errors**
   - Connection issues
   - Transaction failures
   - Concurrency conflicts

3. **System Errors**
   - Key Vault access issues
   - Internal processing errors
   - Unexpected exceptions

### Logging and Monitoring
- **Correlation Tracking**
  - Generates unique correlation ID for each request
  - Tracks request through all processing stages
  - Links related log entries

- **Activity Logging**
  - Request validation results
  - Database operation outcomes
  - Error conditions and stack traces
  - Performance metrics

- **Log Levels**
  - Information: Normal operation events
  - Debug: Detailed processing information
  - Warning: Validation failures
  - Error: System and database errors

### Security Features
- Function-level authorization
- Input sanitization and validation
- Secure configuration management via Key Vault
- Error message sanitization
- Request correlation tracking

### Performance Considerations
- Asynchronous operations
- Efficient database queries
- Connection pooling
- Resource cleanup
- Exception handling optimization

## Dependencies
- Entity Framework Core for data access
- Azure Key Vault for configuration
- System.Text.Json for JSON handling
- Microsoft.Azure.Functions.Worker for Azure Functions
- Application Insights for monitoring

## Best Practices
1. **Error Handling**
   - Comprehensive exception handling
   - Detailed error logging
   - User-friendly error messages
   - Secure error responses

2. **Data Validation**
   - Input validation at multiple levels
   - Strong typing
   - Data sanitization
   - Format validation

3. **Security**
   - Authorization checks
   - Input validation
   - Secure configuration
   - Audit logging

4. **Performance**
   - Async/await patterns
   - Resource management
   - Connection pooling
   - Error handling optimization
