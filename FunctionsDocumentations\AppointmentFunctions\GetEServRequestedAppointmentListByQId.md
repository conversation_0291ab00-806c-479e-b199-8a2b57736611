# GetEServRequestedAppointmentListByQId

## Overview
Retrieves the eServ requested appointment list for a given submitter's QId with optional filtering.

## Endpoint
- **Route**: `appointments/submitter-qid/{qId}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `qId` (path, required): Submitter's QID
- `status` (query, optional): Filter by status
  - InProcess: Submitted, Rework, Reworked
  - Archived: Approved, Cancelled, CancelledByEServ, Rejected
- `requestType` (query, optional): Filter by request type (New, Reschedule, Cancel)
- `clinicCode` (query, optional): Filter by clinic code
- `skip` (query, optional): Number of records to skip
- `take` (query, optional): Number of records to take

## Headers
- `EligibleClinicCodes`: List of eligible clinic codes

## Responses
- **200 OK**: Returns list of `GetAppointmentListResponse`
- **400 Bad Request**: Invalid request parameters
- **204 No Content**: No appointments found

## Business Logic
1. Validates submitter's authorization
2. Validates clinic codes from headers
3. Applies filters based on query parameters
4. Returns paginated list of appointments

## Dependencies
- AppointmentContext
- Status entity
