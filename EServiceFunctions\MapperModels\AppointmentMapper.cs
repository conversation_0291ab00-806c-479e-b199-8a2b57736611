﻿using EServiceFunctions.Models.Appointment;
using EServiceFunctions.RequestResponseModels.Appointment;

namespace EServiceFunctions.MapperModels;

public static class AppointmentMapper
{
    public static GetAppointmentItemResponse MapAppointmentToGetAppointmentItemResponse(Appointment appointment)
    {
        var response = new GetAppointmentItemResponse
        {
            QId = appointment.QId,
            ReqNumber = appointment.ReqNumber,
            FNameEn = appointment.FNameEn,
            MNameEn = appointment.MNameEn,
            LNameEn = appointment.LNameEn,
            FNameAr = appointment.FNameAr,
            MNameAr = appointment.MNameAr,
            LNameAr = appointment.LNameAr,
            Gender = appointment.Gender,
            SecondaryPhoneMobile = appointment.SecondaryPhoneMobile,
            CancellationCall = appointment.CancellationCall,
            Consent = appointment.Consent,
            Nationality = appointment.Nationality,
            Dob = appointment.Dob.ToUtcString(),
            HcNumber = appointment.HCNumber,
            Clinic = appointment.Clinic,
            ClinicTime = appointment.ClinicTime,
            PrefDate = appointment.PrefDate.ToUtcString(),
            AmPm = appointment.AmPmShiftNavigation?.Code,
            AmPmDescriptionEn = appointment.AmPmShiftNavigation?.DescriptionEn,
            AmPmDescriptionAr = appointment.AmPmShiftNavigation?.DescriptionAr,
            SelectedHc = appointment.SelectedHC,
            RequestType = appointment.AppointmentRequestTypeNavigation?.Code,
            RequestTypeDescriptionEn = appointment.AppointmentRequestTypeNavigation?.DescriptionEn,
            RequestTypeDescriptionAr = appointment.AppointmentRequestTypeNavigation?.DescriptionAr,
            PrefContactTime = appointment.PrefContactTimeShiftNavigation?.Code,
            PrefContactTimeDescriptionEn = appointment.PrefContactTimeShiftNavigation?.DescriptionEn,
            PrefContactTimeDescriptionAr = appointment.PrefContactTimeShiftNavigation?.DescriptionAr,
            SubmittedBy = appointment.SubmittedBy,
            SubmittedAt = appointment.SubmittedAt.ToUtcString(),
            SubmitterQId = appointment.SubmitterQId,
            SubmitterEmail = appointment.SubmitterEmail,
            SubmitterMobile = appointment.SubmitterMobile,
            CreatedAt = appointment.CreatedAt.ToUtcString(),
            UpdatedAt = appointment.UpdatedAt.ToUtcString(),
            Status = appointment.StatusNavigation?.Code,
            StatusDescriptionEn = appointment.StatusNavigation?.DescriptionEn,
            StatusDescriptionAr = appointment.StatusNavigation?.DescriptionAr,
            SN = appointment.SN,
            AppointmentId = appointment.AppointmentId
        };

        return response;
    }

    public static Appointment MapCreateUpdateAppointmentRequestToAppointment(CreateUpdateAppointmentRequest createUpdateAppointmentRequest)
    {
        var response = new Appointment
        {
            QId = createUpdateAppointmentRequest.QId,
            FNameEn = createUpdateAppointmentRequest.FNameEn,
            MNameEn = createUpdateAppointmentRequest.MNameEn,
            LNameEn = createUpdateAppointmentRequest.LNameEn,
            FNameAr = createUpdateAppointmentRequest.FNameAr,
            MNameAr = createUpdateAppointmentRequest.MNameAr,
            LNameAr = createUpdateAppointmentRequest.LNameAr,
            Gender = createUpdateAppointmentRequest.Gender,
            SecondaryPhoneMobile = createUpdateAppointmentRequest.SecondaryPhoneMobile,
            CancellationCall = createUpdateAppointmentRequest.CancellationCall,
            Consent = createUpdateAppointmentRequest.Consent,
            Nationality = createUpdateAppointmentRequest.Nationality,
            Dob = createUpdateAppointmentRequest.Dob,
            HCNumber = createUpdateAppointmentRequest.HcNumber,
            Clinic = createUpdateAppointmentRequest.Clinic,
            ClinicTime = createUpdateAppointmentRequest.ClinicTime,
            PrefDate = createUpdateAppointmentRequest.PrefDate,
            AmPm = createUpdateAppointmentRequest.AmPm,
            SelectedHC = createUpdateAppointmentRequest.SelectedHc,
            RequestType = createUpdateAppointmentRequest.RequestType,
            PrefContactTime = createUpdateAppointmentRequest.PrefContactTime,
            SubmittedBy = createUpdateAppointmentRequest.SubmittedBy,
            SubmitterQId = createUpdateAppointmentRequest.SubmitterQId,
            SubmitterEmail = createUpdateAppointmentRequest.SubmitterEmail,
            SubmitterMobile = createUpdateAppointmentRequest.SubmitterMobile,
            AppointmentId = createUpdateAppointmentRequest.AppointmentId
        };
        return response;
    }

    public static UpcomingConfirmedAppointmentListResponse MapGetUpcomingConfirmedAppointmentListResponseToUpcomingConfirmedAppointmentListResponse(GetUpcomingConfirmedAppointmentListResponse getUpcomingConfirmedAppointmentListResponse)
    {
        var response = new UpcomingConfirmedAppointmentListResponse
        {
            AppointmentId = getUpcomingConfirmedAppointmentListResponse.AppointmentId,
            FNameEn = getUpcomingConfirmedAppointmentListResponse.FNameEn,
            MNameEn = getUpcomingConfirmedAppointmentListResponse.MNameEn,
            LNameEn = getUpcomingConfirmedAppointmentListResponse.LNameEn,
            FNameAr = getUpcomingConfirmedAppointmentListResponse.FNameAr,
            MNameAr = getUpcomingConfirmedAppointmentListResponse.MNameAr,
            LNameAr = getUpcomingConfirmedAppointmentListResponse.LNameAr,
            ClinicCode = getUpcomingConfirmedAppointmentListResponse.ClinicCode,
            ClinicNameEn = getUpcomingConfirmedAppointmentListResponse.ClinicNameEn,
            ClinicNameAr = getUpcomingConfirmedAppointmentListResponse.ClinicNameAr,
            PhysicianId = getUpcomingConfirmedAppointmentListResponse.PhysicianId,
            PhysicianFullNameEn = getUpcomingConfirmedAppointmentListResponse.PhysicianFullNameEn,
            PhysicianFullNameAr = getUpcomingConfirmedAppointmentListResponse.PhysicianFullNameAr,
            AppointmentDateTime = getUpcomingConfirmedAppointmentListResponse.AppointmentDateTime,
            AppointmentType = getUpcomingConfirmedAppointmentListResponse.AppointmentType,
            AppointmentHCntCode = getUpcomingConfirmedAppointmentListResponse.AppointmentHCntCode,
            AppointmentHCntNameEn = getUpcomingConfirmedAppointmentListResponse.AppointmentHCntNameEn,
            AppointmentHCntNameAr = getUpcomingConfirmedAppointmentListResponse.AppointmentHCntNameAr
        };
        return response;
    }
    public static GetUpcomingConfirmedAppointmentItemResponse MapGetUpcomingConfirmedAppointmentListResponseToGetUpcomingConfirmedAppointmentItemResponse(GetUpcomingConfirmedAppointmentListResponse getUpcomingConfirmedAppointmentListResponse)
    {
        var response = new GetUpcomingConfirmedAppointmentItemResponse
        {
            AppointmentId = getUpcomingConfirmedAppointmentListResponse.AppointmentId,
            QId = getUpcomingConfirmedAppointmentListResponse.QId,
            QIdExpiryDt = getUpcomingConfirmedAppointmentListResponse.QIdExpiryDt,
            FNameEn = getUpcomingConfirmedAppointmentListResponse.FNameEn,
            MNameEn = getUpcomingConfirmedAppointmentListResponse.MNameEn,
            LNameEn = getUpcomingConfirmedAppointmentListResponse.LNameEn,
            FNameAr = getUpcomingConfirmedAppointmentListResponse.FNameAr,
            MNameAr = getUpcomingConfirmedAppointmentListResponse.MNameAr,
            LNameAr = getUpcomingConfirmedAppointmentListResponse.LNameAr,
            NationalityCode = getUpcomingConfirmedAppointmentListResponse.NationalityCode,
            NationalityEn = getUpcomingConfirmedAppointmentListResponse.NationalityEn,
            NationalityAr = getUpcomingConfirmedAppointmentListResponse.NationalityAr,
            Dob = getUpcomingConfirmedAppointmentListResponse.Dob,
            HcNumber = getUpcomingConfirmedAppointmentListResponse.HCNumber,
            HcExpiryDate = getUpcomingConfirmedAppointmentListResponse.HCExpiryDate,
            GenderCode = getUpcomingConfirmedAppointmentListResponse.GenderCode,
            GenderEn = getUpcomingConfirmedAppointmentListResponse.GenderEn,
            GenderAr = getUpcomingConfirmedAppointmentListResponse.GenderAr,
            PhoneMobile = getUpcomingConfirmedAppointmentListResponse.PhoneMobile,
            AggignedHCntCode = getUpcomingConfirmedAppointmentListResponse.AggignedHCntCode,
            AggignedHCntNameEn = getUpcomingConfirmedAppointmentListResponse.AggignedHCntNameEn,
            AggignedHCntNameAr = getUpcomingConfirmedAppointmentListResponse.AggignedHCntNameAr,
            AppointmentHCntCode = getUpcomingConfirmedAppointmentListResponse.AppointmentHCntCode,
            AppointmentHCntNameEn = getUpcomingConfirmedAppointmentListResponse.AppointmentHCntNameEn,
            AppointmentHCntNameAr = getUpcomingConfirmedAppointmentListResponse.AppointmentHCntNameAr,
            ClinicCode = getUpcomingConfirmedAppointmentListResponse.ClinicCode,
            ClinicNameEn = getUpcomingConfirmedAppointmentListResponse.ClinicNameEn,
            ClinicNameAr = getUpcomingConfirmedAppointmentListResponse.ClinicNameAr,
            GisAddressStreet = getUpcomingConfirmedAppointmentListResponse.GisAddressStreet,
            GisAddressBuilding = getUpcomingConfirmedAppointmentListResponse.GisAddressBuilding,
            GisAddressZone = getUpcomingConfirmedAppointmentListResponse.GisAddressZone,
            GisAddressUnit = getUpcomingConfirmedAppointmentListResponse.GisAddressUnit,
            PhysicianId = getUpcomingConfirmedAppointmentListResponse.PhysicianId,
            PhysicianFullNameEn = getUpcomingConfirmedAppointmentListResponse.PhysicianFullNameEn,
            PhysicianFullNameAr = getUpcomingConfirmedAppointmentListResponse.PhysicianFullNameAr,
            BookedDateTime = getUpcomingConfirmedAppointmentListResponse.BookedDateTime,
            AppointmentDateTime = getUpcomingConfirmedAppointmentListResponse.AppointmentDateTime,
            AppointmentType = getUpcomingConfirmedAppointmentListResponse.AppointmentType,
            AppointmentStatus = getUpcomingConfirmedAppointmentListResponse.AppointmentStatus,
            AppointmentLocation = getUpcomingConfirmedAppointmentListResponse.AppointmentLocation,
            AppointmentFacility = getUpcomingConfirmedAppointmentListResponse.AppointmentFacility
        };
        return response;
    }
}