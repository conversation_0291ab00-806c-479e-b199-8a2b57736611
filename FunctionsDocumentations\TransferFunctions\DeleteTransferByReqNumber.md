# Delete Transfer By Request Number

## Overview
Deletes a transfer request identified by its request number.

## Endpoint
```http
DELETE /transfers/{reqNumber}
```

## Authorization
- Function-level authorization
- QID-based access control via claims
- Requires valid submitter QID

## Parameters

### Path Parameters
- `reqNumber` (string, required): Transfer Request Number

## Response

### Success Response (200 OK)
Returns success message:
```json
"Deleted Successfully"
```

### Error Responses
- 400 Bad Request: Invalid request number or database operation failure
- 401 Unauthorized: Invalid QID or unauthorized access
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Implements proper database transaction handling
- Validates request existence before deletion
- Ensures proper authorization via claims
- Handles cascading deletions if configured
- Maintains data integrity
- Comprehensive error handling

## Business Logic
- Validates request number existence
- Verifies submitter authorization through claims
- Performs complete record deletion
- Handles related data cleanup
- Maintains audit trail
- Validates data consistency

## Security Considerations
- Validates submitter QID authorization via claims
- Implements function-level security
- Ensures data access control
- Validates request ownership
- Maintains deletion audit trail
- Proper error handling

## Performance Optimization
- Efficient database transaction handling
- Proper error handling and logging
- Optimized deletion process
- Transaction isolation level management
- Early validation checks
