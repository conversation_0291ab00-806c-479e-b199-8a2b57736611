# GetEnrollmentListByQId

## Overview
Retrieves a list of enrollments for a given submitter's QId with optional filtering by status.

## Endpoint
- **Route**: `enrollments/submitter-qid/{qId}`
- **Method**: GET
- **Authorization**: Function level with QId validation

## Parameters
- **qId** (path, required): Submitter's QId
- **status** (query, optional): Filter by status category
  - InProcess: Submitted, Rework, Reworked
  - Archived: Approved, Cancelled, CancelledByEServ, Rejected
- **skip** (query, optional): Number of records to skip for pagination
- **take** (query, optional): Number of records to take for pagination

## Response
- **200 OK**: Returns list of enrollments
  ```json
  [
    {
      "QId": "long",
      "ReqNumber": "string",
      "FNameEn": "string",
      "MNameEn": "string",
      "LNameEn": "string",
      "FNameAr": "string",
      "MNameAr": "string",
      "LNameAr": "string",
      "Status": "string",
      "StatusDescriptionEn": "string",
      "StatusDescriptionAr": "string",
      "SubmittedAt": "string"
    }
  ]
  ```
- **204 No Content**: No enrollments found
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Invalid QId access

## Business Logic
1. Validates submitter QId authorization
2. Queries Enrollment table with Status join
3. Filters by submitter QId and optional status
4. Applies pagination (skip/take)
5. Projects to GetEnrollmentListResponse model
6. Orders results by status

## Query Optimization
- Uses AsNoTracking() for read-only data
- Efficient projection in Select statement
- Pagination to limit result set size
