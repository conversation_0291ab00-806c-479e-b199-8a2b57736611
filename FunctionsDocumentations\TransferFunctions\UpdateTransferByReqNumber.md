# Update Transfer By Request Number

## Overview
Updates an existing transfer request identified by its request number.

## Endpoint
```http
PUT /transfers/{reqNumber}
```

## Authorization
- Function-level authorization
- QID-based access control
- Requires valid submitter QID

## Headers
- `X-RequestOrigin` (required): Source of the request

## Parameters

### Path Parameters
- `reqNumber` (string, required): Transfer Request Number

## Request Body
```json
{
  "submitterQId": "long",
  "qId": "long",
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string",
  "transferReason": "string"
}
```

## Response

### Success Response (200 OK)
Returns the updated transfer request:
```json
{
  "reqNumber": "string",
  "submitterQId": "long",
  "qId": "long",
  "status": "string",
  "submittedAt": "string (UTC)",
  "updatedAt": "string (UTC)",
  "updateSource": "string"
  // Additional fields from request
}
```

### Error Responses
- 400 Bad Request: Invalid request number, request body, or missing request origin
- 401 Unauthorized: Invalid QID or unauthorized access
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Preserves original request number and creation details
- Updates submission timestamp
- Records update source
- Implements proper database transaction handling
- Supports multilingual content (English/Arabic)
- Validates request existence

## Business Logic
- Maintains original request number
- Preserves creation timestamp and source
- Updates submission timestamp
- Records update source
- Validates submitter authorization
- Tracks modification state
- Validates transfer reason

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control
- Validates request origin
- Sanitizes input data
- Proper error handling

## Performance Optimization
- Efficient database transaction handling
- Proper error handling and logging
- Optimized data update process
- Change tracking optimization
- Input validation optimization
