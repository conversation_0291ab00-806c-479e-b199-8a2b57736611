# EservicesRESTapis Documentation Structure

# Structure Diagram 

![EservicesRESTapis Structure Diagram](./EServices-Documentation-Structure.png)

## Service Categories

### 1. Healthcare Services
- [Appointment Functions](AppointmentFunctions.md)
  * [Create Appointment](AppointmentFunctions/CreateAppointment.md)
  * [Update Appointment](AppointmentFunctions/UpdateAppointmentByReqNumber.md)
  * [Delete Appointment](AppointmentFunctions/DeleteAppointmentByReqNumber.md)
  * [Get Appointments](AppointmentFunctions/GetAppointmentsListByQIdV1.md)
  * [Get Statistics](AppointmentFunctions/GetAppointmentStatsByQId.md)

- [Family Physician Functions](FamilyPhysicianFunctions.md)
  * [Get Physician List](FamilyPhysicianFunctions/GetFamilyPhysicianList.md)
  * [Get Physician Details](FamilyPhysicianFunctions/GetFamilyPhysicianDetails.md)

- [Health Center Functions](HealthCenterFunctions.md)
  * [Get Clinic List](HealthCenterFunctions/GetClinicList.md)
  * [Get Shift List](HealthCenterFunctions/GetShiftList.md)

### 2. User Management
- [User Profile Functions](UserProfileFunctions.md)
  * [Create Profile](UserProfileFunctions/CreateUserProfile.md)
  * [Update Profile](UserProfileFunctions/UpdateUserProfileByQID.md)
  * [Delete Profile](UserProfileFunctions/DeleteUserProfileByQID.md)
  * [Get Dependents](UserProfileFunctions/GetUserAndDependentListByQID.md)
  * [Validate QID](UserProfileFunctions/ValidateQID.md)

- [Comments and Attachments](CommentAndAttachmentFunctions.md)
  * [Create Attachment](CommentAndAttachmentFunctions/CreateAttachment.md)
  * [Delete Attachment](CommentAndAttachmentFunctions/DeleteAttachment.md)
  * [Create Comment](CommentAndAttachmentFunctions/CreateComment.md)
  * [Get Comments](CommentAndAttachmentFunctions/GetComments.md)

### 3. Administrative Services
- [Assignment Functions](AssignmentFunctions.md)
  * [Create Assignment](AssignmentFunctions/CreateAssignment.md)
  * [Update Assignment](AssignmentFunctions/UpdateAssignmentByReqNumber.md)
  * [Delete Assignment](AssignmentFunctions/DeleteAssignmentByReqNumber.md)
  * [Get Assignments](AssignmentFunctions/GetAssignmentListByQId.md)

- [Registration Functions](RegistrationFunctions.md)
  * [Create Registration](RegistrationFunctions/CreateRegistration.md)
  * [Update Registration](RegistrationFunctions/UpdateRegistrationByReqNumber.md)
  * [Delete Registration](RegistrationFunctions/DeleteRegistrationByReqNumber.md)
  * [Get Registrations](RegistrationFunctions/GetRegistrationListByQID.md)

### 4. System Services
- [EDW Dropdown Functions](EdwDropDownListFunctions.md)
  * [Get Dropdown List](EdwDropDownListFunctions/GetDropdownList.md)
  * [Get Reference Data](EdwDropDownListFunctions/GetReferenceData.md)

- [Miscellaneous Functions](MiscellaneousFunctions.md)
  * [Get System Health](MiscellaneousFunctions/GetSystemHealth.md)
  * [Get Statistics](MiscellaneousFunctions/GetStatistics.md)

## Common Patterns

### API Structure
Each function follows a consistent pattern:
- **Endpoint**: RESTful endpoint definition
- **Authorization**: Security requirements
- **Request/Response**: Data structures
- **Implementation**: Technical details
- **Examples**: Usage examples

### Documentation Format
```
FunctionsDocumentations/
├── [Service]Functions.md         # Service overview
├── [Service]Functions/           # Individual function docs
│   ├── Create[Service].md       # Create operation
│   ├── Update[Service].md       # Update operation
│   ├── Delete[Service].md       # Delete operation
│   └── Get[Service].md          # Retrieve operation
```

### Cross-Service Integration
- User Profile ↔ Appointments
- Appointments ↔ Health Centers
- User Profile ↔ Family Physician
- All Services ↔ EDW Dropdowns

## Quick Links

### Common Operations
- [Create User Profile](UserProfileFunctions/CreateUserProfile.md)
- [Book Appointment](AppointmentFunctions/CreateAppointment.md)
- [Upload Document](CommentAndAttachmentFunctions/CreateAttachment.md)
- [Validate QID](UserProfileFunctions/ValidateQID.md)

### System Integration
- [EDW Integration](EdwDropDownListFunctions.md)
- [Token Management](TokenGeneratorFunctions.md)
- [System Health](MiscellaneousFunctions.md)

## Implementation References
- [Source Code](../EServiceFunctions/Functions/)
- [Database Context](../EServiceFunctions/Data/)
- [Models](../EServiceFunctions/Models/)
- [Extensions](../EServiceFunctions/Extensions/)
