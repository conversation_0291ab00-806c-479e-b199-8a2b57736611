# GetCommentByReqNumber

## Overview
Retrieves comments for a given request number.

## Endpoint
- **Route**: `comments/{reqNumber}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `reqNumber` (path, required): Comment request number

## Responses
- **200 OK**: Returns list of comments
  ```json
  [{
    "Action": "string",
    "CommentText": "string",
    "CreatedAt": "datetime"
  }]
  ```
- **400 Bad Request**: Invalid request number
- **204 No Content**: No comments found

## Business Logic
1. Validates request number
2. Filters comments by:
   - Request number
   - Display to submitter flag
   - Submitter QID (if provided)
3. Returns filtered comments

## Query Parameters
- Comments are filtered to show only those:
  - Matching the request number
  - Marked as displayable to submitter
  - Matching submitter QID (if QID provided)

## Dependencies
- CommentAndAttachmentContext
- Authorization middleware
