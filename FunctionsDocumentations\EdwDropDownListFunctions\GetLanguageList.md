# GetLanguageList

## Overview
Retrieves a list of languages using a stored procedure from EDW.

## Endpoint
- **Route**: `languages`
- **Method**: GET
- **Authorization**: Function level

## Response
- **200 OK**: Returns list of languages
  ```json
  [
    {
      "LanguageCode": "string",           // Language code
      "LanguageDescriptionEn": "string",  // English description
      "LanguageDescriptionAr": "string"   // Arabic description
    }
  ]
  ```
- **204 No Content**: No languages found
- **400 Bad Request**: Error processing request

## Business Logic
1. Logs request URL and operation start
2. Executes SP_GET_PHYSICIAN_LANG_CODES stored procedure
3. Maps results to GetLanguageListResponse model
4. Checks for empty result set
5. Returns formatted response

## Data Source
- Stored Procedure: SP_GET_PHYSICIAN_LANG_CODES
- Context: EDWDbContext (Enterprise Data Warehouse)

## Query Optimization
- Direct stored procedure execution
- No unnecessary data transformation
- Efficient result mapping
