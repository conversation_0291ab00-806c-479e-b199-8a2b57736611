
name: Test Version update changes
run-name:  Test Yaml  - ${{github.run_number}} by @${{ github.actor }}
on:
 workflow_dispatch:

jobs:
 test:
   runs-on: [self-hosted, windows ]
   permissions:
     contents: read
     id-token: write
   environment: STAGE-SECONDARY
   steps:
           - name: Sign in to Azure
             uses: azure/login@v1  # current latest version is v1.6.1 - no need to explicitly mention the minor version. It is always refers the latest
             id: login-azure
             with:
                 client-id: ${{ vars.AZURE_CLIENT_ID }}
                 tenant-id: ${{ vars.AZURE_TENANT_ID }}
                 subscription-id: ${{ vars.AZURE_SUBSCRIPTION_ID }}
           
           - name:  Run Annotation From Action
             uses: public-health-care-center-CORP/WorkflowRepo/shared/deployment/release-annotations@main
             with:
               annotation-name: 'eServicesAPIDeployment-Release-1.1.383'
               resource-id:  '${{vars.AZURE_APP_INSIGHT}}'
               properties: |
                     @{
                            BuildNumber = "1.1.383"
                            BuildRepositoryName = "${{github.repository}}"
                            RepositoryOwner = "${{github.repository_owner}}"
                            CommitId = "4d1ba92236dc90c7233bbd879137852dc9afc366"
                            RepositoryURL = "${{github.repositoryUrl}}"
                            ReleaseId = "1.1.383"
                            SourceBranch = "${{ github.ref_name }}"
                            DeploymentBranch_Tag = "Release-1.1.383"
                            Author = "mmfazrin-phcc-gov"
                            ReleaseEnvironment = "TEST"
                            GitHubWorkflow = "${{github.workflow}}"
                            WorkflowRef = "${{github.workflow_ref}}"
                            DeployedFrom = "${{runner.name}}"
                            DeployedResource = "${{vars.AZURE_WEBAPP_NAME}}"
                            DeployedSlot = "${{vars.AZURE_APP_SLOT}}"
                            Subscription = "${{vars.AZURE_SUBSCRIPTION_ID}}"
                            DeploymentResourceType = "Azure Function App"
                     }
