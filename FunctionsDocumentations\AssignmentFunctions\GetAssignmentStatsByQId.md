# GetAssignmentStatsByQId

## Overview
Retrieves assignment statistics for a given submitter's QId with optional status filtering.

## Endpoint
- **Route**: `assignments/submitter-qid/{qId}/stats`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `qId` (path, required): Submitter's QID
- `status` (query, optional): Filter by status
  - InProcess: Submitted, Rework, Reworked
  - Archived: Approved, Cancelled, CancelledByEServ

## Responses
- **200 OK**: Returns `GetAssignmentStatsResponse`
  ```json
  {
    "Count": integer
  }
  ```
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication failed

## Business Logic
1. Validates submitter's authorization
2. Joins Assignment with Status table
3. Filters by submitter QId and optional status
4. Returns total count of matching assignments

## Dependencies
- AssignmentContext
- Status entity
