﻿namespace EServiceFunctions.RequestResponseModels.CommentAndAttachment;

public class CreateUpdateAttachmentRequest
{
    public string? AttachmentName { get; set; }
    public string? AttachmentType { get; set; }
    /// <summary>
    /// File attachment as base64 string will be transferred as part of request body
    /// </summary>
    public string? AttachmentData { get; set; }
    public long SubmitterQId { get; set; }
}