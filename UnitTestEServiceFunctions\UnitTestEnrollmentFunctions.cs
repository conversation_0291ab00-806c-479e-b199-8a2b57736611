using EServiceFunctions.Models.Enrollment;
using EServiceFunctions.RequestResponseModels.Enrollment;
using static System.Guid;
using Status = EServiceFunctions.Models.Enrollment.Status;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.RequestResponseModels.Assignment;
using static UnitTestEServiceFunctions.MockDataForEnrollmentFunctions;

namespace UnitTestEServiceFunctions;

[ExcludeFromCodeCoverage]
public class TestEdwDbContextForEnrollment : IDbContextFactory<EDWDbContext>
{
    public EDWDbContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<EDWDbContext>()
            .UseInMemoryDatabase(databaseName: "TestEdwDb")
            .Options;

        var context = new EDWDbContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.PersonMoiDetail.AddRange(GetPersonMoiDetails());
        context.SaveChanges();

        return context;
    }
}

[ExcludeFromCodeCoverage]
public class TestEnrollmentDbContext : IDbContextFactory<EnrollmentContext>
{
    public EnrollmentContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<EnrollmentContext>()
            .UseInMemoryDatabase(databaseName: "TestEnrollmentDb")
            .Options;
        var context = new EnrollmentContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();

        context.Enrollment.AddRange(GetEnrollments());
        context.Status.AddRange(GetStatus());
        context.SaveChanges();
        return context;
    }
}

public class EmptyEnrollmentDbContext : IDbContextFactory<EnrollmentContext>
{
    public EnrollmentContext CreateDbContext()
    {
        var options = new DbContextOptionsBuilder<EnrollmentContext>()
            .UseInMemoryDatabase(databaseName: "EmptyEnrollmentDb")
            .Options;
        var context = new EnrollmentContext(options);
        context.Database.EnsureDeleted();
        context.Database.EnsureCreated();
        
        context.Status.AddRange(GetStatus());
        context.SaveChanges();
        return context;
    }
}

public static class MockDataForEnrollmentFunctions
{
    public static IEnumerable<PersonMoiDetail> GetPersonMoiDetails()
    {
        var personMoiDetail = new PersonMoiDetail
        {
            QId = QId,
            Dob = new DateTime(1986, 09, 28, 0, 0, 0, DateTimeKind.Utc),
            EtlLoadDatetime = new DateTime(2023, 10, 15, 0, 0, 0, DateTimeKind.Utc)
        };

        return new List<PersonMoiDetail> { personMoiDetail };
    }

    public static IEnumerable<Enrollment> GetEnrollments()
    {
        var enrollments = new List<Enrollment>();

        var enrollment = new Enrollment
        {
            Id = 1,
            QId = 28614415181,
            ReqNumber = "REQ0000001",
            HCNumber = "**********",
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = NationalityEn,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            SubmittedBy = "Mohamed Fazrin",
            SubmittedAt = GetCurrentTime(),
            SubmitterQId = 28614415181,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            SubmitterNationality = NationalityEn,
            SubmitterHCNumber = "**********",
            SubmitterHCntCode = "HC",
            Consent = true,
            CreatedAt = GetCurrentTime(),
            UpdatedAt = GetCurrentTime(),
            Status = InProcess,
            StatusInternal = InProcess,
            SN = "SN",
            CreateSource = "UnitTest",
            UpdateSource = "UnitTest"
        };
        enrollments.Add(enrollment);
        return enrollments;
    }

    public static IEnumerable<Status> GetStatus()
    {
        var status = new List<Status>
        {
            new()
            {
                Code = InProcess,
                DescriptionEn = InProcess,
                DescriptionAr = InProcess,
                Category = InProcess
            },
            new()
            {
                Code = "Completed",
                DescriptionEn = "Completed",
                DescriptionAr = "Completed",
                Category = "Completed"
            },
            new()
            {
                Code = Cancelled,
                DescriptionEn = Cancelled,
                DescriptionAr = Cancelled,
                Category = Cancelled
            }
        };

        return status;
    }
}

public class UnitTestEnrollmentFunctions(ITestOutputHelper output)
{
    private readonly ILogger<EnrollmentFunctions> _logger = Mock.Of<ILogger<EnrollmentFunctions>>();

    #region Additional Test Cases for 100% Coverage

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_OkObjectResult_For_Valid_QId()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_Unauthorized_For_Invalid_QId()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, 1);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.Unauthorized, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_NoContentResult_For_Invalid_Status()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = $"?status=InProcess1&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_NoContentResult_For_Invalid_Skip()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = $"?status=InProcess&skip=1000&take={Take}";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_OkObjectResult_For_Invalid_Take()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = $"?status=InProcess&skip={Skip}&take=1000";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_OkObjectResult_For_Empty_Status()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = $"?status=&skip={Skip}&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_OkObjectResult_For_Empty_Skip()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = "?status=&skip=&take=10";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_OkObjectResult_For_Empty_Take()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = $"?status=&skip={Skip}&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_OkObjectResult_For_Empty_QueryParameters()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string queryParams = "?status=&skip=&take=";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(query: queryParams);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentListByQId_Should_Return_OkObjectResult_For_No_QueryParameters()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentListByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentStatsByQId_Should_Return_OkObjectResult_And_One_Count_For_Valid_QId()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentStatsByQId(mockHttpRequestData, LongQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentStatsByQId_Should_Return_Zero_Count_For_Invalid_QId()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentStatsByQId(mockHttpRequestData, 1);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessEnrollmentByQId_Should_Return_True_For_Valid_QId()
    {
        //Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);
        const long applicantQId = 28614415181;
        const long submitterQId = 28614415181;

        //Act
        var response = await enrollmentFunctions.CheckInProcessEnrollment(mockHttpRequestData, applicantQId, submitterQId);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessEnrollmentResponse>(result);
        True(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessEnrollmentByQId_Should_Return_False_For_InvalidQId()
    {
        //Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);
        const long applicantQId = 28614415181;
        const long submitterQId = 28614415182;

        //Act
        var response = await enrollmentFunctions.CheckInProcessEnrollment(mockHttpRequestData, applicantQId, submitterQId);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        //Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<CheckInProcessEnrollmentResponse>(result);
        False(okResponseObject.IsInprocessExist);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);
        const string reqNumber = "REQ0000001";

        // Act
        var response = await enrollmentFunctions.GetEnrollmentItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetEnrollmentItemResponse>(result);
        Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentItemByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber_And_Valid_Status()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        const string reqNumber = "REQ0000001";
        mockHttpRequestData.Query.Add(InProcess, InProcess);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentItemByReqNumber(mockHttpRequestData, reqNumber);
        var result = response.ReadBodyToEnd();
        output.WriteLine(result);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponseObject = DeserializeObject<GetAssignmentItemResponse>(result);
        Equal(reqNumber, okResponseObject.ReqNumber);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentItemByReqNumber_Should_Return_NoContentResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ00000100";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentItemByReqNumber_Should_Return_NoContentResult_For_Invalid_StatusNavigation()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ0001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.GetEnrollmentItemByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.NoContent, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateEnrollment_Should_Return_CreatedResult_For_Valid_Request()
    {
        // Arrange
        var mockEnrollmentContext = new EmptyEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var request = new CreateUpdateEnrollmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = NationalityEn,
            SubmitterNationality = NationalityEn,
            SubmitterHCNumber = HcNumber1,
            SubmitterHCntCode = "HC",
            Consent = true,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.CreateEnrollment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateEnrollment_Should_Return_BadRequestResult_For_Invalid_Request()
    {
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var request = new CreateUpdateEnrollmentRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.CreateEnrollment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateEnrollment_Should_Return_BadRequestResult_For_Missing_Header()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var request = new CreateUpdateEnrollmentRequest
        {
            QId = 28614415181,
            SubmitterQId = 28614415181,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = NationalityEn,
            SubmitterNationality = NationalityEn,
            SubmitterHCNumber = HcNumber1,
            SubmitterHCntCode = "HC",
            Consent = true,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
         var response = await enrollmentFunctions.CreateEnrollment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateEnrollmentByReqNumber_Should_Return_OK_For_Valid_ReqNumber()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ0000001";

        var request = new CreateUpdateEnrollmentRequest
        {
            QId = 28614415181,
            SubmitterQId = 28614415181,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = NationalityEn,
            SubmitterNationality = NationalityEn,
            SubmitterHCNumber = HcNumber1,
            SubmitterHCntCode = "HC",
            Consent = true,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Put);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.UpdateEnrollmentByReqNumber(mockHttpRequestData, reqNumber);

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        output.WriteLine(GetResponseText(okResponse));
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateEnrollmentByReqNumber_Should_Return_BadRequestResult_For_Empty_RequestObject()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ0000001";
        var request = new CreateUpdateEnrollmentRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.UpdateEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateEnrollmentByReqNumber_Should_Return_BadRequestResult_For_Missing_Headers()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ12345678";
        var request = new CreateUpdateAssignmentRequest();
        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(RequestOrigin);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.UpdateEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteEnrollmentByReqNumber_Should_Return_OkObjectResult_For_Valid_ReqNumber()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ0000001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.DeleteEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.OK, okResponse.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteEnrollmentByReqNumber_Should_Return_BadRequestObjectResult_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ00000100";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.DeleteEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var okResponse = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, okResponse.StatusCode);
    }

    #endregion

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentItemByReqNumber_Should_Return_BadRequest_For_Empty_ReqNumber()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);
        string reqNumber = "";

        // Act
        var response = await enrollmentFunctions.GetEnrollmentItemByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_GetEnrollmentItemByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, "99999999999"); // Different QId than in the enrollment
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);
        const string reqNumber = "REQ0000001";

        // Act
        var response = await enrollmentFunctions.GetEnrollmentItemByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CheckInProcessEnrollment_Should_Return_Unauthorized_For_Invalid_Access()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData();
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, "99999999999"); // Different QId than submitterQId
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);
        const long applicantQId = 28614415181;
        const long submitterQId = 28614415181;

        // Act
        var response = await enrollmentFunctions.CheckInProcessEnrollment(mockHttpRequestData, applicantQId, submitterQId);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateEnrollment_Should_Return_Unauthorized_For_Invalid_Access()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var request = new CreateUpdateEnrollmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            Consent = true
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, "99999999999"); // Different QId than in request
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.CreateEnrollment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateEnrollment_When_Nationality_IsEmpty_Should_Return_Created()
    {
        // Arrange
        var mockEnrollmentContext = new EmptyEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        
        // Create the request with empty nationality to trigger EDW lookup
        var request = new CreateUpdateEnrollmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            FNameEn = FirstNameEn,
            MNameEn = MiddleNameEn,
            LNameEn = LastNameEn,
            FNameAr = FirstNameAr,
            MNameAr = MiddleNameAr,
            LNameAr = LastNameAr,
            Nationality = "", // Empty to trigger EDW lookup
            SubmitterNationality = NationalityEn,
            SubmitterHCNumber = HcNumber1,
            SubmitterHCntCode = "HC",
            Consent = true,
            AttachId1 = NewGuid(),
            AttachId2 = NewGuid(),
            AttachId3 = NewGuid(),
            AttachId4 = NewGuid(),
            AttachId5 = NewGuid(),
            AttachId6 = NewGuid(),
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        
        // Make sure we have the PhccEnvironment header
        if (!mockHttpRequestData.Headers.Contains(PhccEnvironment))
        {
            mockHttpRequestData.Headers.Add(PhccEnvironment, "TEST");
        }
        
        // Make sure we have the JwtClaimsQId header to match the submitter QId for authorization
        if (!mockHttpRequestData.Headers.Contains(JwtClaimsQId))
        {
            mockHttpRequestData.Headers.Add(JwtClaimsQId, LongQId.ToString());
        }
        
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.CreateEnrollment(mockHttpRequestData);
        
        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateEnrollmentByReqNumber_Should_Return_BadRequest_For_Invalid_ReqNumber()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ00000100"; // Non-existent request number
        var request = new CreateUpdateEnrollmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            Consent = true
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Put);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.UpdateEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateEnrollmentByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ0000001";
        var request = new CreateUpdateEnrollmentRequest
        {
            QId = LongQId,
            SubmitterQId = 99999999999, // Different from the one in the database
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            Consent = true
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Put);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.UpdateEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_DeleteEnrollmentByReqNumber_Should_Return_Unauthorized_For_Different_SubmitterQId()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        const string reqNumber = "REQ0000001";
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(method: Delete);
        mockHttpRequestData.Headers.Remove(JwtClaimsQId);
        mockHttpRequestData.Headers.Add(JwtClaimsQId, "99999999999"); // Different QId than in the enrollment
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.DeleteEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Unauthorized, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateEnrollment_Should_Handle_EDW_Null_Return_Gracefully()
    {
        // Arrange
        var mockEdwDbContextOptions = new DbContextOptionsBuilder<EDWDbContext>()
            .UseInMemoryDatabase(databaseName: "EmptyEdwDb")
            .Options;

        var edwContext = new EDWDbContext(mockEdwDbContextOptions);
        var mockEdwDbContext = new Mock<IDbContextFactory<EDWDbContext>>();
        mockEdwDbContext.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(edwContext);

        var mockEnrollmentContext = new EmptyEnrollmentDbContext();

        var request = new CreateUpdateEnrollmentRequest
        {
            QId = LongQId,
            SubmitterQId = LongQId,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            // Nationality intentionally left empty to trigger EDW lookup
            Nationality = "",
            SubmitterNationality = NationalityEn,
            SubmitterHCNumber = HcNumber1,
            SubmitterHCntCode = "HC",
            Consent = true,
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Post);
        mockHttpRequestData.Headers.Add(PhccEnvironment, "TEST");
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext.Object, _logger);

        // Act
        var response = await enrollmentFunctions.CreateEnrollment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.Created, result.StatusCode); 
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_CreateEnrollment_Should_Return_BadRequest_For_Empty_Body()
    {
        // Arrange
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var mockEdwDbContext = new TestEdwDbContextForEnrollment();
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: "{}", method: Post);
        var enrollmentFunctions = new EnrollmentFunctions(mockEnrollmentContext, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.CreateEnrollment(mockHttpRequestData);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode);
    }

    [Fact]
    [Trait(Category, UnitTest)]
    public async Task Test_UpdateEnrollmentByReqNumber_Should_Handle_DbUpdateException()
    {
        // Arrange
        // Create a mock enrollment context with a real enrollment but that throws an exception on save
        var mockEnrollmentContext = new TestEnrollmentDbContext();
        var dbContext = mockEnrollmentContext.CreateDbContext();

        // Get an existing enrollment to update
        var enrollment = await dbContext.Enrollment.FirstAsync();
        var reqNumber = enrollment.ReqNumber;

        // Create a new context that will throw an exception
        var mockDbContextFactory = new Mock<IDbContextFactory<EnrollmentContext>>();
        var mockDbContext = new Mock<EnrollmentContext>(new DbContextOptionsBuilder<EnrollmentContext>().Options);

        // Setup the enrollment to be found but throw exception on save
        mockDbContext.Setup(c => c.Enrollment).Returns(dbContext.Enrollment);
        mockDbContext.Setup(c => c.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateException("Simulated database update error", new Exception()));

        mockDbContextFactory.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockDbContext.Object);

        var mockEdwDbContext = new TestEdwDbContextForEnrollment();

        var request = new CreateUpdateEnrollmentRequest
        {
            QId = enrollment.QId ?? 0,
            SubmitterQId = enrollment.SubmitterQId ?? 0,
            SubmitterEmail = UserEmail,
            SubmitterMobile = MobilePhone,
            Nationality = NationalityEn,
            SubmitterNationality = NationalityEn,
            SubmitterHCNumber = HcNumber1,
            SubmitterHCntCode = "HC",
            Consent = true,
            SubmittedBy = FirstNameEn + " " + LastNameEn,
            SubmittedAt = GetCurrentTime()
        };

        var json = SerializeObject(request);
        var mockHttpRequestData = MockHelpers.CreateHttpRequestData(payload: json, method: Put);
        var enrollmentFunctions = new EnrollmentFunctions(mockDbContextFactory.Object, mockEdwDbContext, _logger);

        // Act
        var response = await enrollmentFunctions.UpdateEnrollmentByReqNumber(mockHttpRequestData, reqNumber);
        output.WriteLine(response.ReadBodyToEnd());

        // Assert
        NotNull(response);
        IsType<MockHttpResponseData>(response);
        var result = response as MockHttpResponseData;
        Equal(HttpStatusCode.BadRequest, result.StatusCode); // DbUpdateException is mapped to BadRequest in UtilityHelper.DetermineActionResultBasedOnExceptionType
    }
}