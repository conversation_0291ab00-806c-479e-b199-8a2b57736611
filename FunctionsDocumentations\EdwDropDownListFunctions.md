# EDW Drop Down List Functions Documentation

## Overview
This module provides functionality for retrieving various reference data lists from the Enterprise Data Warehouse (EDW) system. It serves as a centralized source for common dropdown lists used across the application.

## Dependencies
- EDWDbContext (Enterprise Data Warehouse)
- MRDDbContext (Medical Records Database)
- Azure Functions runtime
- .NET 8.0

## Security
- Function-level authorization
- Read-only access to reference data
- Logging of all requests

## Data Sources
- EDW Database
  * Language codes (via stored procedure)
- MRD Database
  * Nationalities
  * Visa types
  * Occupations

## Function Documentation

### Reference Data Lists
- [GetOccupationList](EdwDropDownListFunctions/GetOccupationList.md)
- [GetNationalityList](EdwDropDownListFunctions/GetNationalityList.md)
- [GetVisaTypeList](EdwDropDownListFunctions/GetVisaTypeList.md)
- [GetLanguageList](EdwDropDownListFunctions/GetLanguageList.md)

## Common Response Format
All endpoints follow a standard response format:
- 200 OK: List of items
- 204 No Content: No items found
- 400 Bad Request: Invalid request
- 500 Internal Server Error: Server error

## Database Operations
- Async operations using Entity Framework Core
- Read-only queries with AsNoTracking()
- Stored procedure execution for specific lists
- Proper error handling and logging

## Best Practices
1. Use async/await for all database operations
2. Implement proper error handling
3. Log all requests and responses
4. Use AsNoTracking() for read-only queries
5. Include both English and Arabic descriptions
6. Maintain consistent response formats

## API Endpoints

### 1. Get Occupation List
- **Endpoint**: `GET /occupations`
- **Description**: Retrieves a list of occupations from the Medical Records Database
- **Authorization**: Function level
- **Source**: MAP_MOI_OccupationTypes table
- **Response**:
  - **200 OK**:
    ```json
    [
      {
        "PRF_CODE": "string",      // Occupation code
        "PRF_ENDSC": "string",     // English description
        "PRF_ARDSCM": "string"     // Arabic description (male)
      }
    ]
    ```
  - **204 No Content**: No occupations found
  - **400 Bad Request**: Error processing request
  - **500 Internal Server Error**: Database or system error

### 2. Get Nationality List
- **Endpoint**: `GET /nationalities`
- **Description**: Retrieves a list of nationalities from the Medical Records Database
- **Authorization**: Function level
- **Source**: MRD_Nationalities table
- **Response**:
  - **200 OK**:
    ```json
    [
      {
        "NatCode": "string",     // Country/Nationality code
        "NatNamrEn": "string",   // English country name
        "NatNameAr": "string"    // Arabic country name
      }
    ]
    ```
  - **204 No Content**: No nationalities found
  - **400 Bad Request**: Error processing request
  - **500 Internal Server Error**: Database or system error

### 3. Get Visa Type List
- **Endpoint**: `GET /visatypes`
- **Description**: Retrieves a list of visa types from the Medical Records Database
- **Authorization**: Function level
- **Source**: MAP_MOI_VisaTypes table
- **Response**:
  - **200 OK**:
    ```json
    [
      {
        "VisaTypeCode": "string",        // Visa type code
        "VisaDescriptionEn": "string",   // English description
        "VisaDescriptionAr": "string"    // Arabic description
      }
    ]
    ```
  - **204 No Content**: No visa types found
  - **400 Bad Request**: Error processing request
  - **500 Internal Server Error**: Database or system error

### 4. Get Language List
- **Endpoint**: `GET /languages`
- **Description**: Retrieves a list of languages using a stored procedure from EDW
- **Authorization**: Function level
- **Source**: SP_GET_PHYSICIAN_LANG_CODES stored procedure
- **Response**:
  - **200 OK**:
    ```json
    [
      {
        "LanguageCode": "string",           // Language code
        "LanguageDescriptionEn": "string",  // English description
        "LanguageDescriptionAr": "string"   // Arabic description
      }
    ]
    ```
  - **204 No Content**: No languages found
  - **400 Bad Request**: Error processing request
  - **500 Internal Server Error**: Database or system error

## Technical Implementation

### Database Integration
1. **Context Management**
   - Uses `EDWDbContext` for EDW database access
   - Uses `MRDDbContext` for Medical Records database access
   - Implements DbContextFactory pattern for efficient context management

2. **Query Optimization**
   - Uses `AsNoTracking()` for read-only queries
   - Implements efficient projection using Select statements
   - Utilizes stored procedures for complex queries

3. **Connection Management**
   - Async/await pattern for database operations
   - Proper context disposal with 'using' statements
   - Connection pooling for performance

### Error Handling
1. **Database Errors**
   - Catches and logs DbException
   - Returns appropriate HTTP status codes
   - Provides meaningful error messages

2. **Validation**
   - Checks for empty result sets
   - Validates database connections
   - Handles null reference scenarios

3. **Logging**
   - Request URL logging
   - Operation start/end logging
   - Error details with stack traces
   - Result count logging

### Performance Features
1. **Query Optimization**
   - Efficient data projection
   - No-tracking queries
   - Minimal data transfer

2. **Caching Considerations**
   - Response suitable for client-side caching
   - Relatively static reference data
   - Cache-Control headers

3. **Resource Management**
   - Efficient context lifecycle
   - Proper resource disposal
   - Memory optimization

### Multilingual Support
1. **Data Structure**
   - Bilingual descriptions (English/Arabic)
   - Gender-specific Arabic descriptions
   - Standardized naming conventions

2. **Response Format**
   - Consistent field naming
   - Clear language indicators
   - UTF-8 encoding support

### Security
1. **Authorization**
   - Function-level security
   - Azure Function authentication
   - Proper error handling for unauthorized access

2. **Data Protection**
   - Read-only operations
   - Minimal data exposure
   - Secure connection strings

## Error Response Format
```json
{
  "message": "string",     // Error message
  "details": "string",     // Additional error details (if available)
  "stackTrace": "string"   // Stack trace (in development)
}

## Dependencies
- Entity Framework Core
- Azure Functions
- System.Text.Json
- Microsoft.Azure.WebJobs
- Azure Application Insights

## Best Practices
1. **Data Access**
   - Use async/await consistently
   - Implement proper disposal patterns
   - Optimize query performance

2. **Error Handling**
   - Log all exceptions
   - Return appropriate status codes
   - Provide meaningful error messages

3. **Performance**
   - Use no-tracking queries
   - Implement efficient projections
   - Consider caching strategies

4. **Maintenance**
   - Regular logging review
   - Performance monitoring
   - Error pattern analysis