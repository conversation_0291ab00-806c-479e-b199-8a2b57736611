# GetAssignmentListByQId

## Overview
Retrieves the assignment list for a given submitter's QId with optional filtering and pagination.

## Endpoint
- **Route**: `assignments/submitter-qid/{qId}`
- **Method**: GET
- **Authorization**: Function level

## Parameters
- `qId` (path, required): Submitter's QID
- `status` (query, optional): Filter by status
  - InProcess: Submitted, Rework, Reworked
  - Archived: Approved, Cancelled, CancelledByEServ
- `skip` (query, optional): Number of records to skip for pagination
- `take` (query, optional): Number of records to take for pagination

## Responses
- **200 OK**: Returns list of `GetAssignmentListResponse`
  ```json
  [{
    "QId": "string",
    "ReqNumber": "string",
    "FNameEn": "string",
    "MNameEn": "string",
    "LNameEn": "string",
    "FNameAr": "string",
    "MNameAr": "string",
    "LNameAr": "string",
    "Status": "string",
    "StatusDescriptionEn": "string",
    "StatusDescriptionAr": "string",
    "SubmittedAt": "datetime"
  }]
  ```
- **204 No Content**: No assignments found
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication failed

## Business Logic
1. Validates submitter's authorization
2. Retrieves assignments with optional status filter
3. Joins with Status table for detailed status information
4. Applies pagination
5. Orders results by QId

## Dependencies
- AssignmentContext
- Status entity
