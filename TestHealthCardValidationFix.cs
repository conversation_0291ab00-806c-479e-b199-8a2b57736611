using System;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using EServiceFunctions.Helpers;
using TestDoublesForUnitTest;
using Microsoft.Azure.Functions.Worker;
using Moq;
using System.Text.Json;

namespace TestHealthCardValidationFix
{
    public class TestHealthCardValidationFix
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("Testing Health Card Validation Error Message Format...");

            // Test QID extraction
            TestQidExtraction();

            // Test missing health card
            await TestMissingHealthCard();

            // Test invalid health card format
            await TestInvalidHealthCardFormat();

            Console.WriteLine("All tests completed!");
        }

        private static void TestQidExtraction()
        {
            Console.WriteLine("\n=== Testing QID Extraction ===");

            // Test with a simple object that has SubmitterQId
            var testObj = new { SubmitterQId = 98765432109L, QId = 12345678901L };

            // Use reflection to extract QID (similar to the unit test method)
            var submitterQidProperty = testObj.GetType().GetProperty("SubmitterQId");
            if (submitterQidProperty != null)
            {
                var submitterQidValue = submitterQidProperty.GetValue(testObj);
                if (submitterQidValue != null && long.TryParse(submitterQidValue.ToString(), out long parsedQid))
                {
                    Console.WriteLine($"✅ PASS: Extracted SubmitterQId = {parsedQid}");
                }
                else
                {
                    Console.WriteLine("❌ FAIL: Could not extract SubmitterQId");
                }
            }
        }
        
        private static async Task TestMissingHealthCard()
        {
            Console.WriteLine("\n=== Testing Missing Health Card ===");

            var functionContext = Mock.Of<FunctionContext>();
            var request = new MockHttpRequestData(functionContext);
            request.Headers.Add("X-RequestOrigin", "UnitTest");
            request.Headers.Add("X-JWT-Claims-QID", "12345678901");
            request.Headers.Add("IsAuthValReq", "false"); // Bypass authorization
            var logger = Mock.Of<ILogger<object>>();

            var result = await UtilityHelper.ValidateHealthCardNumberAsync(
                null, 12345678901, "Appointment", request, logger);
            
            if (!result.IsValid && result.ErrorResponse != null)
            {
                result.ErrorResponse.Body.Position = 0;
                using var reader = new System.IO.StreamReader(result.ErrorResponse.Body);
                string responseBody = await reader.ReadToEndAsync();
                
                Console.WriteLine($"Response: {responseBody}");
                
                if (responseBody.Contains("and request name Appointment"))
                {
                    Console.WriteLine("✅ PASS: Missing health card error message format is correct");
                }
                else
                {
                    Console.WriteLine("❌ FAIL: Missing health card error message format is incorrect");
                    Console.WriteLine($"Expected to contain: 'and request name Appointment'");
                }
            }
            else
            {
                Console.WriteLine("❌ FAIL: Expected validation to fail for missing health card");
            }
        }
        
        private static async Task TestInvalidHealthCardFormat()
        {
            Console.WriteLine("\n=== Testing Invalid Health Card Format ===");

            var functionContext = Mock.Of<FunctionContext>();
            var request = new MockHttpRequestData(functionContext);
            request.Headers.Add("X-RequestOrigin", "UnitTest");
            request.Headers.Add("X-JWT-Claims-QID", "12345678901");
            request.Headers.Add("IsAuthValReq", "false"); // Bypass authorization
            var logger = Mock.Of<ILogger<object>>();

            var result = await UtilityHelper.ValidateHealthCardNumberAsync(
                "123", 12345678901, "Assignment", request, logger);
            
            if (!result.IsValid && result.ErrorResponse != null)
            {
                result.ErrorResponse.Body.Position = 0;
                using var reader = new System.IO.StreamReader(result.ErrorResponse.Body);
                string responseBody = await reader.ReadToEndAsync();
                
                Console.WriteLine($"Response: {responseBody}");
                
                if (responseBody.Contains("and request name Assignment"))
                {
                    Console.WriteLine("✅ PASS: Invalid health card error message format is correct");
                }
                else
                {
                    Console.WriteLine("❌ FAIL: Invalid health card error message format is incorrect");
                    Console.WriteLine($"Expected to contain: 'and request name Assignment'");
                }
            }
            else
            {
                Console.WriteLine("❌ FAIL: Expected validation to fail for invalid health card format");
            }
        }
    }
}
