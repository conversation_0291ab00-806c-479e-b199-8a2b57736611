using System;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using EServiceFunctions.Helpers;
using TestDoublesForUnitTest;
using Microsoft.Azure.Functions.Worker;
using Moq;

namespace TestHealthCardValidationFix
{
    public class TestHealthCardValidationFix
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("Testing Health Card Validation Error Message Format...");
            
            // Test missing health card
            await TestMissingHealthCard();
            
            // Test invalid health card format
            await TestInvalidHealthCardFormat();
            
            Console.WriteLine("All tests completed!");
        }
        
        private static async Task TestMissingHealthCard()
        {
            Console.WriteLine("\n=== Testing Missing Health Card ===");
            
            var functionContext = Mock.Of<FunctionContext>();
            var request = new MockHttpRequestData(functionContext);
            request.Headers.Add("X-RequestOrigin", "UnitTest");
            var logger = Mock.Of<ILogger<object>>();
            
            var result = await UtilityHelper.ValidateHealthCardNumberAsync(
                null, 12345678901, "Appointment", request, logger);
            
            if (!result.IsValid && result.ErrorResponse != null)
            {
                result.ErrorResponse.Body.Position = 0;
                using var reader = new System.IO.StreamReader(result.ErrorResponse.Body);
                string responseBody = await reader.ReadToEndAsync();
                
                Console.WriteLine($"Response: {responseBody}");
                
                if (responseBody.Contains("and request name Appointment"))
                {
                    Console.WriteLine("✅ PASS: Missing health card error message format is correct");
                }
                else
                {
                    Console.WriteLine("❌ FAIL: Missing health card error message format is incorrect");
                    Console.WriteLine($"Expected to contain: 'and request name Appointment'");
                }
            }
            else
            {
                Console.WriteLine("❌ FAIL: Expected validation to fail for missing health card");
            }
        }
        
        private static async Task TestInvalidHealthCardFormat()
        {
            Console.WriteLine("\n=== Testing Invalid Health Card Format ===");
            
            var functionContext = Mock.Of<FunctionContext>();
            var request = new MockHttpRequestData(functionContext);
            request.Headers.Add("X-RequestOrigin", "UnitTest");
            var logger = Mock.Of<ILogger<object>>();
            
            var result = await UtilityHelper.ValidateHealthCardNumberAsync(
                "123", 12345678901, "Assignment", request, logger);
            
            if (!result.IsValid && result.ErrorResponse != null)
            {
                result.ErrorResponse.Body.Position = 0;
                using var reader = new System.IO.StreamReader(result.ErrorResponse.Body);
                string responseBody = await reader.ReadToEndAsync();
                
                Console.WriteLine($"Response: {responseBody}");
                
                if (responseBody.Contains("and request name Assignment"))
                {
                    Console.WriteLine("✅ PASS: Invalid health card error message format is correct");
                }
                else
                {
                    Console.WriteLine("❌ FAIL: Invalid health card error message format is incorrect");
                    Console.WriteLine($"Expected to contain: 'and request name Assignment'");
                }
            }
            else
            {
                Console.WriteLine("❌ FAIL: Expected validation to fail for invalid health card format");
            }
        }
    }
}
