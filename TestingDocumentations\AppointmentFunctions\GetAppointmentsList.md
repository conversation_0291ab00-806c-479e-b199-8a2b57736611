# Get Appointments List Tests

## Overview

These tests verify the functionality of retrieving appointment lists through the API. The tests cover both successful retrieval scenarios and various error conditions.

## Test Cases

### TestGetAppointmentsListByQIdv1_ValidRequest_ReturnsOk

Tests the successful retrieval of appointments for a valid QID.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestGetAppointmentsListByQIdv1_ValidRequest_ReturnsOk()
{
    // Arrange
    var request = GetRestRequest("appointments/bookings");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var qIdList = new QIdListRequest { QIds = [GetMoqUser().UserQId.ToString()] };
    request.AddJsonBody(qIdList);

    // Act
    var response = await _client.PostAsync<List<AppointmentResponse>>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.Count > 0);
    response.ForEach(item => Equal(GetMoqUser().UserQId.ToString(), item.QId));
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid QID
  - QID list containing the user's QID
- **Expected Output**: 
  - List of appointments
  - Each appointment belongs to the requested QID
- **Validation Points**:
  - Response is not null
  - Response contains appointments
  - All appointments belong to the requested QID

### TestGetAppointmentsListByQIdv1_EmptyQIdList_ReturnsBadRequest

Tests the API's handling of an empty QID list.

#### Implementation
```csharp
[Fact]
[Trait(Category, UnHappyPath)]
public async Task TestGetAppointmentsListByQIdv1_EmptyQIdList_ReturnsBadRequest()
{
    // Arrange
    var request = GetRestRequest("appointments/bookings");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var qIdList = new QIdListRequest { QIds = [] };
    request.AddJsonBody(qIdList);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    Equal(NoContent, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Empty QID list
- **Expected Output**: 204 NoContent response
- **Validation Points**:
  - Appropriate error status code
  - No content in response

## Request/Response Models

### QIdListRequest
```csharp
public class QIdListRequest
{
    public List<string> QIds { get; set; }
}
```

### AppointmentResponse
```csharp
public class AppointmentResponse
{
    public string QId { get; set; }
    public string AppointmentId { get; set; }
    public DateTime AppointmentDate { get; set; }
    public string Status { get; set; }
    public string ServiceType { get; set; }
    public string Location { get; set; }
    // Additional properties
}
```

## Common Test Patterns

### 1. Request Building
```csharp
var request = GetRestRequest("appointments/bookings");
request.AddOrUpdateHeader(JwtClaimsQId, userQId);
request.AddJsonBody(requestBody);
```

### 2. Response Validation
```csharp
NotNull(response);
Equal(expectedStatusCode, response.StatusCode);
// Additional validations
```

### 3. Test Data Generation
```csharp
private User GetMoqUser() => new()
{
    UserQId = 22222222272,
    // Additional properties
};
```

## Error Scenarios

1. **Empty QID List**
   - Input: Empty list of QIDs
   - Expected: 204 NoContent response
   - Validation: Status code check

2. **Invalid QID Format**
   - Input: Malformed QID
   - Expected: 400 BadRequest response
   - Validation: Error message check

3. **Unauthorized Access**
   - Input: Invalid authentication
   - Expected: 401 Unauthorized response
   - Validation: Authentication error check

## Best Practices

1. **Request Validation**
   - Verify request format
   - Check header values
   - Validate input data

2. **Response Handling**
   - Check status codes
   - Validate response content
   - Verify data integrity

3. **Error Handling**
   - Log error responses
   - Validate error messages
   - Check error codes

## Notes

- Tests use consistent QID values
- Comprehensive logging implemented
- Error scenarios properly validated
- Response content thoroughly checked
