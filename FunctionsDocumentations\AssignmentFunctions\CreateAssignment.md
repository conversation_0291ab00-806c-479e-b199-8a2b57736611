# CreateAssignment

## Overview
Creates a new assignment request in the system.

## Endpoint
- **Route**: `assignments`
- **Method**: POST
- **Authorization**: Function level

## Headers
- `X-RequestOrigin`: Required header indicating request origin

## Request Body
```json
{
  "QId": "long",
  "SubmitterQId": "long",
  "HCNumber": "string", // REQUIRED - Health Card Number
  "SubmitterEmail": "string",
  "SubmitterMobile": "string",
  "FNameEn": "string",
  "MNameEn": "string",
  "LNameEn": "string",
  "FNameAr": "string",
  "MNameAr": "string",
  "LNameAr": "string",
  "ChangeReason": "string" // REQUIRED
}
```

### Required Fields
- **HCNumber**: Health Card Number is mandatory for all assignment requests. Must be a valid alphanumeric string between 5-20 characters.
- **ChangeReason**: Reason for the assignment change request.

## Responses
- **201 Created**: Returns created Assignment
- **400 Bad Request**: Invalid request data or validation failures
  - Missing Health Card Number:
    ```json
    {
      "code": 400,
      "message": "Health Card No is missing for QID {QID} and request name Assignment"
    }
    ```
  - Invalid Health Card Format:
    ```json
    {
      "code": 400,
      "message": "Health Card No format is invalid for QID {QID} and request name Assignment"
    }
    ```
- **401 Unauthorized**: Authentication failed
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates request origin header
2. Deserializes and validates request body
3. Verifies submitter's authorization
4. Generates unique request number
5. Sets initial status to "Saved"
6. Creates assignment record

## Dependencies
- AssignmentContext

## Helper Methods
### MapCreateUpdateAssignmentRequestToAssignment
Converts CreateUpdateAssignmentRequest to Assignment entity

### RandomPassword
Generates unique request number

### GetCurrentTime
Returns current UTC time
