﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.MHDS;

[ExcludeFromCodeCoverage]
[Table("MHDSRequestMedicineList")]
public class MHDSRequestMedicineList
{
    [Key] 
    public long Id { get; set; }
    [StringLength(255)]
    public string? RequestNumber { get; set; }
    [StringLength(255)]
    public string? MedicineName { get; set; }
    [StringLength(255)]
    public string? PrescriptionOrderId { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? PrescribedDate { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? PrescriptionDueDate { get; set; }
    [Column(TypeName = "datetime")]
    public DateTime? LastDispenseDate { get; set; }
    [StringLength(255)]
    public string? SupplyDuration { get; set; }
    [StringLength(255)]
    public string? LastDispensedLocation { get; set; }
    [StringLength(255)]
    public string? OrderByName { get; set; }
    public Guid? AttachId { get; set; }
    public MHDSRequestDetails? RequestNumberNavigation { get; set; }
}