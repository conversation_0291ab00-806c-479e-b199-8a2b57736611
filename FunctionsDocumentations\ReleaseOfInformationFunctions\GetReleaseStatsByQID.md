# Get Release Stats By QID

## Overview
Retrieves statistical information about release requests for a given submitter's QID.

## Endpoint
```http
GET /releases/submitter-qid/{qid}/stats
```

## Authorization
- Function-level authorization
- QID-based access control
- Requires valid submitter QID

## Parameters

### Path Parameters
- `qId` (long, required): Submitter's QID

### Query Parameters
- `status` (string, optional): Filter by status category
  - InProcess: Submitted, ConditionallyApproved, ResubmitOriginalsRequested
  - Archived: Approved, Cancelled, CancelledByEServ, Rejected

## Response

### Success Response (200 OK)
Returns the count of release requests matching the criteria:
```json
{
  "count": "integer"
}
```

### Error Responses
- 400 Bad Request: Invalid parameters
- 401 Unauthorized: Invalid QID or unauthorized access

## Implementation Details
- Uses Entity Framework Core with AsNoTracking for optimal performance
- Implements efficient counting mechanism
- Supports status-based filtering
- Provides real-time statistics

## Security Considerations
- Validates submitter QID authorization
- Implements function-level security
- Ensures data access control based on submitter QID

## Performance Optimization
- Uses AsNoTracking for read-only operations
- Implements efficient counting query
- Optimizes database queries using proper joins
- Efficient status-based filtering
