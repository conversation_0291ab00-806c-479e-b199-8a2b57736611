# Get User And Dependent List By QID

## Overview
Retrieves user and dependent profile information based on a QID. The function fetches data from a database, optionally filtering for only the submitter, then enriches the results with live feed data if available. It handles authorization and data inconsistencies, providing comprehensive profile information.

## Endpoint
```http
GET /userprofiles/user-dependents/{qId}
```

## Authorization
- Function-level authorization
- QID-based access control
- Claims validation

## Headers
- `X-PhccEnvironment` (optional): Environment identifier for EDW data retrieval

## Parameters

### Path Parameters
- `qId` (long, required): User QID

### Query Parameters
- `isSubmitterDetailsOnly` (boolean, optional): If true, returns only the submitter's profile without dependents

## Response

### Success Response (200 OK)
Returns the user profile with optional dependent information:
```json
{
  "qId": "long",
  "qIdExpiryDt": "string",
  "fNameEn": "string",
  "mNameEn": "string",
  "lNameEn": "string",
  "fNameAr": "string",
  "mNameAr": "string",
  "lNameAr": "string",
  "dob": "string",
  "nationalityCode": "string",
  "nationalityEn": "string",
  "nationalityAr": "string",
  "genderCode": "string",
  "genderEn": "string",
  "genderAr": "string",
  "visaCode": "string",
  "visaDescriptionEn": "string",
  "visaDescriptionAr": "string",
  "occupationCode": "string",
  "occupationDescriptionEn": "string",
  "occupationDescriptionAr": "string",
  "phoneMobile": "string",
  "secondaryPhoneMobile": "string",
  "prefComMode": "string",
  "prefSMSLang": "string",
  "consent": "boolean",
  "hcNumber": "string",
  "hcExpiryDate": "string",
  "assignedHealthCenter": "string",
  "assignedHealthCenterEn": "string",
  "assignedHealthCenterAr": "string",
  "physicianId": "string",
  "physicianFullNameEn": "string",
  "physicianFullNameAr": "string",
  "gisAddressStreet": "string",
  "gisAddressBuilding": "string",
  "gisAddressZone": "string",
  "gisAddressUnit": "string",
  "gisAddressUpdatedAt": "string",
  "isStaff": "boolean",
  "isCitizen": "boolean",
  "isOGCC": "boolean",
  "dependents": [
    // Array of profiles with same structure
  ]
}
```

### No Content Response (204)
Returned when no profile is found for the given QID.

### Error Responses
- 401 Unauthorized: Invalid or missing authorization
- 500 Internal Server Error: Database operation failure

## Implementation Details
- Retrieves user profile from EDW database
- Fetches dependent relationships from UserDependent table
- Enriches data with HMC live feeds (if enabled)
- Supports multilingual content (English/Arabic)
- Implements proper error handling
- Uses AsNoTracking for efficient queries

## Business Logic
- Validates QID authorization
- Manages dependent relationships
- Enriches healthcare information
- Handles data transformation
- Supports feature flags
- Maintains data consistency

## Security Considerations
- Validates user authorization
- Implements function-level security
- Ensures data access control
- Validates request origin
- Protects sensitive data
- Proper error handling

## Performance Optimization
- Efficient database queries
- Proper connection handling
- Optimized data retrieval
- Feature flag control
- Caching considerations
- Resource management
