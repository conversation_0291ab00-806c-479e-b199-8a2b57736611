# UpdateAssignment Tests

## Overview
Tests that verify the functionality of updating existing assignments through the API.

## Test Cases

### TestUpdateAssignmentByReqNumber
Tests the successful update of an assignment using a valid request number.

#### Test Implementation
```csharp
[Fact] //happy path
public async Task TestUpdateAssignmentByReqNumber()
{
    // Arrange
    var createRequest = CreateAssignment();
    var createResponse = await _client.ExecuteAsync(createRequest);
    createResponse.ThrowIfNull();
    
    var content = DeserializeObject<CreateUpdateAssignmentResponse>(createResponse.Content!);
    NotNull(content);
    NotEmpty(content.ReqNumber);

    var request = UpdateAssignment(content.ReqNumber);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    Equal(OK, response.StatusCode);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid request number
  - Updated assignment data
- **Expected Output**: 200 OK
- **Validation**:
  - Response is not null
  - Status code is 200 OK

### TestUpdateAssignmentByInvalidReqNumber
Tests error handling when updating an assignment with an invalid request number.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestUpdateAssignmentByInvalidReqNumber()
{
    // Arrange
    var request = UpdateAssignment("INVALID");

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(BadRequest, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Invalid request number
- **Expected Output**: 400 BadRequest
- **Validation**:
  - Response is not null
  - Status code is 400 BadRequest

## Common Patterns
1. **Request Setup**:
   - Creates test assignment first (for valid update)
   - Uses `UpdateAssignment` helper method
   - Adds authentication headers

2. **Response Validation**:
   - Checks for null response
   - Verifies status code
   - Validates response content when applicable

3. **Error Handling**:
   - Uses `ThrowIfNull` for critical validations
   - Logs response for debugging

## Helper Methods
```csharp
private static RestRequest UpdateAssignment(string reqNumber)
{
    var request = GetRestRequest("assignments/{reqNumber}", Method.Put);
    request.AddUrlSegment("reqNumber", reqNumber);
    request.AddOrUpdateHeader(JwtClaimsQId, 22222222272);
    // Additional parameters...
    return request;
}
```

## Notes
- Tests handle both successful and error scenarios
- Proper cleanup of test data
- Comprehensive validation of responses
- Logging implemented for debugging purposes
