﻿namespace EServiceFunctions.Models.UserProfile;

public class UserProfileContext(DbContextOptions<UserProfileContext> options) : DbContext(options)
{
    public virtual DbSet<UserDependent>? UserDependent { get; set; }
    public virtual DbSet<UserProfile>? UserProfile { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasAnnotation("ProductVersion", "2.2.3-servicing-35854");

        modelBuilder.Entity<UserDependent>(entity =>
        {
            entity.HasKey(e => new { e.QId, e.ParentQId })
                .HasName("PK_QId_ParentQId");

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.Property(e => e.Dob).HasColumnType("date");

            entity.Property(e => e.IsMinor).HasDefaultValueSql("((1))");

            entity.Property(e => e.IsRelationshipActive).HasDefaultValueSql("((1))");

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);
        });

        modelBuilder.Entity<UserProfile>(entity =>
        {
            entity.HasKey(e => e.QId)
                .HasName("PK__UserProf__CAB1462BC62CCA30");

            entity.Property(e => e.QId).ValueGeneratedNever();

            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasDefaultValueSql("(getdate())");

            entity.Property(e => e.IsActive).HasDefaultValueSql("((1))");

            entity.Property(e => e.Consent).HasDefaultValueSql("((0))");

            entity.Property(e => e.PrefComMode).HasMaxLength(255);

            entity.Property(e => e.PrefSMSLang).HasMaxLength(255);

            entity.Property(e => e.SecondaryPhoneMobile).HasMaxLength(255);

            entity.Property(e => e.UpdatedAt).HasColumnType("datetime");

            entity.Property(e => e.CreateSource).HasMaxLength(255);

            entity.Property(e => e.UpdateSource).HasMaxLength(255);
        });
    }
}