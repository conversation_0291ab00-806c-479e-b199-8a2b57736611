# GetAssignmentStats Tests

## Overview
Tests that verify the functionality of retrieving assignment statistics through the API.

## Test Cases

### TestGetAssignmentStatsByQId
Tests the successful retrieval of assignment statistics for a valid QID.

#### Test Implementation
```csharp
[Fact] //happy path
public async Task TestGetAssignmentStatsByQId()
{
    // Arrange
    const long qId = 22222222272;
    var request = GetRestRequest("assignments/stats");
    request.AddOrUpdateHeader(JwtClaimsQId, qId);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);
    response.ThrowIfNull();

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
    
    var content = DeserializeObject<AssignmentStatsResponse>(response.Content!);
    NotNull(content);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: Valid QID (22222222272)
- **Expected Output**: 200 OK with statistics
- **Validation**:
  - Response is not null
  - Response is successful
  - Status code is 200 OK
  - Response contains valid statistics

### TestGetAssignmentStatsByInvalidQId
Tests error handling when retrieving statistics with an invalid QID.

#### Test Implementation
```csharp
[Fact] //unhappy path
public async Task TestGetAssignmentStatsByInvalidQId()
{
    // Arrange
    var request = GetRestRequest("assignments/stats");
    request.AddOrUpdateHeader(JwtClaimsQId, 0);

    // Act
    var response = await _client.ExecuteAsync(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    Equal(Unauthorized, response.StatusCode);
}
```

#### Test Details
- **Category**: Unhappy Path
- **Input**: Invalid QID (0)
- **Expected Output**: 401 Unauthorized
- **Validation**:
  - Response is not null
  - Status code is 401 Unauthorized

## Common Patterns
1. **Request Setup**:
   - Uses `GetRestRequest` helper method
   - Adds QID to header
   - Sets appropriate endpoint

2. **Response Validation**:
   - Checks for null response
   - Verifies status code
   - Validates response content when applicable

3. **Error Handling**:
   - Uses `ThrowIfNull` for critical validations
   - Logs response for debugging

## Response Model
```csharp
public class AssignmentStatsResponse
{
    public int TotalAssignments { get; set; }
    public int PendingAssignments { get; set; }
    public int CompletedAssignments { get; set; }
    // Additional statistics...
}
```

## Notes
- Tests use consistent QID values across test cases
- Error cases properly validate error responses
- Logging is implemented for debugging purposes
- Response content is validated for structure and data
