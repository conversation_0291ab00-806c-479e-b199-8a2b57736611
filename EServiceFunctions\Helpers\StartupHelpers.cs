using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using EServiceFunctions.Models.Appointment;
using EServiceFunctions.Models.Assignment;
using EServiceFunctions.Models.CommentAndAttachment;
using EServiceFunctions.Models.Enrollment;
using EServiceFunctions.Models.EServiceGeneric;
using EServiceFunctions.Models.FamilyPhysician;
using EServiceFunctions.Models.GeneralWorks;
using EServiceFunctions.Models.HealthCenter;
using EServiceFunctions.Models.HmcLiveFeeds;
using EServiceFunctions.Models.MHDS;
using EServiceFunctions.Models.MHDS.EDW;
using EServiceFunctions.Models.Registration;
using EServiceFunctions.Models.ReleaseItem;
using EServiceFunctions.Models.ReleaseOfInformation;
using EServiceFunctions.Models.Transfer;
using EServiceFunctions.Models.UserProfile;
using Microsoft.FeatureManagement;
using static System.Console;
using static EServiceFunctions.Helpers.KeyVaultHelper;
using static Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId;
using static Microsoft.EntityFrameworkCore.QueryTrackingBehavior;

namespace EServiceFunctions.Helpers;

public static class StartupHelpers
{
    public static void ConfigureApplicationServices(IServiceCollection services)
    {
        services.AddFeatureManagement();
        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();
    }

    public static void ConfigureEnvironmentVariables()
    {
        try
        {
            var config = new ConfigurationBuilder()
                .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true)
                .Build();

            var valuesSection = config.GetSection("Values");
            foreach (var key in valuesSection.AsEnumerable())
            {
                if (key.Value is not null && key.Key.Contains("Values:"))
                {
                    var cleanedKey = key.Key.Replace("Values:", string.Empty);
                    SetEnvironmentVariable(cleanedKey, key.Value);
                    Write($"Environment Variable: {cleanedKey} = {key.Value}\n");
                }
            }
        }
        catch (Exception ex)
        {
            WriteLine($"Error configuring environment variables: {ex.Message}");
            throw;
        }
    }

    public static void ConfigureApplicationInsights(IServiceCollection services)
    {
        services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>((module, o) =>
        {
            module.EnableSqlCommandTextInstrumentation = true;

            if (o.DeveloperMode == true)
            {
                o.EnableDebugLogger = true;
                o.EnableDiagnosticsTelemetryModule = true;
            }
        });

        ConfigureLogging(services);
    }

    private static void ConfigureLogging(IServiceCollection services)
    {
        services.Configure<LoggerFilterOptions>(options =>
        {
            var applicationInsightsRule = options.Rules.FirstOrDefault(
                rule => rule.ProviderName ==
                        "Microsoft.Extensions.Logging.ApplicationInsights.ApplicationInsightsLoggerProvider"
            );

            if (applicationInsightsRule != null)
            {
                options.Rules.Remove(applicationInsightsRule);
                options.Rules.Add(CreateApplicationInsightsRule(applicationInsightsRule, LogLevel.Information));
            }
        });
    }

    private static LoggerFilterRule CreateApplicationInsightsRule(LoggerFilterRule existingRule, LogLevel newLogLevel)
    {
        return new LoggerFilterRule(
            providerName: existingRule.ProviderName,
            categoryName: existingRule.CategoryName,
            logLevel: newLogLevel,
            filter: existingRule.Filter
        );
    }

    public static void ConfigureDatabaseContexts(IServiceCollection services)
    {
        try
        {
            var connectionString = GetEServDevConnectionString();
            var connectionStringMrd = GetSecret("MRDSqlConnectionString");
            var connectionStringGw = GetSecret("GeneralWorksSqlConnectionString");
            var connectionStringEdw = GetSecret("PHCCEDWSqlConnectionString");
            var hmcConnectionString = GetSecret("HMCLiveFeed");

            LogConnectionStrings(connectionString, connectionStringMrd, connectionStringGw, connectionStringEdw);
            AddDbContextFactories(services, connectionString, connectionStringMrd, connectionStringGw,
                connectionStringEdw, hmcConnectionString);
        }
        catch (Exception ex)
        {
            WriteLine($"Error configuring database contexts: {ex.Message}");
            throw;
        }
    }

    private static string GetEServDevConnectionString()
    {
        var isLocal = GetEnvironmentVariable("IsLocal") ??
                      throw new InvalidOperationException("Environment variable 'IsLocal' is not set.");

        if (isLocal.Equals("true", OrdinalIgnoreCase))
        {
            return GetEnvironmentVariable("EServDevConnectionString") ??
                   throw new InvalidOperationException("Environment variable 'EServDevConnectionString' is not set.");
        }

        return GetSecret("SQLConnectionString") ??
               throw new InvalidOperationException("Secret 'SQLConnectionString' is not set.");
    }

    private static void LogConnectionStrings(string connectionString, string connectionStringMrd,
        string connectionStringGw, string connectionStringEdw)
    {
        if (!GetEnvironmentVariable("EDWEnv").Equals("Production", OrdinalIgnoreCase))
        {
            LogConnectionString("EServDev Connection String", connectionString);
            LogConnectionString("Connection String MRD", connectionStringMrd);
            LogConnectionString("Connection String GW", connectionStringGw);
            LogConnectionString("Connection String EDW", connectionStringEdw);
        }
    }

    private static void AddDbContextFactories(IServiceCollection services, string connectionString,
        string connectionStringMrd,
        string connectionStringGw, string connectionStringEdw, string hmcConnectionString)
    {
        services.AddPooledDbContextFactory<EDWDbContext>(opt =>
            ConfigureDbContext(opt, connectionStringEdw));

        services.AddPooledDbContextFactory<HmcLiveFeedContext>(opt =>
            ConfigureDbContext(opt, hmcConnectionString));

        services.AddPooledDbContextFactory<MRDDbContext>(opt =>
            ConfigureDbContext(opt, connectionStringMrd));

        services.AddPooledDbContextFactory<AppointmentContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<AssignmentContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<CommentAndAttachmentContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<EnrollmentContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<HealthCenterContext>(opt =>
            ConfigureDbContext(opt, connectionStringMrd));

        services.AddPooledDbContextFactory<RegistrationContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<ReleaseItemContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<ReleaseContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<TransferContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<UserProfileContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<EServiceContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<EServiceDbContext>(opt =>
            ConfigureDbContext(opt, connectionString));

        services.AddPooledDbContextFactory<GeneralWorksContext>(opt =>
            ConfigureDbContext(opt, connectionStringGw));

        services.AddPooledDbContextFactory<MHDSContext>(opt =>
            ConfigureDbContext(opt, connectionString));
    }

    static void LogConnectionString(string label, string connectionString)
    {
        WriteLine($"{label}: {connectionString}");
        WriteLine(new string('-', 40));
    }

    private static void ConfigureDbContext(DbContextOptionsBuilder opt, string connectionString)
    {
        opt.UseSqlServer(connectionString, options =>
        {
            options.EnableRetryOnFailure(
                maxRetryCount: 4,
                maxRetryDelay: FromSeconds(3),
                errorNumbersToAdd: []);

            opt.UseQueryTrackingBehavior(TrackAll);
            opt.EnableDetailedErrors();
            opt.EnableSensitiveDataLogging();
            opt.ConfigureWarnings(warnings =>
            {
                warnings.Log(FirstWithoutOrderByAndFilterWarning, RowLimitingOperationWithoutOrderByWarning);
            });
        });
    }

    private static string GetEnvironmentVariable(string name) =>
        Environment.GetEnvironmentVariable(name, EnvironmentVariableTarget.Process)!;
}