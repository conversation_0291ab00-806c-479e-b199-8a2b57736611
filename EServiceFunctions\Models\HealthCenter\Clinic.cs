﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EServiceFunctions.Models.HealthCenter;

[Table("MRD_Clinics")]
public class Clinic
{
    [Key]
    public int ID { get; set; }
    public string? CLINIC_ID { get; set; }
    public string? FAC_CODE { get; set; }
    [Column("CLINIC_NAME_EN")]
    public string? ClinicNameEn { get; set; }
    [Column("CLINIC_NAME_ARB")]
    public string? ClinicNameAr { get; set; }
    public DateTime? CREATED_DATE { get; set; }
    public DateTime? LAST_UPDATED_DATE { get; set; }
    [Column("CLINIC_CODE")]
    public string? ClinicCode { get; set; }
    public int? ACTIVE_IND { get; set; }
    public int? IS_SELF_REFERRAL { get; set; }
    public int? IS_CANCEL { get; set; }
    public int? IS_RESCHEDULE { get; set; }
}