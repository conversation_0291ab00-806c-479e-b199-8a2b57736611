# minimal yaml approach.
# Feature pipeline
name: Release Feature Branch
run-name: Release Feature Branch by @${{ github.actor }}
on:
    workflow_dispatch: 
      inputs:
        BRANCH:   #'release/bug-fix'  # please provide branch name for each run
           required: true 
           type: string
jobs:
    cicd-job:
        name: EServices RestAPI CI/CD
        uses: public-health-care-center-CORP/WorkflowRepo/.github/workflows/ci-cd-eservices-restapi-feature-branch-ci-cd.yml@main
        secrets: inherit
        with:
           BRANCH: ${{inputs.BRANCH}}
