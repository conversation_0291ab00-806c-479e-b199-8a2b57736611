# Update Comments Tests

## Overview

These tests verify the functionality of updating existing comments in the system. The test suite covers content updates, attachment modifications, and validation scenarios.

## Test Cases

### TestUpdateComment_ValidRequest_ReturnsOk

Tests the successful update of a comment with valid changes.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateComment_ValidRequest_ReturnsOk()
{
    // Arrange
    var commentId = await CreateTestComment();
    var request = GetRestRequest($"comments/{commentId}");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    var updateRequest = new UpdateCommentRequest
    {
        Content = "Updated content",
        Type = "Updated"
    };
    request.AddJsonBody(updateRequest);

    // Act
    var response = await _client.ExecuteAsync<CommentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    Equal(OK, response.StatusCode);
    NotNull(response.Data);
    Equal(updateRequest.Content, response.Data.Content);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Valid comment ID
  - Updated content
- **Expected Output**: 
  - 200 OK response
  - Updated comment data
- **Validation Points**:
  - Response success
  - Content update
  - Type update

### TestUpdateComment_AddAttachment_ReturnsOk

Tests adding a new attachment to an existing comment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestUpdateComment_AddAttachment_ReturnsOk()
{
    // Arrange
    var commentId = await CreateTestComment();
    var request = GetRestRequest($"comments/{commentId}/attachments");
    request.AddOrUpdateHeader(JwtClaimsQId, GetMoqUser().UserQId);
    
    var filePath = CreateTestFile();
    request.AddFile("attachment", filePath);

    // Act
    var response = await _client.ExecuteAsync<CommentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsSuccessful);
    NotNull(response.Data);
    NotEmpty(response.Data.Attachments);
}
```

#### Test Details
- **Category**: Happy Path
- **Input**: 
  - Comment ID
  - New attachment
- **Expected Output**: 
  - Success response
  - Updated attachments list
- **Validation Points**:
  - File upload success
  - Attachment list update
  - Response validation

## Request/Response Models

### UpdateCommentRequest
```csharp
public class UpdateCommentRequest
{
    public string? Content { get; set; }
    public string? Type { get; set; }
    public List<string>? RemoveAttachments { get; set; }
    public List<IFormFile>? AddAttachments { get; set; }
}
```

### CommentResponse
```csharp
public class CommentResponse
{
    public string CommentId { get; set; }
    public string Content { get; set; }
    public string Type { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string UpdatedBy { get; set; }
    public List<AttachmentInfo> Attachments { get; set; }
}
```

## Update Rules

1. **Content Rules**
   - Non-empty content
   - Maximum length
   - Valid characters

2. **Attachment Rules**
   - Maximum count
   - Size limits
   - Type restrictions

3. **Permission Rules**
   - Author only
   - Time window
   - Status check

## Implementation Details

### 1. Update Processing
```csharp
private async Task<CommentResponse> UpdateComment(
    string commentId,
    UpdateCommentRequest request)
{
    var comment = await GetCommentOrThrow(commentId);
    await ValidateUpdatePermission(comment);

    if (request.Content != null)
        comment.Content = request.Content;

    if (request.Type != null)
        comment.Type = request.Type;

    comment.UpdatedAt = DateTime.UtcNow;
    comment.UpdatedBy = GetCurrentUser();

    await ProcessAttachmentChanges(comment, request);
    await _commentRepository.UpdateAsync(comment);

    return MapToResponse(comment);
}
```

### 2. Attachment Management
```csharp
private async Task ProcessAttachmentChanges(
    Comment comment,
    UpdateCommentRequest request)
{
    // Remove attachments
    if (request.RemoveAttachments?.Any() == true)
    {
        foreach (var attachmentId in request.RemoveAttachments)
        {
            await RemoveAttachment(comment, attachmentId);
        }
    }

    // Add new attachments
    if (request.AddAttachments?.Any() == true)
    {
        foreach (var file in request.AddAttachments)
        {
            await AddAttachment(comment, file);
        }
    }
}
```

### 3. Permission Validation
```csharp
private async Task ValidateUpdatePermission(Comment comment)
{
    var currentUser = GetCurrentUser();
    
    if (comment.CreatedBy != currentUser)
        throw new UnauthorizedException("Only the author can update this comment");

    var updateWindow = DateTime.UtcNow - comment.CreatedAt;
    if (updateWindow > TimeSpan.FromHours(24))
        throw new ValidationException("Comment can only be updated within 24 hours");
}
```

## Error Handling

### 1. Update Validation
```csharp
private void ValidateUpdateRequest(UpdateCommentRequest request)
{
    var errors = new List<string>();

    if (request.Content != null)
    {
        if (string.IsNullOrEmpty(request.Content))
            errors.Add("Content cannot be empty");

        if (request.Content.Length > MaxContentLength)
            errors.Add($"Content exceeds maximum length of {MaxContentLength}");
    }

    if (request.AddAttachments?.Count > MaxAttachments)
        errors.Add($"Maximum {MaxAttachments} attachments allowed");

    if (errors.Any())
        throw new ValidationException(errors);
}
```

### 2. Attachment Validation
```csharp
private async Task ValidateAttachment(IFormFile file)
{
    if (file.Length > MaxFileSize)
        throw new ValidationException($"File size exceeds {MaxFileSize} bytes");

    if (!AllowedFileTypes.Contains(file.ContentType))
        throw new ValidationException($"File type {file.ContentType} not allowed");

    await ScanFileForViruses(file);
}
```

## Test Helper Methods

### 1. Comment Creation
```csharp
private async Task<string> CreateTestComment()
{
    var comment = new Comment
    {
        Content = "Test content",
        Type = "Test",
        CreatedBy = GetMoqUser().UserQId.ToString(),
        CreatedAt = DateTime.UtcNow
    };

    await _commentRepository.AddAsync(comment);
    return comment.Id;
}
```

### 2. File Handling
```csharp
private string CreateTestFile(int sizeInBytes = 1024)
{
    var path = Path.GetTempFileName();
    using var fs = File.Create(path);
    fs.SetLength(sizeInBytes);
    return path;
}
```

## Notes

- Handles partial updates
- Manages attachments efficiently
- Validates permissions
- Tracks update history
- Implements proper cleanup

## Recommendations

1. **Update Strategy**
   - Implement versioning
   - Track change history
   - Support rollback

2. **Attachment Handling**
   - Implement lazy loading
   - Support resumable uploads
   - Optimize storage

3. **Performance**
   - Cache common data
   - Optimize queries
   - Handle concurrency
