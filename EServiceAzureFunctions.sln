﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.31903.286
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{AC75CF68-DC20-41B1-971C-AF286B29CC83}"
	ProjectSection(SolutionItems) = preProject
		DBScripts\AlterStatements_11-Oct-2020.sql = DBScripts\AlterStatements_11-Oct-2020.sql
		DBScripts\AlterStatements_12-Nov-2020.sql = DBScripts\AlterStatements_12-Nov-2020.sql
		DBScripts\AlterStatements_15-Oct-2020.sql = DBScripts\AlterStatements_15-Oct-2020.sql
		DBScripts\AlterStatements_21-Dec-2020.sql = DBScripts\AlterStatements_21-Dec-2020.sql
		DBScripts\AlterStatements_21-Sept-2020.sql = DBScripts\AlterStatements_21-Sept-2020.sql
		DBScripts\AlterStatements_22-Nov-2020.sql = DBScripts\AlterStatements_22-Nov-2020.sql
		DBScripts\AlterStatements_28-Oct-2020.sql = DBScripts\AlterStatements_28-Oct-2020.sql
		CreateUpdateSourceScript.sql = CreateUpdateSourceScript.sql
		DBScripts\EseServDBScrpits.sql = DBScripts\EseServDBScrpits.sql
		StaticDDLScript.sql = StaticDDLScript.sql
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EServiceFunctions", "EServiceFunctions\EServiceFunctions.csproj", "{D75590FE-D3C3-4DEC-B282-1E55F8BB876C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2022", "2022", "{D9D28D67-458F-4ABC-9571-90EABD84E32A}"
	ProjectSection(SolutionItems) = preProject
		DBScripts\2022\AlterStatements_09-Feb2022.sql = DBScripts\2022\AlterStatements_09-Feb2022.sql
		WI2370_InsertStatements_22Jun2022.sql = WI2370_InsertStatements_22Jun2022.sql
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2021", "2021", "{140B2F42-7D39-4EE8-81DE-7385F7A5FCE3}"
	ProjectSection(SolutionItems) = preProject
		DBScripts\2021\AlterStatements_2-Jan-2021.sql = DBScripts\2021\AlterStatements_2-Jan-2021.sql
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "2020", "2020", "{69C1A137-C8B8-442B-8CB9-1A66C32D16E1}"
	ProjectSection(SolutionItems) = preProject
		DBScripts\AlterStatements_11-Oct-2020.sql = DBScripts\AlterStatements_11-Oct-2020.sql
		DBScripts\AlterStatements_12-Nov-2020.sql = DBScripts\AlterStatements_12-Nov-2020.sql
		DBScripts\AlterStatements_15-Oct-2020.sql = DBScripts\AlterStatements_15-Oct-2020.sql
		DBScripts\AlterStatements_21-Dec-2020.sql = DBScripts\AlterStatements_21-Dec-2020.sql
		DBScripts\AlterStatements_21-Sept-2020.sql = DBScripts\AlterStatements_21-Sept-2020.sql
		DBScripts\AlterStatements_22-Nov-2020.sql = DBScripts\AlterStatements_22-Nov-2020.sql
		DBScripts\AlterStatements_28-Oct-2020.sql = DBScripts\AlterStatements_28-Oct-2020.sql
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TestEServiceFunctions", "TestEServiceFunctions\TestEServiceFunctions.csproj", "{7CF75B7E-D2BC-4C63-9592-FBFA294717A6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MockDataLibrary", "MockDataLibrary\MockDataLibrary.csproj", "{24E2C2DA-BC62-4652-971F-74F4F98066D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnitTestEServiceFunctions", "UnitTestEServiceFunctions\UnitTestEServiceFunctions.csproj", "{4EC3C312-51C5-4A51-9436-F6910B47E12D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestDoublesForUnitTest", "TestDoublesForUnitTest\TestDoublesForUnitTest\TestDoublesForUnitTest.csproj", "{CA00D314-2FA1-4308-9B05-945956976147}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D75590FE-D3C3-4DEC-B282-1E55F8BB876C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D75590FE-D3C3-4DEC-B282-1E55F8BB876C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D75590FE-D3C3-4DEC-B282-1E55F8BB876C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D75590FE-D3C3-4DEC-B282-1E55F8BB876C}.Release|Any CPU.Build.0 = Release|Any CPU
		{7CF75B7E-D2BC-4C63-9592-FBFA294717A6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7CF75B7E-D2BC-4C63-9592-FBFA294717A6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7CF75B7E-D2BC-4C63-9592-FBFA294717A6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7CF75B7E-D2BC-4C63-9592-FBFA294717A6}.Release|Any CPU.Build.0 = Release|Any CPU
		{C342ED7D-A730-4CF2-BCEB-4E5C543BECE9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C342ED7D-A730-4CF2-BCEB-4E5C543BECE9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C342ED7D-A730-4CF2-BCEB-4E5C543BECE9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C342ED7D-A730-4CF2-BCEB-4E5C543BECE9}.Release|Any CPU.Build.0 = Release|Any CPU
		{24E2C2DA-BC62-4652-971F-74F4F98066D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{24E2C2DA-BC62-4652-971F-74F4F98066D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{24E2C2DA-BC62-4652-971F-74F4F98066D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{24E2C2DA-BC62-4652-971F-74F4F98066D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{4EC3C312-51C5-4A51-9436-F6910B47E12D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4EC3C312-51C5-4A51-9436-F6910B47E12D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4EC3C312-51C5-4A51-9436-F6910B47E12D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4EC3C312-51C5-4A51-9436-F6910B47E12D}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA00D314-2FA1-4308-9B05-945956976147}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA00D314-2FA1-4308-9B05-945956976147}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA00D314-2FA1-4308-9B05-945956976147}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA00D314-2FA1-4308-9B05-945956976147}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{D9D28D67-458F-4ABC-9571-90EABD84E32A} = {AC75CF68-DC20-41B1-971C-AF286B29CC83}
		{140B2F42-7D39-4EE8-81DE-7385F7A5FCE3} = {AC75CF68-DC20-41B1-971C-AF286B29CC83}
		{69C1A137-C8B8-442B-8CB9-1A66C32D16E1} = {AC75CF68-DC20-41B1-971C-AF286B29CC83}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B7F911BB-FEFF-4AAB-BA49-A9B00DA23624}
	EndGlobalSection
EndGlobal
