# DeleteAssignmentByReqNumber

## Overview
Deletes (soft delete) an assignment based on the request number.

## Endpoint
- **Route**: `assignments/{reqNumber}`
- **Method**: DELETE
- **Authorization**: Function level

## Parameters
- `reqNumber` (path, required): Assignment request number

## Headers
- `X-RequestOrigin`: Required header indicating request origin

## Responses
- **200 OK**: Assignment successfully deleted
- **400 Bad Request**: Invalid request number
- **401 Unauthorized**: Authentication failed
- **404 Not Found**: Assignment not found
- **500 Internal Server Error**: Database error

## Business Logic
1. Validates request origin header
2. Validates request number
3. Verifies submitter's authorization
4. Updates assignment status to indicate deletion
5. Records update time and source

## Dependencies
- AssignmentContext

## Helper Methods
### GetCurrentTime
Returns current UTC time
