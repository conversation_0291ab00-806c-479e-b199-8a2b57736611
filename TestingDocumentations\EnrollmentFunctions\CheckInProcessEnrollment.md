# Check In-Process Enrollment Tests

## Test Cases

### 1. TestCheckInProcessEnrollmentFalse

Tests validation of non-existent in-process enrollment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckInProcessEnrollmentFalse()
{
    // Arrange
    var request = GetRestRequest("enrollments/inprocess-validation/{applicantQId}/{submitterQId}");
    request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
    request.AddUrlSegment("applicantQId", "33322222291");
    request.AddUrlSegment("submitterQId", "22222222270");
    
    // Act
    var response = await _client.GetAsync<CheckInProcessEnrollmentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    False(response.IsInprocessExist);
}
```

### 2. TestCheckInProcessEnrollmentTrue

Tests validation of existing in-process enrollment.

#### Implementation
```csharp
[Fact]
[Trait(Category, HappyPath)]
public async Task TestCheckInProcessEnrollmentTrue()
{
    // Arrange
    var request = GetRestRequest("enrollments/inprocess-validation/{applicantQId}/{submitterQId}");
    request.AddOrUpdateHeader(JwtClaimsQId, "22222222270");
    request.AddUrlSegment("applicantQId", "9876543210");
    request.AddUrlSegment("submitterQId", "22222222270");

    // Act
    var response = await _client.GetAsync<CheckInProcessEnrollmentResponse>(request);
    testOutputHelper.LogToConsole(response);

    // Assert
    NotNull(response);
    True(response.IsInprocessExist);
}
```

## Request Details

### Endpoint
```
GET enrollments/inprocess-validation/{applicantQId}/{submitterQId}
```

### Headers
- `JwtClaimsQId`: "22222222270"

### URL Parameters
- `applicantQId`: Applicant's QID
- `submitterQId`: Submitter's QID

## Response Model

### CheckInProcessEnrollmentResponse
```csharp
public class CheckInProcessEnrollmentResponse
{
    public bool IsInprocessExist { get; set; }
}
```

## Test Data

### Test Case 1 (False)
- Applicant QID: 33322222291
- Submitter QID: 22222222270
- Expected: No in-process enrollment

### Test Case 2 (True)
- Applicant QID: 9876543210
- Submitter QID: 22222222270
- Expected: Has in-process enrollment

## Validation Rules

### Response Validation
1. Response object not null
2. IsInprocessExist boolean value matches expected state

## Error Cases

1. **Invalid QID Format**
   - Non-numeric QID
   - QID length not 11 digits

2. **Authorization Errors**
   - Missing JWT claims
   - Invalid QID in claims

3. **Invalid Parameters**
   - Missing applicant QID
   - Missing submitter QID

## Notes

1. **Authentication**
   - Requires valid JWT token
   - QID must match claims

2. **Response Format**
   - Simple boolean response
   - Clear state indication

3. **Use Cases**
   - Prevent duplicate enrollments
   - Validate enrollment state
